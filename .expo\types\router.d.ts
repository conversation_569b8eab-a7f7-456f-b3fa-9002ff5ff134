/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppFooter`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppHeader`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppLayout`; params?: Router.UnknownInputParams; } | { pathname: `/components/auth-popup`; params?: Router.UnknownInputParams; } | { pathname: `/components/authentication`; params?: Router.UnknownInputParams; } | { pathname: `/components/chatbox`; params?: Router.UnknownInputParams; } | { pathname: `/components/ErrorDisplay`; params?: Router.UnknownInputParams; } | { pathname: `/components/gateway-auth`; params?: Router.UnknownInputParams; } | { pathname: `/components/LoadingSpinner`; params?: Router.UnknownInputParams; } | { pathname: `/components/messages`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/BulletinContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/Bulletins`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/BulletinsContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/GenericBulletinsList`; params?: Router.UnknownInputParams; } | { pathname: `/components/ClassGuides/ClassGuideContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/ClassGuides/ClassGuideTabContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/common/BreadcrumbNavigation`; params?: Router.UnknownInputParams; } | { pathname: `/components/FAQ/FaqContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/FAQ/FaqTabContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/IndustryGuides/IndustryGuideContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/TrainingManuals/TrainingManualContent`; params?: Router.UnknownInputParams; } | { pathname: `/config/tabConfigurations`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/appStateContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/breadcrumbUtils`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/navigationService`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/service`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/stylesheet`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownInputParams; } | { pathname: `/lib/component-props`; params?: Router.UnknownInputParams; } | { pathname: `/lib/component-props/styleguide`; params?: Router.UnknownInputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownInputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownInputParams; } | { pathname: `/modals/options`; params?: Router.UnknownInputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownInputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/ClassCodeProcessor`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/ConvertToArray`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/CustomTypes`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/DecodeHtmlEntities`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/appLayout.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/breadcrumbUtils.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/classGuide-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/core-functionality.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/expo-router-navigation.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/faq-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownInputParams; } | { pathname: `/paas-source/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/components/AppFooter`; params?: Router.UnknownOutputParams; } | { pathname: `/components/AppHeader`; params?: Router.UnknownOutputParams; } | { pathname: `/components/AppLayout`; params?: Router.UnknownOutputParams; } | { pathname: `/components/auth-popup`; params?: Router.UnknownOutputParams; } | { pathname: `/components/authentication`; params?: Router.UnknownOutputParams; } | { pathname: `/components/chatbox`; params?: Router.UnknownOutputParams; } | { pathname: `/components/ErrorDisplay`; params?: Router.UnknownOutputParams; } | { pathname: `/components/gateway-auth`; params?: Router.UnknownOutputParams; } | { pathname: `/components/LoadingSpinner`; params?: Router.UnknownOutputParams; } | { pathname: `/components/messages`; params?: Router.UnknownOutputParams; } | { pathname: `/components/Bulletins/BulletinContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/Bulletins/Bulletins`; params?: Router.UnknownOutputParams; } | { pathname: `/components/Bulletins/BulletinsContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/Bulletins/GenericBulletinsList`; params?: Router.UnknownOutputParams; } | { pathname: `/components/ClassGuides/ClassGuideContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/ClassGuides/ClassGuideTabContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/common/BreadcrumbNavigation`; params?: Router.UnknownOutputParams; } | { pathname: `/components/FAQ/FaqContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/FAQ/FaqTabContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/IndustryGuides/IndustryGuideContent`; params?: Router.UnknownOutputParams; } | { pathname: `/components/TrainingManuals/TrainingManualContent`; params?: Router.UnknownOutputParams; } | { pathname: `/config/tabConfigurations`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/appStateContext`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/breadcrumbUtils`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/navigationService`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/service`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/stylesheet`; params?: Router.UnknownOutputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownOutputParams; } | { pathname: `/lib/component-props`; params?: Router.UnknownOutputParams; } | { pathname: `/lib/component-props/styleguide`; params?: Router.UnknownOutputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownOutputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/options`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownOutputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownOutputParams; } | { pathname: `/Utilities/ClassCodeProcessor`; params?: Router.UnknownOutputParams; } | { pathname: `/Utilities/ConvertToArray`; params?: Router.UnknownOutputParams; } | { pathname: `/Utilities/CustomTypes`; params?: Router.UnknownOutputParams; } | { pathname: `/Utilities/DecodeHtmlEntities`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/appLayout.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/breadcrumbUtils.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/classGuide-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/core-functionality.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/expo-router-navigation.test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/faq-test`; params?: Router.UnknownOutputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownOutputParams; } | { pathname: `/paas-source/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/components/AppFooter${`?${string}` | `#${string}` | ''}` | `/components/AppHeader${`?${string}` | `#${string}` | ''}` | `/components/AppLayout${`?${string}` | `#${string}` | ''}` | `/components/auth-popup${`?${string}` | `#${string}` | ''}` | `/components/authentication${`?${string}` | `#${string}` | ''}` | `/components/chatbox${`?${string}` | `#${string}` | ''}` | `/components/ErrorDisplay${`?${string}` | `#${string}` | ''}` | `/components/gateway-auth${`?${string}` | `#${string}` | ''}` | `/components/LoadingSpinner${`?${string}` | `#${string}` | ''}` | `/components/messages${`?${string}` | `#${string}` | ''}` | `/components/Bulletins/BulletinContent${`?${string}` | `#${string}` | ''}` | `/components/Bulletins/Bulletins${`?${string}` | `#${string}` | ''}` | `/components/Bulletins/BulletinsContent${`?${string}` | `#${string}` | ''}` | `/components/Bulletins/GenericBulletinsList${`?${string}` | `#${string}` | ''}` | `/components/ClassGuides/ClassGuideContent${`?${string}` | `#${string}` | ''}` | `/components/ClassGuides/ClassGuideTabContent${`?${string}` | `#${string}` | ''}` | `/components/common/BreadcrumbNavigation${`?${string}` | `#${string}` | ''}` | `/components/FAQ/FaqContent${`?${string}` | `#${string}` | ''}` | `/components/FAQ/FaqTabContent${`?${string}` | `#${string}` | ''}` | `/components/IndustryGuides/IndustryGuideContent${`?${string}` | `#${string}` | ''}` | `/components/TrainingManuals/TrainingManualContent${`?${string}` | `#${string}` | ''}` | `/config/tabConfigurations${`?${string}` | `#${string}` | ''}` | `/helpers/appStateContext${`?${string}` | `#${string}` | ''}` | `/helpers/authContext${`?${string}` | `#${string}` | ''}` | `/helpers/breadcrumbUtils${`?${string}` | `#${string}` | ''}` | `/helpers/mock${`?${string}` | `#${string}` | ''}` | `/helpers/model${`?${string}` | `#${string}` | ''}` | `/helpers/navigationService${`?${string}` | `#${string}` | ''}` | `/helpers/service${`?${string}` | `#${string}` | ''}` | `/helpers/stylesheet${`?${string}` | `#${string}` | ''}` | `/helpers/voice-recognition${`?${string}` | `#${string}` | ''}` | `/lib/component-props${`?${string}` | `#${string}` | ''}` | `/lib/component-props/styleguide${`?${string}` | `#${string}` | ''}` | `/loaders/fontLoader${`?${string}` | `#${string}` | ''}` | `/mock/mock${`?${string}` | `#${string}` | ''}` | `/modals/options${`?${string}` | `#${string}` | ''}` | `/modals/popup${`?${string}` | `#${string}` | ''}` | `/modals/sidebar${`?${string}` | `#${string}` | ''}` | `/Utilities/ClassCodeProcessor${`?${string}` | `#${string}` | ''}` | `/Utilities/ConvertToArray${`?${string}` | `#${string}` | ''}` | `/Utilities/CustomTypes${`?${string}` | `#${string}` | ''}` | `/Utilities/DecodeHtmlEntities${`?${string}` | `#${string}` | ''}` | `/__tests__/app-test${`?${string}` | `#${string}` | ''}` | `/__tests__/appLayout.test${`?${string}` | `#${string}` | ''}` | `/__tests__/authentication-test${`?${string}` | `#${string}` | ''}` | `/__tests__/breadcrumbUtils.test${`?${string}` | `#${string}` | ''}` | `/__tests__/chatbox-test${`?${string}` | `#${string}` | ''}` | `/__tests__/classGuide-test${`?${string}` | `#${string}` | ''}` | `/__tests__/core-functionality.test${`?${string}` | `#${string}` | ''}` | `/__tests__/expo-router-navigation.test${`?${string}` | `#${string}` | ''}` | `/__tests__/faq-test${`?${string}` | `#${string}` | ''}` | `/__tests__/sidebar-test${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppFooter`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppHeader`; params?: Router.UnknownInputParams; } | { pathname: `/components/AppLayout`; params?: Router.UnknownInputParams; } | { pathname: `/components/auth-popup`; params?: Router.UnknownInputParams; } | { pathname: `/components/authentication`; params?: Router.UnknownInputParams; } | { pathname: `/components/chatbox`; params?: Router.UnknownInputParams; } | { pathname: `/components/ErrorDisplay`; params?: Router.UnknownInputParams; } | { pathname: `/components/gateway-auth`; params?: Router.UnknownInputParams; } | { pathname: `/components/LoadingSpinner`; params?: Router.UnknownInputParams; } | { pathname: `/components/messages`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/BulletinContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/Bulletins`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/BulletinsContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/Bulletins/GenericBulletinsList`; params?: Router.UnknownInputParams; } | { pathname: `/components/ClassGuides/ClassGuideContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/ClassGuides/ClassGuideTabContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/common/BreadcrumbNavigation`; params?: Router.UnknownInputParams; } | { pathname: `/components/FAQ/FaqContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/FAQ/FaqTabContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/IndustryGuides/IndustryGuideContent`; params?: Router.UnknownInputParams; } | { pathname: `/components/TrainingManuals/TrainingManualContent`; params?: Router.UnknownInputParams; } | { pathname: `/config/tabConfigurations`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/appStateContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/authContext`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/breadcrumbUtils`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/mock`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/model`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/navigationService`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/service`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/stylesheet`; params?: Router.UnknownInputParams; } | { pathname: `/helpers/voice-recognition`; params?: Router.UnknownInputParams; } | { pathname: `/lib/component-props`; params?: Router.UnknownInputParams; } | { pathname: `/lib/component-props/styleguide`; params?: Router.UnknownInputParams; } | { pathname: `/loaders/fontLoader`; params?: Router.UnknownInputParams; } | { pathname: `/mock/mock`; params?: Router.UnknownInputParams; } | { pathname: `/modals/options`; params?: Router.UnknownInputParams; } | { pathname: `/modals/popup`; params?: Router.UnknownInputParams; } | { pathname: `/modals/sidebar`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/ClassCodeProcessor`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/ConvertToArray`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/CustomTypes`; params?: Router.UnknownInputParams; } | { pathname: `/Utilities/DecodeHtmlEntities`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/app-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/appLayout.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/authentication-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/breadcrumbUtils.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/chatbox-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/classGuide-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/core-functionality.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/expo-router-navigation.test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/faq-test`; params?: Router.UnknownInputParams; } | { pathname: `/__tests__/sidebar-test`; params?: Router.UnknownInputParams; } | `/paas-source/${Router.SingleRoutePart<T>}` | { pathname: `/paas-source/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
