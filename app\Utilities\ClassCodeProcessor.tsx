const handleCASpecificLogic = (input: string) => {
  return input.slice(0, 4) + `(${input[4]})` + input.slice(5);
};

const splitInput = (input: string) => {
  const numericPart = input.replace(/[^0-9]/g, ""); // Extract numeric part
  const nonNumericPart = input.slice(numericPart.length); // Extract non-numeric part
  return { numericPart, nonNumericPart };
};

const cleanNonNumericPart = (nonNumericPart: string) => {
  return nonNumericPart
    .split("")
    .filter((char) => char.toLowerCase() === "f" || /[0-9]/.test(char)) // Keep 'F' and 'f', remove others
    .join("");
};

const processWC = (input: string, jurisdiction: string | string[]) => {
  if (
    (jurisdiction === "CA" || jurisdiction[0] === "CA") &&
    input.length >= 5
  ) {
    return handleCASpecificLogic(input);
  }

  const { numericPart, nonNumericPart } = splitInput(input);

  if (
    (jurisdiction === "CA" || jurisdiction[0] === "CA") &&
    nonNumericPart.toLowerCase() === "f"
  ) {
    return numericPart + nonNumericPart; // Keep 'f' if jurisdiction is CA
  }

  const cleanedNonNumericPart = cleanNonNumericPart(nonNumericPart);
  return numericPart + cleanedNonNumericPart; // Return the cleaned-up code
};

const ClassCodeProcessor = (
  input: string | undefined,
  jurisdiction: string | string[],
  LOB?: string | string[]
) => {
  if (!/^\d/.test(input as any)) {
    return input; // Return the input as-is if it doesn't start with a number
  } else {
    if (typeof input !== "string" || !input) {
      return "";
    }
    if (LOB === "WC") {
      return processWC(input, jurisdiction);
    }

    return input; // Return the string as-is if no changes are required
  }
};

export default ClassCodeProcessor;
