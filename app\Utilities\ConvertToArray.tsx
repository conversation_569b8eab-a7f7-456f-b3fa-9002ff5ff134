export const convertToNameArray = (
  property: { Code: string; Name: string }[],
  seperator: string
) => {
  const lobArray: string[] = [];
  property?.map((lob: { Code: string; Name: string }) => {
    lobArray.push(lob.Name);
  });
  return lobArray.join(seperator);
};

export const convertToCodeArray = (
  property: { Code: string; Name: string }[],
  seperator: string
) => {
  const lobArray: string[] = [];
  property?.map((lob: { Code: string; Name: string }) => {
    lobArray.push(lob.Code);
  });
  return lobArray.join(seperator);
};
