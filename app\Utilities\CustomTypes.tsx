import { Field } from "../helpers/model";
import { ComponentProps } from "../lib/component-props";
import { View, Text, StyleSheet } from 'react-native';
//dummy comment to trigger a new deployment
export type SearchProps = ComponentProps & {
  fields: {
    BetaUsers: Field<string>;
    BnBBulletinTabs: object[];
    ClearAllText: Field<string>;
    ClassGuideTabs: object[];
    DisplayResultText: Field<string>;
    EducationalBulletinTabs: object[];
    Facets: object[];
    FaqTabs: object[];
    LegislativeBulletinTabs: object[];
    NaicsHyperlink: Field<string>;
    ResultsCountOptions: object[];
    ResultsForText: Field<string>;
    ResultsPerPage: object[];
    ResultsPerPageText: Field<string>;
    ResultsText: Field<string>;
    SearchText: Field<string>;
    SicHyperlink: Field<string>;
    SortByText: Field<string>;
  };
};

export type restPropsType = {
  BetaUsers?: Field<string>;
  BnBBulletinTabs: object[];
  ClearAllText: Field<string>;
  ClassGuideTabs: object[];
  DisplayResultText: Field<string>;
  EducationalBulletinTabs: object[];
  Facets: object[];
  FaqTabs: object[];
  LegislativeBulletinTabs: object[];
  NaicsHyperlink: Field<string>;
  ResultsCountOptions: object[];
  ResultsForText: Field<string>;
  ResultsPerPage?: object[];
  ResultsPerPageText?: Field<string>;
  ResultsText?: Field<string>;
  SearchText: Field<string>;
  SicHyperlink: Field<string>;
  SortByText: Field<string>;
};

export type contentType = {
  Count: number;
  Id: string;
  Key: string;
  Name: string;
  Selected: boolean;
};

export type facetType = {
  Key: string;
  Name: string;
  Values: contentType[];
};

export type dataType = {
  Code: string;
  ContentSubType: string;
  ContentType: string;
  ItemID: string;
  ItemName: string;
  Jurisdiction: {
    Code: string;
    Name: string;
  }[];
  Lobs: {
    Code: string;
    Name: string;
  }[];
  Notes: string;
  SubLobs: {
    Code: string;
    Name: string;
  }[];
  Title: string;
  contentNotes: string;
  jurisdictionArray: string[];
  lobsArray: string[];
  lobsNameArray: string[];
  ParentItemID: string;
};

export type resultperPageType = {
  displayName: string;
  fields: {
    Value: Field<number>;
  };
  id: string;
  name: string;
};

export type documentType = {
  ClassCode: string;
  Code: string;
  ClassGuideType: string;
  ContentType: string;
  ItemId: string;
  ItemID: string;
  Title: string;
  Jurisdiction: {
    Code: string;
    Name: string;
  }[];
  Lob: {
    Code: string;
    Name: string;
  }[];
  Lobs: {
    Code: string;
    Name: string;
  }[];
};

export type classGuideDataType = {
  Analogies: string;
  Category: {
    Code: string;
    Name: string;
  }[];
  GroupNote: {
    GroupNoteTitle: string;
    GroupNoteContent: string;
  }[];
  BPRelated: object[];
  StateException: object[];
  BPToSICMapping: object[];
  PropertyRateNumber?: string;
  EarthquakeGrade?: string;
  EslSusceptibilityGrade?: string;
  LiabilityExposureBase?: string;
  LiabilityClassGroup?: string;
  ClassCode: string;
  ContemplatedOperations: string;
  ContentSubType: string;
  ContentType: string;
  GlCrossReference: documentType[];
  WcCrossReference: documentType[];
  BpCrossReference: documentType[];
  DateTimeUpdated: string;
  GLStateException: object[];
  WCStateException: object[];
  Phraseology: string;
  Lobs: {
    Code: string;
    Name: string;
  }[];
  Jurisdiction: {
    Code: string;
    Name: string;
  }[];
  PremiumBasis: string;
  History: string;
  PaasDocuments: object[];
  Notes: string;
  OperationsNotContemplated: string;
  RelatedGLCodes: object[];
  RelatedWcCodes: object[];
  AdditionalPhraseologies: object[];
  GlToSICMapping: object[];
  WCToSICMapping: object[];
  ExternalLinks: object[];
  RiskSchedule: string;
  IndustrialGroup: string;
};

export type paasTabsType = {
  displayName: string;
  fields: {
    Key: Field<string>;
    Phrase: Field<string>;
  };
  id: string;
  name: string;
};

export type resourceType = {
  Code: string;
  DocumentType: string;
  ContentType: string;
  ItemId: string;
  ItemID: string;
  Name: string;
  Title: string;
  Lobs: {
    Code: string;
    Name: string;
  }[];
  Jurisdiction: {
    Code: string;
    Name: string;
  }[];
};

export type paasContentType = {
  classGuideTabs?: object[];
  restProps?: restPropsType;
  currentTableItem?: dataType;
  faqTabs?: object[];
  searchTerm?: string;
  setCurrentTableItem?: any;
  setPaasSearchClicked?: any;
  BnBBulletinTabs?: object[];
  EducationalBulletinTabs?: object[];
  LegislativeBulletinTabs?: object[];
};

export type requestParamsType = {
  queryParameter: any;
  pageNumb: number;
  dropDownCount: number;
  sortDirection: string;
  sortItem: string;
  lob: string[];
  cs: string[];
  ct: string[];
  js: string[];
  sublob: string[];
};

export type SearchResultLayoutPropsType = {
  isSpinner: boolean;
  requestParams: requestParamsType;
  data: dataType[];
  textFields: any;
  fromResult: string;
  toResult: string;
  totalResults: number;
  setTotalResults: (totalResults: number) => void;
  facets: facetType[];
  handleFacetChange: any;
  clearAllTags: (facets: facetType[]) => void;
  tagCount: number;
  onTableItemClick: (selectedTableItem: dataType) => string;
  getUrl: (selectedTableItem: dataType) => string;
  isTable: boolean;
  setIsTable: (isTable: boolean) => void;
  pagination: number;
  itemsPerPage: number;
  renderPageNumbers: any;
  totalPages: number;
  currentPage: number;
  handleResultsPerPage: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  handleExtremePrevbtn: () => void;
  handlePrevbtn: () => void;
  handleNextbtn: () => void;
  handleExtremeNextbtn: () => void;
  sortColumn: string;
  sortDirection: string;
  handleSortClick: (column: string) => void;
};

export type FacetLayoutPropsType = {
  handleFacetChange: any;
  handleAllBulletinsCheckbox: any;
  handleClassGuidesCheckbox: any;
  facets: facetType[];
  accessToken: string;
  setCurrentTableItem: any;
  totalResults: number;
};

// Utility function for rendering bulletin details
export const renderBulletinsDetails = (label: string, value: string) => {
  if (!value) return null;
  
  return (
    <View style={styles.detailItem}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.detail}>{value}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  detailItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    fontWeight: 'bold',
    color: '#495057',
    fontSize: 14,
  },
  detail: {
    color: '#495057',
    fontSize: 14,
  },
});
