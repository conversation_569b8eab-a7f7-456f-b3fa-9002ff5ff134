// Simple HTML entity decoder for React Native
// Only decodes HTML entities, preserves all original HTML structure and styling

// Enhanced entity map for better text rendering
const ENTITY_MAP = new Map([
  ['&nbsp;', ' '],
  ['&amp;', '&'],
  ['&lt;', '<'],
  ['&gt;', '>'],
  ['&quot;', '"'],
  ['&#39;', "'"],
  ['&apos;', "'"],
  ['&hellip;', '...'],
  ['&mdash;', '—'],
  ['&ndash;', '–'],
  ['&lsquo;', "'"],
  ['&rsquo;', "'"],
  ['&ldquo;', '"'],
  ['&rdquo;', '"'],
  ['&bull;', '•'],
  ['&copy;', '©'],
  ['&reg;', '®'],
  ['&trade;', '™'],
  ['&deg;', '°'],
  ['&plusmn;', '±'],
  ['&frac12;', '½'],
  ['&frac14;', '¼'],
  ['&frac34;', '¾'],
]);

const ENTITY_REGEX = /&(?:nbsp|amp|lt|gt|quot|#39|apos|hellip|mdash|ndash|lsquo|rsquo|ldquo|rdquo|bull|copy|reg|trade|deg|plusmn|frac12|frac14|frac34);/g;

// Simple utility to decode HTML entities - preserves all original HTML structure
export const decodeHTMLEntitiesRN = (text: string): string => {
  if (!text) return '';
  
  // First pass: decode named entities
  let decoded = text.replace(ENTITY_REGEX, (entity) => ENTITY_MAP.get(entity) || entity);
  
  // Second pass: decode numeric entities
  decoded = decoded.replace(/&#(\d+);/g, (match, num) => {
    return String.fromCharCode(parseInt(num, 10));
  });
  
  // Third pass: decode hex entities
  decoded = decoded.replace(/&#x([0-9a-fA-F]+);/g, (match, hex) => {
    return String.fromCharCode(parseInt(hex, 16));
  });
  
  return decoded;
};

// Alias for backward compatibility
export const decodeHTMLEntities = decodeHTMLEntitiesRN;

