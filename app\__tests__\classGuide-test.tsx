import React from 'react';
import { render } from '@testing-library/react-native';
import ClassGuideContent from '../components/ClassGuides/ClassGuideContent';
import { ClassGuideResponse } from '../helpers/model';

// Mock data for testing
const mockClassGuideData: ClassGuideResponse = {
  PremiumBasis: "Payroll",
  WCStateException: [
    {
      ItemId: "{17C23D8E-0294-4BB3-8477-C0FAD193A89C}",
      Title: "Boat Dealers",
      ClassGuideType: "WC Class Guides",
      ClassCode: "8057",
      Jurisdiction: [
        {
          Code: "CA",
          Name: "California"
        }
      ],
      Lob: [
        {
          Code: "WC",
          Name: "Workers Compensation"
        }
      ]
    }
  ],
  RiskSchedule: "34-Commercial Enterprises",
  IndustrialGroup: "347-Automobile Service and Accessories Dealers",
  WCToSICMapping: [
    {
      ItemId: "{37A809B2-3D6C-4977-B0A4-4750E8B284DA}",
      SicCode: "5511",
      NaicsCode: "441110",
      SicDescription: "Motor Vehicle Dealers (New and Used)",
      NaicsDescription: "New Car Dealers"
    }
  ],
  RelatedWcCodes: [
    {
      ItemId: "{73C92EB1-8143-446F-9FB1-63ED71E5DF93}",
      Title: "Automobile - Recycling & Drivers",
      ClassGuideType: "WC Class Guides",
      ClassCode: "3821",
      Jurisdiction: [
        {
          Code: "AK",
          Name: "Alaska"
        }
      ],
      Lob: [
        {
          Code: "WC",
          Name: "Workers Compensation"
        }
      ]
    }
  ],
  GlCrossReference: [],
  BpCrossReference: [],
  ItemID: "{F155BBEE-DE10-4AA6-A240-B58895AC1F61}",
  Jurisdiction: [
    {
      Code: "AK",
      Name: "Alaska"
    }
  ],
  ClassCode: "8380B",
  ItemName: "8380B-CC-Automobile - Sales Or Service Agency  Parts Department Employees Drivers",
  Phraseology: "Automobile - Sales Or Service Agency & Parts Department Employees, Drivers",
  Lobs: [
    {
      Code: "WC",
      Name: "Workers Compensation"
    }
  ],
  ContentType: "Class Guide",
  ContentSubType: "WC Class Guides",
  Analogies: "Air Conditioning Systems - Automobile - Installation, Service Or Repair & Drivers<p>Automobile Brake Repair & Drivers",
  ContemplatedOperations: "<P>The operations contemplated under this classification are those of a dealer selling new or used automobiles...</P>",
  Notes: "<P>Automobile salespersons to be separately rated to Code 8748...</P>",
  History: "<SPAN>Effective as indicated below, per NCCI Item Filing B-1436...</SPAN>",
  Category: [],
  StateExceptionDescription: "8380B",
  DateTimeUpdated: "6/30/2008",
  IsPAASApplicable: true,
  IsQC3Applicable: true,
  StateExclusion: [
    {
      Code: "CA",
      Name: "California"
    }
  ],
  PaasDocuments: [
    {
      ItemId: "{1E5585BE-0629-44F9-B2C6-3178D7885BB4}",
      Title: "Auditing Guidelines for Dealership Garage Policy - Commercial Auto",
      DocumentType: "Educational Bulletins",
      Lobs: [
        {
          Code: "CA",
          Name: "Commercial Automobile"
        }
      ],
      Jurisdiction: [
        {
          Code: "CC",
          Name: "Country Code"
        }
      ]
    }
  ],
  StatusCode: 200,
  StatusMessage: "Success"
};

describe('ClassGuideContent', () => {
  it('renders without crashing', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    // Check if the title is rendered
    expect(getByText(/8380B - Automobile - Sales Or Service Agency/)).toBeTruthy();
  });

  it('displays line of business information', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Line of business:')).toBeTruthy();
    expect(getByText('Workers Compensation')).toBeTruthy();
  });

  it('displays jurisdiction information', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Jurisdiction:')).toBeTruthy();
    expect(getByText('AK')).toBeTruthy();
  });

  it('displays last updated information', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Last updated:')).toBeTruthy();
    expect(getByText('6/30/2008')).toBeTruthy();
  });

  it('displays HTML content sections', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Contemplated Operations')).toBeTruthy();
    expect(getByText('Notes')).toBeTruthy();
    expect(getByText('Analogies')).toBeTruthy();
    expect(getByText('History')).toBeTruthy();
  });

  it('displays SIC/NAICS mapping section', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('SIC/NAICS Mapping')).toBeTruthy();
    expect(getByText('SIC: 5511 | NAICS: 441110')).toBeTruthy();
  });

  it('displays related WC codes section', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Related WC Codes')).toBeTruthy();
    expect(getByText('3821 - Automobile - Recycling & Drivers')).toBeTruthy();
  });

  it('displays state exceptions section', () => {
    const { getByText } = render(
      <ClassGuideContent 
        classGuide={mockClassGuideData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('State Exceptions')).toBeTruthy();
    expect(getByText('8057 - Boat Dealers')).toBeTruthy();
  });
});
