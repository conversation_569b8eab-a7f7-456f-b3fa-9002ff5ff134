import React from 'react';
import { render } from '@testing-library/react-native';
import FaqContent from '../components/FAQ/FaqContent';
import { FaqResponse } from '../helpers/model';

// Mock data for testing
const mockFaqData: FaqResponse = {
  Lobs: [
    {
      Code: "GL",
      Name: "General Liability",
    },
    {
      Code: "WC",
      Name: "Workers Compensation",
    },
  ],
  ReleaseYear: "2014",
  ReleaseMonth: "April",
  Question: '<P style="TEXT-ALIGN: justify; MARGIN: 0in 0in 0pt" class=MsoNormal>What is the proper classification for a risk limited to providing mobile diagnostic testing services?</P>',
  Answer: '<P style="TEXT-ALIGN: justify; MARGIN: 0in 0in 0pt" class=MsoNormal>The proper classification would be Code 8748 - Clerical Office Employees.</P>',
  WCClassificationLink: [
    {
      ItemId: "{12345678-1234-1234-1234-123456789012}",
      Title: "Clerical Office Employees",
      ClassGuideType: "WC Class Guides",
      ClassCode: "8748",
      Jurisdiction: [
        {
          Code: "CA",
          Name: "California"
        }
      ],
      Lob: [
        {
          Code: "WC",
          Name: "Workers Compensation"
        }
      ]
    }
  ],
  GLClassificationLink: [
    {
      ItemId: "{*************-4321-4321-************}",
      Title: "Professional Services",
      ClassGuideType: "GL Class Guides",
      ClassCode: "12345",
      Jurisdiction: [
        {
          Code: "NY",
          Name: "New York"
        }
      ],
      Lob: [
        {
          Code: "GL",
          Name: "General Liability"
        }
      ]
    }
  ],
  Resources: '<P>Additional resources and references can be found in the PAAS documentation.</P>',
  Title: "Mobile Diagnostic Testing Services Classification",
  StatusCode: 200,
  StatusMessage: "Success"
};

describe('FaqContent', () => {
  it('renders without crashing', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    // Check if the title is rendered
    expect(getByText('Mobile Diagnostic Testing Services Classification')).toBeTruthy();
  });

  it('displays line of business information', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Line of business:')).toBeTruthy();
    expect(getByText('General Liability and Workers Compensation')).toBeTruthy();
  });

  it('displays release date information', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Released on:')).toBeTruthy();
    expect(getByText('April 2014')).toBeTruthy();
  });

  it('displays question section', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Question')).toBeTruthy();
  });

  it('displays answer section', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Answer')).toBeTruthy();
  });

  it('displays related classification links section', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Related Classification Links')).toBeTruthy();
    expect(getByText('Workers Compensation')).toBeTruthy();
    expect(getByText('General Liability')).toBeTruthy();
  });

  it('displays WC classification links', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('8748 - Clerical Office Employees')).toBeTruthy();
    expect(getByText('California')).toBeTruthy();
  });

  it('displays GL classification links', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('12345 - Professional Services')).toBeTruthy();
    expect(getByText('New York')).toBeTruthy();
  });

  it('displays resources section', () => {
    const { getByText } = render(
      <FaqContent 
        faq={mockFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Resources')).toBeTruthy();
  });

  it('handles empty data gracefully', () => {
    const emptyFaqData: FaqResponse = {
      Lobs: [],
      ReleaseYear: "",
      ReleaseMonth: "",
      Question: "",
      Answer: "",
      WCClassificationLink: [],
      GLClassificationLink: [],
      Resources: "",
      Title: "Empty FAQ",
      StatusCode: 200,
      StatusMessage: "Success"
    };

    const { getByText } = render(
      <FaqContent 
        faq={emptyFaqData} 
        contentWidth={300} 
      />
    );
    
    expect(getByText('Empty FAQ')).toBeTruthy();
  });
});
