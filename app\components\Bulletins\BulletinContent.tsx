import React from 'react';
import { View } from 'react-native';
import { EducationalBulletins, BoardAndBureauBulletins, LegislativeBulletins } from './Bulletins';

interface BulletinContentProps {
  bulletinData: any;
  bulletinType: string;
  contentWidth: number;
  onClose: () => void;
}

export default function BulletinContent({ 
  bulletinData, 
  bulletinType, 
  contentWidth, 
  onClose 
}: BulletinContentProps) {
  
  // Render bulletin content based on bulletin type
  if (bulletinType?.includes("Educational Bulletin")) {
    return (
      <EducationalBulletins
        bulletinData={bulletinData}
        onClose={onClose}
      />
    );
  } else if (bulletinType?.includes("Board and Bureau Bulletin")) {
    return (
      <BoardAndBureauBulletins
        bulletinData={bulletinData}
        onClose={onClose}
      />
    );
  } else if (bulletinType?.includes("Legislative Bulletin")) {
    return (
      <LegislativeBulletins
        bulletinData={bulletinData}
        onClose={onClose}
      />
    );
  }

  // Default fallback
  return (
    <View>
      {/* Default bulletin content if type is not recognized */}
      <EducationalBulletins
        bulletinData={bulletinData}
        onClose={onClose}
      />
    </View>
  );
}
