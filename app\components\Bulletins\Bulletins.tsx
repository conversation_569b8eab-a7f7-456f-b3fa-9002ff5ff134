import React, { useState, useRef } from "react";
import { View, Text, ScrollView, Pressable } from 'react-native';
import Content from "./BulletinsContent";
import GenericBulletinsList from "./GenericBulletinsList";
import { renderBulletinsDetails } from "../../Utilities/CustomTypes";
import { convertToNameArray } from "../../Utilities/ConvertToArray";
import { bulletinsStyles } from "../../helpers/stylesheet";

export type BulletinType = 'legislative' | 'educational' | 'board-and-bureau';

interface GenericBulletinsProps {
    bulletinData: any;
    onClose?: () => void;
    type: BulletinType;
}

// Simplified tab components - no React.memo needed
const ContentTab = ({ bulletinData, scrollViewRef }: { bulletinData: any; scrollViewRef: React.RefObject<ScrollView> }) => {
    return (
        <Content 
            contenData={bulletinData.Notes} 
            toggle="430a7d47-bfe2-4475-9184-836936a406aa" 
            scrollViewRef={scrollViewRef} 
        />
    );
};

const RelatedLinksTab = ({ bulletinData, scrollViewRef }: { bulletinData: any; scrollViewRef: React.RefObject<ScrollView> }) => {
    
    const wcLinks = bulletinData?.WCClassificationLinks || [];
    const glLinks = bulletinData?.GLClassificationLinks || [];
    const combined = [...wcLinks, ...glLinks];
    const shouldShowSeeMore = combined.length > 12;

    const onRelatedLinkClick = (relatedLink: any) => {
    };

    const renderRelatedLink = (item: any, index: number) => {
        const jurisdictionCode = item?.Jurisdiction?.length === 1
            ? item?.Jurisdiction.map((js: { Code: string; Name: string }) =>
                js.Code.includes("CC") ? "" : js.Code
            ).join('')
            : "";

        const processedCode = `${jurisdictionCode}${item?.Code || ''}`;

        return (
            <View key={index} style={bulletinsStyles.listItem}>
                <Text style={bulletinsStyles.bulletPoint}>•</Text>
                <Pressable
                    onPress={() => onRelatedLinkClick(item)}
                    style={bulletinsStyles.linkWrapper}
                    android_ripple={{ color: 'rgba(0, 53, 142, 0.1)' }}
                >
                    <Text style={bulletinsStyles.linkText}>
                        {jurisdictionCode} {processedCode} {item?.Title?.trim()}
                    </Text>
                </Pressable>
            </View>
        );
    };

    return (
        <GenericBulletinsList
            items={combined}
            itemRenderer={renderRelatedLink}
            shouldShowSeeMore={shouldShowSeeMore}
            displayCount={12}
            title="Related Links"
            scrollViewRef={scrollViewRef}
            onItemClick={onRelatedLinkClick}
        />
    );
};

const ResourcesTab = ({ bulletinData, scrollViewRef }: { bulletinData: any; scrollViewRef: React.RefObject<ScrollView> }) => {    
    const resourceLinks = bulletinData?.ReferenceBulletins || [];
    const shouldShowSeeMore = resourceLinks.length > 8;

    const onResourceClick = (resource: any) => {
    };

    const renderResourceItem = (item: any, index: number) => {
        return (
            <View key={index} style={bulletinsStyles.listItem}>
                <Text style={bulletinsStyles.bulletPoint}>•</Text>
                <Pressable
                    onPress={() => onResourceClick(item)}
                    style={bulletinsStyles.linkWrapper}
                    android_ripple={{ color: 'rgba(0, 53, 142, 0.1)' }}
                >
                    <Text style={bulletinsStyles.linkText}>
                        {item.Title || item.Name} {item.Code}
                    </Text>
                </Pressable>
            </View>
        );
    };

    return (
        <GenericBulletinsList
            items={resourceLinks}
            itemRenderer={renderResourceItem}
            shouldShowSeeMore={shouldShowSeeMore}
            displayCount={8}
            title="Resources"
            scrollViewRef={scrollViewRef}
            onItemClick={onResourceClick}
        />
    );
};

const GenericBulletins = ({ bulletinData, onClose, type }: GenericBulletinsProps) => {
    const [activeTab, setActiveTab] = useState("content");
    const scrollViewRef = useRef<ScrollView>(null!);

    // Simple tab configuration - computed once per render
    const getTabsConfig = () => {
        const tabs = [];
        
        // Always show content if Notes exist
        if (bulletinData?.Notes?.length) {
            tabs.push({ id: "content", label: "Content" });
        }
        
        // Add additional tabs only for educational type
        if (type === 'educational') {
            if (bulletinData?.WCClassificationLinks?.length || bulletinData?.GLClassificationLinks?.length) {
                tabs.push({ id: "related", label: "Related Links" });
            }
            if (bulletinData?.ReferenceBulletins?.length) {
                tabs.push({ id: "resources", label: "Resources" });
            }
        }
        
        return tabs;
    };

    const availableTabs = getTabsConfig();

    const handleTabPress = (tabId: string) => {
        if (activeTab !== tabId) {
            setActiveTab(tabId);
            scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        }
    };

    // Render header fields
    const renderHeaderFields = () => {
        const fields = [];

        // Line of Business (common)
        const lineOfBusinessElement = renderBulletinsDetails(
            "Line Of Business: ",
            convertToNameArray(bulletinData?.Lobs, " and ")
        );
        if (lineOfBusinessElement) {
            fields.push(React.cloneElement(lineOfBusinessElement, { key: 'line-of-business' }));
        }

        // Type-specific fields
        if (type === 'legislative') {
            if (bulletinData?.BillNumber) {
                const billNumberElement = renderBulletinsDetails("Bill Number: ", bulletinData.BillNumber);
                if (billNumberElement) {
                    fields.push(React.cloneElement(billNumberElement, { key: 'bill-number' }));
                }
            }
            if (bulletinData?.Enacted) {
                const enactedElement = renderBulletinsDetails("Enacted: ", bulletinData.Enacted);
                if (enactedElement) {
                    fields.push(React.cloneElement(enactedElement, { key: 'enacted' }));
                }
            }
        } else if (type === 'board-and-bureau' && bulletinData?.CircularNumber) {
            const circularNumberElement = renderBulletinsDetails("Circular Number: ", bulletinData.CircularNumber);
            if (circularNumberElement) {
                fields.push(React.cloneElement(circularNumberElement, { key: 'circular-number' }));
            }
        }

        // Common fields
        const releasedOnElement = renderBulletinsDetails(
            "Released on: ",
            `${bulletinData?.ReleaseMonth} ${bulletinData?.ReleaseYear}`
        );
        if (releasedOnElement) {
            fields.push(React.cloneElement(releasedOnElement, { key: 'released-on' }));
        }

        const bulletinNumberElement = renderBulletinsDetails("Bulletin Number: ", bulletinData?.BulletinNumber);
        if (bulletinNumberElement) {
            fields.push(React.cloneElement(bulletinNumberElement, { key: 'bulletin-number' }));
        }

        return fields;
    };

    // Render current tab content
    const renderTabContent = () => {
        switch (activeTab) {
            case "content":
                return <ContentTab bulletinData={bulletinData} scrollViewRef={scrollViewRef} />;
            
            case "related":
                if (type === 'educational' && (bulletinData?.WCClassificationLinks?.length || bulletinData?.GLClassificationLinks?.length)) {
                    return <RelatedLinksTab bulletinData={bulletinData} scrollViewRef={scrollViewRef} />;
                }
                return null;
            
            case "resources":
                if (type === 'educational' && bulletinData?.ReferenceBulletins?.length) {
                    return <ResourcesTab bulletinData={bulletinData} scrollViewRef={scrollViewRef} />;
                }
                return null;
            
            default:
                return null;
        }
    };

    return (
        <View style={bulletinsStyles.container}>
            {/* Header */}
            <View style={bulletinsStyles.header}>
                <Text style={bulletinsStyles.title}>{bulletinData?.Title}</Text>
                <View style={bulletinsStyles.details}>
                    {renderHeaderFields()}
                </View>
            </View>

            {/* Tabs */}
            {availableTabs.length > 1 && (
                <View style={bulletinsStyles.tabContainer}>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                        {availableTabs.map((tab, index) => (
                            <Pressable
                                key={tab.id}
                                style={[
                                    bulletinsStyles.tab,
                                    activeTab === tab.id && bulletinsStyles.activeTab
                                ]}
                                onPress={() => handleTabPress(tab.id)}
                            >
                                <Text style={[
                                    bulletinsStyles.tabText,
                                    activeTab === tab.id && bulletinsStyles.activeTabText
                                ]}>
                                    {tab.label}
                                </Text>
                            </Pressable>
                        ))}
                    </ScrollView>
                </View>
            )}

            {/* Content */}
            <ScrollView
                ref={scrollViewRef}
                style={bulletinsStyles.contentContainer}
                showsVerticalScrollIndicator={true}
                keyboardShouldPersistTaps="handled"
            >
                <View style={bulletinsStyles.tabContent}>
                    {renderTabContent()}
                </View>
            </ScrollView>
        </View>
    );
};

// Specific bulletin components
const LegislativeBulletins = ({ bulletinData, onClose }: { bulletinData: any; onClose?: () => void }) => (
    <GenericBulletins bulletinData={bulletinData} onClose={onClose} type="legislative" />
);

const EducationalBulletins = ({ bulletinData, onClose }: { bulletinData: any; onClose?: () => void }) => (
    <GenericBulletins bulletinData={bulletinData} onClose={onClose} type="educational" />
);

const BoardAndBureauBulletins = ({ bulletinData, onClose }: { bulletinData: any; onClose?: () => void }) => (
    <GenericBulletins bulletinData={bulletinData} onClose={onClose} type="board-and-bureau" />
);

export default GenericBulletins;
export { LegislativeBulletins, EducationalBulletins, BoardAndBureauBulletins };