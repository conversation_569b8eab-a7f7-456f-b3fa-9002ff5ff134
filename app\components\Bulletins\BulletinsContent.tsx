import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { View, ActivityIndicator, Text, ScrollView, Dimensions } from 'react-native';
import { InteractionManager } from 'react-native';
import RenderHTML from 'react-native-render-html';
import { decodeHTMLEntitiesRN } from "../../Utilities/DecodeHtmlEntities";
import { bulletinsStyles, bulletinsHtmlTags } from "../../helpers/stylesheet";

const { width: screenWidth } = Dimensions.get('window');

const Content = React.memo((props: { 
  contenData: string; 
  toggle: string; 
  scrollViewRef?: React.RefObject<ScrollView>;
}) => {
  const { contenData, toggle } = props;
  const [contentReady, setContentReady] = useState(false);
  const [processedContent, setProcessedContent] = useState('');
  const contentRef = useRef<View>(null);

  // Process content with HTML entity decoding only
  useEffect(() => {
    if (!contenData) {
      return;
    }
    
    // Use InteractionManager to defer heavy processing
    InteractionManager.runAfterInteractions(() => {
      // Only decode HTML entities, preserve all original HTML structure and styling
      const decodedContent = decodeHTMLEntitiesRN(contenData);
      
      
      setProcessedContent(decodedContent);
      setContentReady(true);
    });
  }, [contenData]);

  // Memoize RenderHTML props to prevent unnecessary re-renders
  const renderHTMLProps = useMemo(() => ({
    contentWidth: screenWidth - 40,
    source: { html: processedContent },
    tagsStyles: bulletinsHtmlTags,
    enableExperimentalMarginCollapsing: true,
    enableUserAgentStyles: true,
    renderersProps: {
      img: {
        enableExperimentalPercentWidth: true,
      },
    },
    defaultTextProps: {
      selectable: false,
      allowFontScaling: true,
    },
    systemFonts: [
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Roboto',
      'Helvetica Neue',
      'Arial',
      'sans-serif',
    ],
  }), [processedContent]);


  return (
    <View style={bulletinsStyles.contentWrapper}>
      {toggle === "430a7d47-bfe2-4475-9184-836936a406aa" && (
        contentReady ? (
          <View style={bulletinsStyles.contentOverflowContainer}>
            {/* Simple content display */}
            <View
              ref={contentRef}
              style={bulletinsStyles.expandedContent}
            >
              <RenderHTML {...renderHTMLProps} />
            </View>
        </View>
        ) : (
          <View style={bulletinsStyles.loadingContainer}>
            <ActivityIndicator size="large" color="#00358E" />
          </View>
        )
      )}
    </View>
  );
});

export default Content;