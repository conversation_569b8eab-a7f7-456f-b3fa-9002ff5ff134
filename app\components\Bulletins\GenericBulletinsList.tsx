import React, { useState, useMemo, useCallback, useRef } from "react";
import { View, Text, Pressable, LayoutAnimation, UIManager, Platform, Dimensions, ScrollView, ActivityIndicator } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { bulletinsStyles } from "../../helpers/stylesheet";

// Enable LayoutAnimation for Android
if (Platform.OS === 'android') {
  UIManager.setLayoutAnimationEnabledExperimental?.(true);
}

const { width: screenWidth } = Dimensions.get('window');

interface GenericBulletinsListProps {
  items: any[];
  itemRenderer: (item: any, index: number) => React.ReactElement;
  shouldShowSeeMore: boolean;
  displayCount: number;
  title: string;
  scrollViewRef?: React.RefObject<ScrollView>;
  onItemClick?: (item: any) => void;
}

const GenericBulletinsList = React.memo(({
  items,
  itemRenderer,
  shouldShowSeeMore,
  displayCount,
  title,
  scrollViewRef,
  onItemClick
}: GenericBulletinsListProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const expandButtonRef = useRef<View>(null);
  const [buttonPosition, setButtonPosition] = useState(0);

  // Get display items based on expanded state
  const displayItems = useMemo(() => {
    if (!shouldShowSeeMore || isExpanded) {
      return items;
    }
    return items.slice(0, displayCount);
  }, [items, shouldShowSeeMore, isExpanded, displayCount]);

  // Capture button position before expansion
  const captureButtonPosition = useCallback(() => {
    if (expandButtonRef.current) {
      expandButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
        setButtonPosition(pageY);
      });
    }
  }, []);

  // Handle see more with scroll position management
  const handleSeeMore = useCallback(() => {
    if (!isExpanded) {
      // Expanding - capture current button position
      captureButtonPosition();
    }

    LayoutAnimation.configureNext({
      duration: 200,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
      },
    }, () => {
      if (isExpanded && scrollViewRef?.current && buttonPosition > 0) {
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({
            y: Math.max(0, buttonPosition - 100),
            animated: true
          });
        }, 50);
      }
    });

    setIsExpanded(!isExpanded);
  }, [isExpanded, scrollViewRef, buttonPosition, captureButtonPosition]);

  // Memoized item rendering
  const renderItem = useCallback((item: any, index: number) => {
    return itemRenderer(item, index);
  }, [itemRenderer]);

  return (
    <View style={bulletinsStyles.container}>
      <View style={bulletinsStyles.content}>
        <View style={bulletinsStyles.contentOverflowContainer}>
          <View style={bulletinsStyles.linksContainer}>
            {displayItems.map(renderItem)}
          </View>

          {/* See More Button */}
          {shouldShowSeeMore && (
            <View ref={expandButtonRef} style={bulletinsStyles.seeMoreContainer}>
              <View style={bulletinsStyles.separatorLine} />
              <Pressable
                style={bulletinsStyles.seeMoreButton}
                onPress={handleSeeMore}
                android_ripple={{ color: 'rgba(0, 53, 142, 0.1)' }}
              >
                <Text style={bulletinsStyles.seeMoreText}>
                  {isExpanded ? 'see less' : 'see more'}
                </Text>
                <FontAwesome
                  name={isExpanded ? 'chevron-up' : 'chevron-down'}
                  size={12}
                  color="#00358E"
                  style={bulletinsStyles.seeMoreIcon}
                />
              </Pressable>
            </View>
          )}
        </View>
      </View>
    </View>
  );
});

export default GenericBulletinsList; 