import React, { useState } from 'react';
import { View, Text, ScrollView, Pressable, Dimensions } from 'react-native';
import { ClassGuideResponse } from '../../helpers/model';
import { paasSourceStyles } from '../../helpers/stylesheet';
import { classGuidesTabs } from '../../config/tabConfigurations';
import { ClassGuideTabContent } from './ClassGuideTabContent';

interface ClassGuideContentProps {
  classGuide: ClassGuideResponse;
  contentWidth: number;
}

export default function ClassGuideContent({ classGuide, contentWidth }: ClassGuideContentProps) {
  const [activeTab, setActiveTab] = useState<string>(classGuidesTabs[0]?.id || '');
  const screenWidth = Dimensions.get('window').width;

  // Helper function to get jurisdiction codes
  const getJurisdictionCodes = () => {
    return classGuide.Jurisdiction?.map(j => j.Code).join(', ') || '';
  };

  // Helper function to get LOB names
  const getLobNames = () => {
    return classGuide.Lobs?.map(lob => lob.Name).join(' and ') || '';
  };

  // Helper function to get category names
  const getCategoryNames = () => {
    if (Array.isArray(classGuide.Category) && classGuide.Category.length > 0) {
      return classGuide.Category.map((cat: any) => cat.Name || cat).join(', ');
    }
    return 'Construction or Erection';
  };

  // Get current tab info
  const getCurrentTab = () => {
    return classGuidesTabs.find(tab => tab.id === activeTab) || classGuidesTabs[0];
  };

  return (
    <View style={paasSourceStyles.contentContainer}>
      {/* Class Guide Title */}
      <Text style={paasSourceStyles.title}>
        {classGuide.ClassCode} {classGuide.Phraseology || classGuide.ItemName || 'Class Guide'}
      </Text>

      {/* LOB and Jurisdiction Info */}
      <View style={paasSourceStyles.lobSection}>
        <Text style={paasSourceStyles.lobLabel}>
          LOB: <Text style={paasSourceStyles.lobValue}>{getLobNames()}</Text>
        </Text>
        <Text style={paasSourceStyles.lobLabel}>
          Applicable in: <Text style={paasSourceStyles.lobValue}>{getJurisdictionCodes()}</Text>
        </Text>
        <Text style={paasSourceStyles.lobLabel}>
          Category: <Text style={paasSourceStyles.lobValue}>{getCategoryNames()}</Text>
        </Text>
      </View>

      {/* General Liability Cross Reference */}
      {classGuide.GlCrossReference && classGuide.GlCrossReference.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>General Liability Cross Reference</Text>
          {classGuide.GlCrossReference.map((ref: any, index: number) => (
            <Pressable key={index} style={paasSourceStyles.linkButton}>
              <Text style={paasSourceStyles.linkText}>
                {ref.Jurisdiction?.length === 1
                  ? ref.Jurisdiction.map((js: any) => js.Code).join('') + ' '
                  : ''
                }
                {ref.ClassCode} {ref.Title}
              </Text>
            </Pressable>
          ))}
        </View>
      )}

      {/* Dynamic Tab Navigation */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={paasSourceStyles.tabContainer}
      >
        {classGuidesTabs.map((tab) => (
          <Pressable
            key={tab.id}
            style={[
              paasSourceStyles.tab,
              activeTab === tab.id && paasSourceStyles.activeTab,
            ]}
            onPress={() => setActiveTab(tab.id)}
          >
            <Text
              style={[
                paasSourceStyles.tabText,
                activeTab === tab.id && paasSourceStyles.activeTabText,
              ]}
            >
              {tab.displayName}
            </Text>
          </Pressable>
        ))}
      </ScrollView>

      {/* Dynamic Tab Content */}
      <ScrollView style={paasSourceStyles.scrollContent}>
        <ClassGuideTabContent
          classGuide={classGuide}
          tabId={activeTab}
          tabName={getCurrentTab()?.name || ''}
          contentWidth={contentWidth}
        />
      </ScrollView>
    </View>
  );
};
