import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { RenderHTML } from 'react-native-render-html';
import { useRouter } from 'expo-router';
import { ClassGuideResponse } from '../../helpers/model';
import { paasSourceStyles } from '../../helpers/stylesheet';
import { navigationService, NavigationItem } from '../../helpers/navigationService';

interface ClassGuideTabContentProps {
  classGuide: ClassGuideResponse;
  tabId: string;
  tabName: string;
  contentWidth: number;
}

export const ClassGuideTabContent: React.FC<ClassGuideTabContentProps> = ({
  classGuide,
  tabId,
  tabName,
  contentWidth
}) => {
  const router = useRouter();

  // Handle navigation to related content
  const handleNavigationClick = (item: NavigationItem) => {
    try {
      // Add to breadcrumb trail
      const breadcrumb = navigationService.createBreadcrumbFromNavigationItem(item);
      navigationService.addBreadcrumb(breadcrumb);

      // Navigate to the content
      const navigationPath = navigationService.getNavigationPath(item);
      router.push(navigationPath);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };
  
  // History Tab Content
  const renderHistoryTab = () => (
    <View style={paasSourceStyles.content}>
      {classGuide.History && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>History</Text>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: classGuide.History }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              span: paasSourceStyles.sectionText,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
            }}
          />
        </View>
      )}
    </View>
  );

  // Resources Tab Content
  const renderResourcesTab = () => (
    <View style={paasSourceStyles.content}>
      {/* PAAS Documents */}
      {classGuide.PaasDocuments && classGuide.PaasDocuments.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Related PAAS Documents</Text>
          {classGuide.PaasDocuments.map((doc: any, index: number) => (
            <Pressable key={index} style={paasSourceStyles.linkButton}>
              <Text style={paasSourceStyles.linkText}>{doc.Title}</Text>
              <Text style={paasSourceStyles.sectionText}>
                Type: {doc.DocumentType} | LOB: {doc.Lobs?.map((lob: any) => lob.Name).join(', ')}
              </Text>
            </Pressable>
          ))}
        </View>
      )}
    </View>
  );

  // Class Information Tab Content
  const renderClassInformationTab = () => (
    <View style={paasSourceStyles.content}>
      {/* Notes */}
      {classGuide.Notes && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Notes</Text>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: classGuide.Notes }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
            }}
          />
        </View>
      )}

      {/* Contemplated Operations */}
      {classGuide.ContemplatedOperations && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Contemplated Operations</Text>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: classGuide.ContemplatedOperations }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              h1: paasSourceStyles.sectionTitle,
              h2: paasSourceStyles.sectionTitle,
              h3: paasSourceStyles.sectionTitle,
              h4: paasSourceStyles.sectionTitle,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
              ul: { marginLeft: 20 },
              ol: { marginLeft: 20 },
              li: paasSourceStyles.sectionText,
            }}
          />
        </View>
      )}

      {/* Operations Not Contemplated - Note: This field might need to be added to the API response */}
      {classGuide.OperationsNotContemplated && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Operations Not Contemplated</Text>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: classGuide.OperationsNotContemplated }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
            }}
          />
        </View>
      )}

      {/* Analogies */}
      {classGuide.Analogies && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Analogies</Text>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: classGuide.Analogies }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
            }}
          />
        </View>
      )}
    </View>
  );

  // Related Links Tab Content
  const renderRelatedLinksTab = () => (
    <View style={paasSourceStyles.content}>
      {/* Related GL Codes */}
      {classGuide.RelatedGLCodes && classGuide.RelatedGLCodes.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>GL Classification Links</Text>
          <View style={paasSourceStyles.linkList}>
            {classGuide.RelatedGLCodes.map((code: any, index: number) => (
              <View key={index} style={paasSourceStyles.linkListItem}>
                <Text style={paasSourceStyles.linkBullet}>•</Text>
                <Pressable
                  style={paasSourceStyles.linkButton}
                  onPress={() => handleNavigationClick(code)}
                >
                  <Text style={paasSourceStyles.linkText}>
                    {code.Jurisdiction?.length === 1
                      ? code.Jurisdiction.map((js: any) => js.Code).join('') + ' '
                      : ''
                    }
                    {code.ClassCode} {code.Title}
                  </Text>
                </Pressable>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Additional Phraseologies */}
      {classGuide.AdditionalPhraseologies && classGuide.AdditionalPhraseologies.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Additional Phraseologies</Text>
          {classGuide.AdditionalPhraseologies.map((phrase: any, index: number) => (
            <Text key={index} style={paasSourceStyles.sectionText}>{phrase}</Text>
          ))}
        </View>
      )}

      {/* GL State Exception */}
      {classGuide.GLStateException && classGuide.GLStateException.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>GL State Exceptions</Text>
          {classGuide.GLStateException.map((exception: any, index: number) => (
            <Pressable key={index} style={paasSourceStyles.linkButton}>
              <Text style={paasSourceStyles.linkText}>
                {exception.ClassCode} {exception.Title}
              </Text>
            </Pressable>
          ))}
        </View>
      )}

      {/* GL to SIC Mapping */}
      {classGuide.GlToSICMapping && classGuide.GlToSICMapping.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>GL SIC/NAICS Code Mapping</Text>
          {classGuide.GlToSICMapping.map((mapping: any, index: number) => (
            <View key={index} style={paasSourceStyles.contentSection}>
              <Text style={paasSourceStyles.sectionText}>
                <Text style={{ fontWeight: 'bold' }}>SIC {mapping.SicCode}:</Text> {mapping.SicDescription}
              </Text>
              <Text style={paasSourceStyles.sectionText}>
                <Text style={{ fontWeight: 'bold' }}>NAICS {mapping.NaicsCode}:</Text> {mapping.NaicsDescription}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* External Links */}
      {classGuide.ExternalLinks && classGuide.ExternalLinks.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>External Links</Text>
          {classGuide.ExternalLinks.map((link: any, index: number) => (
            <Pressable key={index} style={paasSourceStyles.linkButton}>
              <Text style={paasSourceStyles.linkText}>{link.Title || link.Url}</Text>
            </Pressable>
          ))}
        </View>
      )}

      {/* Related WC Codes */}
      {classGuide.RelatedWcCodes && classGuide.RelatedWcCodes.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>WC Classification Links</Text>
          <View style={paasSourceStyles.linkList}>
            {classGuide.RelatedWcCodes.map((code: any, index: number) => (
              <View key={index} style={paasSourceStyles.linkListItem}>
                <Text style={paasSourceStyles.linkBullet}>•</Text>
                <Pressable
                  style={paasSourceStyles.linkButton}
                  onPress={() => handleNavigationClick(code)}
                >
                  <Text style={paasSourceStyles.linkText}>
                    {code.Jurisdiction?.length === 1
                      ? code.Jurisdiction.map((js: any) => js.Code).join('') + ' '
                      : ''
                    }
                    {code.ClassCode} {code.Title}
                  </Text>
                </Pressable>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* WC State Exception */}
      {classGuide.WCStateException && classGuide.WCStateException.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>WC State Exceptions</Text>
          {classGuide.WCStateException.map((exception: any, index: number) => (
            <Pressable key={index} style={paasSourceStyles.linkButton}>
              <Text style={paasSourceStyles.linkText}>
                {exception.ClassCode} {exception.Title}
              </Text>
              <Text style={paasSourceStyles.sectionText}>
                Jurisdiction: {exception.Jurisdiction?.map((j: any) => j.Name).join(', ')}
              </Text>
            </Pressable>
          ))}
        </View>
      )}

      {/* WC to SIC Mapping */}
      {classGuide.WCToSICMapping && classGuide.WCToSICMapping.length > 0 && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>WC SIC/NAICS Code Mapping</Text>
          {classGuide.WCToSICMapping.map((mapping: any, index: number) => (
            <View key={index} style={paasSourceStyles.contentSection}>
              <Text style={paasSourceStyles.sectionText}>
                <Text style={{ fontWeight: 'bold' }}>SIC {mapping.SicCode}:</Text> {mapping.SicDescription}
              </Text>
              <Text style={paasSourceStyles.sectionText}>
                <Text style={{ fontWeight: 'bold' }}>NAICS {mapping.NaicsCode}:</Text> {mapping.NaicsDescription}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  // Main render logic based on tab ID and name
  if (tabId === "3a501e30-cd93-4a1b-bfa5-9112d5bf06ee" || tabName === "History") {
    return renderHistoryTab();
  } else if (tabId === "5e89dcc6-97bf-487e-babe-03d5041c95b6" || tabName === "Resources") {
    return renderResourcesTab();
  } else if (tabId === "f282538d-644d-46a5-bd0f-1f17cbcffd20" || tabName === "Class Information") {
    return renderClassInformationTab();
  } else if (tabId === "ce4b5f3a-e1f8-4d52-a825-95b06057b979" || tabName === "Related Links") {
    return renderRelatedLinksTab();
  }

  // Default fallback
  return (
    <View style={paasSourceStyles.content}>
      <Text style={paasSourceStyles.sectionText}>Content not available for this tab.</Text>
    </View>
  );
};
