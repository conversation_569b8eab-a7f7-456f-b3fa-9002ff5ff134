import React, { useState } from 'react';
import { View, Text, ScrollView, Pressable } from 'react-native';
import { RenderHTML } from 'react-native-render-html';
import { FaqResponse } from '../../helpers/model';
import { htmlContentStyles, paasSourceStyles } from '../../helpers/stylesheet';
import { faqTabs } from '../../config/tabConfigurations';
import { FaqTabContent } from './FaqTabContent';

// Base URL for PAAS images
const PAAS_BASE_URL = 'https://corea3.verisk.com';

// Utility function to fix image URLs in HTML content
const fixImageUrls = (html: string): string => {
  if (!html) return html;

  // Replace relative image URLs with absolute URLs
  return html.replace(
    /src="(\/[^"]*\.(?:jpg|jpeg|png|gif|svg|webp|ashx)[^"]*)"/gi,
    `src="${PAAS_BASE_URL}$1"`
  );
};

interface FaqContentProps {
  faq: FaqResponse;
  contentWidth: number;
}

export default function FaqContent({ faq, contentWidth }: FaqContentProps) {
  const [activeTab, setActiveTab] = useState<string>(faqTabs[0]?.id || '');

  // Get current tab info
  const getCurrentTab = () => {
    return faqTabs.find(tab => tab.id === activeTab) || faqTabs[0];
  };

  const renderFaqHeader = () => {
    const lobNames = faq.Lobs?.map(lob => lob.Name).join(' and ') || '';
    const releaseDate = `${faq.ReleaseMonth || ''} ${faq.ReleaseYear || ''}`.trim();
    
    return (
      <View style={htmlContentStyles.headerContainer}>
        <Text style={htmlContentStyles.title}>{faq.Title}</Text>
        <View style={htmlContentStyles.detailsContainer}>
          <View style={htmlContentStyles.detailItem}>
            <Text style={htmlContentStyles.detailLabel}>Line of business:</Text>
            <Text style={htmlContentStyles.detailValue}>{lobNames}</Text>
          </View>
          {releaseDate && (
            <View style={htmlContentStyles.detailItem}>
              <Text style={htmlContentStyles.detailLabel}>Released on:</Text>
              <Text style={htmlContentStyles.detailValue}>{releaseDate}</Text>
            </View>
          )}
        </View>

        {/* Question Section */}
        {faq.Question && (
          <View style={htmlContentStyles.questionContainer}>
            <Text style={htmlContentStyles.questionLabel}>Question</Text>
            <RenderHTML
              contentWidth={contentWidth}
              source={{ html: fixImageUrls(faq.Question) }}
              tagsStyles={{
                p: htmlContentStyles.htmlParagraph,
                h3: htmlContentStyles.htmlParagraph,
                strong: { fontWeight: 'bold' },
                b: { fontWeight: 'bold' },
              }}
            />
          </View>
        )}
      </View>
    );
  };

  const renderQuestion = () => {
    if (!faq.Question) return null;

    return (
      <View style={htmlContentStyles.sectionContainer}>
        <Text style={htmlContentStyles.sectionHeading}>Question</Text>
        <View style={htmlContentStyles.sectionContent}>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: fixImageUrls(faq.Question) }}
            tagsStyles={{
              p: htmlContentStyles.htmlParagraph,
              ul: htmlContentStyles.htmlList,
              ol: htmlContentStyles.htmlList,
              li: htmlContentStyles.htmlListItem,
              strong: htmlContentStyles.htmlStrong,
              b: htmlContentStyles.htmlStrong,
              em: htmlContentStyles.htmlEmphasis,
              i: htmlContentStyles.htmlEmphasis,
              h1: htmlContentStyles.htmlHeading1,
              h2: htmlContentStyles.htmlHeading2,
              h3: htmlContentStyles.htmlHeading3,
              h4: htmlContentStyles.htmlHeading4,
              table: htmlContentStyles.htmlTable,
              tr: htmlContentStyles.htmlTableRow,
              td: htmlContentStyles.htmlTableCell,
              th: htmlContentStyles.htmlTableHeader,
              span: htmlContentStyles.htmlSpan,
              div: htmlContentStyles.htmlDiv,
            }}
          />
        </View>
      </View>
    );
  };

  const renderAnswer = () => {
    if (!faq.Answer) return null;

    return (
      <View style={htmlContentStyles.sectionContainer}>
        <Text style={htmlContentStyles.sectionHeading}>Answer</Text>
        <View style={htmlContentStyles.sectionContent}>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: fixImageUrls(faq.Answer) }}
            tagsStyles={{
              p: htmlContentStyles.htmlParagraph,
              ul: htmlContentStyles.htmlList,
              ol: htmlContentStyles.htmlList,
              li: htmlContentStyles.htmlListItem,
              strong: htmlContentStyles.htmlStrong,
              b: htmlContentStyles.htmlStrong,
              em: htmlContentStyles.htmlEmphasis,
              i: htmlContentStyles.htmlEmphasis,
              h1: htmlContentStyles.htmlHeading1,
              h2: htmlContentStyles.htmlHeading2,
              h3: htmlContentStyles.htmlHeading3,
              h4: htmlContentStyles.htmlHeading4,
              table: htmlContentStyles.htmlTable,
              tr: htmlContentStyles.htmlTableRow,
              td: htmlContentStyles.htmlTableCell,
              th: htmlContentStyles.htmlTableHeader,
              span: htmlContentStyles.htmlSpan,
              div: htmlContentStyles.htmlDiv,
            }}
          />
        </View>
      </View>
    );
  };

  const renderClassificationLinks = () => {
    const wcLinks = faq.WCClassificationLink || [];
    const glLinks = faq.GLClassificationLink || [];
    
    if (wcLinks.length === 0 && glLinks.length === 0) return null;

    return (
      <View style={htmlContentStyles.sectionContainer}>
        <Text style={htmlContentStyles.sectionHeading}>Related Classification Links</Text>
        <View style={htmlContentStyles.sectionContent}>
          {wcLinks.length > 0 && (
            <View style={htmlContentStyles.linkSection}>
              <Text style={htmlContentStyles.linkSectionTitle}>Workers Compensation</Text>
              {wcLinks.map((link, index) => (
                <View key={index} style={htmlContentStyles.linkItem}>
                  <Text style={htmlContentStyles.linkTitle}>
                    {link.ClassCode} - {link.Title}
                  </Text>
                  <Text style={htmlContentStyles.linkJurisdiction}>
                    {link.Jurisdiction?.map(j => j.Name).join(', ')}
                  </Text>
                </View>
              ))}
            </View>
          )}
          {glLinks.length > 0 && (
            <View style={htmlContentStyles.linkSection}>
              <Text style={htmlContentStyles.linkSectionTitle}>General Liability</Text>
              {glLinks.map((link, index) => (
                <View key={index} style={htmlContentStyles.linkItem}>
                  <Text style={htmlContentStyles.linkTitle}>
                    {link.ClassCode} - {link.Title}
                  </Text>
                  <Text style={htmlContentStyles.linkJurisdiction}>
                    {link.Jurisdiction?.map(j => j.Name).join(', ')}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderResources = () => {
    if (!faq.Resources) return null;

    return (
      <View style={htmlContentStyles.sectionContainer}>
        <Text style={htmlContentStyles.sectionHeading}>Resources</Text>
        <View style={htmlContentStyles.sectionContent}>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: fixImageUrls(faq.Resources) }}
            tagsStyles={{
              p: htmlContentStyles.htmlParagraph,
              ul: htmlContentStyles.htmlList,
              ol: htmlContentStyles.htmlList,
              li: htmlContentStyles.htmlListItem,
              strong: htmlContentStyles.htmlStrong,
              b: htmlContentStyles.htmlStrong,
              em: htmlContentStyles.htmlEmphasis,
              i: htmlContentStyles.htmlEmphasis,
              h1: htmlContentStyles.htmlHeading1,
              h2: htmlContentStyles.htmlHeading2,
              h3: htmlContentStyles.htmlHeading3,
              h4: htmlContentStyles.htmlHeading4,
              table: htmlContentStyles.htmlTable,
              tr: htmlContentStyles.htmlTableRow,
              td: htmlContentStyles.htmlTableCell,
              th: htmlContentStyles.htmlTableHeader,
              span: htmlContentStyles.htmlSpan,
              div: htmlContentStyles.htmlDiv,
            }}
          />
        </View>
      </View>
    );
  };



  return (
    <View style={htmlContentStyles.container}>
      {/* FAQ Header */}
      {renderFaqHeader()}

      {/* Dynamic Tab Navigation */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={paasSourceStyles.tabContainer}
      >
        {faqTabs.map((tab) => (
          <Pressable
            key={tab.id}
            style={[
              paasSourceStyles.tab,
              activeTab === tab.id && paasSourceStyles.activeTab,
            ]}
            onPress={() => setActiveTab(tab.id)}
          >
            <Text
              style={[
                paasSourceStyles.tabText,
                activeTab === tab.id && paasSourceStyles.activeTabText,
              ]}
            >
              {tab.displayName}
            </Text>
          </Pressable>
        ))}
      </ScrollView>

      {/* Dynamic Tab Content */}
      <ScrollView style={htmlContentStyles.content} showsVerticalScrollIndicator={false}>
        <FaqTabContent
          faq={faq}
          tabId={activeTab}
          tabName={getCurrentTab()?.name || ''}
          contentWidth={contentWidth}
        />
      </ScrollView>
    </View>
  );
}
