import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { RenderHTML } from 'react-native-render-html';
import { useRouter } from 'expo-router';
import { FaqResponse } from '../../helpers/model';
import { paasSourceStyles } from '../../helpers/stylesheet';
import { navigationService, NavigationItem } from '../../helpers/navigationService';

interface FaqTabContentProps {
  faq: FaqResponse;
  tabId: string;
  tabName: string;
  contentWidth: number;
}

export const FaqTabContent: React.FC<FaqTabContentProps> = ({
  faq,
  tabId,
  tabName,
  contentWidth
}) => {
  const router = useRouter();

  // Handle navigation to related content
  const handleNavigationClick = (item: NavigationItem) => {
    try {
      // Add to breadcrumb trail
      const breadcrumb = navigationService.createBreadcrumbFromNavigationItem(item);
      navigationService.addBreadcrumb(breadcrumb);

      // Navigate to the content
      const navigationPath = navigationService.getNavigationPath(item);
      router.push(navigationPath);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  // Helper function to fix image URLs
  const fixImageUrls = (htmlContent: string): string => {
    if (!htmlContent) return '';
    return htmlContent.replace(
      /src="\/~/g,
      'src="https://digital.experience.content.web/'
    );
  };

  // Answer Tab Content
  const renderAnswerTab = () => (
    <View style={paasSourceStyles.content}>
      {faq.Answer && (
        <View style={paasSourceStyles.contentSection}>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: fixImageUrls(faq.Answer) }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              h1: paasSourceStyles.sectionTitle,
              h2: paasSourceStyles.sectionTitle,
              h3: paasSourceStyles.sectionTitle,
              h4: paasSourceStyles.sectionTitle,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
              ul: { marginLeft: 20 },
              ol: { marginLeft: 20 },
              li: paasSourceStyles.sectionText,
              table: {
                borderWidth: 1,
                borderColor: '#ddd',
              },
              th: {
                backgroundColor: '#f5f5f5',
                padding: 8,
                borderWidth: 1,
                borderColor: '#ddd',
                fontWeight: 'bold',
              },
              td: {
                padding: 8,
                borderWidth: 1,
                borderColor: '#ddd',
              },
              img: {
                maxWidth: contentWidth - 40,
                height: 'auto',
              },
            }}
          />
        </View>
      )}
    </View>
  );

  // Related Links Tab Content
  const renderRelatedLinksTab = () => (
    <View style={paasSourceStyles.content}>
      {/* WC Classification Link */}
      {faq.WCClassificationLink && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>WC Classification Links</Text>
          {Array.isArray(faq.WCClassificationLink) ? (
            <View style={paasSourceStyles.linkList}>
              {faq.WCClassificationLink.map((link: any, index: number) => (
                <View key={index} style={paasSourceStyles.linkListItem}>
                  <Text style={paasSourceStyles.linkBullet}>•</Text>
                  <Pressable
                    style={paasSourceStyles.linkButton}
                    onPress={() => handleNavigationClick(link)}
                  >
                    <Text style={paasSourceStyles.linkText}>
                      {link.Jurisdiction?.length === 1
                        ? link.Jurisdiction.map((js: any) => js.Code).join('') + ' '
                        : ''
                      }
                      {link.Code} {link.Title}
                    </Text>
                  </Pressable>
                </View>
              ))}
            </View>
          ) : (
            <Text style={paasSourceStyles.sectionText}>No WC Classification Links</Text>
          )}
        </View>
      )}

      {/* GL Classification Link */}
      {faq.GLClassificationLink && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>GL Classification Links</Text>
          {Array.isArray(faq.GLClassificationLink) ? (
            <View style={paasSourceStyles.linkList}>
              {faq.GLClassificationLink.map((link: any, index: number) => (
                <View key={index} style={paasSourceStyles.linkListItem}>
                  <Text style={paasSourceStyles.linkBullet}>•</Text>
                  <Pressable
                    style={paasSourceStyles.linkButton}
                    onPress={() => handleNavigationClick(link)}
                  >
                    <Text style={paasSourceStyles.linkText}>
                      {link.Jurisdiction?.length === 1
                        ? link.Jurisdiction.map((js: any) => js.Code).join('') + ' '
                        : ''
                      }
                      {link.Code} {link.Title}
                    </Text>
                  </Pressable>
                </View>
              ))}
            </View>
          ) : (
            <Text style={paasSourceStyles.sectionText}>No GL Classification Links</Text>
          )}
        </View>
      )}
    </View>
  );

  // Resources Tab Content
  const renderResourcesTab = () => (
    <View style={paasSourceStyles.content}>
      {faq.Resources && (
        <View style={paasSourceStyles.contentSection}>
          <Text style={paasSourceStyles.sectionTitle}>Resources</Text>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: fixImageUrls(faq.Resources) }}
            tagsStyles={{
              p: paasSourceStyles.sectionText,
              h1: paasSourceStyles.sectionTitle,
              h2: paasSourceStyles.sectionTitle,
              h3: paasSourceStyles.sectionTitle,
              h4: paasSourceStyles.sectionTitle,
              strong: { fontWeight: 'bold' },
              b: { fontWeight: 'bold' },
              ul: { marginLeft: 20 },
              ol: { marginLeft: 20 },
              li: paasSourceStyles.sectionText,
              a: {
                color: '#007AFF',
                textDecorationLine: 'underline',
              },
              table: {
                borderWidth: 1,
                borderColor: '#ddd',
              },
              th: {
                backgroundColor: '#f5f5f5',
                padding: 8,
                borderWidth: 1,
                borderColor: '#ddd',
                fontWeight: 'bold',
              },
              td: {
                padding: 8,
                borderWidth: 1,
                borderColor: '#ddd',
              },
              img: {
                maxWidth: contentWidth - 40,
                height: 'auto',
              },
            }}
          />
        </View>
      )}
    </View>
  );

  // Main render logic based on tab ID and name
  if (tabId === 'eb74cced-4060-441f-a101-93c654a924f8' || tabName === "Answer") {
    return renderAnswerTab();
  } else if (tabId === "6bc2f7fe-2fe6-45d2-873f-1f7f9a68c516" || tabName === "Related Links") {
    return renderRelatedLinksTab();
  } else if (tabId === "65a7c196-8c5f-4f4c-a70e-664ffd9098a6" || tabName === "Resources") {
    return renderResourcesTab();
  }

  // Default fallback
  return (
    <View style={paasSourceStyles.content}>
      <Text style={paasSourceStyles.sectionText}>Content not available for this tab.</Text>
    </View>
  );
};
