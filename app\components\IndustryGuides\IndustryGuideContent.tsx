import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import { RenderHTML } from 'react-native-render-html';
import { IndustryGuideChapter } from '../../helpers/model';
import { htmlContentStyles } from '../../helpers/stylesheet';

// Base URL for PAAS images
const PAAS_BASE_URL = 'https://corea3.verisk.com';

// Utility function to fix image URLs in HTML content
const fixImageUrls = (html: string): string => {
  if (!html) return html;

  // Replace relative image URLs with absolute URLs
  return html.replace(
    /src="(\/[^"]*\.(?:jpg|jpeg|png|gif|svg|webp|ashx)[^"]*)"/gi,
    `src="${PAAS_BASE_URL}$1"`
  );
};

interface IndustryGuideContentProps {
  chapter: IndustryGuideChapter;
  contentWidth: number;
}

export function IndustryGuideContent({ chapter, contentWidth }: IndustryGuideContentProps) {
  // Custom renderer for tables with horizontal scroll
  const customRenderers = {
    table: ({ TDefaultRenderer, ...props }: any) => (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={true}
        style={htmlContentStyles.htmlTableScrollContainer}
      >
        <TDefaultRenderer {...props} />
      </ScrollView>
    ),
  };

  const renderSections = () => {
    return chapter.SectionList.map((section, index) => (
      <View key={index} style={htmlContentStyles.sectionContainer}>
        <Text style={htmlContentStyles.sectionHeading}>{section.Heading}</Text>
        <View style={htmlContentStyles.sectionContent}>
          <RenderHTML
            contentWidth={contentWidth}
            source={{ html: fixImageUrls(section.SectionText) }}
            renderers={customRenderers}
            tagsStyles={{
              p: htmlContentStyles.htmlParagraph,
              ul: htmlContentStyles.htmlList,
              ol: htmlContentStyles.htmlList,
              li: htmlContentStyles.htmlListItem,
              strong: htmlContentStyles.htmlStrong,
              b: htmlContentStyles.htmlStrong,
              em: htmlContentStyles.htmlEmphasis,
              i: htmlContentStyles.htmlEmphasis,
              h1: htmlContentStyles.htmlHeading1,
              h2: htmlContentStyles.htmlHeading2,
              h3: htmlContentStyles.htmlHeading3,
              h4: htmlContentStyles.htmlHeading4,
              h5: htmlContentStyles.htmlHeading5,
              h6: htmlContentStyles.htmlHeading6,
              table: htmlContentStyles.htmlTable,
              thead: htmlContentStyles.htmlTableHead,
              tbody: htmlContentStyles.htmlTableBody,
              tr: htmlContentStyles.htmlTableRow,
              th: {
                ...htmlContentStyles.htmlTableHeader,
                textAlign: 'center',
                verticalAlign: 'middle',
              },
              td: {
                ...htmlContentStyles.htmlTableCell,
                textAlign: 'center',
                verticalAlign: 'middle',
                flexWrap: 'wrap',
              },
              img: {
                ...htmlContentStyles.htmlImage,
                maxWidth: contentWidth * 2,
                minWidth: contentWidth,
              },
              a: htmlContentStyles.htmlLink,
              blockquote: htmlContentStyles.htmlBlockquote,
              code: htmlContentStyles.htmlCode,
              pre: htmlContentStyles.htmlPre,
              div: htmlContentStyles.htmlDiv,
              span: htmlContentStyles.htmlSpan,
            }}
            systemFonts={['Roboto', 'Arial', 'sans-serif']}
            renderersProps={{
              table: {
                tableStyleSpecs: {
                  outerBorderWidthPx: 1,
                  rowsBorderWidthPx: 1,
                  columnsBorderWidthPx: 1,
                  borderColor: '#dee2e6',
                  cellPaddingEm: 0.8,
                  fitContainer: true,
                },
              },
              img: {
                enableExperimentalPercentWidth: false,
                initialDimensions: {
                  width: contentWidth * 2,
                  height: 200,
                },
              },
            }}
          />
        </View>
      </View>
    ));
  };

  return (
    <ScrollView style={htmlContentStyles.container} showsVerticalScrollIndicator={false}>
      <View style={htmlContentStyles.content}>
        {renderSections()}
      </View>
    </ScrollView>
  );
}


