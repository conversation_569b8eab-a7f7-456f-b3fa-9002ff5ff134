import React, { useState, useEffect } from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { navigationService, BreadcrumbItem } from '../../helpers/navigationService';
import { paasSourceStyles } from '../../helpers/stylesheet';

interface BreadcrumbNavigationProps {
  currentTitle?: string;
  showSearchResults?: boolean;
}

export const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  currentTitle,
  showSearchResults = false
}) => {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  const router = useRouter();

  useEffect(() => {
    // Subscribe to breadcrumb changes
    const unsubscribe = navigationService.subscribe(setBreadcrumbs);
    
    // Get initial breadcrumbs
    setBreadcrumbs(navigationService.getBreadcrumbs());

    return unsubscribe;
  }, []);

  const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
    try {
      // Remove breadcrumbs after the clicked one
      navigationService.removeBreadcrumbsFrom(index);

      // Navigate to the clicked item
      const navigationItem = {
        ItemID: item.id,
        ClassCode: item.classCode || '',
        Title: item.title,
        ContentType: item.contentType,
        Jurisdiction: item.jurisdiction ? [{ Code: item.jurisdiction, Name: '' }] : [],
        Lobs: item.lob ? [{ Code: item.lob, Name: '' }] : []
      };

      const path = navigationService.getNavigationPath(navigationItem);
      router.push(path);
    } catch (error) {
      console.error('Breadcrumb navigation error:', error);
    }
  };

  const handleSearchResultsClick = () => {
    // Navigate back to search results
    router.back();
  };

  const handlePaasAIClick = () => {
    // Clear all breadcrumbs and navigate back to main chat
    navigationService.clearBreadcrumbs();
    router.push('/');
  };

  if (breadcrumbs.length === 0 && !showSearchResults && !currentTitle) {
    return null;
  }

  return (
    <View style={paasSourceStyles.breadcrumbContainer}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={paasSourceStyles.breadcrumbScrollView}
      >
        <View style={paasSourceStyles.breadcrumbContent}>
          {/* PAAS AI Link - Always show as first breadcrumb */}
          <Pressable
            style={paasSourceStyles.breadcrumbItem}
            onPress={handlePaasAIClick}
          >
            <Text style={paasSourceStyles.breadcrumbLink}>PAAS AI</Text>
          </Pressable>
          <Text style={paasSourceStyles.breadcrumbSeparator}> › </Text>

          {/* Search Results Link */}
          {showSearchResults && (
            <>
              <Pressable
                style={paasSourceStyles.breadcrumbItem}
                onPress={handleSearchResultsClick}
              >
                <Text style={paasSourceStyles.breadcrumbLink}>Search Results</Text>
              </Pressable>
              <Text style={paasSourceStyles.breadcrumbSeparator}> › </Text>
            </>
          )}

          {/* Breadcrumb Items */}
          {breadcrumbs.map((item, index) => (
            <React.Fragment key={item.id}>
              <Pressable
                style={paasSourceStyles.breadcrumbItem}
                onPress={() => handleBreadcrumbClick(item, index)}
              >
                <Text style={paasSourceStyles.breadcrumbLink}>
                  {navigationService.truncateTitle(item.title)}
                </Text>
              </Pressable>
              <Text style={paasSourceStyles.breadcrumbSeparator}> › </Text>
            </React.Fragment>
          ))}

          {/* Current Page */}
          {currentTitle && (
            <Text style={paasSourceStyles.breadcrumbCurrent}>
              {navigationService.truncateTitle(currentTitle)}
            </Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default BreadcrumbNavigation;
