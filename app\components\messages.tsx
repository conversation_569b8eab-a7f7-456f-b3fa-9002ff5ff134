import { useEffect, useRef, useState } from "react";
import { Text, View, ScrollView, TextInput, Image, Pressable, KeyboardAvoidingView, Platform } from "react-native";
import { PopUp } from "../modals/popup";
import { CopyButtonData, EmitData, EmitType, Message, ModalState, ModalType, UserData } from "../helpers/model";
import Icon from "@/assets/icons/UserIcon";
import { convStyles, messageStyles } from "../helpers/stylesheet";
import * as Clipboard from 'expo-clipboard';
import SubmitIcon from "@/assets/icons/SubmitIcon";
import TypingIndicator from "@/assets/icons/TypingIndicator";
import ThumbUp from "@/assets/icons/ThumbUp";
import ThumbDown from "@/assets/icons/ThumbDown";
import Copy from "@/assets/icons/CopyIcon";
import { TextInputKeyPressEventData } from "react-native";
import { useAuth } from "../helpers/authContext";


declare global {
  interface Window {
    copyTimeout?: ReturnType<typeof setTimeout>;
  }
}

export function Messages({
  userData,
  isLoading,
  emitChange,
  emitDisabled
}: { 
  userData: UserData, 
  isLoading: boolean,
  emitChange: (emitData: EmitData) => void
  emitDisabled: (isDisabled: boolean) => void
}) {     
const [input, setInput] = useState('');
const [index, setIndex] = useState(-1);
const [modalState, setModalState] = useState(new ModalState());

//TODO add disabled state to authData
const [isDisabled, setDisabled] = useState(false);
const [isListening, setListening] = useState(false);
const [isCopyActive, setIsCopyActive] = useState(new CopyButtonData());
const [isDeactivated, setDeactivated] = useState(false); 
const { authData } = useAuth();
const userName = authData.username;
const scrollViewRef = useRef<ScrollView | null>(null);

useEffect(() => {
  setDisabled(isLoading);
  // if(!isLoading) document.getElementById('input')?.focus(); TODO
}, [isLoading]);

useEffect(() => {
  emitDisabled(isListening || isDisabled);
}, [isListening, isDisabled]);

// Copy Question and Answer to clipboard
const onClickCopy = (message: Message, index: number) => {
  Clipboard.setStringAsync('Question: ' + message.userMsg.toString() + '\n\n' +
  'Response: ' + message.botMsg?.toString());
  setIsCopyActive(new CopyButtonData(true, index));

  clearTimeout(window.copyTimeout);
  window.copyTimeout = setTimeout(() => 
    setIsCopyActive(new CopyButtonData(false, index)), 2000);
}

// Open modal
const toggleModal = (modalType: ModalType) => {
  setModalState({ modalType: modalType, isVisible: !modalState.isVisible, conversationName: '' });
}

const formatUserMessage = (message: string) => {
  return message.trim().replace(/\s+/g, ' ');
}

const onEmitData = (emitType: EmitType) => {
  switch (emitType) {
    case EmitType.message:
      emitChange({ ...new EmitData(), isConfirmed: true, emitType: emitType, 
        message: formatUserMessage(input) });
      setInput('');
      
      break;

    default:
      emitChange({ ...new EmitData(), isConfirmed: true, emitType: emitType });
      break;
  }
}

// Rate the GenAI response
const onClickRate = (emitType: EmitType, index: number) => {
  if (emitType === EmitType.rateDown) {
    setIndex(index);
    toggleModal(ModalType.Feedback);
  } else {
    emitChange({ ...new EmitData(), isConfirmed: true, 
      index: index, emitType: EmitType.rateUp });
  }
}

const onHandlePopupClose = (emitData: EmitData) => {
  if (emitData.isConfirmed === false) return;
  emitChange(emitData);
}

const onVoiceEmit = (data: string) => {
  setInput(input + " " + data);
}

const handleKeyPress = ({ nativeEvent }: { nativeEvent: TextInputKeyPressEventData }) => {
  if (nativeEvent.key === 'Enter' && !(input.trim() === '' || isDisabled)) {
    onEmitData(EmitType.message);
    setInput('');
  }
};

 const parseGreetingMessage=(html = '')=> {
  if (!html) return { conversationStartersDescription: '', conversationStartersList: [] };
  const [headerPart, listPart] = html.split(/<br\s*\/?>/i);
  const listItems = (listPart || '')
    .split(/<\/li>/i)
    .map(item => item.replace(/<li>/i, '').trim())
    .filter(item => item.length > 0);

  return {
    conversationStartersDescription: headerPart?.trim() || '',
    conversationStartersList: listItems,
  };
}

  const { conversationStartersDescription, conversationStartersList } = parseGreetingMessage(authData?.sitecore?.ConversationStartersText?.value)!;
// HTML: Dynamically display messages
const displayMessages = () => {
  const activeConversation = userData.conversations
    .find(conversation => conversation.isActive);

  // If New Conversation, display greeting
  if (activeConversation && activeConversation.title === "New Conversation") {
    return (
      <View>
          {authData?.sitecore?.ConversationStartersText?.value && (
            <View 
              testID="greeting"
              style={messageStyles.message}>
              <View style={messageStyles.header}>
                <Icon userName={authData.sitecore.BotNameText.value}/>
                <Text style={messageStyles.headerText}>{authData.sitecore.BotNameText.value}</Text>
              </View>          
              <View style={messageStyles.greeetingsContent}>
                <Text style={messageStyles.greeting}>{authData.sitecore.GreetingMessageText.value}</Text>
                {conversationStartersDescription.length && <Text style={messageStyles.conversationStartersDescription}>{conversationStartersDescription}</Text>}
                {conversationStartersList.map((item, index) => (
                  <Text key={index} style={messageStyles.conversationStartersList}>{`\u2022 ${item}`}</Text>
                ))}
              </View>
            </View>
          )
        }
      </View>
    );

  // Else, display full message history
  } else if (activeConversation) {
      return activeConversation.messageHistory
        .map((message, index) => (
        <View key={index}>

        {/* Display user message */}
        {message.userMsg !== '' && (
        <View 
        style={messageStyles.message}>
          <View style={messageStyles.header}>
            <Icon userName={userName}/>
            <Text style={messageStyles.headerText}>{userName}</Text>
          </View>
          <View style={messageStyles.content}>
            <Text>{message.userMsg}</Text>
          </View>
        </View>
        )}
        
        {/* Display bot message or Typing indicator */}
        <View style={messageStyles.message}>
          <View style={messageStyles.header}>
            <Icon userName={authData.sitecore.BotNameText.value}/>
            <Text style={messageStyles.headerText}>{authData.sitecore.BotNameText.value}</Text>
          </View>
            <View style={messageStyles.content}>
              {message.botMsg === '' 
                ? <TypingIndicator/> 
                : <Text>{message.botMsg}</Text>
              }
            </View>

          {/* Message Options - Displayed per GenAI message */}
          {message.botMsg !== '' && (
          <View style={messageStyles.footer}>

            {/* Copy Button */}
            <Pressable 
              testID={`copy-${index}`}
              onPress={() => onClickCopy(message, index)}>
                <Copy isActive={isCopyActive.isActive
                  && isCopyActive.index === index}/>
            </Pressable>

            {/* Upvote Button */}
            <Pressable 
              testID={`rate-up-${index}`}
              onPress={() => onClickRate(EmitType.rateUp, index)}
              disabled={message.rating !== 0 || isDisabled}>
              <ThumbUp isActive={message.rating === 1} />
            </Pressable>

            {/* Downvote Button */}
            <Pressable 
              testID={`rate-down-${index}`}
              onPress={() => onClickRate(EmitType.rateDown, index)} 
              disabled={message.rating !== 0 || isDisabled}>
              <ThumbDown isActive={message.rating === -1} />
            </Pressable>           
          </View>
          )}
        </View>
      </View>
      ));
  }

  return null;
}

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      keyboardVerticalOffset={Platform.OS === "ios" ? 145 : 0}
      style={convStyles.container}
    >
      <ScrollView
        keyboardShouldPersistTaps="never"
        ref={scrollViewRef}
        style={convStyles.conversation}
        onContentSizeChange={() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }}
      >
        {displayMessages()}

        <View style={convStyles.inputRow}>
          <View style={convStyles.inputContainer}>
            <TextInput
              testID="message-input"
              editable={!isListening}
              style={[
                convStyles.input,
                { fontStyle: input ? "normal" : "italic" },
              ]}
              // onKeyPress={handleKeyPress}
              onChangeText={setInput}
              value={input}
              placeholderTextColor="grey"
              placeholder={authData.sitecore.InputPlaceholderText.value}
              enablesReturnKeyAutomatically
              multiline
              numberOfLines={2}
              onSubmitEditing={() =>
                input.trim() === "" || isDisabled || isListening
                  ? () => {}
                  : onEmitData(EmitType.message)
              }
              blurOnSubmit={true}
              autoFocus={true}
            />
            <View style={convStyles.inputButtonsContainer}>
              <View style={convStyles.submitButton}>
                <Pressable
                  testID="message-submit-button"
                  onPress={() => onEmitData(EmitType.message)}
                  disabled={input.trim() === "" || isDisabled || isListening}
                >
                  <SubmitIcon
                    isActive={
                      input.trim() !== "" && !isDisabled && !isListening
                    }
                  />
                </Pressable>
              </View>
              {/* <View style={convStyles.divider}>
                    <DividerLogo/>
                  </View>
                  <View style={convStyles.microphoneButton}>
                    <VoiceRecognition 
                      isDisabled = {isDisabled}
                      emitResult={(data) => onVoiceEmit(data)} 
                      emitListening={(isListening) => setListening(isListening)}
                    />
                  </View> */}
            </View>
          </View>
        </View>


      </ScrollView>

      <PopUp
        modalType={modalState.modalType}
        conversationName={modalState.conversationName}
        isVisible={modalState.isVisible}
        index={index}
        onClose={(emitData) => {
          toggleModal(modalState.modalType), onHandlePopupClose(emitData);
        }}
      />
    </KeyboardAvoidingView>
  );
}