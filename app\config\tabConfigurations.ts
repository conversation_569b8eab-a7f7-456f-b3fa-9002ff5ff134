// Tab configurations for Class Guides and FAQs

export interface TabConfig {
  id: string;
  name: string;
  displayName: string;
}

export const classGuidesTabs: TabConfig[] = [
  {
    "id": "f282538d-644d-46a5-bd0f-1f17cbcffd20",
    "name": "Class Information",
    "displayName": "Class Information"
  },
  {
    "id": "5e89dcc6-97bf-487e-babe-03d5041c95b6",
    "name": "Resources",
    "displayName": "Resources"
  },
  {
    "id": "ce4b5f3a-e1f8-4d52-a825-95b06057b979",
    "name": "Related Links",
    "displayName": "Related Links"
  },
  {
    "id": "3a501e30-cd93-4a1b-bfa5-9112d5bf06ee",
    "name": "History",
    "displayName": "History"
  }
];

export const faqTabs: TabConfig[] = [
  {
    "id": "eb74cced-4060-441f-a101-93c654a924f8",
    "name": "Answer",
    "displayName": "Answer"
  },
  {
    "id": "6bc2f7fe-2fe6-45d2-873f-1f7f9a68c516",
    "name": "Related Links",
    "displayName": "Related Links"
  },
  {
    "id": "65a7c196-8c5f-4f4c-a70e-664ffd9098a6",
    "name": "Resources",
    "displayName": "Resources"
  }
];
