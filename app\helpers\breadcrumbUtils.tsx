import { useRouter } from "expo-router";
import { SourceDocumentData } from "./model";

export interface BreadcrumbItem {
  title: string;
  onPress?: () => void;
  isActive?: boolean;
}

/**
 * Generate breadcrumb items for PAAS source navigation
 */
export function generatePaasBreadcrumbs(
  sourceTitle: string,
  router: ReturnType<typeof useRouter>,
  sourceUrl?: string,
  documentName?: string
): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [];

  breadcrumbs.push({
    title: "PAAS AI",
    onPress: () => router.back(),
    isActive: false,
  });

  // Use document name if available, otherwise determine from URL or title
  let displayTitle = documentName || sourceTitle;
  if (!documentName) {
    if (sourceUrl) {
      const sourceInfo = parsePaasSourceInfo(sourceUrl);
      displayTitle = getDocumentTypeFromSourceInfo(sourceInfo);
    } else {
      displayTitle = getDocumentTypeFromTitle(sourceTitle);
    }
  }

  breadcrumbs.push({
    title: displayTitle,
    isActive: true,
  });

  return breadcrumbs;
}

/**
 * Get document type from parsed source info for breadcrumb display
 */
function getDocumentTypeFromSourceInfo(
  sourceInfo: ReturnType<typeof parsePaasSourceInfo>
): string {
  switch (sourceInfo.type) {
    case "training-manual":
      return "Training Manual";
    case "industry-guide":
      return "Industry Guide";
    case "class-guide":
      return "Class Guide";
    case "faq":
      return "FAQ";
    case "bulletin":
      return sourceInfo.contentType || "Bulletin";
    case "search":
      if (sourceInfo.contentType?.toLowerCase().includes('bulletin')) {
        return "Bulletin";
      }
      if (sourceInfo.contentType?.toLowerCase().includes('faq')) {
        return "FAQ";
      }
      if (sourceInfo.contentType?.toLowerCase().includes('class guide')) {
        return "Class Guide";
      }
      return "Search Results";
    default:
      return "Document";
  }
}

/**
 * Extract document type from source title for breadcrumb display (fallback method)
 */
function getDocumentTypeFromTitle(title: string): string {
  const lowerTitle = title.toLowerCase();

  if (lowerTitle.includes("class guide")) return "Class Guide";
  if (lowerTitle.includes("training manual")) return "Training Manual";
  if (lowerTitle.includes("industry guide")) return "Industry Guide";
  if (lowerTitle.includes("search result")) return "Search Results";
  if (lowerTitle.includes("bulletin")) return "Bulletin";
  if (lowerTitle.includes("faq")) return "FAQ";

  return "Document";
}



/**
 * Parse PAAS source URL to extract document information
 */
export function parsePaasSourceInfo(sourceUrl: string): {
  type: string;
  id?: string;
  chapterId?: string;
  contentType?: string;
} {
  if (!sourceUrl) {
    return { type: "unknown" };
  }

  try {
    const url = new URL(sourceUrl, "https://example.com");
    const pathname = url.pathname;
    const searchParams = url.searchParams;

    // Parse different URL patterns
    if (pathname.includes("/paas/search")) {
      const contentType = searchParams.get("contentType");
      const id = searchParams.get("id");

      // Determine specific content type from search results
      if (contentType?.toLowerCase().includes('bulletin')) {
        return {
          type: "bulletin",
          id: id || undefined,
          contentType: contentType || undefined,
        };
      }
      if (contentType?.toLowerCase().includes('faq')) {
        return {
          type: "faq",
          id: id || undefined,
          contentType: contentType || undefined,
        };
      }
      if (contentType?.toLowerCase().includes('class guide')) {
        return {
          type: "class-guide",
          id: id || undefined,
          contentType: contentType || undefined,
        };
      }

      return {
        type: "search",
        id: id || undefined,
        contentType: contentType || undefined,
      };
    }

    if (pathname.includes("/paas/training-manual")) {
      const id = searchParams.get("id");
      const chapterId = searchParams.get("chapterid");

      return {
        type: "training-manual",
        id: id || undefined,
        chapterId: chapterId || undefined,
      };
    }

    if (pathname.includes("/paas/industryguide")) {
      const id = searchParams.get("id");
      const chapterId = searchParams.get("chapterid");

      return {
        type: "industry-guide",
        id: id || undefined,
        chapterId: chapterId || undefined,
      };
    }

    if (pathname.includes("/paas/class-guide")) {
      const id = searchParams.get("id");

      return {
        type: "class-guide",
        id: id || undefined,
      };
    }

    return { type: "unknown" };
  } catch (error) {
    console.error("Error parsing PAAS source URL:", error);
    return { type: "unknown" };
  }
}

/**
 * Determine document type from source data
 */
export function getDocumentTypeFromSource(sourceData: SourceDocumentData): string {
  const docType = sourceData.docType?.toLowerCase() || '';

  if (docType.includes('training manual')) {
    return 'training-manual';
  }

  if (docType.includes('industry guide')) {
    return 'industry-guide';
  }

  if (docType.includes('class guide')) {
    return 'class-guide';
  }

  if (docType.includes('faq')) {
    return 'faq';
  }

  if (docType.includes('bulletin')) {
    return 'bulletin';
  }

  return 'unknown';
}

/**
 * Generate document title based on source info
 */
export function generateDocumentTitle(
  originalTitle: string,
  sourceInfo: ReturnType<typeof parsePaasSourceInfo>
): string {
  // If we have a meaningful original title, use it
  if (originalTitle && originalTitle.trim() !== "") {
    return originalTitle;
  }

  // Otherwise, generate a title based on the source info
  switch (sourceInfo.type) {
    case "training-manual":
      return "Training Manual";
    case "industry-guide":
      return "Industry Guide";
    case "class-guide":
      return "Class Guide";
    case "faq":
      return "FAQ";
    case "bulletin":
      return sourceInfo.contentType || "Bulletin";
    case "search":
      if (sourceInfo.contentType?.toLowerCase().includes('bulletin')) {
        return "Bulletin";
      }
      if (sourceInfo.contentType?.toLowerCase().includes('faq')) {
        return "FAQ";
      }
      if (sourceInfo.contentType?.toLowerCase().includes('class guide')) {
        return "Class Guide";
      }
      return "Search Results";
    default:
      return "Document";
  }
}
