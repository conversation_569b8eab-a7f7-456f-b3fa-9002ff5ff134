import React, { JSX } from "react";

// Simple Field type definition for React Native (replacing Sitecore JSS)
export interface Field<T = string> {
  value: T;
}
export class AuthData {
  userId: string = "";
  accessToken: string = "";
  isAuth: boolean = false;
  username: string = "";
  hasAgreed: boolean = false;
  sitecore: SitecoreData = new SitecoreData();

  constructor(userId: string = "", accessToken: string = "", isAuth: boolean = false, 
    username: string = "") {
    this.userId = userId;
    this.accessToken = accessToken;
    this.isAuth = isAuth;
    this.username = username;
  }
}

export class SitecoreData {
  NewConversationText: scDatum = { value: '' };
  GreetingMessageText: scDatum = { value: '' };
  InputPlaceholderText: scDatum = { value: '' };
  ConversationStartersText: scDatum = { value: '' };
  BotNameText: scDatum = { value: '' };
  HowToUseText: scDatum = { value: '' };
  TermsOfUseText: scDatum = { value: '' };

  constructor() {
    this.NewConversationText = { value: '' };
    this.GreetingMessageText = { value: '' };
    this.InputPlaceholderText = { value: '' };
    this.ConversationStartersText = { value: '' };
    this.BotNameText = { value: '' };
    this.HowToUseText = { value: '' };
    this.TermsOfUseText = { value: '' };
  }
}

export class scDatum {
  value: string = '';
}

export class UserData {
  userId: string = "";
  conversations: Conversation[] = [];

  constructor() {
    this.userId = "";
    this.conversations = [
      {
        isActive: true,
        isDisabled: false,
        conversationId: "",
        title: "New Conversation",
        date: new Date(),
        messageHistory: [
          {
            botMsg: "",
            userMsg: "",
            time: new Date(),
            rating: 0,
            metadata: []
          }
        ]
      }
    ];
  }
}

export interface Conversation {
  isActive: boolean;
  isDisabled: boolean;
  conversationId: string;
  title: string;
  date: Date;
  messageHistory: Message[];
}

export interface Message {
    userMsg: string | JSX.Element;
    botMsg: string | JSX.Element | React.ReactNode;
    time: Date;
    rating: number;
    metadata: {
      source: Source[];
      title: string;
      docType: string;
      itemId: string;
      updateDate: Date;
    } [] | Source[];
  }

  export type Source = {
  section: string;
  docType: string;
  itemId: string;
  jurisdiction: string;
  lob: string;
  source: string;
  title: string;
  updateDate: string;
}

export interface ApiUserData {
  conversationId: string;
  name: string;
  time: string;
  disabled: boolean;

  content: {
    userMsg: string;
    summary: string;
    botMsg: string;
    source: Source[];
    time: string;
    rating: number;
    comment: string;
  }[];
}

export interface ChatResponse {
  answer: string;
  conversationId: string;
  isDisabled: boolean;
  metadata: {
    source: Source[];
    title: string;
    docType: string;
    itemId: string;
    updateDate: Date;
  } [];
}

export enum DateCategory {
  today = "Today",
  lastSevenDays = "Last 7 Days",
  lastThirtyDays = "Last 30 Days",
  older = "Older"
}

export enum EmitType {
  rename = "rename",
  delete = "delete",
  changeTab = "changeTab",
  rateUp = "rateUp",
  rateDown = "rateDown",
  message = "message",
  default = "default"
}

export class CopyButtonData {
  isActive: boolean = false;
  index: number = -1;

  constructor(isActive: boolean = false, index: number = -1) {
    this.isActive = isActive;
    this.index = index;
  }
}

export class EmitData {
  name: string = "";
  feedback: string = "";
  index: number = -1;
  isConfirmed: boolean = false;
  emitType: EmitType = EmitType.default;
  message: string = "";
  conversationId: string = "New Conversation"
  agreedToTerms?: boolean;

  constructor(name: string = '', feedback: string = '', index: number = -1,
    isConfirmed: boolean = false, emitType: EmitType = EmitType.default, 
    message:string = '', conversationId: string = 'New Conversation', agreedToTerms?: boolean) {
      this.name = name;
      this.feedback = feedback;
      this.index = index;
      this.isConfirmed = isConfirmed;
      this.emitType = emitType;
      this.message = message;
      this.conversationId = conversationId;
      this.agreedToTerms = agreedToTerms;
  }
}

export enum ModalType {
  Default = "Default",
  Disclaimer = "Disclaimer",
  HowToUse = "HowToUse",
  Rename = "Rename",
  Delete = "Delete",
  Feedback = "Feedback",
  TermsOfAgreement = "TermsOfAgreement",
}

export class ModalState {
  modalType: ModalType;
  isVisible: boolean;
  conversationName: string;

  constructor(modalType: ModalType = ModalType.Default, isVisible: boolean = false, conversationName: string = "") {
    this.modalType = modalType;
    this.isVisible = isVisible;
    this.conversationName = conversationName;
  }
}

export class ModalData {
  title: string;
  content: string;
  modalType: ModalType;
  requiresConfirmation: boolean = false;
  submitText: string;

  constructor(modalType: ModalType = ModalType.Default) {
    this.modalType = modalType;

    if (this.modalType === ModalType.Disclaimer) {
      this.title = "Disclaimers";
      this.content = '';
      this.requiresConfirmation = false;
      this.submitText = '';

    } else if (this.modalType === ModalType.TermsOfAgreement){
      this.title = "Terms of Agreement";
      this.content = '';
      this.requiresConfirmation = true;
      this.submitText = 'Agree';

    } else if (this.modalType === ModalType.HowToUse){
      this.title = "How to Use";
      this.content = '';
      this.requiresConfirmation = false;
      this.submitText = '';

    } else if (this.modalType === ModalType.Rename){
      this.title = "Rename Conversation";
      this.content = "";
      this.requiresConfirmation = true;
      this.submitText = "Rename";

    } else if (this.modalType === ModalType.Delete){
      this.title = "Delete Conversation";
      this.content = '';
      this.requiresConfirmation = true;
      this.submitText = "Delete";

    } else if (this.modalType === ModalType.Feedback){
      this.title = "Message Feedback";
      this.content = "Why did you choose this rating? (optional)";
      this.requiresConfirmation = true;
      this.submitText = "Submit";

    } else {
      this.title = "";
      this.content = "";
      this.requiresConfirmation = false;
      this.submitText = '';
    }
  }
}


// Training Manual API Response Interfaces
export interface TrainingManualSection {
  Heading: string;
  SectionText: string;
}

export interface TrainingManualReview {
  Question: string;
  Answer: string;
}

export interface TrainingManualChapter {
  SectionList: TrainingManualSection[];
  ReviewList: TrainingManualReview[];
  ItemID: string;
  ItemName: string;
  Title: string;
  ContentType: string;
}

export interface TrainingManualLob {
  Code: string;
  Name: string;
}

export interface TrainingManualResponse {
  StatusCode: number;
  StatusMessage: string;
  TrainingManualName: string;
  TrainingManualItemID: string;
  Lobs: TrainingManualLob[];
  TrainingManualChapter: TrainingManualChapter[];
}

// Industry Guide API Response Interfaces
export interface IndustryGuideSection {
  Heading: string;
  SectionText: string;
}

export interface IndustryGuideChapter {
  SectionList: IndustryGuideSection[];
  ItemID: string;
  ItemName: string;
  Title: string;
  ContentType: string;
}

export interface IndustryGuideLob {
  Code: string;
  Name: string;
}

export interface IndustryGuideResponse {
  StatusCode: number;
  StatusMessage: string;
  IndustryGuideName: string;
  Lobs: IndustryGuideLob[];
  IndustryGuideChapter: IndustryGuideChapter[];
}

// Class Guide API Response Interfaces
export interface ClassGuideJurisdiction {
  Code: string;
  Name: string;
}

export interface ClassGuideLob {
  Code: string;
  Name: string;
}

export interface ClassGuideStateException {
  ItemId: string;
  Title: string;
  ClassGuideType: string;
  ClassCode: string;
  Jurisdiction: ClassGuideJurisdiction[];
  Lob: ClassGuideLob[];
}

export interface ClassGuideMapping {
  ItemId: string;
  SicCode: string;
  NaicsCode: string;
  SicDescription: string;
  NaicsDescription: string;
}

export interface ClassGuideRelatedCode {
  ItemId: string;
  Title: string;
  ClassGuideType: string;
  ClassCode: string;
  Jurisdiction: ClassGuideJurisdiction[];
  Lob: ClassGuideLob[];
}

export interface ClassGuidePaasDocument {
  ItemId: string;
  Title: string;
  DocumentType: string;
  Lobs: ClassGuideLob[];
  Jurisdiction: ClassGuideJurisdiction[];
}

export interface ClassGuideResponse {
  PremiumBasis: string;
  WCStateException: ClassGuideStateException[];
  RiskSchedule: string;
  IndustrialGroup: string;
  WCToSICMapping: ClassGuideMapping[];
  RelatedWcCodes: ClassGuideRelatedCode[];
  GlCrossReference: any[];
  BpCrossReference: any[];
  ItemID: string;
  Jurisdiction: ClassGuideJurisdiction[];
  ClassCode: string;
  ItemName: string;
  Phraseology: string;
  Lobs: ClassGuideLob[];
  ContentType: string;
  ContentSubType: string;
  Analogies: string;
  ContemplatedOperations: string;
  OperationsNotContemplated?: string;
  Notes: string;
  History: string;
  Category: any[];
  StateExceptionDescription: string;
  RelatedGLCodes?: any[];
  AdditionalPhraseologies?: any[];
  GLStateException?: any[];
  GlToSICMapping?: any[];
  ExternalLinks?: any[];
  DateTimeUpdated: string;
  IsPAASApplicable: boolean;
  IsQC3Applicable: boolean;
  StateExclusion: ClassGuideJurisdiction[];
  PaasDocuments: ClassGuidePaasDocument[];
  StatusCode: number;
  StatusMessage: string;
}

// FAQ API Response Interfaces
export interface FaqLob {
  Code: string;
  Name: string;
}

export interface FaqClassificationLink {
  ItemId: string;
  Title: string;
  ClassGuideType: string;
  ClassCode: string;
  Jurisdiction: ClassGuideJurisdiction[];
  Lob: FaqLob[];
}

export interface FaqResponse {
  Lobs: FaqLob[];
  ReleaseYear: string;
  ReleaseMonth: string;
  Question: string;
  Answer: string;
  WCClassificationLink: FaqClassificationLink[];
  GLClassificationLink: FaqClassificationLink[];
  Resources: string;
  Title: string;
  StatusCode: number;
  StatusMessage: string;
}

// Source Document Data Interface (from user's sample data)
export interface SourceDocumentData {
  itemId: string;
  updateDate: string;
  parentItemId: string;
  docType: string;
  jurisdiction: string;
  section: string;
  source: string;
  title: string;
  lob: string;
}


export type dataType = {
  Code: string;
  ContentSubType: string;
  ContentType: string;
  ItemID: string;
  ItemName: string;
  Jurisdiction: {
    Code: string;
    Name: string;
  }[];
  Lobs: {
    Code: string;
    Name: string;
  }[];
  Notes: string;
  SubLobs: {
    Code: string;
    Name: string;
  }[];
  Title: string;
  contentNotes: string;
  jurisdictionArray: string[];
  lobsArray: string[];
  lobsNameArray: string[];
  ParentItemID: string;
};

export type paasContentType = {
  classGuideTabs?: object[];
  restProps?: restPropsType;
  currentTableItem?: dataType;
  faqTabs?: object[];
  searchTerm?: string;
  setCurrentTableItem?: any;
  setPaasSearchClicked?: any;
  BnBBulletinTabs?: object[];
  EducationalBulletinTabs?: object[];
  LegislativeBulletinTabs?: object[];
};


export type restPropsType = {
  BetaUsers?: Field<string>;
  BnBBulletinTabs: object[];
  ClearAllText: Field<string>;
  ClassGuideTabs: object[];
  DisplayResultText: Field<string>;
  EducationalBulletinTabs: object[];
  Facets: object[];
  FaqTabs: object[];
  LegislativeBulletinTabs: object[];
  NaicsHyperlink: Field<string>;
  ResultsCountOptions: object[];
  ResultsForText: Field<string>;
  ResultsPerPage?: object[];
  ResultsPerPageText?: Field<string>;
  ResultsText?: Field<string>;
  SearchText: Field<string>;
  SicHyperlink: Field<string>;
  SortByText: Field<string>;
};

export type paasTabsType = {
  displayName: string;
  fields: {
    Key: Field<string>;
    Phrase: Field<string>;
  };
  id: string;
  name: string;
};

export default {
  ModalData,
  ModalState,
  ModalType,
  EmitData,
  EmitType,
  DateCategory,
  UserData,
  AuthData,
  CopyButtonData,
};