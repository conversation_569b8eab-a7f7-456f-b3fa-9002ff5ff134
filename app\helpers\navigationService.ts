// Navigation service for handling hyperlink navigation and breadcrumbs

export interface BreadcrumbItem {
  id: string;
  title: string;
  contentType: string;
  classCode?: string;
  jurisdiction?: string;
  lob?: string;
}

export interface NavigationItem {
  ItemID?: string;
  ItemId?: string;
  ClassCode: string;
  Title: string;
  ContentType?: string;
  ClassGuideType?: string;
  Jurisdiction?: Array<{ Code: string; Name: string }>;
  Lobs?: Array<{ Code: string; Name: string }>;
  Lob?: Array<{ Code: string; Name: string }>;
  DocumentType?: string;
}

class NavigationService {
  private breadcrumbs: BreadcrumbItem[] = [];
  private listeners: Array<(breadcrumbs: BreadcrumbItem[]) => void> = [];

  // Add a breadcrumb item
  addBreadcrumb(item: BreadcrumbItem) {
    // Check if item already exists to avoid duplicates
    const existingIndex = this.breadcrumbs.findIndex(b => b.id === item.id);
    if (existingIndex >= 0) {
      // Remove all breadcrumbs after the existing one
      this.breadcrumbs = this.breadcrumbs.slice(0, existingIndex + 1);
    } else {
      this.breadcrumbs.push(item);
    }
    this.notifyListeners();
  }

  // Remove breadcrumbs from a specific index
  removeBreadcrumbsFrom(index: number) {
    this.breadcrumbs = this.breadcrumbs.slice(0, index + 1);
    this.notifyListeners();
  }

  // Get current breadcrumbs
  getBreadcrumbs(): BreadcrumbItem[] {
    return [...this.breadcrumbs];
  }

  // Clear all breadcrumbs
  clearBreadcrumbs() {
    this.breadcrumbs = [];
    this.notifyListeners();
  }

  // Subscribe to breadcrumb changes
  subscribe(listener: (breadcrumbs: BreadcrumbItem[]) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.breadcrumbs));
  }

  // Helper to truncate long titles
  truncateTitle(title: string, maxLength: number = 30): string {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength - 3) + '...';
  }

  // Convert navigation item to breadcrumb
  createBreadcrumbFromNavigationItem(item: NavigationItem): BreadcrumbItem {
    const id = item.ItemID || item.ItemId || '';
    const contentType = item.ContentType || item.ClassGuideType || item.DocumentType || '';
    const jurisdiction = item.Jurisdiction?.map(j => j.Code).join(',') || '';
    const lob = (item.Lobs || item.Lob)?.map(l => l.Code).join(',') || '';

    return {
      id: id.replace(/[{()}]/g, ''), // Remove braces from GUID
      title: this.truncateTitle(item.Title),
      contentType: contentType.replace(/s$/, ''), // Remove trailing 's' if present
      classCode: item.ClassCode,
      jurisdiction,
      lob
    };
  }

  // Generate navigation path for different content types
  getNavigationPath(item: NavigationItem): any {
    const id = (item.ItemID || item.ItemId || '').replace(/[{()}]/g, '');
    const title = item.Title || '';
    const contentType = item.ContentType || item.ClassGuideType || item.DocumentType || '';

    // Create proper source data for the navigation
    const sourceData = {
      itemId: id,
      title: title,
      docType: contentType,
      jurisdiction: item.Jurisdiction?.map(j => j.Code).join(',') || '',
      lob: (item.Lobs || item.Lob)?.map(l => l.Code).join(',') || ''
    };

    return {
      pathname: '/paas-source/[id]',
      params: {
        id: id,
        title: title,
        sourceData: JSON.stringify(sourceData)
      }
    };
  }
}

// Export singleton instance
export const navigationService = new NavigationService();
