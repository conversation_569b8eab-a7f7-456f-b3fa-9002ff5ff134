import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    flexDirection: 'column',
  },
  scrollview: {
    flex: 1,
    height: '100%',
  },
  topToolbar: {
    height: '5.5%',
    width: '100%',
  },
  header: {
    flexDirection: 'column',
  },
  banner: {
    backgroundColor: '#00358E',
    justifyContent: 'center',
    flexWrap: 'nowrap',
    paddingTop: 5,
    paddingBottom: 5,
  },
  content: {
    height: '60%',
  },
  contentArea: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    paddingBottom: 20,
  },
  footer: {
    height: '22%',
    backgroundColor: '#00358E',
    flexDirection: 'column',
  },
  bottomToolbar: {
    backgroundColor: '#00358E',
    height: '3%',
    width: '100%',
  }
});

export const topStyles = StyleSheet.create({
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'nowrap',
    alignItems: 'center',
    alignContent: 'center',
    paddingLeft: 10,
    paddingRight: 10,
  },
  leftTop: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 8,
    alignContent: 'center',
    alignItems: 'center',
    width: '25%',
  },
  centerTop: {
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    width: '50%',
    marginRight: 20 //TODO
  },
  rightTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '15%',
    height: 42,
  },
  profileBackdrop: {
    width: '100%',
    height: '100%',
  },
  userProfileContainer: {
    width: 110,
    height: 40,
    backgroundColor: 'white',
    position: 'absolute',
    right: "0%",
    top: '11%',
    zIndex: 2,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    paddingRight: 5,
    paddingLeft: 5,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    borderWidth: 1,
  },
  userProfileRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 3
  },
  userProfile: {
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 16,
    color: 'rgba(0, 0, 0, 0.80)',
    paddingTop: 4,
  },
  text: {
    fontSize: 16,
    color: '#00358E',
    fontFamily: 'Roboto',
    fontWeight: '400',
    lineHeight: 24
  },
  topBannerText: {
    color: 'white',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '500',
    lineHeight: 36,
    flexShrink: 1,
    fontSize: 21
  }
});

export const bottomStyles = StyleSheet.create({
  bottomLogoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
    paddingTop: 20,
    paddingBottom: 10,

  },
  bottomCopyrightText: {
    width: '100%',
    color: '#FFF',
    justifyContent: 'center',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 20,
    paddingTop: 5,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
    flexWrap: 'wrap',
  },
  bottomLinkText: {
    width: '100%',
    color: '#FFF',
    textAlign: 'center',
    fontFamily: 'Roboto',
    fontSize: 12,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 20,
    paddingBottom: 1,
  },
  bottomDisclaimerText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '500',
    lineHeight: 18,
    alignItems: 'center',
    textAlign: 'center',
    paddingLeft: 15,
    paddingRight: 15,
    paddingTop: 10,
    paddingBottom: 10,
  }
});

export const msgStyles = StyleSheet.create({
  message: {
    fontFamily: 'Roboto',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 24,
    color: 'black',
    marginBottom: 10
  },
  list: {
    fontFamily: 'Roboto',
    fontSize: 14,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 24,
    color: 'black',
    marginBottom: 5,
    marginLeft: 5
  },
  messageContainer: {
    marginVertical: 5,
  },
  greeting: {
    fontStyle: 'italic',
  },
  sourcesContainer: {
    marginTop: 10,
  },
  sourcesLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#1A1A1A',
  },
  sourceLink: {
    marginVertical: 2,
  },
  sourceLinkText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#00358E',
  },
});

export const convStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  conversation: {
    flex: 1,
    overflow: 'hidden',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: '10%',
  },
  newConversationButton: {
    fontFamily: 'Roboto',
    fontSize: 16,
    backgroundColor: '#FFC600',
    width: 260,
    padding: 12,
    borderWidth: 1,
    borderColor: '#FFC600',
    borderRadius: 4,
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    alignContent: 'center',
    justifyContent: 'center',

  },
  addIcon: {
    height: 18,
    width: 18,
  },
  inputRow: {
    justifyContent: 'center',
    alignContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    marginBottom: 15,
  },
  inputContainer: {
    width: '90%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#00358E',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  input: {
    width: '85%',
    maxHeight: 60, // Dynamic stretching for multiline
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 19,
    borderRadius: 8,
    paddingLeft: 15,
    paddingTop: 10,
    paddingBottom: 10,
    paddingRight: 15,
  },
  submitDisabled: {
    backgroundColor: 'yellow',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputButtonsContainer: {
    width: '10%',
    flexDirection: 'row',
    // justifyContent: 'space-between', TODO: Microphone
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  submitButton: {

  },
  divider: {

  },
  microphoneButton: {

  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-evenly',
    borderTopWidth: 1,
    borderTopColor: 'rgba(140, 140, 140, 0.25)',
    paddingLeft: 10,
    paddingRight: 10,
    marginLeft: 30,
    marginRight: 30,
  },
  buttonText: {
    color: '#00358E',
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '400',
    lineHeight: 24,
  },
  submitIcon: {
    width: 24,
    height: 24,
  }
});

export const messageStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  message: {
    marginTop: 15,
    marginLeft: 12,
    marginRight: 30,
  },
  text: {
    fontFamily: 'Roboto',
    fontSize: 14,
    lineHeight: 24,
    fontStyle: 'normal',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 8,
  },
  headerText: {
    fontFamily: 'Roboto-Bold',
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 24,
    fontStyle: 'normal',
  },
  greeting: {
    fontFamily: 'Roboto',
    fontSize: 20,
    lineHeight: 24,
    fontStyle: 'normal',
    marginTop: 10,
    marginBottom: 15,
  },
  content: {
    marginLeft: 32,
  },
  sourcesHeader: {
    fontFamily: 'Roboto-Bold',
    fontSize: 14,
    lineHeight: 24,
    fontStyle: 'normal',
    marginTop: 15,

  },
  sources: {
    marginLeft: 32,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 10,
    marginLeft: 32,
    gap: 3,
  },
  greeetingsContent: {
    marginLeft: 32,
    marginBottom: 50,
  },
  conversationStartersDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  conversationStartersList: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
});

export const popupStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  backdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    overflow: 'hidden',
    minHeight: '30%',
    maxHeight: '55%',
    marginLeft: 1,
    marginRight: 1,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    fontFamily: 'Roboto',
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3.84,
    elevation: 4,
  },
  header: {
    minHeight: 60,
    maxHeight: 60,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.2)',
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 0,
    paddingBottom: 0,
  },
  title: {
    height: 30,
    width: '90%',
    color: "#1A1A1A",
    fontFamily: 'Roboto',
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: 500,
    lineHeight: 30,
  },
  close: {
    width: '10%',
    height: 20,
  },
  bodyContainer: {
    flex: 1,
    minHeight: 140,
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 15,
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
  body: {
    overflow: 'hidden',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',

  },
  bodyDisclaimer: {
    overflow: 'hidden',
    alignContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  bodyFeedback: {
    paddingTop: 10,
    paddingBottom: 10,
    justifyContent: 'center',
    marginLeft: 20,
    marginRight: 20,
  },
  feedbackRadio: {
    marginTop: 7,
    marginBottom: 7,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  inputContainer: {
    width: '80%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: 'rgba(0, 0, 0, 0.8)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 35,
  },
  feedbackInput: {
    height: 30,
    width: '80%',
    alignContent: 'center',
    justifyContent: 'flex-start',
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 18,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 10,
  },
  rename: {
    flex: 1,
    paddingTop: 10,
    paddingBottom: 10,
    justifyContent: 'center',
    alignItems: 'center',

  },
  renameContainer: {
    width: '90%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: 'rgba(0, 0, 0, 0.8)',
  },
  input: {
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    minHeight: 30,
    maxHeight: 60,
    lineHeight: 18,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 10,
  },
  footer: {
    height: 60,
    gap: 10,
    backgroundColor: "#FFFFFF",
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.2)',
    alignItems: 'center',
    paddingRight: 20,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
  },
  cancelButton: {
    height: 30,
    backgroundColor: '#FFC600',
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  cancelText: {
    fontFamily: 'Roboto-Bold',
    color: 'black',
    fontSize: 12
  },
  confirmButton: {
    height: 30,
    backgroundColor: '#e80238',
    borderRadius: 5,
    paddingVertical: 6,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  confirmText: {
    fontFamily: 'Roboto-Bold',
    color: 'white',
    fontSize: 12
  },
  disabledButton: {
    height: 30,
    backgroundColor: 'rgba(156, 25, 55, 0.1)',
  },
  disabledText: {
    fontFamily: 'Roboto-Bold',
    color: 'rgba(0, 0, 0, 0.2)',
    fontSize: 12
  },
  // Terms of Agreement specific styles
  termsHeader: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderColor: '#eee',
    justifyContent: 'flex-start',
    alignItems: 'center',
    minHeight: 60,
  },
  termsTitle: {
    fontWeight: 'bold',
    fontSize: 20,
    color: '#222',
  },
  termsContent: {
    paddingHorizontal: 24,
    paddingVertical: 2,
    flex: 1,
    minHeight: 200,
  },
  termsFooter: {
    marginTop: 0,
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    backgroundColor: '#fff',
  },
  termsCheckboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  termsCheckbox: {
    marginRight: 10,
  },
  termsCheckboxInner: {
    width: 18,
    height: 18,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#888',
    alignItems: 'center',
    justifyContent: 'center',
  },
  termsCheckboxText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  termsAgreementText: {
    fontSize: 15,
    color: '#222',
  },
  termsContinueButton: {
    minWidth: 110,
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 6,
    alignItems: 'center',
  },
  termsContinueText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  termsOrDisclaimerContainer: {
    maxWidth: 800,
    width: '100%',
    alignSelf: 'center',
    borderRadius: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
    overflow: 'visible',
  },
});

export const sidebarStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: '20%',
    bottom: 0,
    zIndex: 2,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: {
      width: 1,
      height: 5,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3.84,
    elevation: 5,
  },
  topBoundary: {
    height: '6.5%'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'nowrap',
    paddingTop: 5,
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 5,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(140, 140, 140, 0.15)',
    height: 36
  },
  searchRow: {
    paddingLeft: 10,
    paddingRight: 10,
    flexDirection: 'row',
    gap: 5,
    marginTop: 5,
    marginBottom: 5,
  },
  inputContainer: {
    width: '85%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#00358E',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  searchBar: {
    fontSize: 16,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 18,
    paddingTop: 10,
    paddingBottom: 8,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 10,
  },
  searchButton: {
    width: '15%',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
    backgroundColor: '#FFC600',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#FFC600',
  },
  content: {
    flex: 1,
    overflow: 'scroll',
  },
  category: {
    height: 44,
    backgroundColor: '#F4F4F4',
    width: '100%',
    justifyContent: 'center',
    paddingLeft: 12,
    fontSize: 14,
    fontFamily: 'Roboto-Bold',
    fontStyle: 'normal',
    lineHeight: 24,
    color: '#1A1A1A',
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 44,
    paddingLeft: 18,
    paddingRight: 5,
    overflow: 'hidden',
  },
  titleClick: {
    width: '90%',
    height: '100%',
    alignItems: 'center',
    flexDirection: 'row',
  },
  title: {
    fontSize: 14,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: 300,
    lineHeight: 15,
    color: '#1A1A1A',
  },
  activeTitle: {
    fontSize: 15,
    fontFamily: 'Roboto-Bold',
    fontStyle: 'normal',
    lineHeight: 14,
    color: '#1A1A1A',
  },
  options: {

  },
  optionsIcon: {
    width: 22,
    height: 22,
  },
  footer: {
    height: 44,
    flexDirection: 'row',
    flexWrap: 'nowrap',
    borderTopWidth: 1,
    borderTopColor: 'rgba(140, 140, 140, 0.15)',
  },
  footerButton: {
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerButtonRight: {
    width: '50%',
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 0.8,
    borderLeftColor: 'rgba(140, 140, 140, 0.15)',
  },
  buttonText: {
    color: '#00358E',
    fontSize: 12,
    fontFamily: 'Roboto',
    fontWeight: '400',
    lineHeight: 24,
  },
  bottomBoundary: {
    height: '1%'
  }
});

export const optionsStyles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  container: {
    flex: 1,
  },
  containerInner: {
    flex: 1,
    height: '100%',
    top: '0%',
    marginRight: '20.3%',
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
  content: {
    marginTop: 125,
    left: 1,
    backgroundColor: 'white',
    width: 130,
    height: 75,
    shadowColor: "#000",
    shadowOffset: {
      width: -1,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    justifyContent: 'space-between',
    flexDirection: 'column',
    borderWidth: 0.1,
    borderColor: 'rgba(140, 140, 140, 0.25)',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    paddingLeft: 15,
    borderWidth: 0.1,
    borderColor: 'rgba(140, 140, 140, 0.1)',
  },
  text: {
    fontSize: 14,
    fontFamily: 'Roboto',
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 24,
    color: 'black',
    marginTop: 2
  }
});

// PAAS Source Screen Styles
export const paasSourceStyles = StyleSheet.create({
  // Main container styles
  contentContainer: {
    flex: 1,
  },
  content: {
    padding: 20,
  },

  // Breadcrumb styles
  breadcrumbContainer: {
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  breadcrumbScrollView: {
    flexGrow: 0,
  },
  breadcrumbContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  breadcrumbItem: {
    marginRight: 4,
  },
  breadcrumbLink: {
    color: '#007AFF',
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  breadcrumbSeparator: {
    color: '#666',
    fontSize: 14,
    marginHorizontal: 4,
  },
  breadcrumbCurrent: {
    color: '#333',
    fontSize: 14,
    fontWeight: '500',
  },
  breadcrumbText: {
    fontSize: 14,
    color: "#00358E",
    fontWeight: "500",
  },

  // Title and section styles
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#212529",
    marginBottom: 16,
    lineHeight: 28,
    fontFamily: 'Roboto',
  },

  // Content section styles
  contentSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#212529",
    marginBottom: 12,
  },
  sectionText: {
    fontSize: 14,
    color: "#495057",
    lineHeight: 20,
    marginBottom: 12,
  },

  // Header with back button styles
  headerWithBack: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#00358E',
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    color: 'white',
    fontSize: 18,
    fontWeight: '500',
    fontFamily: 'Roboto',
    textAlign: 'center',
    marginRight: 40, // Compensate for back button width
  },

  // LOB section styles
  lobSection: {
    marginBottom: 20,
  },
  lobLabel: {
    fontSize: 14,
    color: "#495057",
    marginBottom: 4,
  },
  lobValue: {
    fontWeight: "600",
    color: "#212529",
  },

  // Tab styles
  tabContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#dee2e6",
    marginBottom: 10,
    flexGrow: 0,
    flexShrink: 0,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 20,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#00358E",
  },
  tabText: {
    fontSize: 14,
    color: "#6c757d",
    fontWeight: "500",
  },
  activeTabText: {
    color: "#00358E",
    fontWeight: "600",
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },

  // Link styles
  linkButton: {
    paddingVertical: 4,
    paddingHorizontal: 0,
    marginVertical: 1,
    backgroundColor: 'transparent',
    alignSelf: 'flex-start',
  },
  linkText: {
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 18,
    textDecorationLine: 'underline',
    fontFamily: 'System',
  },
  linkList: {
    marginTop: 4,
    marginBottom: 12,
    paddingLeft: 0,
  },
  linkListItem: {
    marginBottom: 2,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  linkBullet: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
    lineHeight: 18,
  },

  // Scroll content
  scrollContent: {
    flex: 1,
    paddingBottom: 20,
  },
});

// HTML Content Styles for PAAS documents
export const htmlContentStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
  },
  content: {
    padding: 0,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeading: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#00358E',
    marginBottom: 12,
    marginTop: 8,
    lineHeight: 22,
    fontFamily: 'Roboto',
  },
  sectionContent: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },

  // Scrollable container for overflowing content
  scrollableContainer: {
    overflow: 'hidden',
  },

  // HTML Text Styles
  htmlParagraph: {
    fontSize: 14,
    color: '#495057',
    lineHeight: 20,
    marginBottom: 8,
    textAlign: 'left',
  },
  htmlList: {
    marginBottom: 8,
    paddingLeft: 16,
  },
  htmlListItem: {
    fontSize: 14,
    color: '#495057',
    lineHeight: 20,
    marginBottom: 4,
  },
  htmlStrong: {
    fontWeight: 'bold',
    color: '#212529',
    // backgroundColor: '#dfdfdfba',
    // paddingHorizontal: 8,
    // paddingVertical: 8,
  },
  htmlEmphasis: {
    fontStyle: 'italic',
    color: '#495057',
  },

  // HTML Headings
  htmlHeading1: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 8,
    marginTop: 16,
    lineHeight: 22,
    fontFamily: 'Roboto',
  },
  htmlHeading2: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 8,
    marginTop: 14,
    lineHeight: 20,
    fontFamily: 'Roboto',
  },
  htmlHeading3: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 6,
    marginTop: 12,
    lineHeight: 18,
    fontFamily: 'Roboto',
  },
  htmlHeading4: {
    fontSize: 15,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 8,
    marginTop: 12,
    lineHeight: 20,
  },
  htmlHeading5: {
    fontSize: 14,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 6,
    marginTop: 10,
    lineHeight: 18,
  },
  htmlHeading6: {
    fontSize: 13,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 6,
    marginTop: 8,
    lineHeight: 16,
  },

  // HTML Table Styles with horizontal scroll
  htmlTableContainer: {
    marginBottom: 16,
    marginTop: 8,
    maxWidth: '100%',
  },
  htmlTableScrollContainer: {
    maxWidth: '100%',
  },

  htmlTable: {
    borderWidth: 1,
    borderColor: '#dee2e6',
    borderRadius: 4,
    backgroundColor: '#ffffff',
    marginBottom: 16,
    marginTop: 8,
    minWidth: '100%',
  },
  htmlTableHead: {
    // backgroundColor: '#dfdfdfba',
  },
  htmlTableBody: {
    backgroundColor: '#ffffff',
  },
  htmlTableRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    minHeight: 36,
  },
  htmlTableHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#212529',
    paddingHorizontal: 8,
    paddingVertical: 8,
    // backgroundColor: '#dfdfdfba',
    borderRightWidth: 1,
    borderRightColor: '#dee2e6',
    textAlign: 'left',
    minWidth: 100,
  },
  htmlTableCell: {
    fontSize: 14,
    color: '#495057',
    // paddingHorizontal: 8,
    // paddingVertical: 8,
    borderRightWidth: 1,
    borderRightColor: '#dee2e6',
    lineHeight: 18,
    textAlign: 'left',
    minWidth: 100,
  },

  // HTML Media and Links
  htmlImageContainer: {
    marginBottom: 12,
    marginTop: 8,
    maxWidth: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 4,
    padding: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  htmlImage: {
    borderRadius: 4,
    maxWidth: '100%',
    height: 'auto',
    backgroundColor: '#ffffff',
  },
  htmlLink: {
    color: '#00358E',
    textDecorationLine: 'underline',
  },

  // HTML Code and Quotes
  htmlBlockquote: {
    borderLeftWidth: 4,
    borderLeftColor: '#00358E',
    backgroundColor: '#f8f9fa',
    padding: 12,
    marginBottom: 12,
    marginTop: 8,
    fontStyle: 'italic',
  },
  htmlCode: {
    fontFamily: 'monospace',
    backgroundColor: '#f8f9fa',
    padding: 2,
    borderRadius: 2,
    fontSize: 13,
    color: '#e83e8c',
  },
  htmlPre: {
    fontFamily: 'monospace',
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 4,
    marginBottom: 12,
    marginTop: 8,
    fontSize: 13,
    color: '#212529',
    overflow: 'hidden',
  },

  // HTML Generic Containers
  htmlDiv: {
    marginBottom: 4,
  },
  htmlSpan: {
    fontSize: 14,
    color: '#495057',
  },

  // Review section styles for Training Manuals
  reviewContainer: {
    marginTop: 32,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  reviewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#00358E',
    marginBottom: 16,
    textAlign: 'center',
  },
  reviewItem: {
    marginBottom: 20,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  questionContainer: {
    marginBottom: 12,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 8,
  },
  answerContainer: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  answerLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 8,
  },

  // Class Guide specific styles
  headerContainer: {
    marginBottom: 24,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#00358E',
    marginBottom: 12,
    lineHeight: 24,
    fontFamily: 'Roboto',
  },
  detailsContainer: {
    marginTop: 8,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 6,
    flexWrap: 'wrap',
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginRight: 8,
    minWidth: 120,
  },
  detailValue: {
    fontSize: 14,
    color: '#212529',
    flex: 1,
  },

  // Mapping section styles
  mappingItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#ffffff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  mappingCode: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00358E',
    marginBottom: 4,
  },
  mappingDescription: {
    fontSize: 13,
    color: '#495057',
    marginBottom: 2,
    lineHeight: 18,
  },

  // Related codes styles
  relatedCodeItem: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#ffffff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  relatedCodeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00358E',
    marginBottom: 4,
  },
  relatedCodeJurisdiction: {
    fontSize: 13,
    color: '#495057',
  },

  // Exception styles
  exceptionItem: {
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#fff3cd',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  exceptionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 4,
  },
  exceptionJurisdiction: {
    fontSize: 13,
    color: '#856404',
  },

  // FAQ specific styles
  linkSection: {
    marginBottom: 16,
  },
  linkSectionTitle: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#00358E',
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
    paddingBottom: 4,
  },
  linkItem: {
    marginBottom: 10,
    padding: 10,
    backgroundColor: '#ffffff',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  linkTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00358E',
    marginBottom: 3,
  },
  linkJurisdiction: {
    fontSize: 13,
    color: '#495057',
  },

  // Image container for better image handling
  imageContainer: {
    marginBottom: 12,
    marginTop: 8,
    alignItems: 'center',
  },
});

// Error display styles
export const errorDisplayStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa',
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#dc3545',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#495057',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  retryButton: {
    backgroundColor: '#00358E',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

// Loading spinner styles
export const loadingSpinnerStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginTop: 16,
    fontSize: 16,
    color: '#495057',
    textAlign: 'center',
  },
});

export const bulletinsStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },

  // Header styles
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#212529",
    marginBottom: 16,
  },
  details: {
    marginTop: 8,
  },

  // Tab styles
  tabContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#dee2e6",
    marginBottom: 10,
    flexGrow: 0,
    flexShrink: 0,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 20,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#00358E",
  },
  tabText: {
    fontSize: 14,
    color: "#6c757d",
    fontWeight: "500",
  },
  activeTabText: {
    color: "#00358E",
    fontWeight: "600",
  },

  // Content styles
  contentContainer: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
  },

  // BulletinsContent specific styles - OPTIMIZED FOR PERFORMANCE
  contentWrapper: {
    flex: 1,
    // Remove any unnecessary flex calculations
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },

  // See More feature styles - OPTIMIZED
  contentOverflowContainer: {
    position: 'relative',
    flex: 1,
    minHeight: 200, // Ensure minimum height for overlay positioning
  },
  expandedContent: {
    // Remove height constraints, let content flow naturally
  },
  // Performance optimized content container - always renders full content
  performanceOptimizedContent: {
    // Always render full content, use CSS to control visibility
  },
  // Consistent height container for all tabs
  consistentHeightContainer: {
    minHeight: 600, // Reduced minimum height to match collapsed content better
  },
  seeMoreContainer: {
    alignItems: 'flex-start', // Changed from 'center' to 'flex-start' for left alignment
    paddingVertical: 16,
    paddingHorizontal: 0, // Removed left padding to align with content
  },
  seeMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start', // Changed from 'center' to 'flex-start' for left alignment
    paddingVertical: 8,
    paddingHorizontal: 0, // Reduced horizontal padding since we're left-aligned
  },
  seeMoreText: {
    fontSize: 14,
    color: '#00358E',
    fontWeight: '500',
    marginRight: 4,
  },
  seeMoreIcon: {
    fontSize: 12,
    color: '#00358E',
    marginLeft: 4,
  },
  separatorLine: {
    height: 1,
    backgroundColor: '#e9ecef',
    marginTop: 8,
    marginBottom: 8,
  },

  // Section styles
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 12,
  },
  bold: {
    fontWeight: 'bold',
  },

  // Links container styles
  linksContainer: {

  },

  // List item styles
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingLeft: 4,
  },

  // Bullet point styles
  bulletPoint: {
    fontSize: 16,
    color: '#6c757d',
    marginRight: 8,
    marginTop: 2,
    fontWeight: 'bold',
  },

  // Link styles
  linkWrapper: {
    flex: 1,
  },
  linkText: {
    fontSize: 14,
    color: '#007AFF',
    lineHeight: 20,
    // Removed textDecorationLine: 'underline'
  },

  // Placeholder styles
  placeholder: {
    padding: 16,
    textAlign: 'center',
    color: '#666',
  },
});

// HTML content styles for BulletinsContent (separate from StyleSheet)
export const bulletinsHtmlTags = {
  p: {
    fontSize: 14,
    lineHeight: 20,
    color: '#212529',
    marginBottom: 8,
  },
  strong: {
    fontWeight: 'bold' as const,
  },
  b: {
    fontWeight: 'bold' as const,
  },
  u: {
    textDecorationLine: 'underline' as const,
  },
  em: {
    fontStyle: 'italic' as const,
  },
  i: {
    fontStyle: 'italic' as const,
  },
  li: {
    fontSize: 14,
    lineHeight: 20,
    color: '#212529',
    marginBottom: 4,
  },
  ul: {
    marginBottom: 8,
  },
  ol: {
    marginBottom: 8,
  },
  table: {
    borderWidth: 1,
    borderColor: '#dee2e6',
    marginBottom: 8,
  },
  tr: {
    borderBottomWidth: 1,
    borderBottomColor: '#dee2e6',
  },
  td: {
    padding: 8,
    fontSize: 14,
    color: '#212529',
  },
  th: {
    padding: 8,
    fontSize: 14,
    fontWeight: 'bold' as const,
    color: '#212529',
    backgroundColor: '#f8f9fa',
  },
  h1: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    color: '#212529',
    marginBottom: 12,
  },
  h2: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    color: '#212529',
    marginBottom: 10,
  },
  h3: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    color: '#212529',
    marginBottom: 8,
  },
  h4: {
    fontSize: 16,
    fontWeight: 'bold' as const,
    color: '#212529',
    marginBottom: 6,
  },
  h5: {
    fontSize: 14,
    fontWeight: 'bold' as const,
    color: '#212529',
    marginBottom: 4,
  },
  h6: {
    fontSize: 12,
    fontWeight: 'bold' as const,
    color: '#212529',
    marginBottom: 4,
  },
  div: {
    fontSize: 14,
    lineHeight: 20,
    color: '#212529',
  },
  span: {
    fontSize: 14,
    lineHeight: 20,
    color: '#212529',
  },
};

// Default export
export default {
  styles,
  topStyles,
  bottomStyles,
  msgStyles,
  convStyles,
  messageStyles,
  popupStyles,
  sidebarStyles,
  optionsStyles,
  paasSourceStyles,
  htmlContentStyles,
  bulletinsStyles
};