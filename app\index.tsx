import React, { useEffect, useState } from "react";
import { View, StyleSheet } from "react-native";
import { Chatbox } from "./components/chatbox";
import { useAuth } from "./helpers/authContext";
import Authentication from "./components/authentication";
import { SitecoreData } from "./helpers/model";

export default function Index() {
  const { authData, setAuthData } = useAuth();
  const [displayApp, setDisplayApp] = useState(true);
  const hardCode = true;

  // Once user is authenticated, fetch sitecore data & check agreement history
  useEffect(() => {
    if (hardCode) {
      const newAuthData = {
        userId: "I29246",
        accessToken: 'eyJraWQiOiJiYi05WVM2cWZlN1haV2NLaFZTWUJTLW5YMXZsZElOTWcyVVlDczNUQ3M0IiwiYWxnIjoiUlMyNTYifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.He5rDJhrCcVWpylbnMwJ0z43dKDAILseBwbcYWG6PDL4D0NB7Os7qCrvNeM2CqEZ-wKHEIzpkjsQDzgeooOuyPyIthwOreabUPpIgjoo9ltBX9AroDnCmlv6oFlmKYkeTJJnFdzN4M_sNNbIHoDNhNr-LsVxlMeAR7GFt-2PyzoRDMHr6_BOhxuLlUGTnRkNa7jynXaUTWwQ9L1MrALczezSBvAiS5jVAWxLsG4XvAP0hnPZLvkxc8v30tI1s-ooOSWBvw6a-vV7PfsnMv6gfnHKB2lv9CLbR0IVofmY1KgUT2k_RN5lQGZccxSzkTNjYddlQI9yTYgmaKjEZTd2Hg',
        isAuth: true,
        username: "Shelby Kurz",
        hasAgreed: false,
        sitecore: new SitecoreData(),
      };
      setAuthData(newAuthData);
    }
  }, [hardCode]);

  return (
    <View style={styles.container}>
      {authData.isAuth === true || hardCode ? <Chatbox /> : <Authentication />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5FCFF",
  },
});
