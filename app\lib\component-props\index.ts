// Simple type definitions for React Native (replacing Sitecore JSS)
export interface ComponentParams {
  [key: string]: any;
}

export interface ComponentRendering {
  componentName: string;
  params?: ComponentParams;
  fields?: { [key: string]: any };
}

export interface SitecoreContextValue {
  [key: string]: any;
}

/**
 * Shared component props
 */
export type ComponentProps = {
  rendering: ComponentRendering;
  params: ComponentParams;
};

/**
 * Component props with context
 * You can access `sitecoreContext` by withSitecoreContext/useSitecoreContext
 * @example withSitecoreContext()(ContentBlock)
 * @example const { sitecoreContext } = useSitecoreContext()
 */
export type ComponentWithContextProps = ComponentProps & {
  sitecoreContext: SitecoreContextValue;
};
