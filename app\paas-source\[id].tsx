import React, { useState, useEffect, useMemo } from "react";
import { View, Text, ScrollView, Pressable, Dimensions } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { TrainingManualContent } from "../components/TrainingManuals/TrainingManualContent";
import { IndustryGuideContent } from "../components/IndustryGuides/IndustryGuideContent";
import ClassGuideContent from "../components/ClassGuides/ClassGuideContent";
import FaqContent from "../components/FAQ/FaqContent";
import BulletinContent from "../components/Bulletins/BulletinContent";
import { AppLayout } from "../components/AppLayout";
import { LoadingSpinner } from "../components/LoadingSpinner";
import { ErrorDisplay } from "../components/ErrorDisplay";

import {
  generatePaasBreadcrumbs,
  parsePaasSourceInfo,
  generateDocumentTitle,
  getDocumentTypeFromSource,
} from "../helpers/breadcrumbUtils";
import { navigationService } from "../helpers/navigationService";
import BreadcrumbNavigation from "../components/common/BreadcrumbNavigation";

import {
  getTrainingManual,
  getIndustryGuide,
  getClassGuide,
  getBulletin,
  getFaq,
} from "../helpers/service";
import { useAuth } from "../helpers/authContext";
import { paasSourceStyles } from "../helpers/stylesheet";
import {
  TrainingManualResponse,
  IndustryGuideResponse,
  TrainingManualChapter,
  IndustryGuideChapter,
  ClassGuideResponse,
  FaqResponse,
  SourceDocumentData,
} from "../helpers/model";

// Types for unified content data
type ContentData =
  | {
      type: "training-manual";
      data: TrainingManualResponse;
      chapter?: TrainingManualChapter;
    }
  | {
      type: "industry-guide";
      data: IndustryGuideResponse;
      chapter?: IndustryGuideChapter;
    }
  | {
      type: "class-guide";
      data: ClassGuideResponse;
      contentType?: string;
    }
  | {
      type: "faq";
      data: FaqResponse;
      contentType?: string;
    }
  | {
      type: "bulletin";
      data: any;
      contentType?: string;
    }
  | null;

export default function PaasSourceScreen() {
  const { id, title, url, sourceData } = useLocalSearchParams<{
    id: string;
    title: string;
    url: string;
    sourceData?: string;
  }>();

  const router = useRouter();
  const { authData } = useAuth();

  // Unified state management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [contentData, setContentData] = useState<ContentData>(null);

  // Handler functions
  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace("/");
    }
  };

  const handleNewConversation = () => {
    router.replace("/");
  };

  // Parse source information for dynamic content
  const sourceInfo = parsePaasSourceInfo(url || "");

  // Memoize parsedSourceData to prevent infinite re-renders
  const parsedSourceData = useMemo<SourceDocumentData | null>(() => {
    if (sourceData) {
      try {
        return JSON.parse(sourceData);
      } catch (e) {
        return null;
      }
    }
    return null;
  }, [sourceData]);

  const documentTitle = generateDocumentTitle(title || "", sourceInfo);
  const breadcrumbs = generatePaasBreadcrumbs(
    documentTitle,
    router,
    url || "",
    documentTitle
  );

  // Create unified source data from either parsedSourceData or URL
  const unifiedSourceData = useMemo(() => {
    if (parsedSourceData) {
      return {
        docType: getDocumentTypeFromSource(parsedSourceData),
        itemId: parsedSourceData.itemId,
        parentItemId: parsedSourceData.parentItemId,
        contentType:
          parsedSourceData.docType ||
          getDocumentTypeFromSource(parsedSourceData),
      };
    } else if (url) {
      const queryString = url.split("?")[1];
      if (queryString) {
        const params = new URLSearchParams(queryString);
        const contentTypeParam = params.get("contentType");
        const idParam = params.get("id");

        if (contentTypeParam && idParam) {
          let docType = "";
          if (contentTypeParam.toLowerCase().includes("bulletin")) {
            docType = "bulletin";
          } else if (contentTypeParam.toLowerCase().includes("class guide")) {
            docType = "class-guide";
          } else if (contentTypeParam.toLowerCase().includes("faq")) {
            docType = "faq";
          }

          return {
            docType,
            itemId: idParam,
            parentItemId: "", // Not available from URL
            contentType: contentTypeParam,
          };
        }
      }
    }
    return null;
  }, [parsedSourceData, url]);

  // Unified data fetching logic - single consistent approach
  useEffect(() => {
    const fetchContent = async () => {
      if (!authData.accessToken || !unifiedSourceData) return;

      setLoading(true);
      setError(null);

      try {
        const { docType, itemId, parentItemId, contentType } =
          unifiedSourceData;

        // Unified content fetching based on docType
        if (docType && itemId) {
          switch (docType) {
            case "training-manual":
              const tmResponse = await getTrainingManual(
                parentItemId,
                authData.accessToken
              );
              const tmChapter = tmResponse.TrainingManualChapter.find(
                (chap) =>
                  chap.ItemID.replace(/[{}]/g, "") ===
                  itemId.replace(/[{}]/g, "")
              );
              setContentData({
                type: "training-manual",
                data: tmResponse,
                chapter: tmChapter || undefined,
              });
              break;

            case "industry-guide":
              const igResponse = await getIndustryGuide(
                parentItemId,
                authData.accessToken
              );
              const igChapter = igResponse.IndustryGuideChapter.find(
                (chap) =>
                  chap.ItemID.replace(/[{}]/g, "") ===
                  itemId.replace(/[{}]/g, "")
              );
              setContentData({
                type: "industry-guide",
                data: igResponse,
                chapter: igChapter || undefined,
              });
              break;

            case "class-guide":
              const cgResponse = await getClassGuide(
                itemId,
                contentType,
                authData.accessToken
              );
              setContentData({ type: "class-guide", data: cgResponse,contentType: contentType });
              break;

            case "faq":
              const faqResponse = await getFaq(itemId,contentType, authData.accessToken);
              setContentData({ type: "faq", data: faqResponse,contentType: contentType });
              break;

            case "bulletin":
              const bulletinResponse = await getBulletin(
                itemId,
                contentType,
                authData.accessToken
              );
              setContentData({
                type: "bulletin",
                data: bulletinResponse,
                contentType: contentType,
              });
              break;
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load content");
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [unifiedSourceData, authData.accessToken]);

  // Add current document to navigation service when content loads
  useEffect(() => {
    if (contentData && documentTitle) {
      const currentItem = {
        ItemID: id as string,
        ClassCode: '',
        Title: documentTitle,
        ContentType: contentData.type.replace('-', ' '),
        Jurisdiction: [],
        Lobs: []
      };

      // Only add if this is a new navigation (not already in breadcrumbs)
      const currentBreadcrumbs = navigationService.getBreadcrumbs();
      const isAlreadyInBreadcrumbs = currentBreadcrumbs.some(b => b.id === id);

      if (!isAlreadyInBreadcrumbs) {
        const breadcrumb = navigationService.createBreadcrumbFromNavigationItem(currentItem);
        navigationService.addBreadcrumb(breadcrumb);
      }
    }
  }, [contentData, documentTitle, id]);

  const renderContent = () => {
    if (loading) {
      return <LoadingSpinner />;
    }

    if (error) {
      return <ErrorDisplay message={error} />;
    }

    if (contentData) {
      const screenWidth = Dimensions.get("window").width;
      const contentWidth = screenWidth - 40;

      // Render content based on unified contentData type
      switch (contentData.type) {
        case "training-manual":
          if (contentData.chapter) {
            return (
              <TrainingManualContent
                chapter={contentData.chapter}
                contentWidth={contentWidth}
              />
            );
          }
          break;

        case "industry-guide":
          if (contentData.chapter) {
            return (
              <IndustryGuideContent
                chapter={contentData.chapter}
                contentWidth={contentWidth}
              />
            );
          }
          break;

        case "class-guide":
          return (
            <ClassGuideContent
              classGuide={contentData.data}
              contentWidth={contentWidth}
            />
          );

        case "faq":
          return (
            <FaqContent faq={contentData.data} contentWidth={contentWidth} />
          );

        case "bulletin":
          return (
            <BulletinContent
              bulletinData={contentData.data}
              bulletinType={contentData.contentType || "Bulletin"}
              contentWidth={contentWidth}
              onClose={() => router.back()}
            />
          );
      }
    }
  };

  return (
    <AppLayout
      showNewConversation={true}
      showBackButton={true}
      onBackPress={handleBack}
      onNewConversationPress={handleNewConversation}
    >
      <ScrollView style={paasSourceStyles.contentContainer}>
        <View style={paasSourceStyles.content}>
          <BreadcrumbNavigation currentTitle={documentTitle} />
          <Text style={paasSourceStyles.title}>{documentTitle}</Text>

          {renderContent()}
        </View>
      </ScrollView>
    </AppLayout>
  );
}
