@charset "UTF-8";
@import "@fontsource/roboto/400.css";
@import "@fontsource/roboto/500.css";
@import "@fontsource/roboto/700.css";
body {
  min-height: 100vh;
  margin: 0;
  display: flex;
  flex-direction: column;
  background-color: white;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

button {
  display: inline-block;
  border: none;
  margin: 0;
  text-decoration: none;
  background-color: transparent;
  color: black;
  font-size: 1rem;
  cursor: pointer;
  text-align: center;
  transition: background 250ms ease-in-out, transform 150ms ease;
  -webkit-appearance: none;
  -moz-appearance: none;
}

select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: transparent;
  border: none;
  padding: 0 1em 0 0;
  margin: 0;
  width: 100%;
  font-family: inherit;
  font-size: inherit;
  cursor: inherit;
  line-height: inherit;
}
select::-ms-expand {
  display: none;
}

/*Colors*/
/*Dimensions*/
html {
  font-size: 18px;
}

body {
  font-family: "Roboto", sans-serif;
  font-size: 1rem;
  font-variant: no-common-ligatures;
  line-height: 1.5;
  text-rendering: optimizelegibility;
  -webkit-text-size-adjust: 100%;
     -moz-text-size-adjust: 100%;
          text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  color: #3f3f3f;
}

h1, h2, h3, .cards .heading-wrapper, h4, h5, h6 {
  font-weight: 500;
  color: #000000;
}

h1 {
  font-size: 2.4rem;
  line-height: 1.3;
}

h2 {
  font-size: 1.9rem;
  margin: 1rem 0;
}
h2.with-cta {
  display: flex;
  flex-wrap: wrap;
}
h2 a {
  margin-left: auto;
  font-size: 0.9rem;
}

h3, .cards .heading-wrapper {
  font-size: 1.3rem;
}
h3 span.material-icons, .cards .heading-wrapper span.material-icons {
  font-size: 0.9rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.2rem;
}

h6 {
  font-size: 1.15rem;
}

p, .paragraph-wrapper {
  font-size: 1rem;
}

.paragraph-wrapper {
  margin: 1rem 0;
}

small {
  font-size: 0.875rem;
}

a {
  color: #004eaa;
  text-decoration: none;
  cursor: pointer;
}
a:hover {
  color: #002d61;
}
a[aria-disabled=true] {
  pointer-events: none;
}
a[aria-disabled=true]:not(.primary):not(.secondary):not(.tertiary) {
  color: #999999;
}
a.primary {
  padding: 0.75rem 2rem;
  background-color: #FFC600;
  color: #000000;
  border-radius: 0.25rem;
}
a.primary:hover {
  background-color: #F4AF2D;
}
a.primary[aria-disabled=true] {
  background-color: #dddddd;
}
a.secondary {
  padding: 0.75rem 2rem;
  background-color: #004eaa;
  color: #ffffff;
  border-radius: 0.25rem;
}
a.secondary:hover {
  background-color: #002d61;
}
a.secondary[aria-disabled=true] {
  background-color: #dddddd;
}
a.tertiary {
  padding: 0.75rem 2rem;
  background-color: #ffffff;
  color: #004eaa;
  border-radius: 0.25rem;
  border: thin solid #004eaa;
}
a.tertiary:hover {
  color: #002d61;
}
a.tertiary[aria-disabled=true] {
  color: #dddddd;
  border-color: #bbbbbb;
}
a.inverse {
  color: #ffffff;
}
a.inverse:hover {
  color: #d8ebef;
}
a.inverse[aria-disabled=true] {
  color: #dddddd;
}

button.primary {
  padding: 0.75rem 2rem;
  background-color: #FFC600;
  color: #000000;
  border-radius: 0.25rem;
}
button.primary:hover {
  background-color: #F4AF2D;
}
button.primary[disabled] {
  background-color: #dddddd;
}
button.secondary {
  padding: 0.75rem 2rem;
  background-color: #004eaa;
  color: #ffffff;
  border-radius: 0.25rem;
}
button.secondary:hover {
  background-color: #002d61;
}
button.secondary[disabled] {
  background-color: #dddddd;
}
button.tertiary {
  padding: 0.75rem 2rem;
  background-color: #ffffff;
  color: #004eaa;
  border-radius: 0.25rem;
  border: thin solid #004eaa;
}
button.tertiary:hover {
  color: #002d61;
}
button.tertiary[disabled] {
  color: #dddddd;
  border-color: #bbbbbb;
}

.call-to-action {
  padding: 2rem 0;
  text-align: center;
  width: 100%;
}

select {
  cursor: pointer;
  position: relative;
  width: -moz-fit-content;
  width: fit-content;
}

input[type=text],
input[type=date],
select {
  border: thin solid #004eaa;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}
input[type=text][disabled],
input[type=date][disabled],
select[disabled] {
  color: #999999;
  border-color: #bbbbbb;
  pointer-events: none;
}

textarea {
  border: thin solid #004eaa;
  padding: 0.5rem 1rem;
  min-height: 8rem;
}
textarea[disabled] {
  color: #999999;
  border-color: #bbbbbb;
  pointer-events: none;
}

::-webkit-calendar-picker-indicator {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24"><path fill="%23004eaa" d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/></svg>');
}

input[type=date i] {
  font-family: "Roboto";
}

input[type=checkbox] {
  accent-color: #004eaa;
}

::-moz-placeholder {
  font-style: italic;
}

::placeholder {
  font-style: italic;
}

.select-wrapper {
  position: relative;
}
.select-wrapper:after {
  font-family: "Material Icons";
  position: absolute;
  pointer-events: none;
  content: "\e5cf";
  top: 0.5rem;
  right: 0.5rem;
  color: #004eaa;
}
.select-wrapper select {
  padding-right: 2rem;
}

label b, .pseudo-label b {
  font-size: 0.85rem;
  display: block;
  padding-bottom: 0.25rem;
}

.main-content-wrapper {
  display: flex;
  z-index: 0;
  position: relative;
}
.main-content-wrapper .desktop-view {
  display: block;
}
.main-content-wrapper .mobile-view {
  display: none;
}
.main-content-wrapper #slide, .main-content-wrapper .toggle {
  display: none;
}

.site {
  max-width: 80rem;
  margin: 0 auto;
  position: relative;
}
.site.flex-wrapper {
  gap: 2rem;
}
.site.flex-wrapper section {
  padding: 1.875rem 0;
}
.site.flex-wrapper section.poll {
  padding: 1rem;
}
.site.flex-wrapper .call-to-action {
  text-align: left;
}

.flex-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.no-margin {
  margin: 0;
}

.no-margin-top {
  margin-top: 0;
}

.no-margin-bottom {
  margin-bottom: 0;
}

.margin-bottom {
  margin-bottom: 1rem;
}

.margin-top {
  margin-top: 1rem;
}

.background-lt-grey {
  background-color: #f8f8f8;
}

.background-lt-grey-2 {
  background-color: #fcfcfc;
}

.background-lt-blue {
  background-color: #f7fbfe;
}

.background-lt-blue-2 {
  background-color: #d8ebef;
  color: #000000;
}

.background-blue {
  background-color: #00358e;
  color: #ffffff;
}
.background-blue h1, .background-blue h2 {
  color: #ffffff;
}

.background-md-blue-2 {
  background-color: #2A7DE1;
  color: #ffffff;
}
.background-md-blue-2 h1, .background-md-blue-2 h2 {
  color: #ffffff;
}

.background-dk-blue {
  background-color: #002D61;
  color: #ffffff;
}
.background-dk-blue h1, .background-dk-blue h2 {
  color: #ffffff;
}

.background-lt-yellow {
  background-color: rgba(255, 198, 0, 0.5);
  color: #000000;
}

.background-yellow {
  background-color: #FFC600;
  color: #000000;
}

.background-dk-yellow {
  background-color: #F4AF2D;
  color: #000000;
}

.sr-only {
  clip: rect(0 0 0 0);
  -webkit-clip-path: inset(50%);
          clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

header {
  background-color: #ffffff;
  padding: 1rem 1rem 0;
  min-height: 8.5rem;
  box-shadow: 0 0 0.75rem 0 rgba(0, 0, 0, 0.2);
  z-index: 2;
}
header a {
  font-size: 0.95rem;
}
header hr {
  margin-left: -1rem;
  margin-right: -1rem;
  margin-bottom: 0;
  opacity: 0.1;
}
header .top-header {
  justify-content: space-between;
}
header .top-header sup {
  font-size: 0.8rem;
}
header .top-header .logo {
  align-self: flex-start;
}
header .top-header .logo img {
  height: 1.25rem;
}
header a.primary {
  margin-left: auto;
}
header nav .non-mobile {
  display: flex;
  margin: 0;
  padding-left: 0;
}
header nav .non-mobile li {
  list-style: none;
  padding: 1.5rem 0.75rem 1.5rem;
  border-bottom: 0.25rem solid #ffffff;
}
header nav .non-mobile li:hover {
  color: #002d61;
  background-color: #f7fbfe;
}
header nav .non-mobile li.current {
  border-color: #000000;
}
header nav .non-mobile li.current a:nth-child(1) {
  font-weight: 500;
}
header nav .non-mobile li.current a[role=menu-item]:not(.heading):first-child {
  font-weight: 400;
}
header nav .non-mobile li.menu-dropdown .mega-menu {
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 4.75rem;
  left: 0;
  opacity: 0;
  visibility: hidden;
}
header nav .non-mobile li.menu-dropdown:hover .mega-menu {
  opacity: 1;
  visibility: visible;
  z-index: 1;
}
header nav .non-mobile a .material-icons {
  vertical-align: middle;
}
header nav .mobile-navigation {
  display: none;
}
header nav.account-alerts-support {
  margin-left: auto;
  text-align: center;
}
header nav.account-alerts-support a {
  padding: 0.3rem 0.5rem;
}
header nav.account-alerts-support a:hover {
  background-color: #f7fbfe;
}
header nav.account-alerts-support .support a {
  display: flex;
  flex-direction: column;
  padding: 0.8rem 0.5rem;
}
header nav.account-alerts-support .account {
  margin-left: 2rem;
  border-left: thin solid #999999;
}
header nav.account-alerts-support .account a {
  padding: 1.5rem 1rem 1.5rem;
  display: inline-block;
  margin-left: 1rem;
}
header nav.account-alerts-support .account a:first-of-type {
  margin-right: 0;
}
header nav.account-alerts-support .account span {
  display: inline-block;
  vertical-align: middle;
}
header .account-alerts-support {
  position: relative;
  margin: 0;
  padding: 0;
}
header .account-alerts-support .user-account {
  display: flex;
  margin: 0;
  padding: 0;
  align-items: center;
}
header .account-alerts-support .user-account li {
  list-style: none;
  padding: 1.5rem 0.75rem 1.5rem;
  padding: 0;
  margin: 0;
  border-bottom: 0.25rem solid #ffffff;
}
header .account-alerts-support .user-account li a {
  text-decoration: none;
}
header .account-alerts-support .user-account .menu-dropdown .dropdown-content {
  display: none;
  position: absolute;
  top: 4.7rem;
  right: 0;
  width: 9rem;
  box-shadow: 0 0 0.15rem 0 rgba(0, 0, 0, 0.2);
  z-index: 2;
}
header .account-alerts-support .user-account .menu-dropdown .dropdown-content a {
  display: block;
  background-color: #ffffff;
  padding: 0.5555rem;
  border-bottom: thin solid #eeeeee;
  font-size: 0.85rem;
}
header .account-alerts-support .user-account .menu-dropdown .dropdown-content a:hover {
  background-color: #f7fbfe;
}
header .account-alerts-support .user-account .menu-dropdown:hover .dropdown-content {
  display: block;
}
header.logo-only {
  min-height: auto;
}

.mega-menu {
  padding: 2rem 4rem;
  background-color: #ffffff;
  box-shadow: 0 0 0.15rem 0 rgba(0, 0, 0, 0.2);
  gap: 1rem;
}
.mega-menu b[role=heading] {
  display: inline-flex;
  width: 100%;
  border-bottom: thin solid #bbbbbb;
  margin-bottom: 1rem;
}
.mega-menu b[role=heading] + p, .mega-menu b[role=heading] + .paragraph-wrapper {
  margin-top: 0;
}
.mega-menu .flex-wrapper {
  align-items: flex-start;
}
.mega-menu [role=menu-item] {
  padding: 0 0.25rem 0.25rem;
  font-size: 1rem;
  display: inline-flex;
  width: 18rem;
}
.mega-menu .heading {
  font-size: 1.2rem;
  font-weight: 500;
}
.mega-menu p, .mega-menu .paragraph-wrapper {
  font-size: 1rem;
}
.mega-menu .sub-group {
  padding-top: 2rem;
}
.mega-menu.one div:not(.sub-group) {
  width: 100%;
}
.mega-menu.one .group2-content,
.mega-menu.one .group1-content {
  width: 100% !important;
}
.mega-menu.two div:not(.sub-group) {
  width: 49%;
}
.mega-menu.two .group2-content,
.mega-menu.two .group1-content {
  width: 100% !important;
}
.mega-menu.three div:not(.sub-group) {
  width: 32%;
}
.mega-menu.three .group2-content,
.mega-menu.three .group1-content {
  width: 100% !important;
}

.mobile-navigation #mobile_navigation {
  display: none;
  position: absolute;
  padding: 2rem;
  top: 5rem;
  left: -1rem;
  width: auto;
  margin-top: 0;
  background-color: #ffffff;
  box-shadow: 0 0 0.15rem 0 rgba(0, 0, 0, 0.2);
  z-index: 2;
}
.mobile-navigation #mobile_navigation li {
  list-style: none;
  color: #004eaa;
}
.mobile-navigation #mobile_navigation li a {
  display: block;
  font-size: 1rem;
  padding: 0.2525rem;
}
.mobile-navigation #mobile_navigation li a[role=heading] {
  color: #3f3f3f;
  font-weight: 700;
  cursor: text;
}
.mobile-navigation #mobile_navigation hr {
  margin: 1rem 0;
}
.mobile-navigation #mobile_navigation .mobile-view.side-nav .heading {
  color: #3f3f3f;
}
.mobile-navigation #mobile_navigation .heading {
  font-weight: 700;
}
.mobile-navigation #mobile_navigation .heading .material-icons.expand_more {
  font-size: 1rem;
  font-weight: 700;
}
.mobile-navigation #mobile_navigation .heading .material-symbols-outlined.expand_less {
  font-size: 1rem;
  font-weight: 700;
}
.mobile-navigation #mobile_navigation li .heading {
  padding: 0.2525rem;
}
.mobile-navigation #mobile_navigation .menu-group {
  margin-bottom: 1rem;
  padding: 0 0 0 0.5rem;
}
.mobile-navigation #mobile_navigation .menu-group li .sub-heading {
  font-weight: 500;
}
.mobile-navigation #mobile_navigation .menu-group li ul {
  padding-left: 0.5rem;
}
.mobile-navigation #mobile_navigation .menu-group.expand-always {
  display: block;
}
.mobile-navigation #closeMenu {
  display: none;
}

footer {
  color: #ffffff;
  background-color: #00358e;
  padding: 1.5rem 1rem;
  margin-top: auto;
  border-top: 1px solid #efefef;
  min-height: 10rem;
}
footer .flex-wrapper {
  gap: 2rem;
  justify-content: space-between;
}
footer .flex-wrapper a {
  color: #FFFFFF;
}
footer small {
  display: block;
}
footer img {
  width: 9.375rem;
}
footer strong {
  display: block;
  font-size: 0.8rem;
  font-weight: 500;
}
footer .copyright-footer-links {
  padding: 2rem;
  text-align: center;
  line-height: 1.625rem;
}
footer .copyright-footer-links a {
  padding: 0 0.5rem;
  text-decoration: underline;
  color: #ffffff;
}
footer .copyright-footer-links a:not(:first-of-type) {
  border-left: thin solid #ffffff;
}
footer .copyright-footer-links a:hover {
  color: #d8ebef;
}
footer .copyright-footer-links + div {
  text-align: right;
}
footer .copyright-footer-links + div img {
  width: 7.75rem;
}

main {
  flex-grow: 2;
  min-width: 0;
  margin-top: 0.5rem;
  min-height: calc(100vh - 19rem);
}
main .no-padding {
  padding: 0;
}
main .no-padding a {
  display: inline-block;
}
main .site.flex-wrapper {
  align-items: flex-start;
}
main .site.flex-wrapper .content-wrapper {
  flex-grow: 2;
  max-width: 70%;
  padding: 1.875rem;
}
main .site.flex-wrapper aside {
  padding: 1.875rem 0;
}
main .site.flex-wrapper aside section {
  width: 19rem;
  padding: 1rem;
  margin: 0;
}
main .site.flex-wrapper aside section .non-underline p a, main .site.flex-wrapper aside section .non-underline .paragraph-wrapper a {
  text-decoration: none;
}
main .site.flex-wrapper aside section .non-underline p a:hover, main .site.flex-wrapper aside section .non-underline .paragraph-wrapper a:hover {
  color: #002d61;
}
main .site.flex-wrapper aside section div {
  font-size: 0.9rem;
}
main .site.flex-wrapper aside section p, main .site.flex-wrapper aside section .paragraph-wrapper {
  font-size: 0.9rem;
}
main .site.flex-wrapper aside section p a, main .site.flex-wrapper aside section .paragraph-wrapper a {
  text-decoration: underline;
  color: #004eaa;
}
main .site.flex-wrapper aside section p:hover, main .site.flex-wrapper aside section .paragraph-wrapper:hover {
  color: #002d61;
}
main .site.flex-wrapper aside section .link-list h3, main .site.flex-wrapper aside section .link-list .cards .heading-wrapper, .cards main .site.flex-wrapper aside section .link-list .heading-wrapper {
  font-size: 0.9rem;
  margin: 0.25rem 0 0;
}
main .site.flex-wrapper aside section:not(:first-of-type) {
  margin-top: 2rem;
}
main .site.flex-wrapper aside.filter h2.filter-toggle-mobile span {
  display: none;
}
main .site.flex-wrapper aside.thin {
  width: 17rem;
}
main .site.flex-wrapper aside.thin section {
  width: 15rem;
}
main .site.flex-wrapper aside .contact-team .with-cta {
  padding: 0 0 0.5rem;
}
main .site.flex-wrapper aside .media-container .media-player {
  aspect-ratio: 16/9;
}
main .site.flex-wrapper aside .media-container .media-player iframe {
  width: 100%;
  height: 100%;
}
main.dashboard {
  margin-top: 0;
}
main.dashboard .dynamic-product-banner {
  padding: 2rem;
  background-image: linear-gradient(to left, rgba(5, 20, 110, 0.7), rgba(5, 20, 110, 0.2)), url("../assets/GettyImages-1214224199.jpg");
  background-repeat: no-repeat;
  background-size: cover;
}
main.dashboard .dynamic-product-banner .messaging h1 {
  margin-top: 0.5rem;
  color: #ffffff;
}
main.dashboard .dynamic-product-banner .messaging p, main.dashboard .dynamic-product-banner .messaging .paragraph-wrapper {
  color: #ffffff;
  font-size: 1.5rem;
}
main.dashboard .configure-dashboard {
  text-align: right;
  padding-bottom: 1rem;
}
main.dashboard .widgets .flex-wrapper {
  flex-wrap: nowrap;
  gap: 1.5rem;
  padding: 0.75rem 0;
}
main.dashboard .widgets .widget {
  background-color: #f8f8f8;
}
main.global-search .site.flex-wrapper {
  gap: 0;
}
main.global-search .site.flex-wrapper aside.filter section {
  width: 17rem;
}
main.global-search .site.flex-wrapper .content-wrapper {
  flex-grow: 2;
  width: unset;
  max-width: unset;
  padding-right: 1.2rem;
}
main.global-search .site.flex-wrapper .content-wrapper .select-wrapper {
  display: inline-block;
}
main.global-search .site.flex-wrapper .content-wrapper h2 {
  font-size: 1.25rem;
  line-height: 1.2;
  margin-top: 0;
  padding-bottom: 0.5rem;
}
main.global-search .site.flex-wrapper .content-wrapper .results-meta-sort.flex-wrapper {
  flex-wrap: wrap;
  margin-top: 1rem;
}
main.global-search .site.flex-wrapper .content-wrapper .results-meta-sort.flex-wrapper form {
  margin-left: auto;
}
main.global-search .site.flex-wrapper .content-wrapper .results-meta-sort.flex-wrapper form label b {
  display: inline-block;
  margin-right: 1rem;
}
main.global-search .site.flex-wrapper .content-wrapper .results-meta-sort.flex-wrapper form .select-wrapper {
  display: inline-block;
}
main.library .site.flex-wrapper .content-wrapper {
  max-width: 100%;
}
main.library .no-content {
  align-items: center;
}
main.library .no-content .message {
  flex-grow: 2;
  max-width: 50%;
}
main.library .no-content .message h2 {
  font-size: 1.75rem;
}
main.library .no-content .image-wrapper {
  position: relative;
  min-width: 20rem;
  margin-top: 2rem;
  width: -moz-fit-content;
  width: fit-content;
  height: 15rem;
  background-image: url(../assets/pfas.png);
  background-position: bottom center;
  overflow: visible;
}
main.library .no-content .image-wrapper .guidance-box {
  position: absolute;
  font-size: 0.9rem;
  bottom: -7rem;
  left: 11rem;
  padding: 1rem 0;
  width: 10rem;
  margin: 0 auto;
}
main.library .no-content .image-wrapper .guidance-box .material-icons {
  display: block;
}
main.library .share-save {
  justify-content: flex-start;
  padding: 4rem;
  background-color: rgb(0, 53, 114);
  width: -moz-fit-content;
  width: fit-content;
}
main.library .share-save #share {
  filter: brightness(70%);
}
main.system-messages-page h2 {
  font-size: 1.5rem;
  font-weight: 400;
}
main.system-messages-page p, main.system-messages-page .paragraph-wrapper {
  max-width: 70rem;
}
main.system-messages-page .flex-wrapper {
  align-items: flex-start;
  flex-wrap: wrap;
}
main.system-messages-page .contact-info {
  padding: 0;
  list-style: none;
  margin: 1.5rem 4rem 0 0;
}
main.system-messages-page .contact-info strong {
  display: inline-block;
  font-size: 1.05rem;
  font-weight: 500;
  padding-bottom: 0.25rem;
  border-bottom: thin solid #bbbbbb;
  margin-bottom: 0.5rem;
}
main.system-messages-page .contact-info li {
  line-height: 2.5;
}
main.system-messages-page .contact-info li .material-icons {
  vertical-align: middle;
  padding-right: 0.5rem;
  line-height: 0.9;
}

.report-container {
  width: 100%;
  aspect-ratio: 16/9;
}
.report-container iframe {
  display: block;
  width: 100%;
  height: 100%;
}

section {
  padding: 1.875rem;
  background-color: #ffffff;
}
section.background-lt-grey {
  background-color: #f8f8f8;
}
section.background-blue {
  background-color: #00358e;
  color: #ffffff;
}
section.background-blue h2 {
  color: #ffffff;
}
section.background-dk-blue {
  background-color: #002D61;
  color: #ffffff;
}
section.background-dk-blue h2 {
  color: #ffffff;
}
section .flex-wrapper.with-cta p, section .flex-wrapper.with-cta .paragraph-wrapper {
  width: 100%;
}
section .flex-wrapper.with-cta .call-to-action {
  text-align: right;
}
section .flex-wrapper .messaging + img {
  margin-left: auto;
  max-width: 40%;
  -o-object-fit: cover;
     object-fit: cover;
  /* flex-grow: 2; */
}

aside {
  padding: 1.875rem;
  width: 20rem;
}
aside strong[role=heading] {
  display: block;
  font-size: 1.125rem;
  font-weight: 500;
  padding-bottom: 1rem;
  border-bottom: thin solid #bbbbbb;
}
aside h2 {
  font-size: 1rem;
  padding-bottom: 0.5rem;
  margin-top: 0;
  border-bottom: thin solid #bbbbbb;
}
aside h3, aside .cards .heading-wrapper, .cards aside .heading-wrapper {
  font-size: 0.9rem;
}
aside article address {
  display: inline-block;
  font-size: 0.9rem;
}
aside article a {
  font-size: 0.9rem;
}
aside article a:not(:first-of-type) {
  border-left: thin solid #bbbbbb;
  padding-left: 0.25rem;
}
aside article a span {
  vertical-align: middle;
}
aside .verisk-activity.with-show-more .show-more-activities,
aside .verisk-activity.with-show-more .show-less-activities {
  padding: 0.5rem 0 0;
  font-size: 0.9rem;
}
aside .link-list {
  font-size: 0.9rem;
  margin-bottom: 0;
}
aside .link-list.link-list-with-vertical-scroll li:nth-of-type(n+4) {
  display: none;
}
aside .link-list.link-list-with-vertical-scroll.show-all {
  max-height: 20rem;
  overflow-y: auto;
}
aside .link-list.link-list-with-vertical-scroll.show-all li:nth-of-type(n+4) {
  display: block;
}
aside .link-list[class*=recommended-item] {
  max-height: 20rem;
  overflow-y: scroll;
  margin-bottom: 1rem;
}
aside .link-list a:not(:first-of-type) {
  border-left: thin solid #bbbbbb;
  margin-left: 0.25rem;
  padding-left: 0.5rem;
}
aside .link-list a[class*=show-less] {
  border-left: none;
}
aside a[class*=recommended-show-btn] {
  font-size: 0.9rem;
}
aside.workspace-nav, .pattern-library aside.brand-patterns {
  padding: 3rem 0 1.875rem 1.5rem;
  min-width: 16rem;
  width: 16rem;
  position: relative;
  min-height: calc(100vh - 21.625rem);
  border-right: 0.25rem double #efefef;
  transition: all 0.5s ease-out;
}
aside.workspace-nav nav, .pattern-library aside.brand-patterns nav {
  width: 100%;
}
aside.workspace-nav .side-nav.heading, .pattern-library aside.brand-patterns .side-nav.heading {
  cursor: text;
  display: block;
  font-size: 1rem;
  padding-bottom: 0.75rem;
  color: #3f3f3f;
  text-decoration: none;
  font-weight: 600;
}
aside.workspace-nav .side-nav.heading span, .pattern-library aside.brand-patterns .side-nav.heading span {
  vertical-align: middle;
  font-size: 1.05rem;
}
aside.workspace-nav a, .pattern-library aside.brand-patterns a {
  display: block;
  font-size: 0.9rem;
  padding: 0.75rem 0.75rem 0.75rem 0rem;
}
aside.workspace-nav a.current, .pattern-library aside.brand-patterns a.current {
  background-color: #f7fbfe;
  font-weight: 700;
}
aside.workspace-nav a:hover, .pattern-library aside.brand-patterns a:hover {
  background-color: #f7fbfe;
}
aside.workspace-nav a span, .pattern-library aside.brand-patterns a span {
  vertical-align: middle;
  font-size: 1.05rem;
}
aside.workspace-nav a[role=heading], .pattern-library aside.brand-patterns a[role=heading] {
  color: #3f3f3f;
  padding-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: text;
}
aside.workspace-nav a[role=heading]:hover, .pattern-library aside.brand-patterns a[role=heading]:hover {
  background-color: transparent;
}
aside.workspace-nav ul, .pattern-library aside.brand-patterns ul {
  list-style-type: none;
  margin-top: 0;
  padding-left: 1.25rem;
}
aside.workspace-nav ul li a, .pattern-library aside.brand-patterns ul li a {
  font-size: 0.9rem;
}
aside.filter {
  width: 18rem;
}
aside.filter h2 {
  border-bottom: none;
}
aside.filter .link-list li {
  padding-bottom: 0;
}
aside.filter .link-list li:first-of-type details summary {
  border-top: none;
  padding-top: 0;
}
aside.filter b {
  padding-bottom: 0.25rem;
  font-size: 0.85rem;
  display: block;
}
aside.filter b:not(:first-of-type) {
  padding-top: 0.5rem;
}
aside.filter details summary {
  display: flex;
  flex-wrap: nowrap;
  position: relative;
  border-bottom: none;
  padding-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  justify-content: space-between;
  width: 100%;
}
aside.filter details summary .lines-item {
  flex-basis: 75%;
}
aside.filter details summary .item-count {
  align-items: center;
  background-color: black;
  border-radius: 12px;
  display: inline-flex;
  margin-left: 6px;
  min-height: 16px;
  min-width: 16px;
  justify-content: center;
}
aside.filter details summary .item-count small {
  color: white;
  font-size: 12px;
  font-weight: 700;
  padding: 2px 8px;
}
aside.filter details summary::after {
  margin-left: 0.25rem;
  font-size: 1.5rem;
}
aside.filter details .options .toggle-content {
  display: -webkit-box;
  visibility: visible;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  overflow: hidden;
}
aside.filter details .options label {
  display: flex;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  align-items: flex-start;
}
aside.filter details .options label span + span {
  margin-left: auto;
}
aside.filter details .options label input[type=text] {
  width: 100%;
  margin-bottom: 0.5rem;
}
aside.filter details .options label input[type=checkbox] {
  margin-top: 0.295rem;
}
aside.filter details [id^=showMore] {
  display: none;
}
aside.filter details .filter-toggle {
  font-size: 0.8rem;
  transform: none;
  box-shadow: unset;
  position: unset;
  padding: 1rem 0 1.5rem 0;
  color: #004eaa;
  cursor: pointer;
}
aside.filter details .filter-toggle .less {
  display: none;
}
aside.filter details section:has(#showMore:checked) .toggle-content {
  display: block;
}
aside.filter details section:has(#showMore:checked) .filter-toggle .more {
  display: none;
}
aside.filter details section:has(#showMore:checked) .filter-toggle .less {
  display: block;
}
aside.filter details details {
  padding-left: 0.5rem;
}
aside.filter input[type=text],
aside.filter input[type=date] {
  font-size: 0.85rem;
}
aside section.with-show-more .item-list .item:nth-of-type(n+4) {
  display: none;
}
aside section.with-show-more .show-less,
aside section.with-show-more .show-more {
  padding: 0.5rem 0 0;
  font-size: 0.9rem;
  display: block;
}
aside section.with-show-more .show-less {
  padding-top: 1rem;
}
aside section.with-show-more .show-less,
aside section.with-show-more .show-more:target {
  display: none;
}
aside section.with-show-more .show-more:target + .show-less {
  display: block;
}
aside section.with-show-more:has(.show-more:target) .item:nth-of-type(n+4) {
  display: block;
}
aside section.with-show-more:has(.show-more:target) .item-list {
  max-height: 20rem;
  overflow-y: scroll;
}
aside section.with-show-more-filters .lines label:nth-of-type(n+7) {
  display: none;
}
aside section.with-show-more-filters .lines .show-less-lines,
aside section.with-show-more-filters .lines .show-more-lines {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
aside section.with-show-more-filters .lines .show-less-lines {
  padding-top: 1rem;
}
aside section.with-show-more-filters .lines .show-less-lines,
aside section.with-show-more-filters .lines .show-more-lines:target {
  display: none;
}
aside section.with-show-more-filters .lines .show-more-lines:target + .show-less-lines {
  display: block;
  border: 0px;
}
aside section.with-show-more-filters .lines:has(.show-more-lines:target) label:nth-of-type(n+7) {
  display: flex;
}
aside section.with-show-more-filters .lines:has(.show-more-lines:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
aside section.with-show-more-filters .topics label:nth-of-type(n+7) {
  display: none;
}
aside section.with-show-more-filters .topics .show-less-topics,
aside section.with-show-more-filters .topics .show-more-topics {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
aside section.with-show-more-filters .topics .show-less-topics {
  padding-top: 1rem;
}
aside section.with-show-more-filters .topics .show-less-topics,
aside section.with-show-more-filters .topics .show-more-topics:target {
  display: none;
}
aside section.with-show-more-filters .topics .show-more-topics:target + .show-less-topics {
  display: block;
  border: 0px;
}
aside section.with-show-more-filters .topics:has(.show-more-topics:target) label:nth-of-type(n+7) {
  display: flex;
}
aside section.with-show-more-filters .topics:has(.show-more-topics:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
aside section.with-show-more-filters .jurisdictions label:nth-of-type(n+7) {
  display: none;
}
aside section.with-show-more-filters .jurisdictions .show-less-jurisdictions,
aside section.with-show-more-filters .jurisdictions .show-more-jurisdictions {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
aside section.with-show-more-filters .jurisdictions .show-less-jurisdictions {
  padding-top: 1rem;
}
aside section.with-show-more-filters .jurisdictions .show-less-jurisdictions,
aside section.with-show-more-filters .jurisdictions .show-more-jurisdictions:target {
  display: none;
}
aside section.with-show-more-filters .jurisdictions .show-more-jurisdictions:target + .show-less-jurisdictions {
  display: block;
  border: 0px;
}
aside section.with-show-more-filters .jurisdictions:has(.show-more-jurisdictions:target) label:nth-of-type(n+7) {
  display: flex;
}
aside section.with-show-more-filters .jurisdictions:has(.show-more-jurisdictions:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
aside section.with-show-more-filters .lob-facet label:nth-of-type(n+7) {
  display: none;
}
aside section.with-show-more-filters .lob-facet .show-less-lob,
aside section.with-show-more-filters .lob-facet .show-more-lob {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
aside section.with-show-more-filters .lob-facet .show-less-lob {
  padding-top: 1rem;
}
aside section.with-show-more-filters .lob-facet .show-less-lob,
aside section.with-show-more-filters .lob-facet .show-more-lob:target {
  display: none;
}
aside section.with-show-more-filters .lob-facet .show-more-lob:target + .show-less-lob {
  display: block;
  border: 0px;
}
aside section.with-show-more-filters .lob-facet:has(.show-more-lob:target) label:nth-of-type(n+7) {
  display: flex;
}
aside section.with-show-more-filters .lob-facet:has(.show-more-lob:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}

.agenda-link-group {
  padding-top: 0.5rem;
  font-size: 0.9rem;
}
.agenda-link-group a:not(:first-of-type) {
  padding-left: 0.5rem;
  margin-left: 0.25rem;
  border-left: thin solid #bbbbbb;
}
.agenda-link-group span.material-icons {
  font-size: 0.8rem;
  vertical-align: middle;
}

article .content-wrapper .references * {
  font-size: 0.9rem;
}
article .content-wrapper .references a {
  word-break: break-all;
}
article .content-wrapper .references .reference-listing p, article .content-wrapper .references .reference-listing .paragraph-wrapper {
  font-size: 0.9rem;
}
article .content-wrapper .pills {
  border-bottom: thin solid #bbbbbb;
}
article address {
  font-style: normal;
}
article .author .bio img.author-photo {
  height: 5rem;
  margin-top: 0;
}
article .author .bio p, article .author .bio .paragraph-wrapper {
  font-size: 0.9rem;
}
article .author .bio .details {
  padding: 0;
}
article.card {
  display: flex;
}
article.card:hover {
  box-shadow: 0px 0px 25px 1px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}
article.card p, article.card .paragraph-wrapper, article.card time {
  color: #3f3f3f;
}
article.card time {
  font-style: italic;
}
article.card p, article.card .paragraph-wrapper {
  margin-top: 0;
  order: 3;
}
article.card a {
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}
article.card a h3, article.card a .cards .heading-wrapper, .cards article.card a .heading-wrapper {
  order: 2;
}
article.card a img {
  border-radius: 0.25rem;
  width: 100%;
  height: 160px;
  -o-object-fit: cover;
     object-fit: cover;
  order: 1;
}
article .key-takeaways {
  border-bottom: thin solid #bbbbbb;
}
article .key-takeaways li {
  list-style: square;
}
article .key-takeaways li:not(:last-of-type) {
  padding-bottom: 1rem;
}
article .site.flex-wrapper .references {
  padding-bottom: 0;
}

.badge {
  margin-left: 0.2rem;
  padding: 0.2rem 0.4rem 0.2rem;
  border-radius: 0.25rem;
  font-size: 0.6rem;
  font-weight: 600;
  vertical-align: middle;
}

.bio {
  gap: 2rem;
  padding: 1rem 0;
  align-items: flex-start;
}
.bio .details {
  padding: 1rem 0;
}
.bio strong {
  margin: 0;
  display: block;
}
.bio strong a {
  display: block;
}
.bio img.author-photo {
  border-radius: 50%;
  margin-top: 1.2rem;
  max-width: 12.5rem;
  max-height: 12.5rem;
}
.bio strong {
  color: #000000;
}
.bio b {
  display: block;
}
.bio p, .bio .paragraph-wrapper {
  margin-top: 0.5rem;
}
.bio .social-icons {
  padding-left: 0.25rem;
  margin-top: 0.75rem;
}
.bio .social-icons a {
  display: inline-block;
}
.bio .social-icons a img {
  width: 1rem;
  margin-right: 0.75rem;
  opacity: 0.85;
}
.bio .social-icons a img:hover {
  opacity: 1;
}

.author-bio strong {
  color: #000000;
  font-size: 1.75rem;
}

/*adding img 100% to make inside rich text image responsive ticket no: 2046*/
/* adding the below styles for laigning richtext component as per ticker:1241*/
.site.flex-wrapper .major-content.rich-text-component.background-dk-blue, .site.flex-wrapper .major-content.rich-text-component.background-lt-grey {
  padding: 1.875rem;
}
.site.flex-wrapper .major-content img {
  width: 100%;
  height: 100%;
}

.breadcrumbs {
  font-size: 0.9rem;
}
.breadcrumbs nav {
  display: flex;
  gap: 0.25rem;
}
.breadcrumbs nav a.current {
  color: #3f3f3f;
  pointer-events: none;
}
.breadcrumbs nav a:not(.current)::after {
  font-family: "Material Icons";
  margin-left: auto;
  content: "\e409";
  font-size: 0.8rem;
  padding-left: 0.25rem;
}

.cards {
  align-items: stretch;
  flex-wrap: wrap;
  margin-left: -1rem;
  justify-content: flex-start;
}
.cards a h3, .cards a .heading-wrapper {
  color: #004eaa;
}
.cards a:hover h3, .cards a:hover .heading-wrapper {
  color: #002d61;
}
.cards h3, .cards .heading-wrapper {
  font-size: 1.2rem;
  line-height: 1.25;
}
.cards .heading-wrapper {
  margin: 1rem 0;
}
.cards .card {
  padding: 1rem;
  /* &:first-of-type {
      padding-left: 0;
  } */
}
.cards .card.one {
  width: 100%;
}
.cards .card.two {
  width: 50%;
}
.cards .card.three {
  width: 30%;
}
.cards .card.four {
  width: 23.5%;
}
.cards .card.five {
  width: 18.75%;
}
.cards .card img {
  border-radius: 0.25rem;
  height: 7.5rem;
  width: 100%;
  aspect-ratio: 16/9;
  -o-object-fit: cover;
     object-fit: cover;
}
.cards .card strong {
  display: block;
  padding: 1.5rem 0;
  font-size: 1.3125rem;
  font-weight: 500;
}
.cards .call-to-action {
  text-align: left;
  display: inline-flex;
  padding-bottom: 0;
}
.cards .call-to-action span.material-icons {
  font-size: 0.9rem;
}
.cards.no-padding {
  padding: 0;
}
.cards.sidebar {
  margin-left: 0;
}
.cards.sidebar .card {
  padding: 0.5rem 0;
}
.cards.sidebar .card strong {
  font-size: 1rem;
  padding: 0.5rem 0;
  font-weight: 700;
}
.cards.sidebar .card b {
  font-size: 0.95rem;
}
.cards.sidebar .card p, .cards.sidebar .card .paragraph-wrapper {
  margin: 0.5rem 0 1.5rem;
  font-size: 0.9rem;
}
.cards.card-wrap {
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-start;
}
.cards.min-width-cards .card {
  min-width: 15rem;
  max-width: 30rem;
}
.cards.carousel-cards {
  overflow-x: auto;
  overscroll-behavior: contain;
  width: 100%;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
}
.cards.carousel-cards .card {
  scroll-snap-align: start;
  flex-shrink: 0;
  position: relative;
}
@media (max-width: 50rem) {
  .cards.carousel-cards .card {
    width: 80%;
    padding: 1rem 2rem 1rem 0;
  }
}

.confirm {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  transition: opacity 200ms;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
.confirm:target {
  visibility: visible;
  opacity: 1;
}
.confirm .popup {
  margin: 20rem auto;
  padding: 2rem;
  background: #ffffff;
  border: 1px solid #bbbbbb;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
  position: relative;
  max-width: 30rem;
}
.confirm .popup h1 {
  font-size: 1.8rem;
  margin-top: 0;
}
.confirm .popup .close {
  position: absolute;
  width: 1rem;
  height: 1rem;
  top: 0;
  right: 1rem;
  transition: all 200ms;
  font-size: 2rem;
  font-weight: 700;
  text-decoration: none;
}
.confirm .popup .content {
  max-height: 400px;
  overflow: auto;
}
.confirm .popup p, .confirm .popup .paragraph-wrapper {
  margin: 0 0 1rem;
}
.confirm .popup p:last-child, .confirm .popup .paragraph-wrapper:last-child {
  margin: 0;
}
.confirm .popup .call-to-action {
  margin-top: 1rem;
  padding-bottom: 0;
}

details {
  list-style: none;
}
details summary {
  display: flex;
  align-items: center;
  color: #004eaa;
  cursor: pointer;
  padding: 0.25rem 0;
  border-top: thin solid #bbbbbb;
  border-bottom: thin solid #bbbbbb;
}
details summary::marker {
  content: none;
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::after {
  font-family: "Material Icons";
  margin-left: auto;
  content: "\e5cf";
  font-size: 2rem;
}
details[open] summary:after {
  content: "\e5ce";
}
details[open] form {
  padding-bottom: 0.75rem;
}
details[open] form summary {
  margin-right: 1rem;
  border: none;
  padding-top: 0;
  margin-bottom: 0;
}
details[open] form details:not([open]) summary:after {
  content: "\e5cf";
}

.explore-insights h2, .hover-lift-cards h2 {
  display: flex;
  flex-wrap: wrap;
}
.explore-insights .tabs, .hover-lift-cards .tabs {
  margin-left: auto;
  width: 19.25rem;
}
.explore-insights .tabs .tabbed nav, .hover-lift-cards .tabs .tabbed nav {
  padding-top: 0;
}
.explore-insights .tabs .tabbed nav .tab, .hover-lift-cards .tabs .tabbed nav .tab {
  font-size: 1rem;
  padding: 1rem;
}
.explore-insights .cards, .hover-lift-cards .cards {
  margin-left: -1rem;
}
.explore-insights .cards .card:first-of-type, .hover-lift-cards .cards .card:first-of-type {
  padding-left: 1rem;
}
.explore-insights .cards h3, .hover-lift-cards .cards h3, .explore-insights .cards .heading-wrapper, .hover-lift-cards .cards .heading-wrapper {
  overflow-wrap: anywhere;
}

.hero {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}
.hero h1 {
  margin: 0;
}
.hero .site.flex-wrapper {
  gap: 0;
  align-items: center;
}
.hero .messaging {
  padding: 0 4rem 0 0;
  min-width: 50%;
  max-width: 60%;
}
.hero .messaging li {
  margin-bottom: 1rem;
  font-size: 1rem;
}
.hero .messaging .project-meta small {
  font-size: 1rem;
}
.hero .messaging .project-meta small:not(:last-of-type) {
  padding-right: 1rem;
  margin-right: 1rem;
  border-right: thin solid;
}
.hero img {
  margin: 0;
  -o-object-fit: cover;
     object-fit: cover;
  flex-grow: 2;
  max-width: 50%;
}
.hero p,
.hero .paragraph-wrapper {
  font-size: 1.25rem;
}
.hero.topic-detail {
  padding: 0;
}
.hero.topic-detail .site.flex-wrapper {
  align-items: stretch;
  justify-content: space-between;
}
.hero.topic-detail .messaging {
  padding: 1rem 2rem 1rem 0;
  align-self: center;
  min-width: unset;
  max-width: unset;
  flex-grow: 2;
}
.hero.topic-detail .messaging p,
.hero.topic-detail .messaging .paragraph-wrapper {
  font-size: 1.25rem;
}
.hero.topic-detail .messaging + img {
  margin: 0;
  max-width: 30%;
}
.hero.article .messaging {
  min-width: 60%;
  max-width: 70%;
}
.hero.article img {
  max-width: 30%;
  margin: 0;
}
.hero.article .article-meta small {
  font-size: 1rem;
}
.hero.article .article-meta small:not(:last-of-type) {
  padding-right: 1rem;
  margin-right: 1rem;
  border-right: thin solid;
}
.hero.product-update .messaging {
  padding: 0 2rem 0 0;
}
.hero.product-update .messaging strong {
  text-transform: uppercase;
  display: block;
  margin-bottom: 0.5rem;
}
.hero.spotlight {
  background-image: var(--background);
  margin-top: 0.1rem;
}
.hero.spotlight.background-lt-grey .messaging h2, .hero.spotlight.background-lt-white .messaging h2 {
  color: #000000;
}
.hero.spotlight.background-lt-grey .messaging h3, .hero.spotlight.background-lt-grey .messaging .cards .heading-wrapper, .cards .hero.spotlight.background-lt-grey .messaging .heading-wrapper, .hero.spotlight.background-lt-white .messaging h3, .hero.spotlight.background-lt-white .messaging .cards .heading-wrapper, .cards .hero.spotlight.background-lt-white .messaging .heading-wrapper {
  color: #000000;
}
@media (min-width: 67.5rem) {
  .hero.spotlight {
    display: block;
  }
}
@media (max-width: 67.5rem) {
  .hero.spotlight {
    display: none;
  }
}
.hero.spotlight .messaging {
  padding: 0 2rem 0 1rem;
}
.hero.spotlight .messaging .paragraph-wrapper {
  font-size: 1.25rem;
}
.hero.spotlight .messaging h2 {
  color: #ffffff;
  text-transform: uppercase;
  font-size: 0.8rem;
  margin-bottom: 2rem;
}
.hero.spotlight .messaging h2 span.tagpill {
  text-transform: capitalize;
  margin-right: 1rem;
  padding: 0.3rem 0.7rem 0.3rem;
  border-radius: 5rem;
  background-color: #CF5792;
  font-size: 0.7rem;
  color: #ffffff;
  vertical-align: text-top;
}
.hero.spotlight .messaging h3, .hero.spotlight .messaging .cards .heading-wrapper, .cards .hero.spotlight .messaging .heading-wrapper {
  color: #ffffff;
  line-height: 1.1;
  font-size: 1.9rem;
}
.hero.spotlight .messaging p,
.hero.spotlight .messaging .paragraph-wrapper {
  margin-bottom: 0;
}
.hero.spotlight .messaging .call-to-action {
  padding: 2rem 0 1rem;
}
@media (min-width: 67.5rem) {
  .hero.spotlight .messaging {
    max-width: 50%;
  }
}
.hero.spotlight img {
  margin: 0 auto;
}
.hero.spotlight .messaging + img {
  max-width: 48%;
}
.hero.spotlight-mobile {
  background-image: var(--background);
  margin-top: 0.1rem;
}
.hero.spotlight-mobile.background-lt-grey .messaging h2, .hero.spotlight-mobile.background-lt-white .messaging h2 {
  color: #000000;
}
.hero.spotlight-mobile.background-lt-grey .messaging h3, .hero.spotlight-mobile.background-lt-grey .messaging .cards .heading-wrapper, .cards .hero.spotlight-mobile.background-lt-grey .messaging .heading-wrapper, .hero.spotlight-mobile.background-lt-white .messaging h3, .hero.spotlight-mobile.background-lt-white .messaging .cards .heading-wrapper, .cards .hero.spotlight-mobile.background-lt-white .messaging .heading-wrapper {
  color: #000000;
}
@media (max-width: 67.5rem) {
  .hero.spotlight-mobile {
    display: block;
  }
}
@media (min-width: 67.5rem) {
  .hero.spotlight-mobile {
    display: none;
  }
}
.hero.spotlight-mobile .messaging .paragraph-wrapper {
  font-size: 1.25rem;
}
.hero.spotlight-mobile .messaging h2 {
  color: #ffffff;
  text-transform: uppercase;
  font-size: 0.8rem;
  margin-bottom: 2rem;
}
.hero.spotlight-mobile .messaging h2 span.tagpill {
  text-transform: capitalize;
  margin-right: 1rem;
  padding: 0.3rem 0.7rem 0.3rem;
  border-radius: 5rem;
  background-color: #CF5792;
  font-size: 0.7rem;
  color: #ffffff;
  vertical-align: text-top;
}
.hero.spotlight-mobile .messaging h3, .hero.spotlight-mobile .messaging .cards .heading-wrapper, .cards .hero.spotlight-mobile .messaging .heading-wrapper {
  line-height: 1.2;
  font-size: 1.9rem;
  color: #ffffff;
}
@media (min-width: 67.5rem) {
  .hero.spotlight-mobile .messaging {
    max-width: 50%;
  }
}
.hero.spotlight-mobile .messaging img {
  max-width: 50%;
}
@media (max-width: 67.5rem) {
  .hero.spotlight-mobile .messaging img {
    display: block;
    max-width: 100%;
  }
}
.hero.spotlight-mobile .messaging .call-to-action {
  padding: 2rem 0 1rem 0;
}

.hub-header {
  padding: 1.25rem 1.875rem;
  background-image: linear-gradient(45deg, rgba(0, 53, 142, 0.75), rgba(0, 53, 142, 0.55)), url(../assets/hub-background.png);
  background-size: cover;
  background-repeat: no-repeat;
}
.hub-header .site.flex-wrapper {
  display: flex;
}
.hub-header h1 {
  display: inline-flex;
  color: #ffffff;
  font-size: 1.875rem;
  margin: 0;
}
.hub-header a {
  margin-left: auto;
  color: #ffffff;
  text-decoration: underline;
  align-self: center;
}
.hub-header a span.material-icons {
  font-size: 0.9rem;
}
.hub-header a:hover {
  color: #d8ebef;
}

.link-list {
  list-style: none;
  padding: 0;
}
.link-list li {
  padding-bottom: 1rem;
}
.link-list .material-icons {
  font-size: 0.9rem;
  vertical-align: middle;
}

.loader {
  position: relative;
  width: 100%;
  text-align: center;
  margin-bottom: 2rem;
  transform: translate3d(0, 100%, 0);
}

.dot {
  width: 0.7rem;
  height: 0.7rem;
  margin: 0.25rem;
  background: #002D61;
  border-radius: 100%;
  display: inline-block;
  animation: slide 1s infinite;
}

.dot:nth-child(1) {
  animation-delay: 0.1s;
  background: #00358e;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
  background: #A4C8F0;
}

.dot:nth-child(3) {
  animation-delay: 0.3s;
  background: #2A7DE1;
}

.dot:nth-child(4) {
  animation-delay: 0.4s;
  background: #d8ebef;
}

.dot:nth-child(5) {
  animation-delay: 0.5s;
  background: #1D6AC9;
}
@keyframes slide {
  0% {
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.75);
  }
  100% {
    transform: scale(1);
  }
}
.newsfeed article {
  padding-bottom: 1rem;
}
.newsfeed article time {
  font-size: 0.875rem;
}
.newsfeed article address {
  display: inline-block;
  font-style: normal;
  font-weight: 700;
  padding-right: 0.5rem;
}
.newsfeed article span {
  font-size: 0.8rem;
}
.newsfeed.panel .date-selector {
  padding-bottom: 1rem;
  margin-bottom: 2rem;
  border-bottom: thin solid #bbbbbb;
}
.newsfeed.panel .date-selector * {
  vertical-align: middle;
  font-size: 1.25rem;
}
.newsfeed.panel .date-selector time {
  margin: 0 1rem;
}
.newsfeed.panel .date-selector span.material-icons {
  font-size: 2.5rem;
}
.newsfeed.panel .flex-wrapper {
  flex-wrap: wrap;
  gap: 1rem 2rem;
  align-content: flex-start;
  align-items: flex-start;
}
.newsfeed.panel .flex-wrapper article {
  width: 48%;
  font-size: 1.2rem;
}

.pills a, .pills button {
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  margin: 0.25rem;
  border-radius: 1.5rem;
}
.pills a:hover, .pills button:hover {
  color: #002d61;
  border-color: #002D61;
}
.pills.removable {
  display: flex;
  flex-wrap: wrap;
}
.pills.removable strong {
  color: #3f3f3f;
  font-size: 0.95rem;
  font-weight: 400;
  padding: 0.25rem 0.5rem;
  margin: 0.25rem;
  border-radius: 1.5rem;
  border: thin solid #bbbbbb;
  /* white-space: nowrap; */
  display: flex;
}
.pills.removable strong a {
  padding: 0 0.25rem 0 1rem;
  margin: 0;
}
.pills.removable strong a span.material-icons {
  vertical-align: middle;
  font-size: 1rem;
}
.pills.removable strong a.clear-all {
  padding-left: 2rem;
}

section.poll {
  border-radius: 0.25rem;
  border: thin solid #002D61;
  position: relative;
}
section.poll span.material-icons {
  position: absolute;
  right: 1rem;
  top: 1rem;
}
section.poll small {
  font-weight: 600;
}
section.poll h2 {
  font-size: 1.5rem;
  line-height: 1.3;
  margin-top: 0.5rem;
}
section.poll .call-to-action {
  padding-bottom: 0;
}
section.poll .call-to-action a.primary {
  display: block;
  width: -moz-fit-content;
  width: fit-content;
  font-weight: 500;
  padding: 0.25rem 1rem;
  font-size: 0.9rem;
}

.recent-insights h2 {
  margin-top: 0;
}
.recent-insights .cards {
  flex-direction: column;
  gap: 0;
  margin-left: 0;
}
.recent-insights .cards article.card {
  padding: 0;
  flex-direction: column;
}
.recent-insights .cards article.card h3, .recent-insights .cards article.card .heading-wrapper {
  margin: 0.5rem 0;
}
.recent-insights .cards article.card:hover {
  box-shadow: unset;
}

.results-listing .cards {
  flex-direction: column;
  gap: 0;
  margin-left: 0;
}
.results-listing .cards .card {
  border-bottom: thin solid #efefef;
  margin-bottom: 0.25rem;
  padding-left: 0;
}
.results-listing .cards .card a {
  flex-direction: row;
  flex-wrap: nowrap;
  display: flex;
  gap: 1rem;
}
.results-listing .cards .card a span.material-icons {
  font-size: 0.9rem;
}
.results-listing .cards .card a p, .results-listing .cards .card a .paragraph-wrapper {
  color: #3f3f3f;
  font-size: 0.95rem;
  margin: 0;
  padding: 0.25rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.results-listing .cards .card img {
  height: 7.5rem;
  width: 7.5rem;
  -o-object-fit: cover;
     object-fit: cover;
  order: unset;
}
.results-listing .cards .card small {
  color: #737373;
}
.results-listing .cards .card small span + span {
  padding-left: 0.5rem;
  margin-left: 0.5rem;
  border-left: thin solid;
}
.results-listing .cards .card strong {
  padding: 0;
  font-size: 1rem;
}
.results-listing .cards .card strong b {
  color: #3f3f3f;
}
.results-listing .cards .card:hover {
  box-shadow: unset;
}
.results-listing .results-table.scrollable {
  position: relative;
  width: 100%;
  overflow: auto;
  z-index: 1;
  border: thin solid #efefef;
}
.results-listing .results-table.scrollable table {
  width: 100%;
  min-width: 1280px;
  border-collapse: separate;
  border-spacing: 0;
}
.results-listing .results-table.scrollable table thead th {
  position: sticky;
  top: 0;
}
.results-listing .results-table.scrollable table th:first-child {
  position: sticky;
  width: 5rem;
  left: 0;
  z-index: 2;
}
.results-listing .results-table.scrollable table td:nth-of-type(1) {
  position: sticky;
  background-color: #ffffff;
  left: 5rem;
  z-index: 2;
  box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
}
.results-listing .results-table.scrollable table tbody th {
  border-bottom: thin solid #efefef;
}
.results-listing .results-table.scrollable table tbody th:not(.active-sort) {
  background-color: #ffffff;
}
.results-listing .results-table.scrollable table thead th:first-child {
  z-index: 5;
}
.results-listing .results-table.scrollable table thead th:nth-of-type(2) {
  z-index: 5;
  left: 5rem;
  box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
}
@media (max-width: 50rem) {
  .results-listing .results-table.scrollable table thead th:nth-of-type(2) {
    width: 5rem;
  }
}
.results-listing .results-table.scrollable table tbody .active-sort {
  background-color: #fcfcfc;
}
.results-listing .results-table.scrollable::-webkit-scrollbar {
  -webkit-appearance: none;
}
.results-listing .results-table.scrollable::-webkit-scrollbar-thumb {
  border-radius: 0.25rem;
  background-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0.05rem rgba(255, 255, 255, 0.1);
}
.results-listing .page-results-wrapper {
  padding-top: 2rem;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  flex-wrap: wrap;
}
.results-listing .page-results-wrapper select {
  padding: 0.25rem 1rem 0.25rem 0.5rem;
}
.results-listing .page-results-wrapper nav {
  margin-left: auto;
}
.results-listing .page-results-wrapper nav ul {
  display: flex;
  align-items: center;
}
.results-listing .page-results-wrapper nav ul li {
  list-style: none;
  margin: 0 0.5rem;
  /* a.disabled {
      span {
          color: $lt-body-text;
          pointer-events:none;
      }  
  } */
}
.results-listing .page-results-wrapper nav ul li.page-number a.page-link {
  padding: 0 0.5rem;
  background-color: #ffffff;
  border: thin solid #004eaa;
}
.results-listing .page-results-wrapper nav ul li.page-number a.page-link:hover {
  background-color: #004eaa;
  color: #ffffff;
}
.results-listing .page-results-wrapper nav ul li.page-number a.page-link.active {
  background-color: #002d61;
  color: #ffffff;
  pointer-events: none;
}
.results-listing .page-results-wrapper .select-wrapper {
  display: inline-block;
}
.results-listing .page-results-wrapper .select-wrapper:after {
  top: 0.3rem;
  right: 0.3rem;
}

.sub-header {
  min-height: 1rem;
  padding: 0;
  margin-top: -0.5rem;
  background-color: #00358e;
}
.sub-header nav {
  padding: 0.75rem 1.875rem;
}
.sub-header nav a {
  color: #ffffff;
  font-size: 1.125rem;
  padding: 0;
  text-decoration: underline;
}
.sub-header nav a:hover {
  color: #d8ebef;
  border-bottom: none;
}
.sub-header strong {
  display: block;
  padding: 1rem 1.875rem;
  color: #ffffff;
  font-size: 1.125rem;
}

.search-box {
  min-width: 40%;
  position: relative;
  display: flex;
}
.search-box select {
  margin: 0 0.25rem;
}
.search-box input {
  background-color: transparent;
  border: thin solid #004eaa;
  flex-grow: 2;
  margin: 0 0.25rem;
}
.search-box input::-moz-placeholder {
  color: #999999;
}
.search-box input::placeholder {
  color: #999999;
}
.search-box button {
  padding: 0.1rem 1rem;
  border-radius: 0 0.25rem 0.25rem 0;
}

.share-save {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  justify-content: flex-start;
  position: relative;
}
.share-save div.popup {
  display: none;
}
.share-save a.secondary, .share-save a.tertiary {
  padding: 0.25rem 0.5rem;
  font-size: 0.85rem;
  width: 5rem;
  gap: 0.5rem;
}
.share-save a span.material-icons {
  font-size: 1.2rem;
  vertical-align: middle;
}
.share-save a#share.sharing + div.popup, .share-save a#bookmark.saved + div.popup {
  display: block;
  position: absolute;
  overflow: visible;
  color: #3f3f3f;
  z-index: 2;
  background-color: #ffffff;
  border-radius: 0.25rem;
  box-shadow: 0 0 0.75rem 0 rgba(0, 0, 0, 0.2);
  padding: 0.5rem 1rem;
}
.share-save a#share.sharing + div.popup a, .share-save a#bookmark.saved + div.popup a {
  font-size: 0.85rem;
}
.share-save a#share.sharing + div.popup a img, .share-save a#bookmark.saved + div.popup a img {
  width: 0.85rem;
  vertical-align: middle;
  margin-right: 0.75rem;
  opacity: 0.8;
  display: inline-block;
}
.share-save a#share.sharing + div.popup a.close, .share-save a#bookmark.saved + div.popup a.close {
  position: absolute;
  right: 0.25rem;
  top: 0;
}
.share-save a#share.sharing + div.popup a.close span.material-icons, .share-save a#bookmark.saved + div.popup a.close span.material-icons {
  font-size: 1rem;
}
.share-save a#share.sharing + div.popup p, .share-save a#share.sharing + div.popup .paragraph-wrapper, .share-save a#bookmark.saved + div.popup p, .share-save a#bookmark.saved + div.popup .paragraph-wrapper {
  font-size: 0.85rem;
  margin: 0.25rem;
}
.share-save a#share.sharing + div.popup::before, .share-save a#bookmark.saved + div.popup::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  bottom: 100%;
  left: 1.5em;
  border: 0.75rem solid transparent;
  border-top: none;
  border-bottom-color: #fff;
  filter: drop-shadow(0 -0.0625rem 0.0625rem rgba(0, 0, 0, 0.1));
}
.share-save a#share.sharing + div.popup.sharing, .share-save a#bookmark.saved + div.popup.sharing {
  padding: 1.25rem 1.25rem 0.5rem;
  top: 2.75rem;
}
.share-save a#share.sharing + div.popup.sharing a, .share-save a#bookmark.saved + div.popup.sharing a {
  display: block;
  line-height: 2.5;
}
.share-save a#share.sharing + div.popup.sharing a.close, .share-save a#bookmark.saved + div.popup.sharing a.close {
  line-height: 1.5;
}
.share-save a#share.sharing + div.popup.saved, .share-save a#bookmark.saved + div.popup.saved {
  top: 2.75rem;
  right: -2rem;
}
.share-save a#bookmark span.saved {
  display: none;
}
.share-save a#bookmark.saved span.save {
  display: none;
}
.share-save a#bookmark.saved span.saved {
  display: inline-flex;
}

.status-tracker {
  position: relative;
  display: flex;
  margin: 1.25rem 0;
}
.status-tracker .step {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 23%;
}
@media (max-width: 48rem) {
  .status-tracker .step {
    font-size: 0.75rem;
  }
}
.status-tracker .step::before {
  position: absolute;
  content: "";
  border-bottom: 0.188rem solid #efefef;
  width: 100%;
  top: 0.5rem;
  left: 0;
  z-index: 2;
}
.status-tracker .step::after {
  position: absolute;
  content: "";
  width: 100%;
  top: 0.5rem;
  left: 0;
  z-index: 2;
}
.status-tracker .step .step-counter {
  position: relative;
  z-index: 5;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background: #ffffff;
  border: thin solid #bbbbbb;
  margin-bottom: 0.375rem;
}
.status-tracker .step .step-name {
  font-size: 0.9rem;
  font-weight: 700;
  margin-top: 0.5rem;
  text-align: center;
}
.status-tracker .step.completed .step-counter {
  border: 2px solid #3f3f3f;
  background-color: #3f3f3f;
}
.status-tracker .step.completed .step-counter .material-icons {
  color: #ffffff;
  font-size: 0.95rem;
  font-weight: 700;
}
.status-tracker .step.completed .step-dt {
  color: #666666;
  display: block;
  font-size: 0.8rem;
  font-weight: 500;
}
.status-tracker .step.completed::before {
  border-bottom: none;
}
.status-tracker .step.completed::after {
  position: absolute;
  content: "";
  border-bottom: 0.188rem solid #3f3f3f;
  width: 100%;
  top: 0.5rem;
  left: 0;
  z-index: 3;
}
.status-tracker .step:first-of-type {
  align-items: flex-start;
  width: 15%;
}
.status-tracker .step:first-of-type .step-name {
  text-align: left;
}
.status-tracker .step:last-of-type {
  align-items: flex-end;
}
.status-tracker .step:last-of-type .step-name {
  text-align: right;
}

table {
  width: 100%;
  font-size: 0.9rem;
}
table tr {
  vertical-align: text-top;
}
table th {
  text-align: left;
  font-weight: 500;
  padding: 0.5rem;
  background-color: #f8f8f8;
}
table th span.material-icons {
  vertical-align: middle;
  font-size: 0.85rem;
}
table th[aria-sort] {
  background-color: #f3f3f3;
}
table td {
  padding: 1rem 0.5rem;
  border-bottom: thin solid #efefef;
}
table td span.material-icons {
  font-size: 1.2rem;
  vertical-align: middle;
}
table td.active-sort {
  background-color: #fcfcfc;
}
table.library-results th.title {
  width: 50%;
}
table.library-results th.title input {
  width: 100%;
}
table.library-results th.action {
  width: 5rem;
}

.tabs {
  width: 100%;
}
.tabs small {
  display: block;
  font-size: 0.75rem;
  font-weight: 400;
  color: #3f3f3f;
}
.tabs .nav-wrapper {
  width: 100%;
}
.tabs .tabbed {
  border-bottom: thin dotted #bbbbbb;
  align-items: flex-end;
}
.tabs .tabbed nav {
  padding: 1.5rem 0 0 0;
  display: flex;
  gap: 0.5rem;
}
.tabs .tabbed nav .tab {
  padding: 1rem 1.5rem;
  background-color: #f7fbfe;
  font-size: 0.9rem;
  font-weight: 700;
  border-radius: 0.25rem;
  text-align: center;
  flex-grow: 1;
}
.tabs .tabbed nav .tab.active {
  background-color: white;
  border: thin solid #dddddd;
  border-radius: 0;
  border-top: 0.2rem solid;
  border-bottom: none;
  color: #3f3f3f;
  margin-bottom: -0.1rem;
}
.tabs .tabbed.no-dotted-bottom-border {
  border-bottom: none;
}
.tabs .tabbed.dynamic-tab {
  overflow-x: hidden; /*hide the radio inputs */
  margin: 1rem 0 4rem;
  border-bottom: none;
}
.tabs .tabbed.dynamic-tab label {
  padding: 1rem 0.2rem;
  font-size: 0.9rem;
  cursor: pointer;
  color: #004eaa;
  margin: 0 2rem 0 0;
  display: inline-block;
}
.tabs .tabbed.dynamic-tab label:has(input:checked) {
  color: #000000;
  font-weight: 700;
  border-bottom: medium solid #000000;
}
.tabs .tabbed.dynamic-tab label:nth-of-type(1):has(input:checked) ~ .sub-tab-content:nth-of-type(1),
.tabs .tabbed.dynamic-tab label:nth-of-type(2):has(input:checked) ~ .sub-tab-content:nth-of-type(2),
.tabs .tabbed.dynamic-tab label:nth-of-type(3):has(input:checked) ~ .sub-tab-content:nth-of-type(3) {
  display: block;
}
.tabs .tabbed.dynamic-tab .sub-tab-content {
  display: none;
}
@media (max-width: 27rem) {
  .tabs .tabbed.dynamic-tab label {
    margin: 0;
  }
}
.tabs .tabbed [type=radio] {
  /* hiding the inputs */
  display: none;
}
.tabs.underline .tabbed {
  border-bottom: none;
}
.tabs.underline .tabbed nav .tab.active {
  border: none;
  border-bottom: 0.2rem solid;
}
.tabs.no-background .tabbed nav .tab {
  background-color: transparent;
}

.timeline {
  margin-top: 2rem;
}
.timeline .flex-wrapper {
  align-items: flex-start;
  justify-content: stretch;
}
.timeline .flex-wrapper time {
  font-size: 0.9rem;
  font-weight: 700;
  line-height: 1;
  width: 8rem;
  text-align: right;
  margin-right: -0.5rem;
  background-color: white;
  z-index: 1;
}
.timeline .flex-wrapper time span {
  display: inline-flex;
  position: relative;
  vertical-align: middle;
  z-index: 1;
  height: 1rem;
  width: 1rem;
  background-color: #ffffff;
  border: thin solid #bbbbbb;
  border-radius: 50%;
  margin-left: 0.5rem;
}
.timeline .flex-wrapper time span.current {
  background-color: #000000;
  border-color: #000000;
}
.timeline .flex-wrapper div {
  border-left: thin solid #bbbbbb;
  padding: 0 0 1rem 1rem;
  width: calc(100% - 8rem);
}
.timeline .flex-wrapper div h3, .timeline .flex-wrapper div .cards .heading-wrapper, .cards .timeline .flex-wrapper div .heading-wrapper {
  font-weight: 400;
  line-height: 1;
  margin: 0;
}
.timeline .flex-wrapper div p, .timeline .flex-wrapper div .paragraph-wrapper {
  margin: 0.5rem 0;
}
.timeline .flex-wrapper div p:last-of-type, .timeline .flex-wrapper div .paragraph-wrapper:last-of-type {
  margin-bottom: 1rem;
}

.verisk-activity .link-list li:not(:first-of-type) {
  padding-top: 1rem;
}
.verisk-activity .cards {
  flex-wrap: wrap;
  justify-content: flex-start;
}
.verisk-activity .agenda-link-group {
  font-size: 0.9rem;
  padding-top: 0.5rem;
}
.verisk-activity .agenda-link-group a:not(:first-of-type) {
  padding-left: 0.5rem;
  margin-left: 0.25rem;
  border-left: thin solid #bbbbbb;
}
.verisk-activity .agenda-link-group span.material-icons {
  font-size: 0.8rem;
  vertical-align: middle;
}

.widget {
  padding: 1rem;
  position: relative;
  height: 20rem;
}
.widget h2 {
  margin-top: 0;
  line-height: 1.8;
  font-weight: 300;
}
.widget .options {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}
.widget .options span {
  font-size: 2rem;
}
.widget.two-thirds {
  width: 68%;
}
.widget.full {
  width: 100%;
}
.widget.half {
  width: 50%;
}
.widget.thirds {
  width: 33%;
}

.signed-in header a.primary {
  display: none;
}
.signed-in .site {
  max-width: unset;
}
.signed-in aside.workspace-nav, .signed-in .pattern-library aside.brand-patterns, .pattern-library .signed-in aside.brand-patterns {
  display: inline-flex;
}
.signed-in .main-content-wrapper .toggle {
  display: block;
  text-decoration: none;
  color: #004eaa;
  background-color: white;
  box-shadow: 0 0 0.75rem 0 rgba(0, 0, 0, 0.2);
  transform: rotate(-90deg);
  position: fixed;
  top: 13rem;
  left: 15.15rem;
  transition: all 0.5s ease-out;
  z-index: 1;
  cursor: pointer;
}
.signed-in .main-content-wrapper .toggle span.material-icons {
  font-size: 1.5rem;
  display: block;
}
.signed-in .main-content-wrapper .toggle span.material-icons.more {
  display: none;
}
.signed-in .main-content-wrapper .toggle:hover {
  color: #002d61;
}
.signed-in .main-content-wrapper #slide:checked ~ aside.workspace-nav, .signed-in .main-content-wrapper .pattern-library #slide:checked ~ aside.brand-patterns, .pattern-library .signed-in .main-content-wrapper #slide:checked ~ aside.brand-patterns {
  margin-left: -15.5rem;
}
.signed-in .main-content-wrapper #slide:checked + .toggle {
  left: 0;
}
.signed-in .main-content-wrapper #slide:checked + .toggle span.material-icons.less {
  display: none;
}
.signed-in .main-content-wrapper #slide:checked + .toggle span.material-icons.more {
  display: block;
}
.signed-in .main-content-wrapper #slide {
  display: none;
}
.signed-in .account-alerts-support {
  display: flex;
}
.signed-in main .hero.topic-detail {
  padding-left: 1.875rem;
}
.signed-in main .site.flex-wrapper .filter {
  margin-left: 1rem;
}
.signed-in main .site.flex-wrapper .content-wrapper {
  padding: 1.875rem;
}
.signed-in main .site.flex-wrapper p.no-results, .signed-in main .site.flex-wrapper .no-results.paragraph-wrapper {
  margin-left: 1.875rem;
}

.pattern-library header {
  width: 100%;
  background-color: #ffffff;
}
.pattern-library header .top-header .logo img {
  height: 2rem;
}
.pattern-library header .top-header h1 {
  text-align: center;
  font-weight: 400;
  margin-top: 0.5rem;
}
.pattern-library header.demo-signed-in, .pattern-library header.demo-guest {
  position: unset;
  margin-bottom: 15rem;
}
.pattern-library header.demo-guest .account-alerts-support {
  display: none;
}
.pattern-library header.demo-guest nav + .primary {
  display: inline-flex;
  align-self: center;
}
.pattern-library aside.brand-patterns {
  position: sticky;
  top: 0;
  max-height: 48rem;
  overflow-y: hidden;
  display: inline-block;
  border-right: none;
  padding-top: 0;
}
.pattern-library aside.brand-patterns nav {
  width: 100%;
  padding-right: 1rem;
}
.pattern-library aside.brand-patterns ul {
  padding-left: 1rem;
}
.pattern-library aside.brand-patterns a {
  line-height: 1.8;
  padding: 0.25rem 0.2rem;
}
.pattern-library aside.brand-patterns h2 {
  margin-top: 1rem;
}
.pattern-library aside.brand-patterns:hover {
  overflow-y: auto;
}
.pattern-library main.patterns {
  border-left: 0.25rem double #efefef;
  scroll-behavior: smooth;
  padding: 1.875rem;
}
.pattern-library main.patterns .intro {
  margin: 1rem 0;
}
.pattern-library main.patterns pre {
  text-align: left;
  white-space: pre-line;
  padding: 1rem;
  background-color: #f8f8f8;
  border: thin solid #bbbbbb;
}
.pattern-library main.patterns section h2 {
  font-size: 2.75rem;
  font-weight: 400;
  margin: 2rem 0;
  padding: 6rem 0 1rem;
  border-top: 1rem dotted #efefef;
}
.pattern-library main.patterns section h3, .pattern-library main.patterns section .cards .heading-wrapper, .cards .pattern-library main.patterns section .heading-wrapper {
  font-size: 2rem;
  font-weight: 400;
  width: 100%;
  margin-top: 1rem;
}
.pattern-library main.patterns section.typography .flex-wrapper, .pattern-library main.patterns section.iconography .flex-wrapper, .pattern-library main.patterns section.header .flex-wrapper, .pattern-library main.patterns section.hero-component .flex-wrapper, .pattern-library main.patterns section.interactive-elements .flex-wrapper {
  flex-direction: column;
  align-items: flex-start;
}
.pattern-library main.patterns section.typography .flex-wrapper h4, .pattern-library main.patterns section.iconography .flex-wrapper h4, .pattern-library main.patterns section.header .flex-wrapper h4, .pattern-library main.patterns section.hero-component .flex-wrapper h4, .pattern-library main.patterns section.interactive-elements .flex-wrapper h4 {
  font-size: 1.5rem;
  font-weight: 400;
}
.pattern-library main.patterns section.typography .flex-wrapper.cards, .pattern-library main.patterns section.iconography .flex-wrapper.cards, .pattern-library main.patterns section.header .flex-wrapper.cards, .pattern-library main.patterns section.hero-component .flex-wrapper.cards, .pattern-library main.patterns section.interactive-elements .flex-wrapper.cards {
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1rem 2rem;
}
.pattern-library main.patterns section.typography .flex-wrapper.cards .card, .pattern-library main.patterns section.iconography .flex-wrapper.cards .card, .pattern-library main.patterns section.header .flex-wrapper.cards .card, .pattern-library main.patterns section.hero-component .flex-wrapper.cards .card, .pattern-library main.patterns section.interactive-elements .flex-wrapper.cards .card {
  padding: 1rem;
  min-width: 15rem;
  text-align: center;
}
.pattern-library main.patterns section.typography .flex-wrapper.cards .material-icons, .pattern-library main.patterns section.iconography .flex-wrapper.cards .material-icons, .pattern-library main.patterns section.header .flex-wrapper.cards .material-icons, .pattern-library main.patterns section.hero-component .flex-wrapper.cards .material-icons, .pattern-library main.patterns section.interactive-elements .flex-wrapper.cards .material-icons {
  font-size: 3rem;
}
.pattern-library main.patterns section.typography pre, .pattern-library main.patterns section.iconography pre, .pattern-library main.patterns section.header pre, .pattern-library main.patterns section.hero-component pre, .pattern-library main.patterns section.interactive-elements pre {
  white-space: pre-wrap; /* css-3 */
  white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
}
.pattern-library main.patterns section.typography pre + h2, .pattern-library main.patterns section.iconography pre + h2, .pattern-library main.patterns section.header pre + h2, .pattern-library main.patterns section.hero-component pre + h2, .pattern-library main.patterns section.interactive-elements pre + h2 {
  border: none;
  padding: 0;
  margin: 1rem 0;
}
.pattern-library main.patterns section.typography h1, .pattern-library main.patterns section.typography h2, .pattern-library main.patterns section.typography h3, .pattern-library main.patterns section.typography .cards .heading-wrapper, .cards .pattern-library main.patterns section.typography .heading-wrapper, .pattern-library main.patterns section.typography h4, .pattern-library main.patterns section.typography h5, .pattern-library main.patterns section.typography h6 {
  margin: 2rem 0;
}
.pattern-library main.patterns section.typography .flex-wrapper:first-of-type h3:first-of-type, .pattern-library main.patterns section.typography .flex-wrapper:first-of-type .cards .heading-wrapper:first-of-type, .cards .pattern-library main.patterns section.typography .flex-wrapper:first-of-type .heading-wrapper:first-of-type {
  margin-top: 0;
}
.pattern-library main.patterns section.colors p, .pattern-library main.patterns section.colors .paragraph-wrapper {
  margin: 0.25rem 0 0;
}
.pattern-library main.patterns section.colors p:first-of-type, .pattern-library main.patterns section.colors .paragraph-wrapper:first-of-type {
  margin-top: 0.5rem;
}
.pattern-library main.patterns section.colors h2 {
  border-top: none;
  padding-top: 0;
  margin-top: 0;
}
.pattern-library main.patterns section.header .section-child, .pattern-library main.patterns section.hero-component .section-child {
  margin-bottom: 2rem;
}
.pattern-library main.patterns section.header .section-child p, .pattern-library main.patterns section.header .section-child .paragraph-wrapper, .pattern-library main.patterns section.hero-component .section-child p, .pattern-library main.patterns section.hero-component .section-child .paragraph-wrapper {
  margin-top: 2rem;
}
.pattern-library main.patterns section.header .section-child img, .pattern-library main.patterns section.hero-component .section-child img {
  width: 100%;
  box-shadow: 0 0 0.15rem 0 #bbbbbb;
}
.pattern-library main.patterns section.header .section-child.mobile-view img, .pattern-library main.patterns section.hero-component .section-child.mobile-view img {
  width: 50%;
  display: block;
}
.pattern-library main.patterns section.interactive-elements aside.workspace-nav, .pattern-library main.patterns section.interactive-elements aside.brand-patterns {
  box-shadow: 0 0 0.15rem 0 #bbbbbb;
  border: none;
  min-height: 25rem;
  display: inline-flex;
}
.pattern-library main.patterns section.interactive-elements .call-to-action {
  text-align: left;
}
.pattern-library main.patterns section.interactive-elements .glossary .material-icons {
  vertical-align: middle;
}
.pattern-library main.patterns .cards {
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 2rem;
  margin-left: 0;
}
.pattern-library main.patterns .cards .card .color {
  height: 5rem;
  width: 12rem;
  border: thin solid #bbbbbb;
}
.pattern-library main.patterns .cards .card .color.black {
  background-color: #000000;
}
.pattern-library main.patterns .cards .card .color.black-2 {
  background-color: rgba(0, 0, 0, 0.8);
}
.pattern-library main.patterns .cards .card .color.grey-1 {
  background-color: #3f3f3f;
}
.pattern-library main.patterns .cards .card .color.grey-2 {
  background-color: #666666;
}
.pattern-library main.patterns .cards .card .color.grey-3 {
  background-color: #999999;
}
.pattern-library main.patterns .cards .card .color.grey-4 {
  background-color: #bbbbbb;
}
.pattern-library main.patterns .cards .card .color.grey-5 {
  background-color: #dddddd;
}
.pattern-library main.patterns .cards .card .color.grey-6 {
  background-color: #eeeeee;
}
.pattern-library main.patterns .cards .card .color.grey-7 {
  background-color: #efefef;
}
.pattern-library main.patterns .cards .card .color.grey-8 {
  background-color: #f2f2f2;
}
.pattern-library main.patterns .cards .card .color.grey-9 {
  background-color: rgba(102, 102, 102, 0.2);
}
.pattern-library main.patterns .cards .card .color.white {
  background-color: #ffffff;
}
.pattern-library main.patterns .cards .card .color.blue-1 {
  background-color: #00358E;
}
.pattern-library main.patterns .cards .card .color.blue-2 {
  background-color: #004EAA;
}
.pattern-library main.patterns .cards .card .color.blue-3 {
  background-color: #1D6AC9;
}
.pattern-library main.patterns .cards .card .color.blue-4 {
  background-color: #002D61;
}
.pattern-library main.patterns .cards .card .color.blue-5 {
  background-color: #d8ebef;
}
.pattern-library main.patterns .cards .card .color.blue-6 {
  background-color: #f7fbfe;
}
.pattern-library main.patterns .cards .card .color.blue-7 {
  background-color: #A4C8F0;
}
.pattern-library main.patterns .cards .card .color.blue-8 {
  background-color: #2A7DE1;
}
.pattern-library main.patterns .cards .card .color.red-1 {
  background-color: #d40019;
}
.pattern-library main.patterns .cards .card .color.red-2 {
  background-color: #d10077;
}
.pattern-library main.patterns .cards .card .color.gold-1 {
  background-color: #ffc600;
}
.pattern-library main.patterns .cards .card .color.gold-2 {
  background-color: #f4af2d;
}
.pattern-library main.patterns .cards .card .color.gold-3 {
  background-color: #ffe899;
}
.pattern-library main.patterns .cards .card .color.green-1 {
  background-color: #6abf4b;
}
.pattern-library main.patterns .cards .card .color.green-2 {
  background-color: #006a35;
}
.pattern-library main.patterns h4 + .sub-header {
  margin-bottom: 3rem;
}

@media (max-width: 67.5rem) {
  .desktop-view {
    display: none;
  }
  .mobile-view {
    display: flex;
  }
  header nav .non-mobile {
    display: none;
  }
  header nav .mobile-navigation {
    display: inline-flex;
  }
  header nav .mobile-navigation a {
    font-size: 2rem;
  }
  header nav .mobile-navigation a.material-icons {
    padding: 1.5rem 0.75rem 1.5rem 0;
  }
  .newsfeed.panel .flex-wrapper article {
    width: 100%;
  }
  section {
    padding: 1.875rem;
    /* &.explore-lobs {
        .cards {
            justify-content: center;
            .card {
                max-width: 25%;
                min-width: 15rem;

                 img {
                    height: 10rem;
                }

                strong {
                    padding-top: .75rem;
                }
            }
        }
    } */
  }
  section.hero .messaging, section.hero.topic-detail .messaging, section.ei-weekly-digest .messaging, section.mileage-confirm .messaging, section.paas-monthly-newsletter .messaging {
    max-width: 100%;
    width: 100%;
    padding: 0;
  }
  section.hero img, section.hero.topic-detail img, section.ei-weekly-digest img, section.mileage-confirm img, section.paas-monthly-newsletter img {
    display: none;
  }
  section.hero .call-to-action, section.hero.topic-detail .call-to-action, section.ei-weekly-digest .call-to-action, section.mileage-confirm .call-to-action, section.paas-monthly-newsletter .call-to-action {
    text-align: center;
  }
  section.hero.topic-detail {
    padding: 1.875rem;
  }
  section .box-container {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  section .box-container .box {
    width: auto;
    display: block;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards .card {
    /* width: auto; */
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards.min-width-cards {
    flex-direction: column;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards.min-width-cards .card {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: unset;
    max-width: unset;
    padding-right: 0;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards.min-width-cards .card img {
    width: 15rem;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards.min-width-cards .card h3, section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards.min-width-cards .card .heading-wrapper {
    flex-grow: 2;
  }
  section.explore-insights .cards, section.hover-lift-cards .cards {
    flex-direction: column;
    gap: 0;
  }
  section.explore-insights .cards article, section.hover-lift-cards .cards article {
    max-width: 100%;
    min-width: 100%;
    margin: 0 auto;
  }
  section.explore-insights .cards article img, section.hover-lift-cards .cards article img {
    height: 15rem;
  }
  section.explore-insights .call-to-action, section.hover-lift-cards .call-to-action {
    text-align: left;
  }
  section.all-topics-list {
    text-align: left;
  }
  section .carousel-cards {
    flex-wrap: nowrap;
    justify-content: space-between;
  }
  .signed-in .main-content-wrapper #slide, .signed-in .main-content-wrapper .toggle {
    display: none;
  }
  .signed-in aside.workspace-nav, .signed-in .pattern-library aside.brand-patterns, .pattern-library .signed-in aside.brand-patterns {
    display: none;
  }
  main.global-search .site.flex-wrapper {
    flex-direction: column;
  }
  main.global-search .site.flex-wrapper aside.filter {
    width: 100%;
    margin-left: 0;
  }
  main.global-search .site.flex-wrapper aside.filter section {
    width: 100%;
  }
  main.library .no-content.flex-wrapper {
    flex-direction: column;
    align-items: flex-start;
    padding-bottom: 10rem;
  }
  main.library .no-content.flex-wrapper .message {
    max-width: 100%;
  }
  main.library .no-content.flex-wrapper .image-wrapper {
    margin-top: 0;
  }
  footer .copyright-footer-links small {
    font-size: 0.75rem;
  }
  footer .copyright-footer-links small a {
    display: block;
  }
  footer .copyright-footer-links small a:not(:first-of-type) {
    border-left: 0;
  }
  main .site.flex-wrapper aside.filter, main .site.flex-wrapper aside.filter.thin {
    padding: 0;
  }
  main .site.flex-wrapper aside.filter h2.filter-toggle-mobile, main .site.flex-wrapper aside.filter.thin h2.filter-toggle-mobile {
    display: flex;
    align-items: flex-end;
    margin-bottom: 0;
    width: 100%;
    padding: 0 0.9375rem;
  }
  main .site.flex-wrapper aside.filter h2.filter-toggle-mobile span, main .site.flex-wrapper aside.filter.thin h2.filter-toggle-mobile span {
    display: inline-block;
    vertical-align: middle;
  }
  main .site.flex-wrapper aside.filter .link-list, main .site.flex-wrapper aside.filter.thin .link-list {
    padding: 0 0.8rem 0 0.9375rem;
  }
  main .site.flex-wrapper aside.filter .link-list-closed, main .site.flex-wrapper aside.filter.thin .link-list-closed {
    display: none;
    margin-top: 0;
  }
}
@media (max-width: 50rem) {
  header .top-header.flex-wrapper {
    display: block;
    position: relative;
  }
  header .top-header.flex-wrapper .logo {
    position: absolute;
    top: -0.5rem;
  }
  header .top-header.flex-wrapper .search-box {
    min-width: 100%;
    padding-top: 2rem;
  }
  header .top-header.flex-wrapper a:last-of-type {
    position: absolute;
    top: -0.5rem;
    right: 0;
    font-size: 0.85rem;
  }
  header nav.account-alerts-support .user-account .menu-dropdown .support {
    margin-right: 1.5rem;
  }
  header nav.account-alerts-support .user-account .menu-dropdown .account {
    margin-left: 0;
  }
  header nav.account-alerts-support .user-account .menu-dropdown .account a {
    margin-left: 0;
    padding: 1.5rem 0 1.5rem 1rem;
  }
  header nav.account-alerts-support .user-account .menu-dropdown .dropdown-content {
    width: auto;
  }
  main .site.flex-wrapper {
    flex-direction: column;
  }
  main .site.flex-wrapper .site.flex-wrapper aside, main .site.flex-wrapper .site.flex-wrapper aside.thin {
    width: 100%;
  }
  main .site.flex-wrapper .site.flex-wrapper aside section, main .site.flex-wrapper .site.flex-wrapper aside.thin section {
    width: 100%;
  }
  main .site.flex-wrapper .content-wrapper {
    max-width: 100%;
    width: 100%;
  }
  main .site.flex-wrapper aside {
    width: 100%;
  }
  main .site.flex-wrapper aside section {
    width: 100%;
  }
  section .flex-wrapper.with-cta {
    flex-direction: column;
  }
  section .flex-wrapper.with-cta .call-to-action {
    text-align: left;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards {
    margin-left: 0;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards .card {
    width: 100%;
    padding: 1rem 0;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards .card img {
    height: 10rem;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards .call-to-action {
    margin-bottom: 2rem;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) a.primary {
    display: block;
    width: -moz-fit-content;
    width: fit-content;
  }
  section.explore-insights h2, section.hover-lift-cards h2 {
    flex-direction: column;
  }
  section.explore-insights h2 .tabs, section.hover-lift-cards h2 .tabs {
    margin-left: 0;
  }
  section.explore-insights .cards article img, section.hover-lift-cards .cards article img {
    height: 10rem;
  }
  footer .site.flex-wrapper {
    flex-direction: column;
    gap: 0;
  }
  footer .site.flex-wrapper .copyright-footer-links {
    padding: 1rem 2rem;
  }
}
@media (max-width: 30rem) {
  header .top-header.flex-wrapper .search-box {
    display: flex;
    flex-direction: column;
  }
  header .top-header.flex-wrapper .search-box select, header .top-header.flex-wrapper .search-box input {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  header .top-header.flex-wrapper .search-box #mainSearch {
    padding: 0.4rem 0.6rem;
    border-radius: 0.25rem;
    width: 100%;
    margin-left: 0.2rem;
  }
  main.legislative-monitoring.hub .content-wrapper .lmon-table-view .results-listing .page-results-wrapper nav ul {
    padding: 0;
  }
  main.legislative-monitoring.hub .content-wrapper .lmon-table-view .results-listing .page-results-wrapper nav li:first-of-type {
    margin-left: 0;
  }
  main.legislative-monitoring.hub .content-wrapper .lmon-table-view .results-listing .page-results-wrapper nav li:last-child {
    margin-right: 0;
  }
}
@media (max-width: 40rem) {
  header .top-header.flex-wrapper .search-box .select-wrapper:after {
    top: 0.2rem;
  }
  header .top-header.flex-wrapper .search-box select, header .top-header.flex-wrapper .search-box input {
    font-size: 0.8rem;
  }
  header .top-header.flex-wrapper .search-box button {
    padding: 0.1rem 0.6rem;
    font-size: 0.9rem;
  }
  section {
    overflow-wrap: break-word;
  }
  section .bio {
    flex-direction: column;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics) .cards.min-width-cards .card {
    flex-direction: column;
    align-items: flex-start;
  }
  section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics) .cards.min-width-cards .card img {
    width: 100%;
  }
  main.global-search .site.flex-wrapper .content-wrapper {
    width: 100%;
  }
  main.global-search .site.flex-wrapper .content-wrapper h2 + .results-meta-sort.flex-wrapper {
    align-items: flex-start;
    width: 100%;
  }
  main.global-search .site.flex-wrapper .content-wrapper h2 + .results-meta-sort.flex-wrapper form {
    margin-left: 0;
    margin-top: 0.5rem;
  }
  main.global-search .site.flex-wrapper .content-wrapper .results-listing .cards {
    width: 100%;
    margin-left: 0;
  }
  main.global-search .site.flex-wrapper .content-wrapper .results-listing .cards .card img {
    display: none;
  }
  main.global-search .site.flex-wrapper .content-wrapper .results-listing .page-results-wrapper {
    flex-direction: column;
  }
  main.global-search .site.flex-wrapper .content-wrapper .results-listing .page-results-wrapper nav {
    margin-top: 1rem;
    margin-left: -1.5rem;
  }
  main.global-search .site.flex-wrapper .content-wrapper .results-listing .page-results-wrapper nav ul {
    padding-left: 0;
  }
  main.legislative-monitoring.search .site.flex-wrapper .content-wrapper .results-listing .page-results-wrapper {
    flex-direction: column;
  }
  main.legislative-monitoring.search .site.flex-wrapper .content-wrapper .results-listing .page-results-wrapper nav {
    margin-top: 1rem;
    margin-left: -4.5rem;
  }
}
@media (min-width: calc(92.5rem + 1px)) {
  body:not(.signed-in) main:not([class*=search]) .site.flex-wrapper .content-wrapper {
    padding-left: 0;
  }
}
@media (max-width: 92.5rem) {
  .timeline {
    border-left: thin solid #bbbbbb;
  }
  .timeline .flex-wrapper {
    flex-direction: column;
  }
  .timeline .flex-wrapper time {
    width: auto;
    margin-left: -0.5rem;
    padding-bottom: 0.5rem;
    background-color: transparent;
  }
  .timeline .flex-wrapper time span {
    margin-left: 0;
    float: left;
    margin-right: 0.5rem;
  }
  .timeline .flex-wrapper div {
    border-left: none;
    width: calc(100% - 1rem);
  }
  p.no-results, .no-results.paragraph-wrapper {
    margin-left: 1.875rem;
  }
}
@media (max-width: 92.5rem) {
  body:not(.signed-in) main.emerging-issues > .site.flex-wrapper {
    max-width: 80rem;
    margin: 0;
  }
}
@media (min-width: 67.5rem) and (max-width: 91.325rem) {
  body:not(.signed-in) main.global-search .site.flex-wrapper aside.filter {
    margin-left: 1rem;
  }
}
@media (max-width: 92.5rem) {
  body:not(.signed-in) .hero.topic-detail {
    padding-left: 1.875rem;
  }
}
@media (min-width: calc(92.5rem + 1px)) {
  body:not(.signed-in) .hero.topic-detail {
    padding-left: 0.5rem;
  }
}
@media (max-width: 93.75rem) {
  body:not(.signed-in) .hub-page.lob .hero.topic-detail {
    padding-left: 1.875rem;
  }
}

/* @media (min-width: 123.75rem) {

     .cards {
            .card {
                img {
                    height: 12rem;
                }
            }
        }
} */
main.decision-roadmap {
  font-family: "Roboto", sans-serif;
}
main.decision-roadmap .revision-wrapper {
  border-radius: 0.3125rem;
  border: 0.0625rem solid #BBBBBB;
  background-color: #FAFAFA;
  box-sizing: border-box;
  box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 rgba(0, 0, 0, 0.15);
  margin: 2.5rem 3% 2.5rem 3%;
}
main.decision-roadmap .revision-wrapper .revision-header {
  margin: 1.75rem 1.4375rem 0 1.4375rem;
}
@media (max-width: 42rem) {
  main.decision-roadmap .revision-wrapper .revision-header {
    flex-wrap: wrap;
  }
}
main.decision-roadmap .revision-wrapper .revision-header .revision-title {
  font-size: 1.5rem;
  white-space: nowrap;
  color: #3F3F3F;
}
main.decision-roadmap .revision-wrapper .revision-header .revision-title span {
  font-weight: 500;
}
main.decision-roadmap .revision-wrapper .revision-header .revision-title-vertical-line {
  width: 0.0625rem;
  height: 2.3125rem;
  background-color: #BBBBBB;
  margin: 0 0.875rem 0 1.125rem;
}
@media (max-width: 42rem) {
  main.decision-roadmap .revision-wrapper .revision-header .revision-title-vertical-line {
    display: none;
  }
}
main.decision-roadmap .revision-wrapper .revision-header .choose-state {
  font-size: 0.75rem;
  color: #3F3F3F;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper {
  -moz-column-gap: 0.8125rem;
       column-gap: 0.8125rem;
  row-gap: 0.625rem;
}
@media (max-width: 52rem) {
  main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper {
    flex-wrap: wrap;
  }
}
@media (max-width: 42rem) {
  main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper {
    flex-wrap: nowrap;
  }
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state {
  color: #004EAA;
  font-weight: 500;
  min-width: 11.25rem;
  border-bottom: 0.0625rem solid #004EAA;
  box-shadow: none;
  z-index: 2;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .select-state-placeholder {
  color: #004EAA;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-d7l1ni-option {
  background-color: #FFFFFF;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-d7l1ni-option:hover {
  color: #0F2C5E;
  cursor: pointer;
  background-color: #F0F6FA;
  font-weight: 500;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-tr4s17-option {
  background-color: #004EAA;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-1fdsijx-ValueContainer {
  padding: 0.125rem 0.25rem 0.125rem 0.25rem;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-1xc3v61-indicatorContainer,
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-15lsz6c-indicatorContainer {
  color: #004EAA;
  padding: 0.125rem 0.25rem 0.125rem 0.25rem;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-1xc3v61-indicatorContainer:hover,
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-15lsz6c-indicatorContainer:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-1gxfbx6-control,
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-tzexq5-control {
  background-color: transparent;
  cursor: pointer;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-1dimb5e-singleValue {
  z-index: 1;
  color: #004EAA;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-1dimb5e-singleValue:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-qbdosj-Input,
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-166bipr-Input {
  color: #004EAA;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-qbdosj-Input:hover,
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-166bipr-Input:hover {
  cursor: pointer;
  color: #0F2C5E;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-syji7d-Group {
  padding-top: 0;
  padding-bottom: 0;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-tjinjn-MenuList {
  max-height: 10.25rem;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-tjinjn-MenuList .css-syji7d-Group:nth-child(2) {
  border-top: 0.0625rem solid #BBBBBB;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-tjinjn-MenuList .css-nkt5xy-option {
  color: #999999;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-tjinjn-MenuList .css-i4bv87-MuiSvgIcon-root {
  font-size: 1rem;
  position: relative;
  top: 0.075rem;
  right: 0.0625rem;
  margin-right: 0.1875rem;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-9dakgz {
  max-height: 10.3rem;
  padding-top: 0;
  padding-bottom: 0;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-9dakgz .css-1s9izoc:nth-child(2) {
  border-top: 0.0625rem solid #BBBBBB;
  padding-top: 0;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-9dakgz .css-nkt5xy-option {
  color: #999999;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state .css-9dakgz .css-vubbuv {
  font-size: 1rem;
  position: relative;
  top: 0.07rem;
  right: 0.0625rem;
  margin-right: 0.1875rem;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state :hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state ::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state ::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .select-state ::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
main.decision-roadmap .revision-wrapper .revision-header .state-multistate-wrapper .multistate-text {
  color: #3F3F3F;
  font-size: 0.75rem;
  width: auto;
}
main.decision-roadmap .revision-wrapper .revision-header .lob-subscription {
  display: flex;
  align-items: center;
}
main.decision-roadmap .revision-wrapper .revision-header .lob-subscription .lob-subscription-icon {
  width: 1rem;
  height: 1.125rem;
  vertical-align: middle;
  padding-bottom: 0.1rem;
}
main.decision-roadmap .revision-wrapper .revision-footnote {
  border-radius: 0;
  box-sizing: border-box;
  margin: 0 3% 1rem 7.5%;
  color: #3F3F3F;
  font-size: 0.75rem;
}
main.decision-roadmap .revision-wrapper .content-wrapper {
  margin: 1.75rem 1.5rem 0.75rem 1.5rem;
  display: flex;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper {
    flex-wrap: wrap;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-filed {
  display: flex;
  justify-content: center;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-filed {
    justify-content: start;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective {
  display: flex;
  align-items: end;
  flex-direction: column;
  flex: 1;
  row-gap: 0.375rem;
  padding-top: 0.375rem;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective {
    flex-direction: row;
    gap: 0.3125rem;
    padding-top: 0;
  }
}
@media (min-width: 75rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective {
    padding-top: 0;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .not-filed-state {
  font-weight: 500;
  margin-right: 0.5625rem;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  align-items: center;
  text-align: end;
  width: 5.625rem;
  line-height: normal;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .not-filed-state {
    display: flex;
    gap: 0.25rem;
    margin: 0;
    padding-bottom: 0.375rem;
    width: auto;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .content-date {
  font-weight: 500;
  margin-right: 0.5625rem;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  align-items: center;
  text-align: end;
  line-height: normal;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .content-date {
    display: flex;
    gap: 0.25rem;
    margin: 0;
    padding-bottom: 0.375rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .content-effective {
  font-weight: 500;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .status-tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 0.0625rem dashed #000000;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .status-tooltip .tooltip-text {
  visibility: hidden;
  width: 12.25rem;
  background-color: #FFFFFF;
  text-align: start;
  border: 0.0625rem solid #BBBBBB;
  font-weight: 500;
  font-size: 0.75rem;
  border-radius: 0.3125rem;
  padding: 0.75rem;
  position: absolute;
  z-index: 100;
  top: 170%;
  left: 60%;
  margin-left: -3.75rem;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .status-tooltip .tooltip-text::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 1.5625rem;
  border: 0.625rem solid transparent;
  border-bottom-color: #BBBBBB;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .status-tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 1.625rem;
  border: 0.5625rem solid transparent;
  border-bottom-color: #FFFFFF;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .status-pending {
  border-bottom: none;
  line-height: normal;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .status-tooltip:hover .tooltip-text {
  visibility: visible;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper {
  height: 2.3125rem;
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse {
  width: 2.3125rem;
  height: 2.3125rem;
  padding: 0.125rem 0.125rem 0.125rem 0.125rem;
  border: 0.0625rem solid #BBBBBB;
  background-color: #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse {
    width: 1.625rem;
    height: 1.625rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-approved-icon {
  color: #009D4F;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-approved-icon {
    width: 1.25rem;
    height: 1.125rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-pending-icon {
  color: #F4AF2D;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-pending-icon {
    width: 1.25rem;
    height: 1.125rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-disapproved-icon {
  color: #D40019;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-disapproved-icon {
    width: 1.25rem;
    height: 1.125rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-withdrawn-icon {
  color: #D40019;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper .content-ellipse .status-withdrawn-icon {
    width: 1.25rem;
    height: 1.125rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper hr {
  width: 1rem;
  height: 0.0625rem;
  background-color: #BBBBBB;
  border: none;
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper hr {
    width: 0.0625rem;
    height: 0.3125rem;
    margin: 0 auto 0 auto;
  }
}
@media (max-width: 64rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .content-date-effective .ellipse-wrapper {
    display: block;
    justify-content: center;
    height: 1.9375rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content {
  border-radius: 0.3125rem;
  border: 0.0625rem solid #BBBBBB;
  background-color: #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 rgba(0, 0, 0, 0.15);
  padding: 1rem 1.375rem 1rem 1.375rem;
  max-height: 45.3125rem;
  flex-grow: 1;
  width: 89%;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content {
  display: flex;
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
  row-gap: 1rem;
  flex-direction: row;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content {
    flex-direction: column-reverse;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content:before {
  content: "";
  align-self: stretch;
  border: 0.0625rem dotted #BBBBBB;
}
@media (min-width: 60.625rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content:before {
    height: 4.375rem;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs {
  order: -1;
  width: 50%;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs {
    width: auto;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-type-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: #3F3F3F;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-tabs {
  display: flex;
  gap: 0.25rem;
  margin-top: 1rem;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-tabs .tab {
  height: 2.8125rem;
  border-radius: 0.1875rem;
  background-color: #EAF2FC;
  color: #004EAA;
  font-size: 0.9375rem;
  text-align: center;
  letter-spacing: 0.03125rem;
  box-sizing: border-box;
  white-space: nowrap;
  flex-grow: 1;
  padding: 0.875rem 0 1.875rem 0;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-tabs .tab:hover {
  border-top: 0.1875rem solid #004EAA;
  border-radius: 0;
  padding: 0.75rem 0 1.875rem 0;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-tabs .selected-tab {
  border-top: 0.1875rem solid #004EAA;
  border-left: 0.0625rem solid #BBBBBB;
  border-right: 0.0625rem solid #BBBBBB;
  border-radius: 0;
  color: #3F3F3F;
  background-color: #FFFFFF;
  text-align: center;
  font-size: 0.9375rem;
  font-weight: bold;
  z-index: 1;
  position: relative;
  height: 3.5rem;
  padding: 0.75rem 0 1.875rem 0;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-tabs .disabled-tab {
  height: 2.8125rem;
  border-radius: 0.1875rem;
  background-color: #dddddd;
  color: #999999;
  font-size: 0.9375rem;
  text-align: center;
  letter-spacing: 0.03125rem;
  box-sizing: border-box;
  white-space: nowrap;
  flex-grow: 1;
  padding: 0.875rem 0 1.875rem 0;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-type-tabs .content-tabs .lob-subscription-icon {
  width: 1rem;
  height: 1.125rem;
  vertical-align: middle;
  padding-bottom: 0.1875rem;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description {
  display: flex;
  color: #3F3F3F;
  gap: 0.75rem;
  width: 50%;
}
@media (min-width: 60.625rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description {
    height: 4.7em;
  }
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description {
    width: 100%;
  }
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description .content-description-item {
  width: 90%;
  display: flex;
  align-items: center;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description .content-description-item .content-description-item-text {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description .content-description-item .content-description-item-text span {
  font-weight: 500;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description .content-tab-arrow-wrapper {
  width: 10%;
  display: flex;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description .content-tab-arrow-wrapper .content-tab-arrow {
  display: flex;
  justify-content: center;
  margin: auto;
  width: 4.25rem;
  cursor: pointer;
}
main.decision-roadmap .revision-wrapper .content-wrapper .outer-content .content .content-description .content-tab-arrow-wrapper .content-tab-arrow .expand-less-icon {
  color: #004EAA;
  width: 2.1875rem;
  height: 2.1875rem;
}
main.decision-roadmap .forms-detail-content {
  padding-top: 1rem;
  line-height: 1.4375rem;
  color: #3F3F3F;
  font-size: 0.875rem;
  max-height: 24rem;
  overflow-y: scroll;
}
main.decision-roadmap .forms-detail-content:hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .forms-detail-content::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .forms-detail-content::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .forms-detail-content::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
main.decision-roadmap .forms-detail-content .forms-action {
  color: #D40019;
}
main.decision-roadmap .forms-detail-content .forms-filing-id {
  padding-top: 1rem;
}
main.decision-roadmap .forms-detail-content .forms-detail-value {
  font-weight: 500;
}
main.decision-roadmap .forms-detail-content .forms-detail-circular-list {
  font-weight: 500;
  padding-right: 0.5rem;
}
main.decision-roadmap .forms-detail-content .forms-preview-document {
  float: right;
  color: #004EAA;
}
main.decision-roadmap .forms-detail-content .forms-preview-document:hover {
  color: #0F2C5E;
}
main.decision-roadmap .forms-detail-content .circular-download-btn {
  color: #004EAA;
  text-transform: capitalize;
  padding: 0;
  min-width: auto;
}
main.decision-roadmap .forms-detail-content .circular-download-btn span {
  margin-left: 0.3125rem;
}
main.decision-roadmap .forms-detail-content .circular-download-btn:not(:last-of-type) {
  margin-right: 0.9375rem;
}
main.decision-roadmap .forms-detail-content .circular-download-btn:hover {
  color: #0F2C5E;
  background-color: unset;
}
main.decision-roadmap .forms-detail-content .forms-summary {
  border-top: 0.05rem solid #BBBBBB;
}
main.decision-roadmap .forms-detail-content .forms-amendment {
  border-bottom: 0.05rem solid #BBBBBB;
}
main.decision-roadmap .forms-detail-content .forms-summary-amendment {
  border-top: 0.05rem solid #BBBBBB;
  border-bottom: 0.05rem solid #BBBBBB;
}
main.decision-roadmap .forms-tab-content {
  border-top: 0.0625rem dashed #BBBBBB;
  top: -0.0625rem;
  position: relative;
}
main.decision-roadmap .forms-tab-content .forms-wrapper {
  display: flex;
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}
main.decision-roadmap .forms-tab-content .forms-wrapper .forms-list-pane {
  width: 50%;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .forms-tab-content .forms-wrapper .forms-list-pane {
    width: 100%;
  }
}
main.decision-roadmap .forms-tab-content .forms-wrapper .forms-detail {
  width: 50%;
  max-width: 50%;
  margin-top: 1.125rem;
  box-sizing: border-box;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .forms-tab-content .forms-wrapper .forms-detail {
    display: none;
  }
}
main.decision-roadmap .forms-tab-content .forms-wrapper .forms-detail .forms-default-text {
  padding-top: 4.5rem;
}
main.decision-roadmap .forms-tab-content .forms-wrapper .forms-detail .forms-default-text span {
  font-weight: 700;
}
main.decision-roadmap .forms-tab-content .forms-wrapper .forms-detail .selected-forms-item {
  font-size: 0.9375rem;
  color: #3F3F3F;
  margin-bottom: 1.125rem;
}
main.decision-roadmap .forms-tab-content .forms-wrapper .forms-detail .selected-forms-item .selected-forms-item-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .rules-detail-content {
  padding-top: 1rem;
  line-height: 1.4375rem;
  color: #3F3F3F;
  font-size: 0.875rem;
  max-height: 24rem;
  overflow-y: scroll;
}
main.decision-roadmap .rules-detail-content:hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .rules-detail-content::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .rules-detail-content::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .rules-detail-content::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
main.decision-roadmap .rules-detail-content .rules-action {
  color: #D40019;
}
main.decision-roadmap .rules-detail-content .rules-filing-id {
  padding-top: 1rem;
}
main.decision-roadmap .rules-detail-content .rules-detail-value {
  font-weight: 500;
}
main.decision-roadmap .rules-detail-content .rules-detail-circular-list {
  font-weight: 500;
  padding-right: 0.5rem;
}
main.decision-roadmap .rules-detail-content .rules-preview-document {
  float: right;
  color: #004EAA;
}
main.decision-roadmap .rules-detail-content .rules-preview-document:hover {
  color: #0F2C5E;
}
main.decision-roadmap .rules-detail-content .circular-download-btn {
  color: #004EAA;
  text-transform: capitalize;
  padding: 0;
  min-width: auto;
}
main.decision-roadmap .rules-detail-content .circular-download-btn span {
  margin-left: 0.3125rem;
}
main.decision-roadmap .rules-detail-content .circular-download-btn:not(:last-of-type) {
  margin-right: 0.9375rem;
}
main.decision-roadmap .rules-detail-content .circular-download-btn:hover {
  color: #0F2C5E;
  background-color: unset;
}
main.decision-roadmap .rules-tab-content {
  border-top: 0.0625rem dashed #BBBBBB;
  top: -0.0625rem;
  position: relative;
}
main.decision-roadmap .rules-tab-content .rules-wrapper {
  display: flex;
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}
main.decision-roadmap .rules-tab-content .rules-wrapper .rules-list-pane {
  width: 50%;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .rules-tab-content .rules-wrapper .rules-list-pane {
    width: 100%;
  }
}
main.decision-roadmap .rules-tab-content .rules-wrapper .rules-detail {
  width: 50%;
  margin-top: 1.125rem;
  box-sizing: border-box;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .rules-tab-content .rules-wrapper .rules-detail {
    display: none;
  }
}
main.decision-roadmap .rules-tab-content .rules-wrapper .rules-detail .rules-default-text {
  padding-top: 4.5rem;
}
main.decision-roadmap .rules-tab-content .rules-wrapper .rules-detail .rules-default-text span {
  font-weight: 700;
}
main.decision-roadmap .rules-tab-content .rules-wrapper .rules-detail .selected-rules-item {
  font-size: 0.9375rem;
  color: #3F3F3F;
  margin-bottom: 1.125rem;
}
main.decision-roadmap .rules-tab-content .rules-wrapper .rules-detail .selected-rules-item .selected-rules-item-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .loss-costs-detail-content {
  padding-top: 1rem;
  line-height: 1.4375rem;
  color: #3F3F3F;
  font-size: 0.875rem;
  max-height: 24rem;
  overflow-y: scroll;
}
main.decision-roadmap .loss-costs-detail-content:hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .loss-costs-detail-content::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .loss-costs-detail-content::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .loss-costs-detail-content::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
main.decision-roadmap .loss-costs-detail-content .loss-costs-action {
  color: #D40019;
}
main.decision-roadmap .loss-costs-detail-content .loss-costs-filing-id {
  padding-top: 1rem;
}
main.decision-roadmap .loss-costs-detail-content .loss-costs-detail-value {
  font-weight: 500;
}
main.decision-roadmap .loss-costs-detail-content .loss-costs-detail-circular-list {
  font-weight: 500;
  padding-right: 0.5rem;
}
main.decision-roadmap .loss-costs-detail-content .circular-download-btn {
  color: #004EAA;
  text-transform: capitalize;
  padding: 0;
  min-width: auto;
}
main.decision-roadmap .loss-costs-detail-content .circular-download-btn span {
  margin-left: 0.3125rem;
}
main.decision-roadmap .loss-costs-detail-content .circular-download-btn:not(:last-of-type) {
  margin-right: 0.9375rem;
}
main.decision-roadmap .loss-costs-detail-content .circular-download-btn:hover {
  color: #0F2C5E;
  background-color: unset;
}
main.decision-roadmap .loss-costs-tab-content {
  border-top: 0.0625rem dashed #BBBBBB;
  top: -0.0625rem;
  position: relative;
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper {
  display: flex;
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-list-pane {
  width: 50%;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-list-pane {
    width: 100%;
  }
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-detail {
  width: 50%;
  margin-top: 1.125rem;
  box-sizing: border-box;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-detail {
    display: none;
  }
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-detail .loss-costs-default-text {
  padding-top: 4.5rem;
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-detail .loss-costs-default-text span {
  font-weight: 700;
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-detail .selected-loss-costs-item {
  font-size: 0.9375rem;
  color: #3F3F3F;
  margin-bottom: 1.125rem;
}
main.decision-roadmap .loss-costs-tab-content .loss-costs-wrapper .loss-costs-detail .selected-loss-costs-item .selected-rules-item-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .subject-tab-content {
  padding-bottom: 0.9375rem;
  border-top: 0.0625rem dashed #BBBBBB;
  top: -0.0625rem;
  position: relative;
}
main.decision-roadmap .subject-tab-content .subject-tab-text {
  font-weight: 500;
  color: #3F3F3F;
  font-size: 0.8125rem;
  padding-bottom: 1.25rem;
}
main.decision-roadmap .subject-tab-content .subject-wrapper {
  display: flex;
  padding-top: 0.9375rem;
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list {
  width: 50%;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list {
    width: 100%;
  }
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list {
  box-sizing: border-box;
  max-height: 25rem;
  overflow-y: scroll;
  overflow: overlay;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list:hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info {
  font-size: 0.9375rem;
  border-bottom: 0.0625rem solid #BBBBBB;
  padding: 0.625rem 0 0.625rem 0.4375rem;
  font-weight: 500;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info .subject-name {
  color: #004EAA;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info .subject-types {
  color: #3F3F3F;
  font-weight: 400;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info:hover {
  background-color: #F0F6FA;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info:hover .subject-name {
  color: #0F2C5E;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info-selected {
  background-color: #F0F6FA;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .subject-info-selected .subject-name {
  color: #3F3F3F;
  font-weight: 700;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .detail-pane {
  padding: 1.875rem;
  border-bottom: 0.0625rem dashed #BBBBBB;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .detail-pane:hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .detail-pane::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .detail-pane::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .detail-pane::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
@media (min-width: 60.625rem) {
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-list .subject-section-list .detail-pane {
    display: none;
  }
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-detail {
  width: 50%;
  box-sizing: border-box;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-detail {
    display: none;
  }
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-detail .subject-default-text {
  padding-top: 4.5rem;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-detail .selected-topic-item {
  font-size: 0.9375rem;
  color: #3F3F3F;
  font-weight: 700;
  margin-bottom: 1.875rem;
  height: 2.5em;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-detail .selected-topic-item .selected-topic-item-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .subject-tab-content .subject-wrapper .subject-background-container {
  padding-top: 1.0625rem;
}
@media (min-width: 60.625rem) {
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-background-container {
    max-height: 24rem;
    overflow-y: scroll;
  }
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-background-container:hover::-webkit-scrollbar-thumb {
    display: block;
  }
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-background-container::-webkit-scrollbar {
    width: 0.625rem;
    background-color: transparent;
  }
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-background-container::-webkit-scrollbar-track {
    background-color: transparent;
    width: 0.25rem;
  }
  main.decision-roadmap .subject-tab-content .subject-wrapper .subject-background-container::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 0.3125rem;
    width: 0.25rem;
    border: 0.1875rem solid transparent;
    background-clip: padding-box;
    display: none;
  }
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .list-section-list-wrapper {
    width: auto;
  }
}
main.decision-roadmap .list-section-list-wrapper .list-section-filing-id {
  font-size: 0.8125rem;
  color: #3F3F3F;
  font-weight: 500;
  padding-top: 0.9375rem;
}
main.decision-roadmap .list-section-list-wrapper .list-section-notfiled-text {
  font-weight: 700;
  font-size: 0.9rem;
  color: #3F3F3F;
  padding-top: 0.9375rem;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section {
  color: #004EAA;
  line-height: normal;
  font-weight: 500;
  font-size: 0.8125rem;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0.9375rem 0 0 0;
  align-items: end;
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section {
  text-align: left;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  -moz-column-gap: 0.3125rem;
       column-gap: 0.3125rem;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .list-section-type-text {
  text-decoration: none !important;
  color: #3F3F3F;
  white-space: nowrap;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-d7l1ni-option {
  background-color: #FFFFFF;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-d7l1ni-option:hover {
  cursor: pointer;
  background-color: #EAF2FC;
  color: #0F2C5E;
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-10wo9uf-option:hover {
  color: #0F2C5E;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-tr4s17-option {
  background-color: #004EAA;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-tr4s17-option:hover {
  cursor: pointer;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1fdsijx-ValueContainer,
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .react-select__value-container {
  padding: 0.125rem 0.25rem 0.125rem 0;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1xc3v61-indicatorContainer,
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-15lsz6c-indicatorContainer {
  color: #004EAA;
  padding: 0.125rem 0.25rem 0.125rem 0.25rem;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1gxfbx6-control,
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-tzexq5-control {
  background-color: transparent;
  cursor: pointer;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1gxfbx6-control:hover,
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-tzexq5-control:hover {
  color: #0F2C5E;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1dimb5e-singleValue {
  color: #004EAA;
  margin-left: 0;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1dimb5e-singleValue:hover {
  color: #0F2C5E;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-1nmdiq5-menu {
  width: 10rem;
  margin-top: 0;
  border-radius: 0;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .all-list-section .select-filter-type .css-9dakgz {
  max-height: 10rem;
  padding-top: 0;
  padding-bottom: 0;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .list-section-download-options {
  padding-bottom: 0.3rem;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .pointer-event-disabled {
  pointer-events: none;
}
main.decision-roadmap .list-section-list-wrapper .wrapper-all-list-section .pointer-event-enabled {
  pointer-events: visible;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list {
  margin-top: 0;
  margin-bottom: 0;
  border: 0.0625rem solid #BBBBBB;
  box-sizing: border-box;
  border-top: 0.125rem solid #004EAA;
  box-shadow: 0 0.125rem 0.1875rem 0 #BBBBBB;
  color: #3F3F3F;
  font-size: 0.9375rem;
  line-height: 1.25rem;
  max-height: 25rem;
  overflow-y: scroll;
  overflow: overlay;
  padding-left: 0 !important;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list:hover::-webkit-scrollbar-thumb {
  display: block;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list::-webkit-scrollbar {
  width: 0.625rem;
  background-color: transparent;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list::-webkit-scrollbar-track {
  background-color: transparent;
  width: 0.25rem;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list::-webkit-scrollbar-thumb {
  background-color: #dddddd;
  border-radius: 0.3125rem;
  width: 0.25rem;
  border: 0.1875rem solid transparent;
  background-clip: padding-box;
  display: none;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info {
  border-bottom: 0.0625rem solid #BBBBBB;
  padding: 0 0 0 0.6575rem;
  height: 3rem;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info:hover {
  background-color: #EAF2FC;
  cursor: pointer;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info:hover:hover .list-section-number {
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info:hover .list-section-title {
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-number-title {
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-number-title .list-section-number {
  font-weight: 700;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-preview,
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-download {
  margin: 0.4375rem;
  padding: 0.25rem;
  color: #004EAA;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-preview:hover,
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-download:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info .list-section-hide-download {
  margin: 0.4375rem;
  padding: 0.25rem;
  visibility: hidden;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info:is(:last-child) {
  border-bottom: 0;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .selected-cursor {
  cursor: pointer;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info.selected-item {
  background-color: #EAF2FC;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info.selected-item .list-section-number {
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-info.selected-item .list-section-title {
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical {
  border-bottom: 0.0625rem solid #BBBBBB;
  padding: 0 0 0 0.6575rem;
  cursor: default;
  height: 3rem;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-number-title {
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-number-title .list-section-number {
  font-weight: 700;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-preview,
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-download {
  margin: 0.4375rem;
  padding: 0.25rem;
  color: #004EAA;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-preview:hover,
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-download:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical .list-section-hide-download {
  margin: 0.4375rem;
  padding: 0.25rem;
  visibility: hidden;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical:is(:last-child) {
  border-bottom: 0;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical.selected-item {
  background-color: #EAF2FC;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical.selected-item .list-section-number {
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .list-section-list .list-section-topical.selected-item .list-section-title {
  font-weight: 500;
}
main.decision-roadmap .list-section-list-wrapper .selected-item-content {
  padding: 1.875rem;
  border-bottom: 0.0625rem dashed #BBBBBB;
}
@media (min-width: 60.625rem) {
  main.decision-roadmap .list-section-list-wrapper .selected-item-content {
    display: none;
  }
}
main.decision-roadmap .list-section-not-filed {
  border-top: 0.0625rem dashed #BBBBBB;
  top: -0.0625rem;
  position: relative;
}
main.decision-roadmap .empty-subject-section {
  padding-top: 1.0625rem;
  font-size: 0.9375rem;
  font-weight: 500;
  color: #3F3F3F;
}
main.decision-roadmap .topical-sections {
  display: flex;
  border-bottom: 0.0625rem dashed #BBBBBB;
  justify-content: space-between;
  height: 2.625rem;
  gap: 0.5rem;
  overflow: hidden;
}
main.decision-roadmap .topical-sections .lock-icon-span {
  align-items: center;
}
main.decision-roadmap .topical-sections .no-lock-icon-span {
  align-items: center;
}
main.decision-roadmap .topical-sections .lob-subscription-icon {
  width: 1rem;
  height: 1.125rem;
  vertical-align: middle;
  padding-bottom: 0.1875rem;
}
main.decision-roadmap .topical-sections .topical {
  box-sizing: border-box;
  font-size: 0.875rem;
  font-weight: 500;
  color: #004EAA;
  text-align: center;
  line-height: normal;
}
main.decision-roadmap .topical-sections .topical:hover {
  color: #0F2C5E;
}
main.decision-roadmap .topical-sections .selected-detail {
  color: #3F3F3F;
  font-weight: 700;
  padding-bottom: 0.3125rem;
}
main.decision-roadmap .topical-sections .selected-detail:hover {
  text-decoration: none;
}
main.decision-roadmap .topical-sections .overview-hr {
  width: 0.0625rem;
  height: 100%;
  border: none;
  background-color: #BBBBBB;
  margin: auto;
}
main.decision-roadmap .preview-document-sections {
  height: 2.125rem;
}
main.decision-roadmap .corresponding-forms {
  padding-top: 1.0625rem;
  line-height: 1.4375rem;
  color: #3F3F3F;
  font-size: 0.875rem;
}
main.decision-roadmap .corresponding-forms .hint-text {
  font-size: 0.8125rem;
  color: #3F3F3F;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms {
  font-size: 0.9375rem;
  font-weight: 500;
  color: #004EAA;
  padding-bottom: 0.6875rem;
  border-bottom: 0.0625rem dotted #dddddd;
  margin-bottom: 1.0625rem;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-d7l1ni-option {
  background-color: #FFFFFF;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-d7l1ni-option:hover {
  color: #0F2C5E;
  cursor: pointer;
  background-color: #F0F6FA;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-10wo9uf-option {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-tr4s17-option {
  background-color: #004EAA;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-1xc3v61-indicatorContainer,
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-15lsz6c-indicatorContainer {
  color: #004EAA;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-1xc3v61-indicatorContainer:hover,
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-15lsz6c-indicatorContainer:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-1dimb5e-singleValue {
  color: #004EAA;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-1dimb5e-singleValue:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-1gxfbx6-control,
main.decision-roadmap .corresponding-forms .select-corresponding-forms .css-tzexq5-control {
  cursor: pointer;
}
main.decision-roadmap .corresponding-forms .forms-selected-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-forms .forms-preview-document {
  float: right;
  color: #004EAA;
  margin-left: 1rem;
}
main.decision-roadmap .corresponding-forms .forms-detail {
  padding-top: 1rem;
}
main.decision-roadmap .corresponding-forms .forms-detail .forms-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-forms .forms-detail .forms-filing-id {
  padding-top: 1rem;
}
main.decision-roadmap .corresponding-forms .forms-text-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-rules {
  padding-top: 1.0625rem;
  line-height: 1.4375rem;
  color: #3F3F3F;
  font-size: 0.875rem;
}
main.decision-roadmap .corresponding-rules .hint-text {
  font-size: 0.8125rem;
  color: #3F3F3F;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules {
  font-size: 0.9375rem;
  font-weight: 500;
  color: #004EAA;
  padding-bottom: 0.6875rem;
  border-bottom: 0.0625rem dotted #dddddd;
  margin-bottom: 1.0625rem;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-d7l1ni-option {
  background-color: #FFFFFF;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-d7l1ni-option:hover {
  color: #0F2C5E;
  cursor: pointer;
  background-color: #F0F6FA;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-10wo9uf-option {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-tr4s17-option {
  background-color: #004EAA;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-1xc3v61-indicatorContainer,
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-15lsz6c-indicatorContainer {
  color: #004EAA;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-1xc3v61-indicatorContainer:hover,
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-15lsz6c-indicatorContainer:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-1dimb5e-singleValue {
  color: #004EAA;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-1dimb5e-singleValue:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-1gxfbx6-control,
main.decision-roadmap .corresponding-rules .select-corresponding-rules .css-tzexq5-control {
  cursor: pointer;
}
main.decision-roadmap .corresponding-rules .rules-selected-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-rules .rules-preview-document {
  float: right;
  color: #004EAA;
  margin-left: 1rem;
}
main.decision-roadmap .corresponding-rules .rules-detail {
  padding-top: 1rem;
}
main.decision-roadmap .corresponding-rules .rules-detail .rules-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-rules .rules-text-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-loss-costs {
  padding-top: 1.0625rem;
  line-height: 1.4375rem;
  color: #3F3F3F;
  font-size: 0.875rem;
}
main.decision-roadmap .corresponding-loss-costs .hint-text {
  font-size: 0.8125rem;
  color: #3F3F3F;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs {
  font-size: 0.9375rem;
  font-weight: 500;
  color: #004EAA;
  padding-bottom: 0.6875rem;
  border-bottom: 0.0625rem dotted #dddddd;
  margin-bottom: 1.0625rem;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-d7l1ni-option {
  background-color: #FFFFFF;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-d7l1ni-option:hover {
  color: #0F2C5E;
  cursor: pointer;
  background-color: #F0F6FA;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-10wo9uf-option {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-tr4s17-option {
  background-color: #004EAA;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-1xc3v61-indicatorContainer,
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-15lsz6c-indicatorContainer {
  color: #004EAA;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-1xc3v61-indicatorContainer:hover,
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-15lsz6c-indicatorContainer:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-1dimb5e-singleValue {
  color: #004EAA;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-1dimb5e-singleValue:hover {
  color: #0F2C5E;
  cursor: pointer;
}
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-1gxfbx6-control,
main.decision-roadmap .corresponding-loss-costs .select-corresponding-loss-costs .css-tzexq5-control {
  cursor: pointer;
}
main.decision-roadmap .corresponding-loss-costs .loss-costs-selected-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-loss-costs .loss-costs-detail {
  padding-top: 1rem;
}
main.decision-roadmap .corresponding-loss-costs .loss-costs-detail .loss-costs-value {
  font-weight: 500;
}
main.decision-roadmap .corresponding-loss-costs .loss-costs-detail .loss-costs-download {
  padding-top: 1rem;
}
main.decision-roadmap .corresponding-loss-costs .loss-costs-text-value {
  font-weight: 500;
}
main.decision-roadmap.modal {
  position: fixed;
  left: 0;
  top: 0;
  min-width: 100vw;
  width: 100%;
  min-height: 100vh;
  height: 100vh;
  background-color: rgba(51, 51, 51, 0.4588235294);
  transition-property: background-color;
  transition-duration: 300ms;
  z-index: 10;
  display: flex;
  align-items: flex-start;
  padding-top: 0.9375rem;
  padding-bottom: 0.9375rem;
  box-sizing: border-box;
  justify-content: center;
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden;
}
@media (min-height: 650px) {
  main.decision-roadmap.modal {
    align-items: center;
  }
}
main.decision-roadmap.modal .modal-content {
  background-color: #FFFFFF;
  padding: 0.5rem 1rem;
  animation-name: translateY;
  animation-duration: 0.3s;
  animation-timing-function: ease-in;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 91.67%;
  overflow-y: auto;
  overflow-x: hidden;
  max-width: 48rem;
  height: -webkit-fill-available;
  flex-grow: 1;
  flex: 1;
  /* 768px */
}
main.decision-roadmap.modal .modal-content .header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0.75rem;
}
main.decision-roadmap.modal .modal-content .header-close {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 0.25rem 0.75rem;
}
main.decision-roadmap.modal .modal-content .header-close button.close {
  margin-right: 0.5rem;
}
main.decision-roadmap.modal .modal-content button.close {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 625rem;
  /* full rounded 9999px */
  padding: 0.25rem;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
  color: #333333;
  background-color: #F9FAFB;
  border-color: transparent;
}
main.decision-roadmap.modal .modal-content button.close :hover {
  background-color: rgba(249, 250, 251, 0.8);
}
main.decision-roadmap.modal .modal-content button.close svg {
  width: 1.188rem;
  /*19px*/
  height: 1.188rem;
  /*19px*/
}
main.decision-roadmap.modal .modal-content .preview-tab-wrapper {
  position: relative;
  width: 100%;
}
main.decision-roadmap.modal .modal-content .preview-tab-wrapper .download-wrapper {
  position: absolute;
  right: 0;
  top: 0.3125rem;
}
main.decision-roadmap.modal .modal-content .preview-tab-wrapper .tabs {
  width: 100%;
}
main.decision-roadmap.modal .modal-content .preview-tab-wrapper .tabs .topical-sections {
  width: 100%;
  justify-content: flex-start;
  gap: 0.75rem;
}
main.decision-roadmap.modal .modal-content .preview-tab-wrapper .tabs a {
  cursor: pointer;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
}
main.decision-roadmap.modal .modal-content .document-container {
  width: 100%;
  margin-top: 0.75rem;
  /*650px*/
  overflow-y: auto;
  padding-right: 0.5rem;
  height: -webkit-fill-available;
}
main.decision-roadmap.modal .modal-content .document-container .document {
  /*650px*/
  width: 100%;
}
main.decision-roadmap.modal .modal-content .document-container .document .page-container {
  border-radius: 0.25rem;
  border: 0.125rem solid rgb(209, 213, 219);
  box-shadow: 0 0.0625rem 0.1875rem 0 rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem -0.0625rem rgba(0, 0, 0, 0.1);
  width: 100%;
  display: flex;
  flex-direction: column;
}
main.decision-roadmap.modal .modal-content .document-container .document .page-container .page {
  width: 100%;
  height: 100%;
}
main.decision-roadmap.modal .modal-content .loader {
  /*650px*/
  width: auto;
  min-height: auto;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 30%;
  bottom: 30%;
}
main.decision-roadmap.modal .modal-content .loader span {
  border-radius: 625rem;
  /* full rounded 9999px */
  border-width: 0;
  border-top-width: 0.125rem;
  border-style: double;
  border-color: #004EAA;
  height: 3rem;
  width: 3rem;
  animation: spin 1s ease-in-out infinite;
  animation-delay: 0.3s;
}
main.decision-roadmap.modal .modal-content .error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
main.decision-roadmap.modal .modal-content .error .message {
  color: #DC2626;
}
@keyframes translateY {
  from {
    transform: translateY(50%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
main.decision-roadmap .download-dropdown {
  position: relative;
}
main.decision-roadmap .download-dropdown .dropdown-btn {
  font-size: 0.8375rem;
  color: #004EAA;
  white-space: nowrap;
  font-weight: 500;
}
main.decision-roadmap .download-dropdown .dropdown-btn:hover {
  color: #0F2C5E;
}
main.decision-roadmap .download-dropdown .dropdown-btn-disabled {
  font-size: 0.8375rem;
  color: #BBBBBB;
  white-space: nowrap;
  font-weight: 500;
  pointer-events: none;
}
main.decision-roadmap .download-dropdown .dropdown-btn-disabled:hover {
  text-decoration: none;
}
main.decision-roadmap .download-dropdown .pointer-events-visible {
  pointer-events: visible;
}
main.decision-roadmap .download-dropdown .pointer-events-none {
  pointer-events: none;
  opacity: 0.5;
}
main.decision-roadmap .download-dropdown .dropdown-menu {
  position: absolute;
  top: 1.8rem;
  right: 0;
  background: #FFFFFF;
  box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 rgba(0, 0, 0, 0.45);
  padding: 0.4375rem 0 0 0;
  z-index: 12;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item {
  color: #666666;
  font-weight: 400;
  height: 1.575rem;
  padding: 0 0.5625rem 0 0.75rem;
  width: 100%;
  min-width: 10rem;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item:hover {
  background: #F0F6FA;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item .dropdown-item-checkbox {
  display: none;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item .dropdown-item-checkmark {
  height: 1.15rem;
  width: 1.15rem;
  overflow: hidden;
  border: 0.0625rem solid #666666;
  background-color: #FFFFFF;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item input:checked ~ .dropdown-item-checkmark:after {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 0.875rem;
  font-weight: 700;
  content: "✓";
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item .dropdown-item-checkmark:after {
  display: none;
  color: white;
  background-color: #004EAA;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item .dropdown-item-text {
  padding: 0 0.3125rem 0 0.5625rem;
  white-space: nowrap;
  font-size: 0.78rem;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-item-selected {
  font-weight: 500;
}
main.decision-roadmap .download-dropdown .dropdown-menu .dropdown-options-seperator {
  height: 0.0625rem;
  margin-block-start: 0.25rem;
  margin-block-end: 0.25rem;
  margin-inline-start: 0.625rem;
  margin-inline-end: 0.625rem;
  border: 0.0625rem dotted #dddddd;
}
main.decision-roadmap .download-dropdown .dropdown-menu .download-now-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: auto;
  font-size: 1rem;
  color: #004EAA;
  height: 2.1875rem;
  width: 100%;
  font-size: 0.8275rem;
}
main.decision-roadmap .download-dropdown .dropdown-menu .download-now-btn:hover {
  background: #F0F6FA;
}
main.decision-roadmap .download-dropdown .dropdown-menu .download-dropdown-visible {
  color: #004EAA;
}
main.decision-roadmap .download-dropdown .dropdown-menu .download-dropdown-disabled {
  color: #999999;
}
main.decision-roadmap .read-full-wrapper {
  border-bottom: 0.05rem solid #BBBBBB;
  padding-bottom: 1rem;
  padding-top: 0.5rem;
}
main.decision-roadmap .read-full-wrapper .read-full-content .read-full-label {
  font-weight: 700;
  font-size: 0.875rem;
}
main.decision-roadmap .read-full-wrapper .read-full-content td {
  border-bottom: none;
}
main.decision-roadmap .read-full-wrapper .read-full-clamp {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
main.decision-roadmap .read-full-wrapper .read-full-btn {
  color: #004EAA;
  text-transform: capitalize;
  padding: 0 !important;
  display: flex;
  flex-wrap: nowrap;
  text-align: left;
}
main.decision-roadmap .read-full-wrapper .read-full-btn span {
  margin-left: 0.125rem;
}
main.decision-roadmap .read-full-wrapper .read-full-btn:focus {
  background: #ADD8E6;
}
main.decision-roadmap .read-full-wrapper .read-full-btn:hover {
  color: #0F2C5E;
  background-color: unset;
}
main.decision-roadmap .read-full-wrapper:last-child {
  border-bottom: none;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-content p:last-of-type, main.decision-roadmap .read-full-wrapper.summary .read-full-content .paragraph-wrapper:last-of-type {
  margin-bottom: 0 !important;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-content p:empty, main.decision-roadmap .read-full-wrapper.summary .read-full-content .paragraph-wrapper:empty {
  display: none;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-content a {
  color: #3F3F3F;
  cursor: initial;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-content a:hover {
  color: #3F3F3F;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-content table {
  width: auto !important;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-clamp table {
  display: block;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-clamp table tbody {
  display: block;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-clamp table tbody tr {
  display: block;
  height: auto !important;
}
main.decision-roadmap .read-full-wrapper.summary .read-full-clamp table tbody tr td {
  display: block;
  padding-bottom: 0;
}
main.decision-roadmap .read-full-wrapper.summary-amendment {
  border-bottom: none;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
}
main.decision-roadmap .read-full-wrapper.dr-timeline {
  border-bottom: none;
  padding: 0rem;
}
main.decision-roadmap .read-full-wrapper.dr-timeline .read-full-btn {
  font-size: 0.875rem;
}
main.decision-roadmap .Copyright {
  font-family: Arial;
  font-size: 9pt;
}
main.decision-roadmap .EMTableofContents {
  margin-top: 12pt;
  margin-bottom: 6pt;
  page-break-before: always;
  page-break-inside: avoid;
  page-break-after: avoid;
  border-bottom: 1.5pt solid #000000;
  padding-bottom: 1pt;
  font-family: Arial;
  font-size: 20pt;
  font-weight: bold;
}
main.decision-roadmap .EMbaseheading {
  font-family: Arial;
}
main.decision-roadmap .EMbasetext {
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMbody1 {
  margin-bottom: 6pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMbody2 {
  margin-bottom: 6pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMbody3 {
  margin-bottom: 6pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMbody4 {
  margin-bottom: 6pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMheader {
  font-family: Arial;
}
main.decision-roadmap .EMheading1 {
  margin-top: 12pt;
  margin-bottom: 6pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  border-bottom: 1.5pt solid #000000;
  padding-bottom: 1pt;
  font-family: Arial;
  font-size: 20pt;
  font-weight: bold;
}
main.decision-roadmap .EMheading2 {
  margin-top: 8pt;
  margin-bottom: 6pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 20pt;
  font-family: Arial;
  font-size: 16pt;
  font-weight: bold;
}
main.decision-roadmap .EMheading3 {
  margin-top: 8pt;
  margin-bottom: 6pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  font-family: Arial;
}
main.decision-roadmap .EMlanguage {
  margin-right: 36pt;
  margin-left: 54pt;
  margin-bottom: 6pt;
  text-align: justify;
  line-height: 11pt;
  font-family: Arial;
  font-size: 10pt;
}
main.decision-roadmap .EMlist1 {
  margin-left: 54pt;
  margin-bottom: 6pt;
  text-indent: -18pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMlist2 {
  margin-left: 63pt;
  margin-bottom: 6pt;
  text-indent: -18pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMlist3 {
  margin-left: 72pt;
  margin-bottom: 6pt;
  text-indent: -18pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMquote {
  margin-right: 36pt;
  margin-left: 54pt;
  margin-bottom: 6pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMsectionreference {
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .EMsectiontext {
  margin-left: 0pt;
  margin-bottom: 6pt;
  line-height: 15pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMsectiontitle {
  margin-top: 12pt;
  margin-bottom: 6pt;
  text-align: center;
  page-break-before: always;
  page-break-inside: avoid;
  page-break-after: avoid;
  border-bottom: 1.5pt solid #000000;
  padding-bottom: 16pt;
  font-family: Arial;
  font-size: 22pt;
  font-weight: bold;
}
main.decision-roadmap .EMtitle {
  margin-top: 10pt;
  margin-bottom: 10pt;
  text-align: center;
  page-break-after: avoid;
  font-family: Arial;
  font-size: 24pt;
  font-weight: bold;
}
main.decision-roadmap .EMtocheading {
  margin: 8pt 36pt 2pt 14.4pt;
  text-indent: -14.4pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  font-family: Arial;
  font-size: 14pt;
}
main.decision-roadmap .EMtoctext1 {
  margin-top: 4pt;
  margin-right: 36pt;
  margin-left: 14.4pt;
  text-indent: -14.4pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMtoctext2 {
  margin-right: 36pt;
  margin-left: 32.4pt;
  text-indent: -14.4pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .EMtoctext3 {
  margin-right: 36pt;
  margin-left: 50.4pt;
  text-indent: -14.4pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .FilingHeader {
  margin-top: 0pt;
  line-height: normal;
  font-family: Arial;
}
main.decision-roadmap .Footer {
  font-family: "Times New Roman";
  font-size: 10pt;
}
main.decision-roadmap .Header {
  font-family: "Times New Roman";
  font-size: 10pt;
}
main.decision-roadmap .Revision {
  font-family: "Times New Roman";
  font-size: 12pt;
}
main.decision-roadmap .TOC1 {
  margin: 8pt 36pt 2pt 14.4pt;
  text-indent: -14.4pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  font-family: Arial;
  font-size: 14pt;
}
main.decision-roadmap .TOC2 {
  margin-top: 4pt;
  margin-right: 36pt;
  margin-left: 14.4pt;
  text-indent: -14.4pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .TOC3 {
  margin-right: 36pt;
  margin-left: 32.4pt;
  text-indent: -14.4pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .TOC4 {
  margin-right: 36pt;
  margin-left: 50.4pt;
  text-indent: -14.4pt;
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap .TOC5 {
  margin-left: 48pt;
  font-family: Calibri;
  font-size: 9pt;
}
main.decision-roadmap .TOC6 {
  margin-left: 60pt;
  font-family: Calibri;
  font-size: 9pt;
}
main.decision-roadmap .TOC7 {
  margin-left: 72pt;
  font-family: Calibri;
  font-size: 9pt;
}
main.decision-roadmap .TOC8 {
  margin-left: 84pt;
  font-family: Calibri;
  font-size: 9pt;
}
main.decision-roadmap .TOC9 {
  margin-left: 96pt;
  font-family: Calibri;
  font-size: 9pt;
}
main.decision-roadmap .TOCHeading {
  margin-top: 12pt;
  margin-bottom: 3pt;
  page-break-after: avoid;
  font-family: "Calibri Light";
  font-size: 16pt;
  font-weight: bold;
}
main.decision-roadmap .blockhd1 {
  margin-top: 4pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd2 {
  margin-top: 4pt;
  margin-left: 15.1pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd3 {
  margin-top: 4pt;
  margin-left: 30.25pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd4 {
  margin-top: 4pt;
  margin-left: 45.35pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd5 {
  margin-top: 4pt;
  margin-left: 59.75pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd6 {
  margin-top: 4pt;
  margin-left: 74.9pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd7 {
  margin-top: 4pt;
  margin-left: 90pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd8 {
  margin-top: 4pt;
  margin-left: 105.1pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blockhd9 {
  margin-top: 4pt;
  margin-left: 120.25pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .blocktext1 {
  margin-top: 4pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext2 {
  margin-top: 4pt;
  margin-left: 15.1pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext3 {
  margin-top: 4pt;
  margin-left: 30pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext4 {
  margin-top: 4pt;
  margin-left: 45.35pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext5 {
  margin-top: 4pt;
  margin-left: 59.75pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext6 {
  margin-top: 4pt;
  margin-left: 74.9pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext7 {
  margin-top: 4pt;
  margin-left: 90pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext8 {
  margin-top: 4pt;
  margin-left: 105.1pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .blocktext9 {
  margin-top: 4pt;
  margin-left: 120.25pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .center {
  margin-top: 4pt;
  text-align: center;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .colline {
  text-align: justify;
  line-height: 4pt;
  border-bottom: 0.75pt solid #000000;
  font-family: Arial;
}
main.decision-roadmap .columnheading {
  text-align: center;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .isonormal {
  margin-top: 4pt;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .outlinehd1 {
  margin-top: 4pt;
  margin-left: 15.1pt;
  text-indent: -15.1pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd2 {
  margin-top: 4pt;
  margin-left: 30.25pt;
  text-indent: -30.25pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd3 {
  margin-top: 4pt;
  margin-left: 45.35pt;
  text-indent: -45.35pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd4 {
  margin-top: 4pt;
  margin-left: 59.75pt;
  text-indent: -59.75pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd5 {
  margin-top: 4pt;
  margin-left: 74.9pt;
  text-indent: -74.9pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd6 {
  margin-top: 4pt;
  margin-left: 90pt;
  text-indent: -90pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd7 {
  margin-top: 4pt;
  margin-left: 105pt;
  text-indent: -105pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd8 {
  margin-top: 4pt;
  margin-left: 120pt;
  text-indent: -120pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinehd9 {
  margin-top: 4pt;
  margin-left: 135.35pt;
  text-indent: -135.35pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt1 {
  margin-top: 4pt;
  margin-left: 15pt;
  text-indent: -15pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt2 {
  margin-top: 4pt;
  margin-left: 30pt;
  text-indent: -30pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt3 {
  margin-top: 4pt;
  margin-left: 45pt;
  text-indent: -45pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt4 {
  margin-top: 4pt;
  margin-left: 60pt;
  text-indent: -60pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt5 {
  margin-top: 4pt;
  margin-left: 75pt;
  text-indent: -75pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt6 {
  margin-top: 4pt;
  margin-left: 90pt;
  text-indent: -90pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt7 {
  margin-top: 4pt;
  margin-left: 105pt;
  text-indent: -105pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt8 {
  margin-top: 4pt;
  margin-left: 120pt;
  text-indent: -120pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .outlinetxt9 {
  margin-top: 4pt;
  margin-left: 135pt;
  text-indent: -135pt;
  text-align: justify;
  page-break-inside: avoid;
  line-height: 11pt;
  font-family: Arial;
  font-weight: bold;
}
main.decision-roadmap .sectiontitlecenter {
  margin-top: 4pt;
  text-align: center;
  page-break-inside: avoid;
  page-break-after: avoid;
  border-top: 0.75pt solid #000000;
  padding-top: 3pt;
  font-family: Arial;
  font-size: 12pt;
  font-weight: bold;
  text-transform: uppercase;
}
main.decision-roadmap .sectiontitleflushleft {
  margin-top: 4pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  border-top: 0.75pt solid #000000;
  padding-top: 3pt;
  font-family: Arial;
  font-size: 12pt;
  font-weight: bold;
  text-transform: uppercase;
}
main.decision-roadmap .tablecenter {
  margin-top: 3pt;
  text-align: center;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .tablejust {
  margin-top: 3pt;
  text-align: justify;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .tableleft {
  margin-top: 3pt;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .tableright {
  margin-top: 3pt;
  text-align: right;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .tabletext {
  margin-top: 4pt;
  line-height: 11pt;
  font-family: Arial;
}
main.decision-roadmap .tabletxtdecpage {
  margin-top: 3pt;
  line-height: 11pt;
  font-family: Arial;
  font-size: 9pt;
}
main.decision-roadmap .titleflushleft {
  margin-top: 4pt;
  page-break-inside: avoid;
  page-break-after: avoid;
  border-top: 0.75pt solid #000000;
  padding-top: 3pt;
  font-family: Arial;
  font-size: 12pt;
  font-weight: bold;
  text-transform: uppercase;
}
main.decision-roadmap .title12 {
  text-align: center;
  page-break-inside: avoid;
  page-break-after: avoid;
  font-family: Arial;
  font-size: 12pt;
  font-weight: bold;
  text-transform: uppercase;
}
main.decision-roadmap .title18 {
  text-align: center;
  line-height: 18pt;
  font-family: Arial;
  font-size: 18pt;
  font-weight: bold;
  text-transform: uppercase;
}
main.decision-roadmap span.EMbasetextChar {
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap span.EMbody1Char {
  font-family: Arial;
  font-size: 11pt;
}
main.decision-roadmap span.FooterChar {
  font-family: "Times New Roman";
}
main.decision-roadmap span.HeaderChar {
  font-family: "Times New Roman";
}
main.decision-roadmap span.Heading1Char {
  font-family: Arial;
  font-size: 16pt;
  font-weight: bold;
}
main.decision-roadmap span.Heading2Char {
  font-family: Arial;
  font-size: 14pt;
  font-weight: bold;
  font-style: italic;
}
main.decision-roadmap span.Heading3Char {
  font-family: Arial;
  font-size: 13pt;
  font-weight: bold;
}
main.decision-roadmap span.Hyperlink {
  text-decoration: underline;
  color: #0563c1;
  -aw-style-name: hyperlink;
}
main.decision-roadmap .timeline-section {
  margin-top: 1.25rem;
  max-height: 21.5rem;
  overflow: auto;
}
main.decision-roadmap .timeline-section .timeline-no-data {
  font-size: 0.875rem;
}
main.decision-roadmap .timeline-section .timeline-no-data .timeline-no-data-highlighted {
  font-weight: 500;
}
main.decision-roadmap .timeline-section .timeline-item {
  display: flex;
  -moz-column-gap: 1.3125rem;
       column-gap: 1.3125rem;
  font-size: 0.875rem;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-date {
  font-weight: 500;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-line-wrapper {
  position: relative;
  display: flex;
  justify-content: center;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-line-wrapper .timeline-pointer {
  position: absolute;
  min-width: 1.125rem;
  height: 1.125rem;
  border-radius: 50%;
  border: 0.125rem solid #004EAA;
  background-color: #FFFFFF;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-line-wrapper .timeline-line {
  width: 0.0938rem;
  height: 100%;
  background-color: #999999;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-line-wrapper .timeline-arrow {
  display: none;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-content {
  margin-bottom: 2.1875rem;
  row-gap: 0.625rem;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-content .timeline-download-section {
  padding-top: 0.5rem;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-content .timeline-download-section .timeline-download {
  color: #004EAA;
  display: flex;
  align-items: center;
  padding: 0.375rem 0 0.125rem 0;
  -moz-column-gap: 0.3125rem;
       column-gap: 0.3125rem;
}
main.decision-roadmap .timeline-section .timeline-item .timeline-content .timeline-download-section .timeline-download:hover {
  color: #0F2C5E;
}
main.decision-roadmap .timeline-section .timeline-item:first-child .timeline-line-wrapper .timeline-pointer {
  background-color: #004EAA;
}
main.decision-roadmap .timeline-section .timeline-item:last-child .timeline-line-wrapper .timeline-line {
  height: calc(100% - 0.625rem);
}
main.decision-roadmap .timeline-section .timeline-item:last-child .timeline-line-wrapper .timeline-arrow {
  display: flex;
  position: absolute;
  height: 100%;
  align-items: flex-end;
}
main.decision-roadmap .timeline-section .timeline-item:last-child .timeline-line-wrapper .timeline-arrow .timeline-arrow-icon {
  color: #999999;
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed {
  display: flex;
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
  flex-direction: row;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .entitlement-subscription-container .not-subscribed {
    flex-direction: column;
  }
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-first {
  width: 50%;
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-first p, main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-first .paragraph-wrapper {
  font-size: 0.85rem;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-first {
    flex-direction: column;
    width: 100%;
  }
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-first .not-subscribed-title {
  color: #3F3F3F;
  font-weight: 500;
  margin-top: 0.625rem;
  margin-bottom: 1.125rem;
  font-size: 0.9375rem;
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-last {
  width: 50%;
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-last p, main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-last .paragraph-wrapper {
  font-size: 0.85rem;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .entitlement-subscription-container .not-subscribed .not-subscribed-last {
    flex-direction: column;
    width: 100%;
  }
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .no-split {
  width: 100%;
  margin-top: 0.625rem;
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .no-split p, main.decision-roadmap .entitlement-subscription-container .not-subscribed .no-split .paragraph-wrapper {
  font-size: 0.85rem;
}
@media (max-width: 60.625rem) {
  main.decision-roadmap .entitlement-subscription-container .not-subscribed .no-split {
    flex-direction: column;
    width: 100%;
  }
}
main.decision-roadmap .entitlement-subscription-container .not-subscribed .no-split .not-subscribed-title {
  color: #3F3F3F;
  font-weight: 500;
  margin-top: 0.625rem;
  margin-bottom: 1.125rem;
  font-size: 0.9375rem;
}
main.decision-roadmap .dr-banner {
  display: flex;
  margin: 1.875rem 3% 0 3%;
  border-radius: 0.3125rem;
  background-image: url(../assets/dr/dr-banner.png);
  color: #FFFFFF;
  gap: 1.25rem;
}
main.decision-roadmap .dr-banner h3, main.decision-roadmap .dr-banner .cards .heading-wrapper, .cards main.decision-roadmap .dr-banner .heading-wrapper {
  font-size: 1.625rem;
  color: #FFFFFF;
}
main.decision-roadmap .kampyle_vertical_button {
  height: 2.112rem;
  min-height: 2.112rem;
  max-height: 2.112rem;
}
main.decision-roadmap .kampyle_button {
  height: 2.112rem;
  min-height: 2.112rem;
  max-height: 2.112rem;
}
main.decision-roadmap .kampyle-button-text {
  height: 2.112rem;
  min-height: 2.112rem;
  max-height: 2.112rem;
}
main.decision-roadmap .auto-cursor {
  cursor: auto;
}
main.decision-roadmap .cross-hair-cursor {
  cursor: crosshair;
}
main.decision-roadmap .default-cursor {
  cursor: default;
}
main.decision-roadmap .grab-cursor {
  cursor: grab;
}
main.decision-roadmap .help-cursor {
  cursor: help;
}
main.decision-roadmap .initial-cursor {
  cursor: initial;
}
main.decision-roadmap .not-allowed-cursor {
  cursor: not-allowed;
}
main.decision-roadmap .pointer-cursor {
  cursor: pointer;
}
main.decision-roadmap .pointer-events-none {
  pointer-events: none;
}
main.decision-roadmap .progress-cursor {
  cursor: progress;
}
main.decision-roadmap .wait-cursor {
  cursor: wait;
}
main.decision-roadmap .zoom-in-cursor {
  cursor: zoom-in;
}
main.decision-roadmap .zoom-out-cursor {
  cursor: zoom-out;
}
main.decision-roadmap .site.flex-wrapper {
  flex-direction: row;
}
main.decision-roadmap .site.flex-wrapper .beta-tag {
  padding: 0.304rem 1.125rem;
  border-radius: 1.875rem;
  background-color: #CF5792;
  font-weight: 500;
  color: #FFFFFF;
  position: relative;
  left: 1.25rem;
  top: 0.105rem;
  font-size: 0.875rem;
  letter-spacing: 0.0563rem;
}

main.emerging-issues {
  min-height: unset;
}
main.emerging-issues .project-status {
  padding: 0 0 1rem;
  border-bottom: thin solid #bbbbbb;
}
main.emerging-issues .project-status details summary {
  border: none;
}
main.emerging-issues .project-status details summary:after {
  margin-left: 0;
}
main.emerging-issues .project-status details table {
  border-collapse: separate;
  border-spacing: 0.75rem 0;
  margin-left: -0.75rem;
}
main.emerging-issues .project-status details table th, main.emerging-issues .project-status details table td {
  background-color: #ffffff;
}
main.emerging-issues .project-status details table th:first-of-type, main.emerging-issues .project-status details table td:first-of-type {
  padding-left: 0;
}
main.emerging-issues .project-status details table th {
  font-size: 0.85rem;
  border-bottom: thin dotted;
}
main.emerging-issues .project-status details table th.status, main.emerging-issues .project-status details table th.date {
  width: 5rem;
}
main.emerging-issues .project-status details table td {
  border-bottom: none;
}
main.emerging-issues .project-status details table td.status, main.emerging-issues .project-status details table td.date {
  font-size: 0.85rem;
  font-weight: 500;
}
main.emerging-issues .project-status details table td .material-icons {
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.1rem;
  background-color: #3f3f3f;
  color: white;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 0.25rem;
}
main.emerging-issues .project-status details table td p, main.emerging-issues .project-status details table td .paragraph-wrapper {
  width: 100%;
  font-size: 0.9rem;
}
main.emerging-issues .project-status details table td:first-child {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
main.emerging-issues .content-wrapper {
  flex-grow: 2;
}
main.emerging-issues .content-wrapper.guest-user {
  padding: 1.875rem;
}
main.emerging-issues .content-wrapper.all-topics {
  flex-wrap: wrap;
  align-items: flex-start;
}
main.emerging-issues .content-wrapper.all-topics h2 {
  font-size: 1.125rem;
  padding-bottom: 1rem;
  border-bottom: thin solid #bbbbbb;
  font-weight: 700;
}
main.emerging-issues .content-wrapper.all-topics .link-list {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
}
main.emerging-issues .content-wrapper.all-topics .link-list li {
  width: 45%;
  margin-right: 1rem;
}
main.emerging-issues.hub-page li a.list-content {
  font-size: 1rem;
  font-weight: 500;
  word-break: break-word;
  padding: 1.25rem 0;
}
main.emerging-issues.hub-page .no-padding-top {
  padding-top: 0rem;
}
main.emerging-issues.hub-page .share-alignment {
  margin: auto;
}
main.emerging-issues.hub-page ul.list-height {
  max-height: 40.625rem;
  overflow: auto;
}
main.emerging-issues.hub-page .verisk-activity .agenda-link-group.top-line {
  border-top: thin solid #bbbbbb;
}
main.emerging-issues.hub-page .pills.tag-pills-alignment {
  flex-wrap: wrap;
}
main.emerging-issues.topic-detail-page span.isonet-content {
  justify-content: flex-end;
  margin-left: auto;
  margin-right: 0;
  color: #ffffff;
}
main.emerging-issues.topic-detail-page span.material-icons.size {
  font-size: 1.125rem;
  color: #ffffff;
}
main.emerging-issues.topic-detail-page .share-save a#share.sharing + div.popup.saved,
main.emerging-issues.topic-detail-page .share-save a#bookmark.saved + div.popup.saved.popup-content-alignment {
  right: -4rem;
}
main.emerging-issues .author .author-details {
  border-bottom: none;
  padding-bottom: 0px;
}
main.emerging-issues .newsfeed .load-news {
  width: 70%;
}
main.emerging-issues .main-container {
  display: flex;
  background-color: lightgrey;
  align-items: center;
  justify-content: center;
}
main.emerging-issues .text-container {
  margin: 1rem;
  padding: 1rem;
}
main.emerging-issues .vertical-container {
  display: flex;
  flex-direction: column;
  background-color: lightgrey;
  align-items: center;
  justify-content: center;
  margin: 1rem;
  padding: 1rem;
}
main.emerging-issues .youtube-aside {
  width: 15rem;
}

main.legislative-monitoring .site.flex-wrapper .content-wrapper {
  /* max-width: unset; */
}
main.legislative-monitoring .site.flex-wrapper .content-wrapper h1 .source-link {
  font-size: 0.8rem;
  font-weight: normal;
}
main.legislative-monitoring .site.flex-wrapper .content-wrapper h1 .source-link span {
  vertical-align: middle;
  font-size: 0.8rem;
}
main.legislative-monitoring .disclaimer {
  font-size: 0.75rem;
  display: block;
  margin-top: 2rem;
}
main.legislative-monitoring aside.thin {
  display: flex;
  flex-direction: column;
}
main.legislative-monitoring aside.thin section {
  margin-left: auto;
}
main.legislative-monitoring aside section.background-lt-grey ul.link-list li a small {
  font-size: 0.813rem;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper {
  max-width: calc(100% - 17rem);
  /* .lmon-map-view, */
  /* .lmon-list-view.active, .lmon-map-view.active {
      display: block;
  } */
  /* &.width-100 {
      max-width:100%;
      width:100%;
  } */
}
@media (min-width: 67.5rem) {
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper.description .media-container .site .media-player {
    width: 46%;
  }
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper .glossary-hub {
  justify-content: flex-end;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper .glossary-hub a .material-icons {
  vertical-align: middle;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper h2:first-of-type {
  border-bottom: thin solid #bbbbbb;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form {
  padding-bottom: 2rem;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form p, main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .paragraph-wrapper {
  font-size: 1.15rem;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper {
  gap: 2rem;
  flex-wrap: wrap;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .tabs {
  width: unset;
  margin-left: auto;
  gap: 0.5rem;
  align-self: flex-end;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .tabs .tabbed nav {
  padding-top: 0;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .tabs .tabbed nav a span {
  vertical-align: top;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .tabs .tabbed nav a.active span {
  color: #3f3f3f;
}
@media (max-width: 28rem) {
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .pseudo-label input {
    margin-bottom: 0.5rem;
  }
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .pseudo-label span {
    display: none;
  }
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .select-wrapper:after {
  top: 0.5rem;
  right: 0.5rem;
  color: #004eaa;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper .lmon-table-view .results-listing .results-table.scrollable {
  margin-bottom: 2rem;
  /* table {
      min-width: 0;
  } */
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper .lmon-table-view .results-listing .page-results-wrapper {
  padding-top: 1rem;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper .lmon-table-view {
  display: none;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper:has(.table-view.active) .lmon-table-view {
  display: block;
}
main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper:has(.table-view.active) .lmon-map-view {
  display: none;
}
main.legislative-monitoring.hub #usa-map {
  width: 100%;
  height: auto;
}
main.legislative-monitoring.hub svg .annotation-state-active {
  fill: #004eaa;
}
main.legislative-monitoring.hub svg .annotation-state-active + .annotation-line {
  stroke: #bbbbbb;
  opacity: 1;
}
main.legislative-monitoring.hub svg .annotation-line {
  opacity: 0;
}
main.legislative-monitoring.hub svg .annotation-text {
  text-anchor: start;
  pointer-events: none;
}
main.legislative-monitoring.hub svg a[disabled=true] {
  pointer-events: none;
}
main.legislative-monitoring.hub svg a[disabled=true] path {
  fill: #f0f0f0;
}
main.legislative-monitoring.hub svg a[disabled=true] path.annotation-line {
  display: none;
}
main.legislative-monitoring.hub svg a[disabled=true] path.annotation-line + path {
  display: none;
}
main.legislative-monitoring.hub svg a[disabled=true] text {
  display: none;
}
main.legislative-monitoring.hub svg a:not([disabled=true]) path {
  fill: #004eaa;
}
main.legislative-monitoring.hub svg a:not([disabled=true]):hover + .annotation-state-active {
  fill: #002d61;
}
main.legislative-monitoring.hub svg a:not([disabled=true]):hover path {
  fill: #002d61;
}
main.legislative-monitoring.hub svg text {
  font-size: 0.9rem;
  font-style: normal;
  font-variant: normal;
  font-weight: 500;
  font-stretch: normal;
  text-align: center;
  line-height: 100%;
  writing-mode: lr-tb;
  text-anchor: middle;
  fill: #ffffff;
  fill-opacity: 1;
  stroke: none;
  stroke-width: 1px;
  stroke-linecap: butt;
  stroke-linejoin: miter;
  stroke-opacity: 1;
}
main.legislative-monitoring.hub svg + .flex-wrapper {
  justify-content: space-between;
}
@media (max-width: 80rem) {
  main.legislative-monitoring.hub .site.flex-wrapper {
    flex-wrap: wrap;
  }
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper {
    width: 100%;
    max-width: 100%;
  }
  main.legislative-monitoring.hub .site.flex-wrapper aside.thin {
    flex-direction: row;
    width: 100%;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 2rem;
  }
  main.legislative-monitoring.hub .site.flex-wrapper aside.thin section {
    margin: 0;
  }
}
@media (max-width: 67.5rem) {
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper {
    width: 100%;
    max-width: 100%;
  }
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper form .flex-wrapper .tabs {
    display: none;
  }
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper:has(.map-view.active) .lmon-table-view {
    display: block;
  }
  main.legislative-monitoring.hub .site.flex-wrapper .content-wrapper:has(.map-view.active) .lmon-map-view {
    display: none;
  }
}
@media (max-width: 48rem) {
  main.legislative-monitoring.hub .site.flex-wrapper aside.thin {
    flex-direction: column;
  }
  main.legislative-monitoring.hub .site.flex-wrapper aside.thin section {
    width: 100%;
  }
}
main.legislative-monitoring.search h1 {
  margin-top: 1rem;
}
main.legislative-monitoring.search .site.flex-wrapper {
  gap: 0rem;
}
main.legislative-monitoring.search .site.flex-wrapper aside.thin.filter {
  width: 15rem;
}
main.legislative-monitoring.search .site.flex-wrapper aside.thin.filter details summary span {
  max-width: 85%;
}
main.legislative-monitoring.search .site.flex-wrapper .content-wrapper {
  max-width: calc(98% - 16rem);
}
main.legislative-monitoring.search .site.flex-wrapper .content-wrapper .glossary-search {
  justify-content: flex-end;
}
main.legislative-monitoring.search .site.flex-wrapper .content-wrapper .glossary-search a .material-icons {
  vertical-align: middle;
}
main.legislative-monitoring.search .site.flex-wrapper .content-wrapper .results-listing .results-table table th a.sortarrow {
  display: flex;
  width: -moz-max-content;
  width: max-content;
}
main.legislative-monitoring.search .site.flex-wrapper .content-wrapper .results-listing .results-table table th span.material-icons {
  margin: auto;
}
@media (max-width: 67.5rem) {
  main.legislative-monitoring.search .site.flex-wrapper {
    flex-direction: column;
  }
  main.legislative-monitoring.search .site.flex-wrapper aside.filter.thin {
    width: 100%;
    margin-left: 0;
  }
  main.legislative-monitoring.search .site.flex-wrapper aside.filter.thin section {
    width: 100%;
  }
  main.legislative-monitoring.search .site.flex-wrapper .content-wrapper {
    max-width: 100%;
  }
}
main.legislative-monitoring.search form label {
  display: flex;
}
main.legislative-monitoring.search form label.date-range {
  align-items: center;
}
main.legislative-monitoring.search form label.date-range input[type=date] {
  margin-left: auto;
}
main.legislative-monitoring.search .content-wrapper .results-meta-sort {
  padding: 2rem 0 0;
  font-weight: 500;
  justify-content: space-between;
  gap: 0.5rem;
  flex-wrap: wrap;
}
main.legislative-monitoring.search .content-wrapper .results-meta-sort .download-results {
  font-size: 0.9rem;
}
main.legislative-monitoring.search .content-wrapper .results-meta-sort .download-results span {
  display: inline-block;
  vertical-align: middle;
  font-weight: normal;
}
main.legislative-monitoring.detail h1 + section {
  padding-top: 0;
}
main.legislative-monitoring.detail h1 + .site.flex-wrapper {
  align-items: center;
}
main.legislative-monitoring.detail h1 + .site.flex-wrapper .glossary {
  margin-left: auto;
}
main.legislative-monitoring.detail h1 + .site.flex-wrapper .glossary .material-icons {
  vertical-align: middle;
}
main.legislative-monitoring.detail .material-icons.filed {
  color: #CD792D;
}
main.legislative-monitoring.detail .material-icons.under-review {
  color: #737373;
}
main.legislative-monitoring.detail .material-icons.approved {
  color: #006a35;
  font-weight: 700;
}
main.legislative-monitoring.detail .material-icons.withdrawn, main.legislative-monitoring.detail .material-icons.disapproved {
  color: #d40019;
  font-weight: 700;
}
main.legislative-monitoring.detail .site.flex-wrapper .content-wrapper {
  max-width: unset;
}
main.legislative-monitoring.detail .site.flex-wrapper .content-wrapper h2 {
  font-size: 1.7rem;
}
main.legislative-monitoring.detail .site.flex-wrapper .content-wrapper section:not(:first-of-type) {
  padding-top: 0;
}
main.legislative-monitoring.detail .overview-fields {
  align-items: flex-start;
  gap: 1rem;
  line-height: 1.8;
  justify-content: space-between;
  flex-wrap: wrap;
}
main.legislative-monitoring.detail .overview-fields .group {
  width: 30%;
}
main.legislative-monitoring.detail .overview-fields .group .pseudo-label {
  width: 100%;
}
main.legislative-monitoring.detail .overview-fields .group .pseudo-label b {
  font-size: 1rem;
  display: inline-block;
}
main.legislative-monitoring.detail .description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
main.legislative-monitoring.detail #showMore {
  display: none;
}
main.legislative-monitoring.detail .toggle {
  font-size: 0.9rem;
  transform: none;
  display: block;
  box-shadow: unset;
  position: unset;
  padding: 1rem 0 0 0;
}
main.legislative-monitoring.detail .toggle .less {
  display: none;
}
main.legislative-monitoring.detail section:has(#showMore:checked) .description {
  display: block;
}
main.legislative-monitoring.detail section:has(#showMore:checked) .toggle .more {
  display: none;
}
main.legislative-monitoring.detail section:has(#showMore:checked) .toggle .less {
  display: block;
}
main.legislative-monitoring.detail .legend {
  font-size: 0.9rem;
  padding: 1rem 0;
  border-top: thin solid #bbbbbb;
  border-bottom: thin solid #bbbbbb;
  flex-wrap: wrap;
}
main.legislative-monitoring.detail .legend b {
  padding-right: 0.5rem;
}
main.legislative-monitoring.detail .legend span {
  display: flex;
  padding: 0.25rem 0;
}
main.legislative-monitoring.detail .legend span i {
  vertical-align: middle;
  margin-right: 0.5rem;
}
main.legislative-monitoring.detail .legend span:not(:last-of-type) {
  padding-right: 1rem;
}
main.legislative-monitoring.detail form {
  margin-top: 1.5rem;
}
main.legislative-monitoring.detail form .flex-wrapper {
  flex-direction: column;
  align-items: flex-start;
}
main.legislative-monitoring.detail form .flex-wrapper.timeline-controls {
  flex-direction: row;
  gap: 2rem;
  padding-bottom: 1rem;
}
main.legislative-monitoring.detail form .flex-wrapper.timeline-controls label {
  display: flex;
  align-items: flex-start;
}
main.legislative-monitoring.detail form .flex-wrapper.timeline-controls label input {
  margin: 0.25rem 0.5rem 0 0;
}
main.legislative-monitoring.detail form .flex-wrapper.timeline-controls label b {
  padding: 0;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label {
  display: table;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label input,
main.legislative-monitoring.detail form .flex-wrapper.filings label b,
main.legislative-monitoring.detail form .flex-wrapper.filings label a,
main.legislative-monitoring.detail form .flex-wrapper.filings label span.material-icons,
main.legislative-monitoring.detail form .flex-wrapper.filings label .element-wrapper {
  display: table-cell;
  padding: 0.5rem;
  vertical-align: top;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label input {
  width: 1rem;
  height: 1rem;
  margin-top: 0.75rem;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label b {
  font-size: 1rem;
  width: 18rem;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label a {
  width: 10rem;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label span.material-icons {
  width: 2.8rem;
  font-size: 1.25rem;
  text-align: center;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label .element-wrapper {
  width: 12rem;
  font-weight: normal;
}
main.legislative-monitoring.detail form .flex-wrapper.filings label .element-wrapper .badge {
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
  background-color: #666666;
  color: #ffffff;
  margin-left: 0.2rem;
  border-radius: 0.25rem;
  font-weight: 600;
  vertical-align: middle;
}
main.legislative-monitoring.detail textarea {
  width: 100%;
  margin-top: 1.5rem;
}
main.legislative-monitoring.detail .contact-info-panel .flex-wrapper {
  margin-top: 1.5rem;
  gap: 3rem;
  align-items: flex-start;
  flex-wrap: wrap;
}
main.legislative-monitoring.detail .contact-info-panel img {
  height: 2rem;
}
main.legislative-monitoring.detail .contact-info-panel ul {
  margin: 0;
  padding: 0;
}
main.legislative-monitoring.detail .contact-info-panel ul span {
  display: block;
  padding-bottom: 0.5rem;
  font-weight: 700;
}
main.legislative-monitoring.detail .contact-info-panel ul li {
  list-style: none;
  line-height: 1.8;
}
main.legislative-monitoring.detail .contact-info-panel ul li a {
  font-weight: 500;
}
@media (max-width: 46.75rem) {
  main.legislative-monitoring.detail form .flex-wrapper.filings label {
    display: flex;
    flex-wrap: wrap;
  }
  main.legislative-monitoring.detail form .flex-wrapper.filings label input,
  main.legislative-monitoring.detail form .flex-wrapper.filings label b,
  main.legislative-monitoring.detail form .flex-wrapper.filings label a,
  main.legislative-monitoring.detail form .flex-wrapper.filings label span.material-icons,
  main.legislative-monitoring.detail form .flex-wrapper.filings label .element-wrapper {
    display: unset;
  }
  main.legislative-monitoring.detail form .flex-wrapper.filings label b {
    width: calc(100% - 2rem);
  }
  main.legislative-monitoring.detail form .flex-wrapper.filings label a {
    margin-left: 1.5rem;
    width: 8.5rem;
  }
  main.legislative-monitoring.detail form .flex-wrapper.filings label span.material-icons {
    padding-left: 0;
    width: 2rem;
  }
  main.legislative-monitoring.detail form .flex-wrapper.filings label .element-wrapper {
    width: auto;
    padding: 0.5rem 0;
  }
}
main.legislative-monitoring.glossary h1 {
  font-size: 1.75rem;
  padding-bottom: 2.5rem;
}
main.legislative-monitoring.glossary h2 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-top: 0;
}
main.legislative-monitoring.glossary h3, main.legislative-monitoring.glossary .cards .heading-wrapper, .cards main.legislative-monitoring.glossary .heading-wrapper {
  font-size: 1.1rem;
}
main.legislative-monitoring.glossary h3:not(:first-of-type), main.legislative-monitoring.glossary .cards .heading-wrapper:not(:first-of-type), .cards main.legislative-monitoring.glossary .heading-wrapper:not(:first-of-type) {
  padding-top: 0.75rem;
}
main.legislative-monitoring.glossary .content-wrapper {
  position: relative;
}
main.legislative-monitoring.glossary section table {
  font-size: 0.8rem;
  max-width: 70rem;
}
main.legislative-monitoring.glossary section table th {
  font-weight: 700;
}
main.legislative-monitoring.glossary section table th.fieldname {
  width: 15rem;
}
main.legislative-monitoring.glossary section table th.abbreviation {
  width: 7rem;
}
main.legislative-monitoring.glossary section table td {
  padding: 0.5rem;
}
main.legislative-monitoring.glossary section table td:first-of-type {
  font-weight: 700;
}
main.legislative-monitoring.glossary section table p, main.legislative-monitoring.glossary section table .paragraph-wrapper {
  margin: 0.25rem 0;
  font-size: 0.8rem;
}
main.legislative-monitoring.glossary section table p:first-of-type, main.legislative-monitoring.glossary section table .paragraph-wrapper:first-of-type {
  margin-top: 0;
}
main.legislative-monitoring.glossary section table p:last-of-type, main.legislative-monitoring.glossary section table .paragraph-wrapper:last-of-type {
  margin-bottom: 0;
}
@media (max-width: 92.5rem) {
  main.legislative-monitoring.glossary section table {
    max-width: 100%;
  }
}
main.legislative-monitoring.glossary section:not(:last-of-type) {
  padding-bottom: 0;
}
main.legislative-monitoring.glossary section.lob-abbreviations table {
  max-width: 50rem;
}
main.legislative-monitoring.glossary .disclaimer {
  max-width: 65rem;
}
@media (max-width: 79.99rem) {
  main.legislative-monitoring.glossary .lob-abbreviations {
    display: flex;
    flex-wrap: wrap;
  }
  main.legislative-monitoring.glossary .lob-abbreviations h2 {
    width: 100%;
  }
  main.legislative-monitoring.glossary .lob-abbreviations div {
    width: 45%;
  }
  main.legislative-monitoring.glossary .lob-abbreviations div:first-of-type {
    margin-right: 2rem;
  }
}
@media (max-width: 42.5rem) {
  main.legislative-monitoring.glossary .lob-abbreviations {
    flex-direction: column;
  }
  main.legislative-monitoring.glossary .lob-abbreviations div {
    width: 100%;
  }
  main.legislative-monitoring.glossary .lob-abbreviations div:first-of-type {
    margin-right: 0;
  }
}
@media (min-width: 80rem) {
  main.legislative-monitoring.glossary .content-wrapper {
    display: flex;
    flex-wrap: wrap;
  }
  main.legislative-monitoring.glossary .content-wrapper h1 {
    width: 100%;
  }
  main.legislative-monitoring.glossary .content-wrapper .field-definitions {
    width: 60%;
    padding-right: 1.5rem;
    padding-top: 0;
    margin-right: 1.5rem;
    border-right: thin solid #efefef;
  }
  main.legislative-monitoring.glossary .content-wrapper .lob-abbreviations {
    padding-top: 0;
    width: 33%;
  }
}
@media (max-width: 67.5rem) {
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper {
    flex-direction: column;
  }
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper aside:not(.filter) {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
    gap: 1rem;
  }
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper aside:not(.filter) section {
    margin-left: 0;
  }
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper aside:not(.filter) section:not(:first-of-type) {
    margin-top: 0;
  }
  main.legislative-monitoring:not(.search):not(.hub) h1 + .site.flex-wrapper {
    align-items: flex-start;
    flex-direction: row;
  }
}
@media (max-width: 48rem) {
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper aside:not(.filter) {
    flex-direction: column;
  }
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper aside:not(.filter) section {
    width: 100%;
  }
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper .overview-fields {
    flex-direction: column;
    gap: 0;
  }
  main.legislative-monitoring:not(.search):not(.hub) .site.flex-wrapper .overview-fields .group {
    width: 100%;
  }
}

main.right-dynamic-rail aside {
  margin-left: auto;
}
main.right-dynamic-rail aside section ul li p, main.right-dynamic-rail aside section ul li .paragraph-wrapper {
  font-size: 0.8rem;
  font-weight: 500;
}
main.right-dynamic-rail aside section ul li .bottom-links {
  font-size: 0.8rem;
}
main.right-dynamic-rail aside section ul li .bottom-links a:not(:first-of-type) {
  padding-left: 0.5rem;
  margin-left: 0.25rem;
  border-left: thin solid #bbbbbb;
}
main.right-dynamic-rail aside section ul li .bottom-links span.material-icons {
  font-size: 0.8rem;
  vertical-align: middle;
  margin-left: 0.2rem;
}

main.lob .hot-topics .cards, main.lob .major-projects .cards {
  flex-wrap: wrap;
}
main.lob .hot-topics .cards .card, main.lob .major-projects .cards .card {
  overflow-wrap: anywhere;
}
main.lob .hot-topics .cards .card .flex-wrapper, main.lob .major-projects .cards .card .flex-wrapper {
  align-items: flex-start;
  gap: 1rem;
}
main.lob .hot-topics .cards .card img, main.lob .major-projects .cards .card img {
  width: 10rem;
  height: 10rem;
}
main.lob .hot-topics .cards .card h3, main.lob .hot-topics .cards .card .heading-wrapper, main.lob .major-projects .cards .card h3, main.lob .major-projects .cards .card .heading-wrapper {
  padding-bottom: 0.25rem;
}
main.lob .hot-topics .cards .card small, main.lob .major-projects .cards .card small {
  text-transform: uppercase;
  font-size: 0.8rem;
  font-weight: 500;
}
main.lob .hot-topics .cards .card p, main.lob .hot-topics .cards .card .paragraph-wrapper, main.lob .major-projects .cards .card p, main.lob .major-projects .cards .card .paragraph-wrapper {
  padding-top: 0.25rem;
  font-size: 1rem;
}
main.lob .hot-topics .cards .card .paragraph-wrapper, main.lob .major-projects .cards .card .paragraph-wrapper {
  padding-top: 0.25rem;
}
main.lob .hot-topics .cards .card .card-links, main.lob .major-projects .cards .card .card-links {
  padding-top: 1rem;
  font-size: 0.9rem;
}
main.lob .hot-topics .cards .card .card-links a:not(:first-of-type), main.lob .major-projects .cards .card .card-links a:not(:first-of-type) {
  padding-left: 0.5rem;
  margin-left: 0.25rem;
  border-left: thin solid #bbbbbb;
}
main.lob .hot-topics .cards .card .card-links span.material-icons, main.lob .major-projects .cards .card .card-links span.material-icons {
  font-size: 0.8rem;
  vertical-align: middle;
}
@media (max-width: 78.125rem) {
  main.lob .hot-topics .cards .card .card-links a, main.lob .major-projects .cards .card .card-links a {
    line-height: 2;
    display: block;
  }
  main.lob .hot-topics .cards .card .card-links a:not(:first-of-type), main.lob .major-projects .cards .card .card-links a:not(:first-of-type) {
    padding-left: 0;
    margin-left: 0;
    border-left: 0;
  }
}
@media (max-width: 48rem) {
  main.lob .hot-topics .cards, main.lob .major-projects .cards {
    flex-direction: column;
  }
  main.lob .hot-topics .cards .card, main.lob .major-projects .cards .card {
    width: 100%;
  }
  main.lob .hot-topics .cards .card img, main.lob .major-projects .cards .card img {
    display: none;
  }
  main.lob .hot-topics .cards .card .card-links a, main.lob .major-projects .cards .card .card-links a {
    display: inline-block;
  }
  main.lob .hot-topics .cards .card .card-links a:not(:first-of-type), main.lob .major-projects .cards .card .card-links a:not(:first-of-type) {
    padding-left: 0.5rem;
    margin-left: 0.25rem;
    border-left: thin solid #bbbbbb;
  }
}
main.lob .upcoming-events .center {
  text-align: center;
}
main.lob .upcoming-events .cards .card time {
  font-weight: 500;
}
main.lob .upcoming-events .cards .card button .secondary {
  border-radius: none;
}
main.lob .progress_bar {
  display: flex;
}
main.lob .progress_bar .status {
  margin-bottom: 28px;
}
main.lob .progress_bar .statusdetails {
  font-size: 1rem;
}
main.lob .progress_bar .stepper-wrapper {
  position: relative;
  right: 7%;
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.25rem;
  min-width: 65rem;
}
main.lob .progress_bar .stepper-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
@media (max-width: 48rem) {
  main.lob .progress_bar .stepper-item {
    font-size: 0.75rem;
  }
}
main.lob .progress_bar .stepper-item::before {
  position: absolute;
  content: "";
  border-bottom: 0.188 solid #ccc;
  width: 100%;
  top: 1rem;
  left: -50%;
  z-index: 2;
}
main.lob .progress_bar .stepper-item::after {
  position: absolute;
  content: "";
  border-bottom: 0.125rem solid #ccc;
  width: 100%;
  top: 1rem;
  left: 50%;
  z-index: 2;
}
main.lob .progress_bar .stepper-item .step-counter {
  position: relative;
  z-index: 5;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1.875rem;
  height: 1.875rem;
  border-radius: 50%;
  background: #ffffff;
  border: 0.125rem solid #bbbbbb;
  margin-bottom: 0.375rem;
}
main.lob .progress_bar .stepper-item.active {
  font-weight: 700;
}
main.lob .progress_bar .material-icons {
  color: #ffffff;
  font-weight: 700;
}
main.lob .progress_bar .stepper-item.completed .step-counter {
  border: 2px solid #004eaa;
  background-color: #004eaa;
}
main.lob .progress_bar .stepper-item.completed .step-name {
  font-weight: 700;
}
main.lob .progress_bar .stepper-item.completed::after {
  position: absolute;
  content: "";
  border-bottom: 0.188rem solid #2480d5;
  width: 100%;
  top: 1rem;
  left: 50%;
  z-index: 3;
}
main.lob .progress_bar .step-dt {
  color: #ccc;
}
main.lob .progress_bar .stepper-item:first-child::before {
  content: none;
}
main.lob .progress_bar .stepper-item:last-child::after {
  content: none;
}
main.lob .progress_bar a {
  font-weight: 700;
}
main.lob .progress_bar a span {
  top: 6px;
  position: relative;
}
main.lob .past-events .card img {
  width: 30%;
}
main.lob .past-events .card p, main.lob .past-events .card .paragraph-wrapper {
  font-size: 1rem;
}
main.lob .past-events .card .card-links {
  font-size: 0.9rem;
}
main.lob .past-events .card .card-links a:not(:first-of-type) {
  padding-left: 0.5rem;
  margin-left: 0.25rem;
  border-left: thin solid #bbbbbb;
}
main.lob .past-events .card .card-links span.material-icons {
  font-size: 0.8rem;
  vertical-align: middle;
}
main.lob .project-status {
  padding: 0 0 1rem;
  border-bottom: thin solid #bbbbbb;
}
main.lob .project-status details summary {
  border: none;
}
main.lob .project-status details summary:after {
  margin-left: 0;
}
main.lob .project-status details table {
  border-collapse: separate;
  border-spacing: 0.75rem 0;
  margin-left: -0.75rem;
}
main.lob .project-status details table th, main.lob .project-status details table td {
  background-color: #ffffff;
}
main.lob .project-status details table th:first-of-type, main.lob .project-status details table td:first-of-type {
  padding-left: 0;
}
main.lob .project-status details table th {
  font-size: 0.85rem;
  border-bottom: thin dotted;
}
main.lob .project-status details table th.status, main.lob .project-status details table th.date {
  width: 5rem;
}
main.lob .project-status details table td {
  border-bottom: none;
}
main.lob .project-status details table td.status, main.lob .project-status details table td.date {
  font-size: 0.85rem;
  font-weight: 500;
}
main.lob .project-status details table td .material-icons {
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.1rem;
  background-color: #3f3f3f;
  color: white;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 0.25rem;
}
main.lob .project-status details table td p, main.lob .project-status details table td .paragraph-wrapper {
  width: 100%;
  font-size: 0.9rem;
}
main.lob .project-status details table td:first-child {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
main.lob .events .cards.flex-wrapper .card .event-button {
  text-align: center;
}
main.lob .events .cards.flex-wrapper .card .event-button a.secondary {
  background-color: #ffffff;
  color: #004eaa;
  border-radius: 0.25rem;
  border: 1px solid #004EAA;
}
main.lob .events .cards.flex-wrapper .card .event-button a.secondary:hover {
  color: #002d61;
  border-color: #002d61;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section {
  padding-bottom: 0;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section h2 {
  font-size: 1.7rem;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section h3, main.lob.deep-dive .site.flex-wrapper .content-wrapper section .cards .heading-wrapper, .cards main.lob.deep-dive .site.flex-wrapper .content-wrapper section .heading-wrapper {
  font-size: 1.1rem;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section p:last-of-type, main.lob.deep-dive .site.flex-wrapper .content-wrapper section .paragraph-wrapper:last-of-type {
  margin-bottom: 0;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section ul {
  margin-bottom: 0;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section ul.highlights {
  list-style-type: none;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section ul.highlights li {
  padding-bottom: 0.5rem;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section ul.highlights li::before {
  font-family: "Material Icons";
  content: "square";
  -webkit-font-feature-settings: "liga";
  vertical-align: middle;
  margin-right: 0.5rem;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section.pills {
  border-bottom: none;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section.author {
  border-top: thin solid #bbbbbb;
  margin-top: 2.5rem;
  padding-top: 0;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section.author h2 {
  margin-bottom: 0;
}
main.lob.deep-dive .site.flex-wrapper .content-wrapper section.author img.author-photo {
  height: 5rem;
}
main.lob.deep-dive .bio .social-icons a:not(:nth-of-type(3)) {
  display: none;
}
main.lob.deep-dive .bio p, main.lob.deep-dive .bio .paragraph-wrapper {
  display: none;
}
main.lob .sign-in-button {
  font-weight: 500;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
main.lob .sign-in-button .lock-icon {
  font-size: 2rem;
  padding-right: 0.625rem;
  color: #004eaa;
}
main.lob .card-links a {
  font-weight: 500;
  font-size: 1rem;
}
main.lob .hero .share-save {
  text-align: right;
}
main.lob .hero .share-save a span.material-icons {
  font-size: 1rem;
  position: relative;
  bottom: 0.05rem;
  margin-right: 0.188rem;
}
main.lob section.upcoming-events {
  padding: 0rem;
}
main.lob section.upcoming-events.scroll {
  overflow-y: scroll;
  height: 37rem;
}
main.lob section.past-events.scroll {
  overflow-y: scroll;
  height: 18rem;
}
main.lob .agenda-link-group {
  padding-top: 0.5rem;
  font-size: 0.9rem;
}
main.lob .agenda-link-group a:not(:first-of-type) {
  padding-left: 0.5rem;
  margin-left: 0.25rem;
  border-left: thin solid #bbbbbb;
}
main.lob .agenda-link-group span.material-icons {
  font-size: 0.8rem;
  vertical-align: middle;
}
main.lob .agenda-link-group.top-line {
  border-top: thin solid #bbbbbb;
}
main.lob .major-iso-link {
  margin-top: 1.5rem;
}
main.lob .content-wrapper.key-takeaways,
main.lob .verisk-activity-container,
main.lob section.general-liability {
  padding: unset !important;
}
main.lob .left-pane {
  width: 70%;
}
main.lob span.material-icons.register {
  font-weight: 100 !important;
}
main.lob section.major-content.background-dk-blue a,
main.lob section.major-content.background-dk-blue p,
main.lob section.major-content.background-dk-blue .paragraph-wrapper,
main.lob section.major-content.background-dk-blue span,
main.lob section.major-content.background-dk-blue h2 {
  background-color: #002D61;
  color: #ffffff;
}
main.lob section.major-content.background-lt-white a,
main.lob section.major-content.background-lt-white p,
main.lob section.major-content.background-lt-white .paragraph-wrapper,
main.lob section.major-content.background-lt-white span,
main.lob section.major-content.background-lt-white h2 {
  color: #000000;
  background-color: #ffffff;
}
main.lob section.major-content.background-lt-grey a,
main.lob section.major-content.background-lt-grey p,
main.lob section.major-content.background-lt-grey .paragraph-wrapper,
main.lob section.major-content.background-lt-grey span,
main.lob section.major-content.background-lt-grey h2 {
  background-color: #f8f8f8;
  color: #000000;
}

body:not(.signed-in) main.base-page .major-content img {
  width: 100%;
  height: 100%;
}
body:not(.signed-in) main.base-page .references:not(.left-rail-section .references) {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0;
}
body:not(.signed-in) main.base-page .references:not(.left-rail-section .references) details {
  padding: 1.875rem;
}
@media (min-width: calc(92.5rem + 1px)) {
  body:not(.signed-in) main.base-page .references:not(.left-rail-section .references) details {
    padding: 1.875rem 0;
  }
}

main.state-filings {
  /*Component Created in DR Repo */
}
main.state-filings .updates-section--pagination {
  display: flex;
  align-items: center;
  justify-content: center;
}
main.state-filings .updates-section--pagination li {
  list-style: none;
  margin: 0 0.5rem;
  cursor: pointer;
}
main.state-filings .updates-section--pagination li.page-number a.page-link {
  padding: 0 0.5rem;
  background-color: #ffffff;
  border: thin solid #004eaa;
}
main.state-filings .updates-section--pagination li.page-number a.page-link:hover {
  background-color: #004eaa;
  color: #ffffff;
}
main.state-filings .updates-section--pagination li.page-number a.page-link.active {
  background-color: #002d61;
  color: #ffffff;
  pointer-events: none;
}
main.state-filings .updates-section--pagination li a.disabled {
  cursor: default;
}
main.state-filings .updates-section--pagination li a.disabled span {
  color: #737373;
  pointer-events: none;
}
main.state-filings .updates-section {
  flex-grow: 0;
  width: 60%;
}
main.state-filings .cards-direction {
  flex-direction: column;
}
main.state-filings .cards .helper {
  padding: 0rem;
}
main.state-filings .updates-date {
  color: #999999;
  font-size: 1.25rem;
}
main.state-filings .margin-helper {
  margin-bottom: 0.5rem;
}
main.state-filings .width-align {
  margin-top: 0;
  width: 90%;
}
main.state-filings .bottom-bor {
  border-bottom: thin solid #efefef;
}
main.state-filings .nav-with-arrow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
main.state-filings .cta-container {
  display: flex;
  gap: 1rem;
  width: 50%;
  margin: auto 0;
}
main.state-filings .cta-action-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  gap: 1rem;
  padding: 2rem 0 2rem 0;
}
main.state-filings .cta-border {
  border: 1px solid #004eaa;
}
main.state-filings .cta-image {
  background-color: #A4C8F0;
  width: 15%;
  height: 200%;
  border-radius: 50%;
}
main.state-filings .cta-text {
  color: #004eaa;
}
main.state-filings #slide:checked + .toggle .state-filings .topic-detail-page .site .cta-container {
  flex-direction: column;
}
main.state-filings .intro-container {
  width: 50%;
}
main.state-filings .padding-left-helper {
  padding-left: 1.875rem;
}
main.state-filings .tabs-helper {
  width: 60%;
}
main.state-filings .bottom-border-helper {
  margin-bottom: 2rem;
  border-bottom: thin solid #bbbbbb;
}
main.state-filings .tabs .border-helper {
  border-bottom: none;
}
main.state-filings .tabs .tabbed nav .tab.border-none {
  border: none;
  border-top: 0.2rem solid;
}
main.state-filings .facet-child {
  margin-left: 1rem;
}
main.state-filings .commercial-lines-child label:nth-of-type(n + 6) {
  display: none;
}
main.state-filings .personal-lines-child label:nth-of-type(n + 6) {
  display: none;
}
main.state-filings .personal-lines .show-less-apl,
main.state-filings .personal-lines .show-more-apl {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
main.state-filings .personal-lines .show-less-apl {
  padding-top: 1rem;
}
main.state-filings .personal-lines .show-less-apl,
main.state-filings .personal-lines .show-more-apl:target {
  display: none;
}
main.state-filings .personal-lines .show-more-apl:target + .show-less-apl {
  display: block;
  border: 0px;
}
main.state-filings .personal-lines:has(.show-more-apl:target) label:nth-of-type(n + 5) {
  display: flex;
}
main.state-filings .personal-lines:has(.show-more-apl:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
main.state-filings .filing-info label:nth-of-type(n + 6) {
  display: none;
}
main.state-filings .filing-info .show-less-fi,
main.state-filings .filing-info .show-more-fi {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
main.state-filings .filing-info .show-less-fi {
  padding-top: 1rem;
  width: 100%;
  margin-left: -0.01rem;
}
main.state-filings .filing-info .show-less-fi,
main.state-filings .filing-info .show-more-fi:target {
  display: none;
}
main.state-filings .filing-info .show-more-fi:target + .show-less-fi {
  display: block;
  border: 0px;
}
main.state-filings .filing-info:has(.show-more-fi:target) label:nth-of-type(n + 6) {
  display: flex;
}
main.state-filings .filing-info:has(.show-more-fi:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
main.state-filings .commercial-lines .show-less-acl,
main.state-filings .commercial-lines .show-more-acl {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
main.state-filings .commercial-lines .show-less-acl {
  padding-top: 1rem;
}
main.state-filings .commercial-lines .show-less-acl,
main.state-filings .commercial-lines .show-more-acl:target {
  display: none;
}
main.state-filings .commercial-lines .show-more-acl:target + .show-less-acl {
  display: block;
  border: 0px;
}
main.state-filings .commercial-lines:has(.show-more-acl:target) label:nth-of-type(n + 5) {
  display: flex;
}
main.state-filings .commercial-lines:has(.show-more-acl:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
main.state-filings .filing-topics label:nth-of-type(n + 6) {
  display: none;
}
main.state-filings .filing-topics .show-less-ft,
main.state-filings .filing-topics .show-more-ft {
  padding: 0.5rem 0 1rem;
  font-size: 0.9rem;
  display: block;
}
main.state-filings .filing-topics .show-less-ft {
  padding-top: 1rem;
}
main.state-filings .filing-topics .show-less-ft,
main.state-filings .filing-topics .show-more-ft:target {
  display: none;
}
main.state-filings .filing-topics .show-more-ft:target + .show-less-ft {
  display: block;
  border: 0px;
}
main.state-filings .filing-topics:has(.show-more-ft:target) label:nth-of-type(n + 6) {
  display: flex;
}
main.state-filings .filing-topics:has(.show-more-ft:target) .options {
  max-height: 20rem;
  overflow-y: scroll;
}
main.state-filings .sfh-filter {
  padding: 0.875rem;
  flex: 0 0 auto;
  min-width: 250px;
}
main.state-filings input[type=checkbox] {
  margin-top: 5px;
}
main.state-filings .FrSearch {
  display: flex;
  width: 100%;
}
main.state-filings .search-results-loader {
  width: 100%;
}
main.state-filings .results-count-heading {
  margin-top: 4.3rem;
  margin-bottom: 2rem;
}
main.state-filings .sfh-results {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
main.state-filings .sfh-results .items-per-page {
  border: thin solid #004eaa;
  padding: 0.1rem 0.4rem 0.1rem 0.4rem;
}
main.state-filings .has-tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 0.0625rem dotted #004eaa;
  color: #004eaa;
  cursor: pointer;
}
main.state-filings .has-tooltip .has-tooltip-text {
  visibility: hidden;
  background-color: white;
  color: black;
  text-align: center;
  border-radius: 0.375rem;
  padding: 0.5rem;
  font-size: 0.8rem;
  transform: translateX(-50%);
  /* Position the tooltip */
  position: absolute;
  z-index: 1;
  top: 100%;
  left: 50%;
  margin-left: -3.75rem;
  /* Add shadow to the tooltip */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.25);
}
main.state-filings .has-tooltip:hover .has-tooltip-text {
  visibility: visible;
  cursor: pointer;
}
main.state-filings .tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 0.0625rem dotted #004eaa;
  color: #004eaa;
}
main.state-filings .tooltip .tooltiptext {
  display: flex;
  visibility: hidden;
  width: 12.5rem;
  background-color: white;
  color: black;
  text-align: left;
  border-radius: 0.5rem;
  padding: 0.125rem;
  position: absolute;
  z-index: 1;
  border: 1rem;
  box-shadow: 0.005rem 0 1rem rgba(0, 0, 0, 0.1);
  font-size: 0.6rem;
  padding-left: 0.5rem;
  transform: translateX(-50%);
}
main.state-filings .position-tooltip-fix {
  top: 1.2rem;
  left: 37%;
}
main.state-filings .position-tooltip {
  top: -0.625rem;
  left: 100%;
}
main.state-filings .tooltip-title {
  display: block;
  text-align: left;
  margin-bottom: 0.1rem;
  margin-left: -0.9375rem;
  padding-left: 0;
  font-weight: bold;
}
main.state-filings .tooltip .tooltiptext ul {
  list-style-type: disc;
  padding-left: 1rem;
  margin-left: 0.5rem;
}
main.state-filings .tooltip .tooltiptext ul li {
  padding-left: 0;
}
main.state-filings .tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -0.3125rem;
  border-width: 0.3125rem;
  border-style: solid;
  border-color: transparent transparent transparent transparent;
}
main.state-filings .tooltip:hover .tooltiptext {
  visibility: visible;
}
main.state-filings .results-listing1 {
  width: 100%;
}
main.state-filings .results-listing1 .results-table .scrollable {
  overflow-x: auto;
}
main.state-filings .results-listing1 .results-table table {
  width: 100%;
  border-collapse: collapse;
  border: 0.0625rem solid #eeeeee;
}
main.state-filings .results-listing1 .results-table table thead th {
  text-align: left;
  padding: 0.625 rem;
  border-bottom: 0.0625rem solid #ddd;
}
main.state-filings .results-listing1 .results-table table thead th a {
  color: #004eaa;
  text-decoration: none;
}
main.state-filings .results-listing1 .results-table table thead th a:hover {
  text-decoration: underline;
}
main.state-filings .results-listing1 .results-table table tbody tr:hover {
  background-color: #f9f9f9;
}
main.state-filings .results-listing1 .results-table table tbody tr th {
  border-bottom: 0.0625rem solid #ddd;
}
main.state-filings .results-listing1 .results-table table tbody tr td {
  padding: 0.625 rem;
  border-bottom: 0.0625rem solid #ddd;
}
main.state-filings .results-listing1 .results-table table tbody tr td.searchresultJurisdiction {
  font-weight: bold;
}
main.state-filings .results-listing1 .page-results-wrapper {
  padding-top: 2rem;
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  flex-wrap: wrap;
}
main.state-filings .results-listing1 .page-results-wrapper select {
  padding: 0.25rem 1rem 0.25rem 0.5rem;
}
main.state-filings .results-listing1 .page-results-wrapper nav {
  margin-left: auto;
}
main.state-filings .results-listing1 .page-results-wrapper nav ul {
  display: flex;
  align-items: center;
}
main.state-filings .results-listing1 .page-results-wrapper nav ul li {
  list-style: none;
  margin: 0 0.5rem;
}
main.state-filings .results-listing1 .page-results-wrapper nav ul li.page-number a.page-link {
  padding: 0 0.5rem;
  background-color: #ffffff;
  border: thin solid #004eaa;
}
main.state-filings .results-listing1 .page-results-wrapper nav ul li.page-number a.page-link:hover {
  background-color: #004eaa;
  color: #ffffff;
}
main.state-filings .results-listing1 .page-results-wrapper nav ul li.page-number a.page-link.active {
  background-color: #002d61;
  color: #ffffff;
  pointer-events: none;
}
main.state-filings .results-listing1 .page-results-wrapper nav ul li a.disabled span {
  color: #737373;
  pointer-events: none;
}
main.state-filings .results-listing1 .page-results-wrapper .select-wrapper {
  display: inline-block;
}
main.state-filings .results-listing1 .page-results-wrapper .select-wrapper:after {
  top: 0.3rem;
  right: 0.3rem;
}
main.state-filings .unfiltered-text {
  width: 40%;
  margin-top: 2.7rem;
}
main.state-filings .landing-banner {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding-left: 1.875rem;
}
main.state-filings .landing-banner .leftPanel {
  flex: 2.2;
  background-color: #2A7DE1;
  color: #ffffff;
  padding: 0.875rem 2.3125rem 0.75rem 1.875rem;
}
main.state-filings .landing-banner .leftPanel h1 {
  padding-bottom: 1rem;
  margin-bottom: 1rem;
  color: #ffffff;
  font-size: 2.25rem;
  font-weight: 500;
}
main.state-filings .landing-banner .leftPanel ul {
  list-style-type: disc;
  padding-left: 1.9rem;
  width: 100%;
}
main.state-filings .landing-banner .leftPanel li:not(:last-child) {
  margin-bottom: 0.5rem;
}
main.state-filings .landing-banner .rightPanel {
  flex: 1;
  background-color: #00358e;
  padding: 1.875rem;
  display: flex;
  flex-direction: column;
  padding-left: 2rem;
  align-items: center;
}
main.state-filings .landing-banner .rightPanel h2 {
  color: #ffffff;
}
main.state-filings .landing-banner .button-design {
  background-color: #FFC600;
  width: 78%;
  padding: 0.8rem 1.5rem;
  margin: 0.625rem 0;
  border: none;
  border-radius: 0.3125rem;
  cursor: pointer;
  font-size: 0.96rem;
  text-align: center;
  color: #000000;
}
main.state-filings .position-back-to-top {
  display: flex;
  flex-direction: column;
  align-items: center;
}
main.state-filings .position-back-to-top .back-to-top-text {
  font-size: 0.75rem;
  margin-left: -1rem;
  color: #004eaa;
  font-weight: 700;
}
main.state-filings .button-icon {
  transition: opacity 0.3s ease-in-out;
  background-image: url(../assets/backtotop.svg);
  background-size: contain;
  background-repeat: no-repeat;
  width: 4rem;
  height: 3rem;
  margin-bottom: -0.5rem;
}
main.state-filings .back-to-top-button {
  position: fixed;
  bottom: 2rem;
  right: 1rem;
  border: none;
  cursor: pointer;
  opacity: 0;
  margin-bottom: -2rem;
}
main.state-filings .back-to-top-button.visible {
  opacity: 1;
}
main.state-filings .back-to-top-fixed {
  position: absolute;
  bottom: 0rem;
  right: 1rem;
  cursor: pointer;
  opacity: 1;
  margin-bottom: -1rem;
}
main.state-filings .details-heading {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}

main.premium-audit-advisory-service .paas-section-hero {
  display: flex;
}
main.premium-audit-advisory-service .paas-section-hero .paas-section-hero-content {
  width: 100%;
}
main.premium-audit-advisory-service .paas-section-hero .paas-section-hero-content h2 {
  font-size: 1.5rem;
  margin: 0 0 1.25rem;
}
main.premium-audit-advisory-service .paas-section-hero .paas-section-hero-content p, main.premium-audit-advisory-service .paas-section-hero .paas-section-hero-content .paragraph-wrapper {
  margin-bottom: 0;
}
main.premium-audit-advisory-service .paas-section-hero img {
  width: 60%;
  height: auto;
}
@media (min-width: 1080px) {
  main.premium-audit-advisory-service .paas-section-hero {
    padding: 2.3333rem 0 1.7222rem 2rem;
    position: relative;
    overflow: hidden;
    width: 100%;
  }
}
@media (min-width: 67.5rem) {
  main.premium-audit-advisory-service .paas-section-hero-content {
    width: 40%;
  }
}
main.premium-audit-advisory-service .tabbed nav .tab {
  flex-grow: unset;
}
main.premium-audit-advisory-service aside.filter.thin {
  margin-left: 2rem;
  padding-top: 0;
}
main.premium-audit-advisory-service aside.filter.thin form li {
  padding-left: 1.5rem;
}
main.premium-audit-advisory-service aside.filter.thin .keyword-class-codes {
  position: relative;
}
main.premium-audit-advisory-service aside.filter.thin .keyword-class-codes .form {
  display: inline-flex;
  flex-wrap: wrap;
  width: 100%;
}
main.premium-audit-advisory-service aside.filter.thin .keyword-class-codes label:first-child {
  color: #004eaa;
  font-weight: 500;
  margin: 0.5rem 0;
  display: block;
}
main.premium-audit-advisory-service aside.filter.thin .keyword-class-codes input {
  padding: 0.625rem 2.5rem 0.625rem 1rem;
  border-radius: 0.5rem 0 0 0.5rem;
  border: 1px solid #707070;
  border-right: none;
  min-height: 2.75rem;
  width: calc(100% - 2.75rem);
  color: #00358e;
  font-size: unset;
}
main.premium-audit-advisory-service aside.filter.thin .keyword-class-codes button {
  font-size: 1.5rem;
  padding: 0.5625rem;
  color: #666666;
  background-color: #ffffff;
  border: 1px solid #707070;
  border-radius: 0 0.5rem 0.5rem 0;
  margin-bottom: 1rem;
  max-width: 44px;
  min-height: 2.75rem;
}
main.premium-audit-advisory-service .results-listing.table-view .scrollable {
  overflow: visible;
}
main.premium-audit-advisory-service .results-listing .cards .card a p, main.premium-audit-advisory-service .results-listing .cards .card a .paragraph-wrapper {
  display: block;
}
main.premium-audit-advisory-service .has-tooltip {
  border-bottom: thin dashed #3f3f3f;
  position: relative;
}
main.premium-audit-advisory-service .has-tooltip .tooltip {
  padding: 0.25rem 1rem;
  overflow-wrap: break-word;
  width: 12rem;
  background: #ffffff;
  color: #3f3f3f;
  box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 0.25rem;
  left: 0;
  transform: translate(calc(-50% + 2rem), 1.5rem);
  display: none;
  z-index: 3;
}
main.premium-audit-advisory-service .has-tooltip .tooltip::before {
  content: "";
  position: absolute;
  top: -0.625rem;
  left: calc(50% - 0.625rem);
  border-bottom: 0.625rem solid #ffffff;
  border-left: 0.625rem solid transparent;
  border-right: 0.625rem solid transparent;
}
main.premium-audit-advisory-service .has-tooltip:hover .tooltip {
  display: block;
}
main.premium-audit-advisory-service .has-tooltip:hover .tooltip::before {
  content: "";
  position: absolute;
  top: -0.625rem;
  left: calc(50% - 0.625rem);
  border-bottom: 0.625rem solid #ffffff;
  border-left: 0.625rem solid transparent;
  border-right: 0.625rem solid transparent;
}
main.premium-audit-advisory-service.search .no-class-code {
  font-size: 16px;
  line-height: 22px;
  font-weight: 400;
}
main.premium-audit-advisory-service.search .no-class-code b {
  font-size: 16px;
  line-height: 22px;
  font-weight: 600;
}
main.premium-audit-advisory-service.search .flex-wrapper .content-wrapper {
  max-width: calc(98% - 21.125rem);
  padding-top: 0;
  width: 100%;
}
main.premium-audit-advisory-service.search .flex-wrapper .content-wrapper .clearTags {
  display: inline-block;
  color: #4A4A4A;
  font-family: Roboto;
  font-size: 0.95rem;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  cursor: pointer;
  margin: 0.25rem;
  padding: 0.25rem 0.5rem;
}
main.premium-audit-advisory-service.search .results-meta-sort {
  padding: 2rem 0 0;
  gap: 2rem;
  font-size: 0.9rem;
}
main.premium-audit-advisory-service.search .results-meta-sort .tabs {
  width: unset;
}
main.premium-audit-advisory-service.search .results-meta-sort .tabs .tabbed {
  border-bottom: none;
}
main.premium-audit-advisory-service.search .results-meta-sort .tabs .tabbed nav {
  padding-top: 0;
  margin-left: 0.5rem;
}
main.premium-audit-advisory-service.search .results-meta-sort .tabs .tabbed nav a.active span {
  color: #3f3f3f;
  font-weight: 700;
}
main.premium-audit-advisory-service.search .results-meta-sort .tabs .tabbed nav span {
  vertical-align: top;
  color: #004eaa;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table {
  min-width: unset;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table a {
  color: #004eaa;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table a:hover {
  text-decoration: none;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table thead th {
  cursor: pointer;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table thead th:last-child {
  cursor: auto;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table thead th:last-child a {
  cursor: auto;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table thead th:nth-of-type(2) {
  box-shadow: none;
  width: 40%;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table tbody th {
  font-weight: normal;
}
main.premium-audit-advisory-service.search .results-listing.table-view .results-table table tbody td:nth-of-type(1) {
  box-shadow: none;
}
main.premium-audit-advisory-service.search .results-listing.list-view {
  border: thin solid #eeeeee;
  padding: 0 0.75rem;
}
main.premium-audit-advisory-service.search .results-listing.list-view .card p:first-child, main.premium-audit-advisory-service.search .results-listing.list-view .card .paragraph-wrapper:first-child {
  color: #004eaa;
}
main.premium-audit-advisory-service.search .results-listing.list-view .card:last-child {
  border-bottom: none;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper {
  padding: 0 1.875rem;
  max-width: 48rem;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper h2 {
  font-size: 1.5rem;
  margin-bottom: 0.4rem;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .paas-search-breadcrumb span {
  text-transform: uppercase;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .share-save {
  gap: 1rem;
  margin-top: 0;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper p strong, main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .paragraph-wrapper strong {
  font-size: 1.2rem;
  font-weight: 400;
  margin-bottom: 0;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .tabbed.dynamic-tab hr {
  border: thin solid #efefef;
  margin: 0;
  position: relative;
  top: 3.44rem;
  opacity: 1;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .tabbed.dynamic-tab .sub-tab-content h3, main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .tabbed.dynamic-tab .sub-tab-content .cards .heading-wrapper, .cards main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .tabbed.dynamic-tab .sub-tab-content .heading-wrapper {
  font-weight: 700;
  font-size: 1rem;
}
main.premium-audit-advisory-service .site.flex-wrapper .faq-search-wrapper .tabbed.dynamic-tab .sub-tab-content .link-list li {
  display: flex;
  gap: 0.4rem;
}
main.premium-audit-advisory-service .paas-class-guides,
main.premium-audit-advisory-service .paas-board-bureau,
main.premium-audit-advisory-service .paas-educational,
main.premium-audit-advisory-service .paas-legislative {
  padding-top: 0;
}
main.premium-audit-advisory-service .paas-class-guides-topSection {
  padding-right: 0;
  width: 100%;
  max-width: 100%;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-title {
  margin: 0.625rem 0;
  font-size: 1.25rem;
  line-height: 1.25;
  display: block;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details {
  font-size: 0.875rem;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item {
  margin-bottom: 0.5rem;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item .label {
  font-weight: 700;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item .detail {
  display: inline-flex;
  align-items: flex-start;
  flex: 1;
  margin-left: 0.25rem;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item .detail .has-tooltip {
  color: #3f3f3f;
  border-bottom: thin dashed #004eaa;
  position: relative;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item .detail .has-tooltip .tooltip {
  padding: 0.25rem 1rem;
  width: 12rem;
  background: #ffffff;
  color: #3f3f3f;
  box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 0.25rem;
  left: 0;
  transform: translate(calc(-50% + 2rem), 1.5rem);
  display: none;
  z-index: 3;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item .detail .has-tooltip:hover .tooltip {
  display: block;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item .detail a {
  display: inline-flex;
  align-items: center;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-details-item.flex {
  display: flex;
}
main.premium-audit-advisory-service .paas-class-guides-topSection .paas-class-guides-actions .share-save {
  gap: 0.625rem;
  justify-content: flex-end;
  margin-top: 1rem;
}
main.premium-audit-advisory-service .paas-class-guides-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1140px;
  width: 100%;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol {
  flex-basis: 100%;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabs {
  padding-bottom: 1.25rem;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabs .tabbed nav {
  border-bottom: 1px solid #ebebeb;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabs .tabbed nav .tab.active {
  font-weight: 700;
  color: #3f3f3f;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabs .tabbed nav .tab {
  font-weight: 400;
  margin-bottom: -3px;
  padding: 0.625rem;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent.active {
  display: inline-block;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent {
  display: none;
  width: 100%;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .content-label {
  margin-top: 0;
  margin-bottom: 0.375rem;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists {
  margin: 0 0 1.25rem;
  list-style: none;
  padding-left: 0;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping p, main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping .paragraph-wrapper {
  display: flex;
  gap: 1rem;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping p span, main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping .paragraph-wrapper span {
  flex-basis: 20%;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping p div, main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping .paragraph-wrapper div {
  flex-basis: 80%;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping p span:last-child, main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists .industry-mapping .paragraph-wrapper span:last-child {
  flex-basis: 80%;
}
main.premium-audit-advisory-service .paas-class-guides-content-leftCol .tabContent .linkLists li {
  margin-bottom: 0.5rem;
}
main.premium-audit-advisory-service .paas-class-guides-content-rightCol {
  flex-basis: 100%;
  padding-top: 1rem;
}
main.premium-audit-advisory-service .paas-class-guides-content-rightCol aside {
  padding: 0;
  width: 100%;
}
main.premium-audit-advisory-service .paas-class-guides-content-rightCol aside h2 {
  border-bottom-color: #e5e5e5;
}
main.premium-audit-advisory-service .paas-class-guides-content-rightCol aside.thin section {
  margin-bottom: 1.25rem;
}
@media (min-width: 1081px) {
  main.premium-audit-advisory-service .paas-class-guides-topSection {
    padding-right: 1.25rem;
    max-width: 798px;
  }
  main.premium-audit-advisory-service .paas-class-guides-content {
    flex-wrap: nowrap;
  }
  main.premium-audit-advisory-service .paas-class-guides-content-leftCol {
    flex-basis: 70%;
  }
  main.premium-audit-advisory-service .paas-class-guides-content-rightCol {
    flex-basis: 30%;
  }
}
main.premium-audit-advisory-service .paas-board-bureau-topSection {
  padding-right: 0;
  width: 100%;
  max-width: 100%;
}
main.premium-audit-advisory-service .paas-board-bureau-topSection .paas-board-bureau-title {
  color: #000000;
  margin: 0.625rem 0;
  font-size: 1.25rem;
  line-height: 1.25;
  display: block;
}
main.premium-audit-advisory-service .paas-board-bureau-topSection .paas-board-bureau-details {
  font-size: 0.875rem;
}
main.premium-audit-advisory-service .paas-board-bureau-topSection .paas-board-bureau-details-item {
  margin-bottom: 0.5rem;
}
main.premium-audit-advisory-service .paas-board-bureau-topSection .paas-board-bureau-details-item .label {
  font-weight: 700;
}
main.premium-audit-advisory-service .paas-board-bureau-topSection .paas-board-bureau-details-item .detail {
  display: inline-flex;
  align-items: flex-start;
  flex: 1;
  margin-left: 0.25rem;
}
main.premium-audit-advisory-service .paas-board-bureau-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  max-width: 1140px;
  width: 100%;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol {
  flex-basis: 100%;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol .tabs {
  padding-bottom: 1.25rem;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol .tabs .tabbed nav {
  border-bottom: 1px solid #ebebeb;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol .tabs .tabbed nav .tab {
  font-weight: 400;
  margin-bottom: -3px;
  padding: 0.625rem;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol .tabs .tabbed nav .tab.active {
  font-weight: 700;
  color: #3f3f3f;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol .tabContent {
  display: none;
}
main.premium-audit-advisory-service .paas-board-bureau-content-leftCol .tabContent.active {
  display: inline-block;
}
@media (min-width: 1081px) {
  main.premium-audit-advisory-service .paas-board-bureau-topSection {
    padding-right: 1.25rem;
    max-width: 798px;
  }
  main.premium-audit-advisory-service .paas-board-bureau-content {
    flex-wrap: nowrap;
  }
  main.premium-audit-advisory-service .paas-board-bureau-content-leftCol {
    flex-basis: 70%;
  }
}
main.premium-audit-advisory-service .hover-lift-cards {
  padding-top: 0;
}
main.premium-audit-advisory-service .hover-lift-cards time {
  font-style: normal !important;
  display: block;
  color: #737373 !important;
}
main.premium-audit-advisory-service .learnCardItem {
  height: 100%;
  background-color: white;
  margin-right: 1rem;
  margin-bottom: 1rem;
  padding: 0.25rem;
}
main.premium-audit-advisory-service .learnCardItem .cardTitle {
  font-size: 21.6px;
  color: #004EAA;
  font-family: Roboto;
  font-weight: 500;
  line-height: 27px;
}
main.premium-audit-advisory-service .learnCardItem p, main.premium-audit-advisory-service .learnCardItem .paragraph-wrapper {
  margin-top: 0.5rem;
  font-family: Roboto;
  color: #3F3F3F;
  font-weight: 400;
  font-size: 18px;
  line-height: 27px;
}
main.premium-audit-advisory-service .learnCardItem .carouselImage {
  height: 200px;
  width: 100%;
}
main.premium-audit-advisory-service .learnCardItem .carouselImage img {
  width: 100%;
  height: 190px;
}
main.premium-audit-advisory-service .learnCardItem:hover {
  box-shadow: 0px 0px 25px 1px rgba(0, 0, 0, 0.15);
}
main.premium-audit-advisory-service .learnCarousel {
  width: 100%;
  padding-left: 2.25rem;
  overflow-x: hidden;
  position: relative;
  margin-bottom: 2rem;
}
main.premium-audit-advisory-service .learnCarousel .slick-slider {
  display: flex;
}
main.premium-audit-advisory-service .learnCarousel .slick-slider .slick-next {
  visibility: hidden;
}
main.premium-audit-advisory-service .learnCarousel .slick-slider .slick-next::after {
  content: ">";
  visibility: visible;
  display: flex;
  position: absolute;
  top: 0.25rem;
  font-size: 50px;
  height: 190px;
  background-color: #1A4999;
  color: #FFFFFF;
  padding-top: 50px;
}
main.premium-audit-advisory-service .learnCarousel .slick-slider .slick-prev {
  display: none !important;
}

.platform-updates h2 {
  margin-bottom: 0;
}
.platform-updates .material-symbols-outlined {
  border: 0.8rem solid #f8f8f8;
  padding: 1.2rem;
  border-radius: 5rem;
  background-color: #eeeeee;
  font-size: 2rem;
}
.platform-updates .messaging {
  margin-left: 1rem;
}
.platform-updates .messaging h3, .platform-updates .messaging .cards .heading-wrapper, .cards .platform-updates .messaging .heading-wrapper {
  margin-bottom: 0;
}
.platform-updates .messaging h3 span, .platform-updates .messaging .cards .heading-wrapper span, .cards .platform-updates .messaging .heading-wrapper span {
  margin-left: 0.2rem;
  padding: 0.2rem 0.4rem 0.2rem;
  border-radius: 5rem;
  background-color: #A4C8F0;
  font-size: 0.6rem;
  font-weight: 400;
  vertical-align: 0.2rem;
}
.platform-updates .messaging p, .platform-updates .messaging .paragraph-wrapper {
  margin-top: 0.5rem;
}

.alert-banner.flex-wrapper {
  position: relative;
  padding: 1rem;
  background-color: #ffe899;
  z-index: 5;
  border-bottom: thin solid #bbbbbb;
  text-align: center;
}
.alert-banner.flex-wrapper .alert-banner-content {
  width: 100%;
  font-size: 0.9rem;
}
.alert-banner.flex-wrapper a {
  margin-left: 1.5rem;
  color: #3f3f3f;
  align-self: flex-start;
}
.alert-banner.flex-wrapper a span {
  font-size: 1.125rem;
  padding: 0.15rem;
}
.alert-banner.flex-wrapper a span:hover {
  border-radius: 50%;
  background-color: rgba(102, 102, 102, 0.2);
}

.alert-banner.hidden {
  display: none;
}

.alert-banner {
  padding: 1.25rem 1.875rem;
  display: flex;
  align-items: center;
}
.alert-banner h2 + .align-side-by-side {
  margin-bottom: 1rem;
  align-items: center;
}
.alert-banner h2 + .align-side-by-side span {
  font-size: 1.34rem;
  line-height: 1;
}
.alert-banner h2 + .align-side-by-side .align-side-by-side__editor {
  font-size: 1rem;
}
.alert-banner__blue {
  background-color: #002D61;
}
.alert-banner__blue h2 {
  color: #ffffff;
}
.alert-banner__blue a {
  color: #ffffff;
  text-decoration: underline;
  font-weight: 500;
}
.alert-banner__blue a:hover {
  color: #d8ebef;
}
.alert-banner__blue div {
  color: #ffffff;
}
.alert-banner__yellow {
  background-color: #ffe899;
}
.alert-banner__yellow a {
  font-weight: 500;
}
.alert-banner__grey {
  background-color: rgba(242, 242, 242, 0.4);
}
.alert-banner__grey a {
  font-weight: 500;
}
.alert-banner__white {
  background-color: #ffffff;
}
.alert-banner__white a {
  font-weight: 500;
}
.alert-banner .align-side-by-side {
  display: flex;
  font-size: 0.9rem;
}
.alert-banner .align-side-by-side__editor {
  padding: 0 0.25rem;
}
.alert-banner span {
  vertical-align: middle;
  font-size: 0.9rem;
  flex: 0 1;
  line-height: 1.5;
}
.alert-banner__container {
  flex: 1;
}
.alert-banner__container p:nth-child(1), .alert-banner__container .paragraph-wrapper:nth-child(1) {
  margin: 0;
}

main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner__grey {
  background-color: #f8f8f8;
}
main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner .align-side-by-side {
  display: inline-block;
  margin-bottom: unset;
}
main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner .align-side-by-side span.material-icons {
  font-size: 1rem;
}
main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner .align-side-by-side p, main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner .align-side-by-side .paragraph-wrapper {
  font-size: 1rem;
  display: inline;
}
main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner .align-side-by-side p a, main .site.flex-wrapper aside section.vertical-alert-banner.alert-banner .align-side-by-side .paragraph-wrapper a {
  text-decoration: none;
}

.modal-container {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 5;
  display: flex;
}
.modal-container .inactivity-modal {
  background-color: #ffffff;
  width: 24rem;
  padding: 1.5rem 0;
  position: absolute;
  border-radius: 0.25rem;
  text-align: center;
  margin-top: 10%;
  left: 50%;
  transform: translateX(-50%);
}
.modal-container .inactivity-modal h2 {
  margin: 0 0 1rem 0;
  padding: 0 4rem;
  font-size: 1.25rem;
  line-height: 1.5rem;
}
.modal-container .inactivity-modal .inactivity-content {
  margin: 0.5rem 0 1rem 0;
}
.modal-container .inactivity-modal .inactivity-content .material-symbols-outlined {
  vertical-align: middle;
  font-size: 1.25rem;
}
.modal-container .inactivity-modal .inactivity-content h2 {
  margin-bottom: 0.5rem;
  padding: 0;
  font-size: 1.25rem;
}
.modal-container .inactivity-modal .inactivity-content p, .modal-container .inactivity-modal .inactivity-content .paragraph-wrapper {
  margin: 0;
}
.modal-container .inactivity-modal .call-to-action {
  padding: 1rem 0 1rem 0;
}
.modal-container .inactivity-modal .call-to-action a, .modal-container .inactivity-modal .call-to-action button {
  padding: 0.65rem 1.5rem;
}
.modal-container .inactivity-modal .call-to-action a.primary, .modal-container .inactivity-modal .call-to-action button.primary {
  margin-left: 0.25rem;
}
.modal-container .inactivity-modal .call-to-action a.tertiary, .modal-container .inactivity-modal .call-to-action button.tertiary {
  border: thin solid #000000;
  color: #000000;
}
.modal-container .inactivity-modal .call-to-action a.tertiary:hover, .modal-container .inactivity-modal .call-to-action button.tertiary:hover {
  background-color: #000000;
  color: #ffffff;
}

.isonet-sign-in-container {
  margin: 8rem auto;
  max-width: 28rem;
  min-height: 28rem;
  padding: 1rem;
}
.isonet-sign-in-container form {
  padding: 2rem;
}
.isonet-sign-in-container form h1 {
  font-weight: 400;
  font-size: 1.5rem;
}
.isonet-sign-in-container form p, .isonet-sign-in-container form .paragraph-wrapper {
  margin: 0;
  font-size: 0.8rem;
  font-weight: 400;
}
.isonet-sign-in-container form input {
  border: none;
  border-bottom: thin solid #bbbbbb;
  background-color: #f8f8f8;
  outline: none;
  width: 100%;
  margin: 1rem 0;
}
.isonet-sign-in-container form input.isonet-user-id, .isonet-sign-in-container form input[type=password] {
  padding: 0.8rem 0;
  font-style: normal;
  font-size: 0.8rem;
}
.isonet-sign-in-container form label.checkbox-wrapper {
  display: flex;
  align-items: center;
  margin-top: 2rem;
}
.isonet-sign-in-container form label.checkbox-wrapper input[type=checkbox] {
  width: unset;
}
.isonet-sign-in-container form label.checkbox-wrapper span {
  padding-left: 0.5rem;
  font-size: 0.8rem;
}
.isonet-sign-in-container form .call-to-action {
  padding: 1rem 0;
}
.isonet-sign-in-container form .call-to-action a {
  box-shadow: 0 0.1rem 0.2rem 0 #bbbbbb;
}
.isonet-sign-in-container form .call-to-action a.primary {
  padding: 0.75rem 9.4rem;
}

@media (max-width: 31.25rem) {
  .isonet-sign-in-container form .call-to-action a.primary {
    padding: 0.75rem 2rem;
  }
}
.toast-example {
  display: flex;
  justify-content: center;
  height: 100vh;
}
.toast-example .notifications {
  position: fixed;
  top: 13.5rem;
  right: 1.25rem;
}
.toast-example .notifications li {
  background-color: #ffffff;
}
.toast-example .notifications .toast {
  width: 28rem;
  position: relative;
  display: flex;
  gap: 1rem;
  overflow: hidden;
  list-style: none;
  padding: 1rem 1.0625rem;
  margin-bottom: 0.625rem;
  box-shadow: 0 0.1rem 0.2rem 0 #bbbbbb;
  justify-content: space-between;
  animation: show_toast 0.3s ease forwards;
}
.toast-example .notifications .toast .material-icons {
  align-self: flex-start;
}
.toast-example .notifications .toast .column {
  display: grid;
}
.toast-example .notifications .toast .column span {
  max-width: 20rem;
}
.toast-example .notifications .toast.success {
  border: thin solid #006a35;
  border-left-width: 0.5rem;
}
.toast-example .notifications .toast.success .material-icons {
  color: #006a35;
}
.toast-example .notifications .toast.error {
  border: thin solid #d40019;
  border-left-width: 0.5rem;
}
.toast-example .notifications .toast.error .material-icons {
  color: #d40019;
}
.toast-example .notifications .toast.warning {
  border: thin solid #FFC600;
  border-left-width: 0.5rem;
}
.toast-example .notifications .toast.warning .material-icons {
  color: #FFC600;
}
.toast-example .notifications .toast.info {
  border: thin solid #2A7DE1;
  border-left-width: 0.5rem;
}
.toast-example .notifications .toast.info .material-icons {
  color: #2A7DE1;
}
.toast-example .notifications .toast span.material-icons:last-child {
  color: #3f3f3f;
  font-weight: 400;
  cursor: pointer;
  border: thin solid transparent;
}
.toast-example .notifications .toast span.material-icons:last-child:hover {
  border-radius: 100%;
  border: thin solid #bbbbbb;
}
.toast-example .notifications :where(.toast, .column) {
  align-items: normal;
}
@keyframes show_toast {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0%);
  }
}
.toast-example .notifications .toast.hide {
  animation: hide_toast 0.3s ease forwards;
}
@keyframes hide_toast {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(calc(100% + 25px));
  }
}
.toast-example .buttons .btn {
  box-shadow: 0 0.1rem 0.2rem 0 #bbbbbb;
}
.toast-example .call-to-action {
  text-align: left;
}
@media (max-width: 67.5rem) {
  .toast-example {
    align-items: center;
  }
}
@media screen and (max-width: 45rem) {
  .toast-example .notifications {
    width: 95%;
  }
  .toast-example .notifications .toast {
    width: 100%;
    font-size: 1rem;
    margin-left: 1.25rem;
  }
  .toast-example .buttons .btn {
    margin: 0.5rem 0 0.0625rem;
    padding: 0.5rem 0.9375rem;
  }
  .toast-example .call-to-action {
    text-align: center;
  }
}

.toast-message .toast-content {
  display: flex;
  gap: 1.25rem;
}
.toast-message .toast-content .toast-success-icon {
  color: #006a35;
}
.toast-message .toast-content .toast-error-icon {
  color: #d40019;
}
.toast-message .toast-content .toast-title {
  font-weight: 700;
  font-size: 0.9rem;
  margin-top: 0.1875rem;
  margin-bottom: 0.2rem;
  line-height: 1;
  text-transform: capitalize;
}
.toast-message .toast-content .toast-info {
  margin: 0;
  font-size: 0.9rem;
}
.toast-message .toast-content .toast-info p, .toast-message .toast-content .toast-info .paragraph-wrapper {
  margin: 0;
}
.toast-message .toast-content .toast-info a {
  font-size: 0.9rem;
}
.toast-message .Toastify__close-button .Toastify__close-button--light svg {
  fill: #000000;
}
.toast-message .Toastify__toast-theme--light.Toastify__toast--success {
  background-color: #ffffff;
  color: #3f3f3f;
  border: 0.0313rem solid #006a35;
  border-left: 0.3125rem solid #006a35;
  box-shadow: 0 0.1rem 0.2rem 0 #bbbbbb;
  width: 25rem;
  border-radius: 0;
}
.toast-message .Toastify__toast-theme--light.Toastify__toast--error {
  background-color: #ffffff;
  color: #3f3f3f;
  border: 0.0313rem solid #d40019;
  border-left: 0.3125rem solid #d40019;
  box-shadow: 0 0.1rem 0.2rem 0 #bbbbbb;
  width: 25rem;
  border-radius: 0;
}
.toast-message .Toastify__toast-container {
  width: auto;
}
.toast-message .Toastify__zoom-enter {
  display: none;
}
.toast-message .Toastify__close-button {
  fill: #000000;
  color: #000000;
}
.toast-message .Toastify__close-button svg {
  color: #000000 path;
  color-fill: #000000;
}

.media-container {
  padding: 3rem 2rem;
}
.media-container .site .media-player {
  width: 100%;
  aspect-ratio: 16/9;
}
.media-container .site .media-player iframe {
  display: block;
  width: 100%;
  height: 100%;
}
.media-container .site .messaging {
  align-self: center;
}
@media (min-width: 67.5rem) {
  .media-container .site .messaging {
    max-width: 50%;
  }
}
.media-container .site .messaging h2 {
  margin: 0;
}
.media-container .site .messaging p, .media-container .site .messaging .paragraph-wrapper {
  margin-top: 0.5rem;
}
@supports not (aspect-ratio: 16/9) {
  .media-container .site .media-player {
    position: relative;
    padding-bottom: 56.25%;
    padding-top: 30px;
    height: 0;
    overflow: hidden;
  }
  .media-container .site .media-player > iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
@media (max-width: 67.5rem) {
  .media-container .site.flex-wrapper {
    flex-wrap: wrap;
  }
}

main aside .media-container h3, main aside .media-container .cards .heading-wrapper, .cards main aside .media-container .heading-wrapper {
  margin-top: 0;
}

/* reserve for emergency overrides across platform */