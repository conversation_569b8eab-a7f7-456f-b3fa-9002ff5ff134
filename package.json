{"name": "paas", "main": "expo-router/entry", "version": "1.0.0", "scheme": "com.verisk.int.sso", "scripts": {"start": "npx expo start", "android": "expo run:android", "ios": "npx expo run:ios", "web": "npx expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/roboto": "^0.2.3", "@expo/vector-icons": "^14.0.2", "@okta/okta-react-native": "^2.17.0", "@react-navigation/native": "^7.0.14", "@sitecore-jss/sitecore-jss-cli": "^22.5.4", "@sitecore-jss/sitecore-jss-nextjs": "^21.7.0", "@sitecore-jss/sitecore-jss-react-native": "^22.5.4", "escape-string-regexp": "^5.0.0", "expo": "^53.0.9", "expo-auth-session": "^6.0.1", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "install": "^0.13.0", "invariant": "^2.2.4", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-radio-buttons-group": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.5", "metro": "^0.82.0", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}