const fs = require('fs');
const path = require('path');

// Directory where chunk files are located
const chunksDir = path.resolve('.next/static/chunks/pages');

// Regex pattern to match [[...path]] chunk files with hash
const chunkFilePattern = /^\[\[\.\.\.path\]\]-.*\.js$/;

// Find the chunk file
const chunkFile = fs.readdirSync(chunksDir).find(file => chunkFilePattern.test(file));

if (!chunkFile) {
    console.error(`❌ No [[...path]] chunk file found in ${chunksDir}`);
    process.exit(1);
}

const targetFile = path.join(chunksDir, chunkFile);
const content = fs.readFileSync(targetFile, 'utf8');

// Check if it's returning HTML instead of JS
if (content.trim().startsWith('<')) {
    console.error(`❌ Chunk file "${chunkFile}" contains HTML instead of JS. Build may be incomplete or broken.`);
    process.exit(1);
}

console.log(`✅ Chunk file "${chunkFile}" is valid JavaScript.`);
