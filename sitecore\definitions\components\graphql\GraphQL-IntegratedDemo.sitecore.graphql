# This file contains a GraphQL query that will be executed and the result provided to
# your JSS component. You can run this query in GraphiQL ($endpoint/ui) for a nice editing experience.

# Note that we're executing _two queries_ (datasource and contextItem)
# within the context of the IntegratedDemoQuery _operation_. This makes it
# very efficient at gathering data from multiple sources.

query IntegratedDemoQuery($datasource: String!, $contextItem: String!, $language: String!) {
  # Datasource query
  # $datasource will always be set to the ID of the rendering's datasource item
  # (as long as the GraphQLData helper is used)
  datasource: item(path: $datasource, language: $language) {
    id
    name
    # Strongly-typed querying on known templates is possible!
    ...on GraphQLIntegratedDemo {
      # Single-line text field
      sample1 {
        # the 'jsonValue' field is a JSON blob that represents the object that
        # should be passed to JSS field rendering helpers (i.e. text, image, link)
        jsonValue
        value
      }
      # General Link field
      sample2 {
        jsonValue
        # Structured querying of the field's values is possible
        text
        target
        url
        # Access to the template definition is possible
        definition {
          type
          shared
        }
      }
    }
  }

  # Context/route item query
  # $contextItem will always be set to the ID of the current context item (the route item)
  # (as long as the GraphQLData helper is used)
  contextItem: item(path: $contextItem, language: $language) {
    id
    # Get the page title from the app route template
    ...on AppRoute {
      pageTitle {
        value
      }
    }

    # List the children of the current route
    children(hasLayout: true) {
      results {
        id
        # typing fragments can be used anywhere!
        # so in this case, we're grabbing the 'pageTitle'
        # field on all child route items.
        ...on AppRoute {
          pageTitle {
            jsonValue
            value
          }
        }
        url{
          path
        }
      }
    }
  }
}
