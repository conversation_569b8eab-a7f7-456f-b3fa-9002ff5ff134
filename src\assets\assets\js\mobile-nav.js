/* MOBILE NAVIGAGTION */


/* click display the menu list (headings) */

const hamburgerMenu = document.getElementById("hamburger-menu");
const closeMenu = document.getElementById("closeMenu");
const mobileNav = document.getElementById("mobile_navigation");

hamburgerMenu.addEventListener("click", toggleMenu);
hamburgerMenu.addEventListener("touchstart", toggleMenu);
closeMenu.addEventListener("click", toggleMenu);
closeMenu.addEventListener("touchstart", toggleMenu);

function toggleMenu(event) {
event.preventDefault();
if (mobileNav.style.display === "none") {
    mobileNav.style.display = "block";
    closeMenu.style.display = "block";
    hamburgerMenu.style.display = "none";
} else {
    mobileNav.style.display = "none";
    closeMenu.style.display = "none";
    hamburgerMenu.style.display = "block";
}
}

document.addEventListener("click", function(event) {
if (event.target.closest("#mobile_navigation") === null && event.target !== hamburgerMenu) {
    mobileNav.style.display = "none";
    closeMenu.style.display = "none";
    hamburgerMenu.style.display = "block";
}
});

document.addEventListener("touchstart", function(event) {
if (event.target.closest("#mobile_navigation") === null && event.target !== hamburgerMenu) {
    mobileNav.style.display = "none";
    closeMenu.style.display = "none";
    hamburgerMenu.style.display = "block";
}
});


/* click the main menu --> sub menu list opens and closes */

function toggleSubHeading() {
    const subHeading = this.nextElementSibling;
    const expandMore = this.querySelector('.expand_more');
    const expandLess = this.querySelector('.expand_less');

    subHeading.classList.toggle('active');
    expandMore.classList.toggle('hidden');
    expandLess.classList.toggle('hidden');

    if (subHeading.style.display === 'block') {
        subHeading.style.display = 'none';
        expandMore.style.display = 'inline';
        expandLess.style.display = 'none';

    } else {
        subHeading.style.display = 'block';
        expandMore.style.display = 'none';
        expandLess.style.display = 'inline';

    }
}

const heading = document.querySelectorAll('.heading');
heading.forEach((element) => {
    element.addEventListener('click', toggleSubHeading);
    element.addEventListener('touch', toggleSubHeading);
});