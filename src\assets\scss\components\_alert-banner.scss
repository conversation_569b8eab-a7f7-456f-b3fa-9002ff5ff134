 .alert-banner.flex-wrapper {
     position: relative;
     padding: 1rem;
     background-color: $background-primary-40;
     z-index: 5;
     border-bottom: thin solid $border-md-grey;
     text-align: center;

     .alert-banner-content {
         width: 100%;
         font-size: .9rem;
     }

     a {
         margin-left: 1.5rem;
         color: $body-text;
         align-self: flex-start;

         span {
             font-size: 1.125rem;
             padding: .15rem;
         }

         span:hover {
             border-radius: 50%;
             background-color: $the6s-hover-20;
         }
     }
 }

 .alert-banner.hidden {
     display: none;
 }

 .alert-banner {
     padding: 1.25rem 1.875rem;
     display: flex;
     align-items: center;

     h2+.align-side-by-side {
         margin-bottom: 1rem;
         align-items: center;

         span {
             font-size: 1.34rem;
             line-height: 1;
         }

         .align-side-by-side__editor {
             font-size: 1rem;
         }
     }

     &__blue {
         h2 {
             color: $white
         }

         background-color: $background-dk-blue;

         a {
             color: $white;
             text-decoration: underline;
             font-weight: 500;

         }

         a:hover {
             color: $inverse-link-hover;

         }

         div {
             color: $white;
         }

     }

     &__yellow {

         background-color: $alert-banner-yellow;

         a {
             font-weight: 500;
         }
     }

     &__grey {
         background-color: $alert-banner-grey;

         a {
             font-weight: 500;
         }
     }

     &__white {
         background-color: $white;

         a {
             font-weight: 500;
         }
     }

     .align-side-by-side {
         display: flex;
         font-size: 0.9rem;

         &__editor {
             padding: 0 0.25rem;
         }
     }

     span {
         vertical-align: middle;
         font-size: 0.9rem;
         flex: 0 1;
         line-height: 1.5;
     }

     &__container {
         flex: 1;

         p:nth-child(1) {
             margin: 0;
         }
     }
 }

 // for vertical alert banner

 main {
    .site.flex-wrapper {
        aside {
            section.vertical-alert-banner.alert-banner {
                &__grey {
                    background-color: $background-lt-grey;
                }
               .align-side-by-side {
                 display: inline-block;
                 margin-bottom: unset;

				 .align-side-by-side__editor {
					font-size: 0.9rem;
				}
                  span.material-icons{
                    font-size: 0.9rem;
                }
                     p {
                         font-size: 1rem;
                         display: inline;
                         a {
                             text-decoration: none;
                         }
                     }
                 
               }
               
            }
        }
    }
}