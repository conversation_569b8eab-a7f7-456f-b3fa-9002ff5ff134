.toast-message {

    .Toastify__toast {

        @media (max-width: 30rem) {
            margin-bottom: 1rem;
        }
    }

    .toast-content {
        display: flex;
        gap: 1.25rem;

        .toast-success-icon {
            color: $green-2;
        }

        .toast-error-icon {
            color: $red-1;
        }

        .toast-title {
            font-weight: 700;
            font-size: 0.9rem;
            margin-top: 0.1875rem;
            margin-bottom: 0.2rem;
            line-height: 1;
            text-transform: capitalize;
        }

        .toast-info {
            margin: 0;
            font-size: 0.9rem;

            p {
                margin: 0
            }

            a {
                font-size: 0.9rem;
            }
        }
    }

    .Toastify__close-button .Toastify__close-button--light {
        svg {
            fill: $black;
        }
    }

    // to fix the design for toast success message
    .Toastify__toast-theme--light.Toastify__toast--success {
        background-color: $white;
        color: $body-text;
        border: 0.0313rem solid $green-2;
        border-left: 0.3125rem solid $green-2;
        box-shadow: 0 0.1rem 0.2rem 0 $border-md-grey;
        width: 25rem;
        border-radius: 0;

        @media (max-width: 30rem) {
            width: 80%;
        }
    }

    // to fix the design for toast error message
    .Toastify__toast-theme--light.Toastify__toast--error {
        background-color: $white;
        color: $body-text;
        border: 0.0313rem solid $red-1;
        border-left: 0.3125rem solid $red-1;
        box-shadow: 0 0.1rem 0.2rem 0 $border-md-grey;
        width: 25rem;
        border-radius: 0;

        @media (max-width: 30rem) {
            width: 80%;
        }
    }

    .Toastify__toast-container--bottom-left,
    .Toastify__toast-container--bottom-center,
    .Toastify__toast-container--bottom-right {

        @media (max-width: 30rem) {
            left: 10%;
        }
    }

    // to fix the width of toast container
    .Toastify__toast-container {
        width: auto;

        @media (max-width: 30rem) {
            width: 100%;
        }
    }

    // to remove the default icon for toast frame work
    .Toastify__zoom-enter {
        display: none;
    }

    // to fix color of close button in toast container
    .Toastify__close-button {
        fill: $black;
        color: $black;

        svg {
            color :$black path {
                fill: $black;
            }
        }
    }
}