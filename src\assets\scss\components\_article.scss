article {
    .content-wrapper {
        .references {
            * {
                font-size: .9rem;
            }
            a {
                    word-break: break-all;
            }

            .reference-listing {
                p {
                    font-size: .9rem;
                }
            }
        }
        .pills {
            border-bottom: thin solid $border-md-grey;
        }
    }

	address {
		font-style: normal;
		display: flex;
		.authors-column {
		  margin-left: 0.4rem;
		  display: flex;
		  flex-direction: column;
		}
    }

   .author {
        .bio {
            img.author-photo {
                height: 5rem;
                margin-top: 0;
            }
            p {
                font-size: .9rem;
            }
            .details {
                padding: 0;
            }
        }
    }

    &.card {
        display: flex;
        &:hover {
            box-shadow: 0px 0px 25px 1px rgba(0, 0, 0, 0.15);
            cursor: pointer;
        }

        p, time {
            color: $body-text;
        }
        time {
            font-style: italic;
        }
        p {
            margin-top: 0;
            order: 3;
        }

        a {
            // align-items: stretch;
            // flex-grow: 1;
            flex-direction: column;
            align-items: flex-start;
            width: 100%;
            h3 {
                order: 2;
            }
            img {
                border-radius: .25rem;
                width: 100%;
                height: 160px;
                object-fit: cover;
                order: 1;
            }
        }
    }
    .key-takeaways {
        border-bottom: thin solid $border-md-grey;
        li {
            list-style: square;
            &:not(:last-of-type) {
                padding-bottom: 1rem;
            }
        }
    }
    .site.flex-wrapper {
        .references {
            padding-bottom: 0;
        }
    }
}