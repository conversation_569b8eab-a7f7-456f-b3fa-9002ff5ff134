aside {
    padding: 1.875rem;
    width: 20rem;

    strong[role="heading"] {
        display: block;
        font-size: 1.125rem;
        font-weight: 500;
        padding-bottom: 1rem;
        border-bottom: thin solid $border-md-grey;
    }

    h2 {
        font-size: 1rem;
        padding-bottom: .5rem;
        margin-top: 0;
        border-bottom: thin solid $border-md-grey;
    }

    h3 {
        font-size: .9rem;
    }

    article {
        address {
            display: inline-block;
            font-size: .9rem;
        }

        a {
            font-size: .9rem;

            &:not(:first-of-type) {
                border-left: thin solid $theBs;
                padding-left: 0.25rem;
            }

            span {
                vertical-align: middle;
            }
        }
    }

    // Internal changes
    .verisk-activity.with-show-more {

        .show-more-activities,
        .show-less-activities {
            padding: 0.5rem 0 0;
            font-size: 0.9rem;
        }
    }

    .link-list {
        &.link-list-with-vertical-scroll {
            li:nth-of-type(n+4) {
                display: none
            }

            &.show-all {
                max-height: 20rem;
                overflow-y: auto;

                li:nth-of-type(n+4) {
                    display: block;
                }
            }
        }

        font-size: .9rem;
        margin-bottom: 0;

        &[class*='recommended-item'] {
            max-height: 20rem;
            overflow-y: scroll;
            margin-bottom: 1rem;
        }

        a {
            &:not(:first-of-type) {
                border-left: thin solid $theBs;
                margin-left: 0.25rem;
                padding-left: 0.5rem;
            }

            &[class*='show-less'] {
                border-left: none;
            }
        }
    }

    a {
        &[class*='recommended-show-btn'] {
            font-size: .9rem;
        }
    }

    &.workspace-nav {
        //   display: none;
        padding: 3rem 0 1.875rem 1.5rem;
        min-width: 16rem;
        width: 16rem;
        position: relative;
        min-height: calc(100vh - 21.625rem);
        border-right: .25rem double $border-lt-grey;
        -webkit-transition: all .5s ease-out;
        -moz-transition: all .5s ease-out;
        transition: all .5s ease-out;

        nav {
            width: 100%;
        }

        .side-nav.heading {
            cursor: text;
            display: block;
            font-size: 1rem;
            padding-bottom: .75rem;
            color:#3f3f3f;
            text-decoration: none;
            font-weight: 600;
            span {
                vertical-align: middle;
                font-size: 1.05rem;
            }
        }

        a {
            display: block;
            font-size: .9rem;
            padding:  .75rem .75rem .75rem 0rem;

            // &.heading {
            //     cursor: auto;
            // }
            &.current {
                background-color: $background-lt-blue;
                font-weight: 700;
            }

            &:hover {
                background-color: $background-lt-blue;
            }

            span {
                vertical-align: middle;
                font-size: 1.05rem;
            }

            &[role="heading"] {
                color: $body-text;
                padding-bottom: .75rem;
                font-size: 1rem;
                font-weight: 600;
                cursor: text;

                &:hover {
                    background-color: transparent;
                }
            }
        }

        ul {
            list-style-type: none;
            margin-top: 0;
            padding-left: 1.25rem;

            li {
                a {
                    font-size: .9rem;
                }
            }
        }
    }

    &.filter {
        width: 18rem;

        h2 {
            border-bottom: none;
        }

        .link-list {
            li {
                padding-bottom: 0;

                &:first-of-type {
                    details summary {
                        border-top: none;
                        padding-top: 0;
                    }
                }
            }
        }

        b {
            padding-bottom: .25rem;
            font-size: .85rem;
            display: block;

            &:not(:first-of-type) {
                padding-top: .5rem;
            }
        }

        details {
            summary {
                display: flex;
                flex-wrap: nowrap;
                position: relative;
                border-bottom: none;
                padding-top: 1rem;
                margin-bottom: 0.5rem;
                font-weight: 500;
                justify-content: space-between;
                width: 100%;
                .lines-item {
					align-items: center;
                    display: inline-flex;
                    flex-basis: 75%;
					span {
						align-self: center;
						font-size: 0.8888888889rem;
						margin-left: 0.2222222222rem;
					}
                }

                .item-count {
                    align-items: center;
                    background-color: black;
                    border-radius: 12px;
                    display: inline-flex;
                    margin-left: 6px;
                    min-height: 16px;
                    min-width: 16px;
                    justify-content: center;

                    small {
                        color: white;
                        font-size: 12px;
                        font-weight: 700;
                        padding: 2px 8px;
                    }
                }
                &::after {
                    margin-left: 0.25rem;
                    font-size: 1.5rem;
                }
            }

            .options {
                .toggle-content {
                    display: -webkit-box;
                    visibility: visible;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    word-wrap: break-word;
                    overflow: hidden;
                }

                label {
                    display: flex;
                    gap: .5rem;
                    padding-bottom: .5rem;
                    align-items: flex-start;

                    span+span {
                        margin-left: auto;
                    }

                    input[type="text"] {
                        width: 100%;
                        margin-bottom: .5rem;
                    }

                    input[type="checkbox"] {
                        margin-top: 0.295rem;
                    }
                }
            }

            [id^="showMore"] {
                display: none;
            }

            .filter-toggle {
                font-size: .8rem;
                transform: none;
                box-shadow: unset;
                position: unset;
                padding: 1rem 0 1.5rem 0;
                color: $default-link;
                cursor: pointer;

                .less {
                    display: none;
                }
            }

            section:has(#showMore:checked) {
                .toggle-content {
                    display: block;
                }

                .filter-toggle {
                    .more {
                        display: none;
                    }

                    .less {
                        display: block;
                    }
                }
            }

            details {
                padding-left: .5rem;
            }
        }

        input[type=text],
        input[type=date] {
            font-size: .85rem;
        }
    }

    section {
        &.with-show-more {
            .item-list {
                .item:nth-of-type(n+4) {
                    display: none;
                }
            }

            .show-less,
            .show-more {
                padding: .5rem 0 0;
                font-size: .9rem;
                display: block;
            }

            .show-less {
                padding-top: 1rem;
            }

            .show-less,
            .show-more:target {
                display: none;
            }

            .show-more:target+.show-less {
                display: block;
            }

            &:has(.show-more:target) {
                .item:nth-of-type(n+4) {
                    display: block;
                }

                .item-list {
                    max-height: 20rem;
                    overflow-y: scroll;
                }
            }
        }

        &.with-show-more-filters {

            // Internal changes
            .lines {

                label:nth-of-type(n+7) {
                    display: none;
                }

                .show-less-lines,
                .show-more-lines {
                    padding: .5rem 0 1rem;
                    font-size: .9rem;
                    display: block;
                }

                .show-less-lines {
                    padding-top: 1rem;
                }

                .show-less-lines,
                .show-more-lines:target {
                    display: none;
                }

                .show-more-lines:target+.show-less-lines {
                    display: block;
                    border: 0px;
                }

                &:has(.show-more-lines:target) {
                    label:nth-of-type(n+7) {
                        display: flex;
                    }

                    .options {
                        max-height: 20rem;
                        overflow-y: scroll;
                    }

                }
            }

            .topics {

                label:nth-of-type(n+7) {
                    display: none;
                }

                .show-less-topics,
                .show-more-topics {
                    padding: .5rem 0 1rem;
                    font-size: .9rem;
                    display: block;
                }

                .show-less-topics {
                    padding-top: 1rem;
                }

                .show-less-topics,
                .show-more-topics:target {
                    display: none;
                }

                .show-more-topics:target+.show-less-topics {
                    display: block;
                    border: 0px;
                }

                &:has(.show-more-topics:target) {
                    label:nth-of-type(n+7) {
                        display: flex;
                    }

                    .options {
                        max-height: 20rem;
                        overflow-y: scroll;
                    }

                }
            }

            .jurisdictions {

                label:nth-of-type(n+7) {
                    display: none;
                }

                .show-less-jurisdictions,
                .show-more-jurisdictions {
                    padding: .5rem 0 1rem;
                    font-size: .9rem;
                    display: block;
                }

                .show-less-jurisdictions {
                    padding-top: 1rem;
                }

                .show-less-jurisdictions,
                .show-more-jurisdictions:target {
                    display: none;
                }

                .show-more-jurisdictions:target+.show-less-jurisdictions {
                    display: block;
                    border: 0px;
                }

                &:has(.show-more-jurisdictions:target) {
                    label:nth-of-type(n+7) {
                        display: flex;
                    }

                    .options {
                        max-height: 20rem;
                        overflow-y: scroll;
                    }

                }
            }

            .lob-facet {

                label:nth-of-type(n+7) {
                    display: none;
                }

                .show-less-lob,
                .show-more-lob {
                    padding: .5rem 0 1rem;
                    font-size: .9rem;
                    display: block;
                }

                .show-less-lob {
                    padding-top: 1rem;
                }

                .show-less-lob,
                .show-more-lob:target {
                    display: none;
                }

                .show-more-lob:target+.show-less-lob {
                    display: block;
                    border: 0px;
                }

                &:has(.show-more-lob:target) {
                    label:nth-of-type(n+7) {
                        display: flex;
                    }

                    .options {
                        max-height: 20rem;
                        overflow-y: scroll;
                    }

                }
            }
        }
    }
    .keyword-class-codes {
        position: relative;
        form {
          display: inline-flex;
          flex-wrap: wrap;
          width: 100%;
        }
        label:first-child {
          color: $default-link;
          font-weight: 500;
          margin: 0.5rem 0;
          display: block;
        }
        input {
          padding: 0.625rem 2.5rem 0.625rem 1rem;
          border-radius: 0.5rem 0 0 0.5rem;
          border: thin solid #707070;
          border-right: none;
          min-height: 2.75rem;
          width: calc(100% - 2.75rem);
          color: $background-blue;
          font-weight: 600;
          font-size: 1rem;
    
          &::placeholder {
            font-size: 0.875rem;
            font-weight: 400;
          }
        }
        button {
          font-size: 1.5rem;
          padding: 0.5625rem;
          color: $the6s;
          background-color: $white;
          border: thin solid #707070;
          border-radius: 0 0.5rem 0.5rem 0;
          margin-bottom: 1rem;
          max-width: rem(44);
          min-height: 2.75rem;
    
          &:hover {
            background-color: $theEs;
          }
        }
      }
}