.banner {
    padding: 1.25rem 1.875rem;
    background-image: linear-gradient(45deg,
    rgba(0,53,142, 0.75),
    rgba(0,53,142, 0.55)), url(../../../);
    background-size: cover;
    background-repeat: no-repeat;
    .site.flex-wrapper {
        display: flex;
    }
    //hub-background.png)
    h1 {
        display: inline-flex;
        color: $white;
        font-size: 1.875rem;
        margin: 0;
    }
    a {
        margin-left: auto;
        color: $inverse-link;
        text-decoration: underline;
        align-self: center;
        span.material-icons {
            font-size:.9rem;
        }
        &:hover {
            color: $inverse-link-hover;
        }
    }
}