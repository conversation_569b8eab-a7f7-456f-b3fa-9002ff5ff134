.bio {
  gap: 2rem;
  padding: 1rem 0;
  align-items: flex-start;

  .details {
    padding: 1rem 0;
  }

  strong {
    margin: 0;
    display: inline;

    a {
      display: inline;
    }
  }

  img.author-photo {
    border-radius: 50%;
    margin-top: 1.2rem;
    max-width: 12.5rem;
    max-height: 12.5rem;
  }

  strong {
    color: $black;
  }

  b {
    display: inline;
    font-weight: 500;
  }

  p {
    margin-top: 0.5rem;
  }

  .social-icons {
    padding-left: 0.25rem;
    margin-top: 0.75rem;

    a {
      display: inline-block;

      img {
        width: 1rem;
        margin-right: 0.75rem;
        opacity: 0.85;

        &:hover {
          opacity: 1;
        }
      }
    }
  }
}

.author-bio {
  strong {
    color: $black;
    font-size: 1.75rem;
  }
}

// Internal changes
/*adding img 100% to make inside rich text image responsive ticket no: 2046*/
/* adding the below styles for laigning richtext component as per ticker:1241*/
.site {
  &.flex-wrapper {
    .major-content {
      &.rich-text-component.background-dk-blue,
      &.rich-text-component.background-lt-grey {
        padding: 1.875rem;
      }
      //Images on desktop will be actual width with max-width of 100% of the div.
      // on mobile always images will be 100% width of the div
      @media (max-width: 67.5rem) {
        img {
          width: 100%;
          height: 100%;
        }
      }
      img {
        max-width: 100%;
        height: 100%;
      }
    }
  }
}

.major-content {
  .rich-text-gradient {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      overflow: hidden;
      position: relative;
      max-height: 10.5rem;
      /* Assuming a line height of 1.5em */
  }

  .rich-text-gradient::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 10.5rem;
      /* Adjust based on the line height */
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 44.79%, #FFFFFF 98%);

  }
}



.major-content {
&.rich-text-component{  
  &:has(+ .major-content.rich-text-component) {
      padding-bottom: 0px;
  }

  +.major-content{
      &.rich-text-component{
          padding-top: 0px;
      }
  }
  +.alert-banner{
      border-radius: 4px;
      padding: 1rem 1.5rem;
  }
}
}
