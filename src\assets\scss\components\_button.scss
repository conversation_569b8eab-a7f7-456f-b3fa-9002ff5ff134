.button {
  display: flex;
  padding: rem(12) rem(32);
  align-items: center;
  gap: rem(8);
  border-radius: rem(4);
  font-size: rem(16);
  font-weight: 500;
  line-height: rem(24);
  cursor: pointer;
}

.cancel-btn {
  @extend .button;
  margin-left: rem(120);
  border: thin solid #00358e;
  background: #fff;
  color: #00358e;

  &:hover {
    border: thin solid #002665;
    background: #e6ebf4;
  }

  &:focus {
    box-shadow: 0 0 0 rem(2) #fff, 0 0 0 rem(4) #00358e;
  }
}

.update-btn {
  @extend .button;
  border: thin solid #ffc600;
  background: #ffc600;
  color: #1a1a1a;

  &:hover {
    border: thin solid #e8b400;
    background: #e8b400;
  }

  &:disabled {
    border: thin solid #dbdbdb;
    background: #dbdbdb;
    cursor: default;
  }

  &:focus {
    box-shadow: 0 0 0 rem(2) #fff, 0 0 0 rem(4) #00358e;
  }
}
