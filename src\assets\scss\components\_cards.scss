.cards {
    align-items: stretch;
    flex-wrap: wrap;
    margin-left: -1rem;
    // gap: 1.5rem;
    // #Internal changes
    justify-content: flex-start;

    a {
        h3 {
            color: $default-link;
        }
        &:hover {
            h3 {
                color: $default-link-hover;
            }

        }
    }

    h3 {
        font-size: 1.2rem;
        line-height: 1.25;
    }
    // Internal changes
    .heading-wrapper {
        @extend h3;
        margin: 1rem 0;
    }
    .card {
        padding: 1rem;
        &.one {
            width: 100%;
        }
        &.two {
            width: 50%;
        }
        &.three {
            width: 30%;
        }
        &.four {
            width: 23.5%;
        }
        &.five {
            width: 18.75%;
        }

        img {
            border-radius: .25rem;
            height: 7.5rem;
            width: 100%;
            aspect-ratio: 16 / 9;
            object-fit: cover;
        }
        strong {
            display: block;
            padding: 1.5rem 0;
            font-size: 1.3125rem;
            font-weight: 500;
        }
        /* &:first-of-type {
            padding-left: 0;
        } */
    }

    .call-to-action {
        text-align: left;
        display: inline-flex;
        padding-bottom: 0;
        span.material-icons {
            font-size: .9rem;
        }
    }
    &.no-padding {
        padding: 0;
    }
    &.sidebar {
        margin-left: 0;
        .card {
            padding: .5rem 0;
            strong {
                font-size: 1rem;
                padding: 0.5rem 0;
                font-weight: 700;
            }
            b {
                font-size: .95rem;
            }
            p {
                margin: 0.5rem 0 1.5rem;
                font-size: .9rem;
            }
        }
    }
    &.card-wrap {
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: flex-start;
    }
    &.min-width-cards {
        .card {
            min-width: 15rem;
            max-width: 30rem;
        }
    }

    &.carousel-cards {
        overflow-x: auto;
        overscroll-behavior: contain;
        width: 100%;
        scroll-snap-type: x mandatory;
        scroll-behavior: smooth;

        .card {
            scroll-snap-align: start;
            flex-shrink: 0;
            position: relative;

            @media (max-width: 50rem) {
                width: 80%;
                padding: 1rem 2rem 1rem 0;
            }
        }
    }
}

