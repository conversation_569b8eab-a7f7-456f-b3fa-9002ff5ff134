.custom-checkbox,
.email-notif-checkbox {
  input[type='checkbox'] {
    position: absolute;
    opacity: 0;
    cursor: pointer;

    &:checked + .checkmark {
      background-color: $background-blue;
      border-color: $background-blue;

      &::after {
        display: block;
      }
    }

    &:not(:checked):disabled + .checkmark {
      background-color: transparent;
      border-color: $neutral-100;
      opacity: 0.6;
    }

    &:disabled + .checkmark {
      background-color: $neutral-100;
      border-color: $neutral-100;
      opacity: 0.6;

      &::after {
        border-color: $white;
      }
    }
  }

  .checkmark {
    position: relative;
    display: inline-block;
    width: rem(16);
    height: rem(16);
    min-width: rem(16);
    background-color: $white;
    border-radius: rem(4);
    border: thin solid $border-neutral;
    background: $white;
    transition: background-color 0.3s, border-color 0.3s;

    &::after {
      content: '';
      position: absolute;
      display: none;
      top: 42%;
      left: 50%;
      width: rem(4);
      height: rem(8);
      border: solid $white;
      border-width: 0 rem(1) rem(1) 0;
      transform: translate(-50%, -50%) rotate(45deg);
    }
  }
}
