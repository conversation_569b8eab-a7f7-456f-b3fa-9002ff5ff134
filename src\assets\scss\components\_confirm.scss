.confirm {
	position: absolute;
	top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.5);
    transition: opacity 200ms;
    visibility: hidden;
    opacity: 0;
    z-index: 2;
    &:target {
        visibility: visible;
        opacity: 1;
    }
    .popup {
        margin: 20rem auto;
        padding: 2rem;
        background: $white;
        border: 1px solid $border-md-grey;
        box-shadow: 0 0 50px rgba(0,0,0,0.5);
        position: relative;
        max-width: 30rem;
        h1 {
            font-size: 1.8rem;
            margin-top: 0;
        }
        .close {
            position: absolute;
            width: 1rem;
            height: 1rem;
            top: 0;
            right: 1rem;
            transition: all 200ms;
            font-size: 2rem;
            font-weight: 700;
            text-decoration: none;
        }
        .content {
            max-height: 400px;
            overflow: auto;
        }
        p {
            margin: 0 0 1rem;
            &:last-child {
                margin: 0;
            }
        }

        .call-to-action {
            margin-top: 1rem;
            padding-bottom: 0;
        }
    }
}