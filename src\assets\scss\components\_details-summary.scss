details {
    list-style: none;
    summary {
         display: flex;
         align-items: center;
         color: $default-link;
         cursor: pointer;
         padding: .25rem 0;
         border-top: thin solid $border-md-grey;
         border-bottom: thin solid $border-md-grey;
         &::marker {
              content: none;
         }
         &::-webkit-details-marker {
              display: none;
         }
         &::after {
              font-family: 'Material Icons';
              margin-left: auto;
              content: '\e5cf';
              font-size: 2rem;
         }
    }
    &[open] {
          summary {
               &:after {
                    content: '\e5ce';
               }
          }
          form {
               padding-bottom: .75rem;
               summary {
                    margin-right: 1rem;
                    border:none;
                    padding-top: 0;
                    margin-bottom: 0;
               }
               details:not([open]) {
                    summary {
                         &:after {
                              content: '\e5cf';
                         }
                    }
                    
               }
          }
          
    } 
}