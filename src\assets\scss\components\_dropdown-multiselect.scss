.dropdown {
    &.multiselect {
      position: relative;
      display: inline-block;
      width: fit-content;
    }
  
    .dropdown-input {
      padding: 0 0.6666666667rem;
      width: 100%;
  
      input {
        border-radius: 0.1111111111rem;
        width: 100%;
      }
    }
  
    &-button {
      display: flex;
      font-size: 0.9rem;
      align-items: center;
      width: 100%;
      justify-content: space-between;
      min-height: 2.4444444444rem;
      padding: 0.5rem 0.5rem 0.5rem 1rem;
      border: thin solid #004eaa;
      .material-icons{
      color: #004eaa;
      font-size: 1rem;
      transform: rotate(180deg);
    }

      .dropdown-text {
        display: none;
        padding-right: 1.3333333333rem;
  
        .show {
          display: inline-flex;
        }
      }
  
      .count {
        display: none;
  
        .show {
          display: inline-flex;
        }
      }
  
      &.is-open {
        border-bottom: transparent;
  
        .material-icons {
          transform: rotate(0deg);
        }
      }
    }
  
    &-container {
      border: thin solid #004eaa;
      border-top-width: 0;
      display: none;
      position: absolute;
      border-bottom-left-radius: 0.1111111111rem;
      border-bottom-right-radius: 0.1111111111rem;
      background-color: #f9f9f9;
      box-shadow:0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
      z-index: 999;
      padding: 0;
      max-height: 13.8888888889rem;
      width: 100%;
      left: 0;
      right: 0;
  
      .dropdown-content {
        margin: 0.6666666667rem 0 0;
        padding: 0.6666666667rem 0 0;
        overflow-y: auto;
  
        label {
          align-items: flex-start;
          display: inline-flex;
          font-size: 0.7777777778rem;
          margin-bottom: 0.4444444444rem;
  
          input {
            margin-right: 0.2777777778rem;
          }
        }
      }
    }
  
    .show {
      display: flex;
      flex-direction: column;
    }
  
    .clear-btn {
      align-self: flex-end;
      font-size: 0.7777777778rem;
      margin-top: 10px;
      cursor: pointer;
      color: #00358e;
      margin-right: 0.6666666667rem;
  
      &:hover {
        color: #002665;
      }
    }
  
    &-option {
      &-nested {
        margin-left: 0.8888888889rem;
      }
    }
  }