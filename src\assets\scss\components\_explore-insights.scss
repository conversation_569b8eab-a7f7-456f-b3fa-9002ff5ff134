.explore-insights {
    h2 {
        display: flex;
        flex-wrap: wrap;
    }

    .emerging-issues {
        align-items: center;
        gap: 0.5rem;

        .rss-icon-link {
            display: flex;
            align-items: center;
            margin: 0;
            position: relative;

            .rss-icon {
                width: 2rem;
                height: 2rem;
            }

            .tooltip-text {
                visibility: hidden;
                background-color: #555;
                color: #fff;
                text-align: center;
                padding: 0.3125rem;
                border-radius: 0.250rem;
                position: absolute;
                z-index: 1;
                bottom: 125%;
                left: 50%;
                transform: translateX(-50%);
                white-space: nowrap;
                opacity: 0;
                transition: opacity 0.3s;
            }
        }

        .rss-icon-link:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -0.3125rem;
            border-width: 0.3125rem;
            border-style: solid;
            border-color: #555 transparent transparent transparent;
        }

    }

    .tabs {
        margin-left: auto;
        width: 19.25rem;

        .tabbed {
            nav {
                padding-top: 0;

                .tab {
                    font-size: 1rem;
                    padding: 1rem;
                }
            }
        }
    }

    .cards {
        margin-left: -1rem;

        .card:first-of-type {
            padding-left: 1rem;
        }

        h3 {
            overflow-wrap: anywhere;
        }
    }
}

.hover-lift-cards {
    @extend .explore-insights;
}