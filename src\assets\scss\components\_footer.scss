footer {
    color: $white;
    background-color: $background-blue;
    padding: 1.5rem 1rem;
    margin-top: auto;
    border-top: 1px solid $border-lt-grey;
    min-height: 10rem;

    .flex-wrapper {
        gap: 2rem;
        justify-content: space-between;

        a {
            color: #FFFFFF;
        }

        // #Internal changes
    }

    small {
        display: block;
    }

    img {
        width: 9.375rem;
    }

    strong {
        display: block;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .copyright-footer-links {
        padding: 2rem;
        text-align: center;
        line-height: 1.625rem;

        a,
        .optanon-toggle-display {
            padding: 0 .5rem;
            text-decoration: underline;
            color: $inverse-link;
            font-size: 0.875rem;


            &:not(:first-of-type) {
                border: 0px;
                border-left: thin solid $white;
            }

            &:hover {
                color: $inverse-link-hover;
                background-color: none;
            }
        }

        &+div {
            text-align: right;

            img {
                width: 7.75rem;
            }
        }
    }
}