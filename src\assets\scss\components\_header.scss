header {
    background-color: $white;
    padding: 1rem 1rem 0;
    min-height: 8.5rem;
    position: relative;
    box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.2);
    z-index: 2;

    a {
        font-size: .95rem;
    }

    hr {
        margin-left: -1rem;
        margin-right: -1rem;
        margin-bottom: 0;
        opacity: .1;
    }

    .top-header {
        justify-content: space-between;

        sup {
            font-size: .8rem;
        }

        .logo {
            align-self: flex-start;

            img {
                height: 1.25rem;
            }
        }
    }

    a.primary {
        margin-left: auto;
    }

    nav {
        .non-mobile {
            display: flex;
            margin: 0;
            padding-left: 0;

            li {
                list-style: none;
                padding: 1.5rem .75rem 1.5rem;
                border-bottom: .25rem solid $white;

                &:hover {
                    color: $default-link-hover;
                    background-color: $background-lt-blue;
                }

                &.current {
                    border-color: $black;

                    a:nth-child(1) {
                        font-weight: 500;
                    }

                    a[role="menu-item"]:not(.heading):first-child {
                        font-weight: 400;
                    }
                }

                &.menu-dropdown {
                    .mega-menu {
                        display: flex;
                        justify-content: space-between;
                        position: absolute;
                        top: 4.75rem;
                        left: 0;
                        opacity: 0;
                        visibility: hidden;

                    }

                    &:hover {
                        .mega-menu {
                            opacity: 1;
                            visibility: visible;
                            z-index: 1; // Internal changes
                        }
                    }
                }
            }

            a {

                .material-icons {
                    vertical-align: middle;
                }
            }
        }

        .mobile-navigation {
            display: none;
        }

        &.account-alerts-support {
            margin-left: auto;
            text-align: center;

            a {
                padding: 0.3rem 0.5rem;

                &:hover {
                    background-color: $background-lt-blue;
                }
            }

            .support {
                a {
                    display: flex;
                    flex-direction: column;
                    padding: .8rem .5rem;
                }
            }

            .account {
                margin-left: 2rem;
                border-left: thin solid $the9s;

                a {
                    padding: 1.5rem 1rem 1.5rem;
                    display: inline-block;
                    margin-left: 1rem;

                    &:first-of-type {
                        margin-right: 0;
                    }
                }

                span {
                    display: inline-block;
                    vertical-align: middle;
                }
            }
        }
    }

    .account-alerts-support {
        position: relative;
        margin: 0;
        padding: 0;

        .user-account {
            display: flex;
            margin: 0;
            padding: 0;
            align-items: center;

            li {
                list-style: none;
                padding: 1.5rem .75rem 1.5rem;
                padding: 0;
                margin: 0;
                border-bottom: .25rem solid $white;

                a {
                    text-decoration: none;
                }
            }

            .menu-dropdown {

                .dropdown-content {
                    display: none;
                    position: absolute;
                    top: 4.7rem;
                    right: 0;
                    width: 9rem;
                    box-shadow: 0 0 0.15rem 0 rgba(0, 0, 0, 0.2);
                    z-index: 2;

                    a {
                        display: block;
                        background-color: $white;
                        padding: 0.5555rem;
                        border-bottom: thin solid $theEs;
                        font-size: 0.85rem;
                    }

                    a:hover {
                        background-color: $background-lt-blue;
                    }
                }

                &:hover .dropdown-content {
                    display: block;
                }
            }
        }
    }

    &.logo-only {
        min-height: auto;
    }

    /* --- Resources Dropdown Styles --- */
    .resources-dropdown {
        position: relative;

        .resources-button {
            display: flex;
            align-items: center;
            padding: 1.5rem 0.5rem 1.5rem;
            text-decoration: none;
            color: #004eaa;
            cursor: pointer;

            span {
                display: inline-block;
                vertical-align: middle;
                margin-right: 0.5rem;
            }
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            min-width: 11rem;
            box-shadow: 0 0 0.15rem rgba(0, 0, 0, 0.2);
            z-index: 5;

            a {
                display: block;
                padding: 0.75rem 1rem;
                color: #004eaa;
                text-decoration: none;
                font-size: 0.85rem;
                border-bottom: 1px solid #eee;

                &:hover {
                    background-color: #f0f6ff;
                }
            }
        }

        &:hover .dropdown-content {
            display: block;
        }
    }

}