.hero {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;

  h1 {
    margin: 0;
  }

  .site.flex-wrapper {
    gap: 0;
    align-items: center;
  }

  .video-wrapper {
    width: 100% !important;
    max-width: 60rem !important;
    height: auto !important;
    aspect-ratio: 16 / 9 !important;
    overflow: hidden !important;
    position: relative !important;
  }

  .video-wrapper video {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }

  .messaging {
    padding: 0 4rem 0 0;
    min-width: 50%;
    max-width: 60%;

    li {
      margin-bottom: 1rem;
      font-size: 1rem;
    }

    .project-meta {
      small {
        font-size: 1rem;

        &:not(:last-of-type) {
          padding-right: 1rem;
          margin-right: 1rem;
          border-right: thin solid;
        }
      }
    }
  }

  img {
    margin: 0;
    object-fit: cover;
    flex-grow: 2;
    max-width: 50%;
  }

  p,
  .paragraph-wrapper {
    font-size: 1.25rem;
  }

  &.topic-detail {
    padding: 0;

    .site.flex-wrapper {
      align-items: stretch;
      justify-content: space-between;
    }

    .messaging {
      padding: 1rem 2rem 1rem 0;
      align-self: center;
      min-width: unset;
      max-width: unset;
      flex-grow: 2;

      p,
      .paragraph-wrapper {
        font-size: 1.25rem;
      }

      & + img {
        margin: 0;
        max-width: 30%;
      }
    }
  }

  &.article {
    .messaging {
      min-width: 60%;
      max-width: 70%;
    }

    img {
      max-width: 30%;
      margin: 0;
    }

    .article-meta {
      small {
        font-size: 1rem;

        &:not(:last-of-type) {
          padding-right: 1rem;
          margin-right: 1rem;
          border-right: thin solid;
        }
      }
    }
  }

  &.product-update {
    // background-image: url('../assets/verisk-banner-bg-d.png');
    .messaging {
      padding: 0 2rem 0 0;

      .eyebrow-head {
        text-transform: uppercase;
        display: block;
        margin-bottom: 0.5rem;
      }
    }
  }

  &.spotlight {
    &.background-lt-grey,
    &.background-lt-white {
      .messaging {
        h2 {
          color: $black;
        }

        h3 {
          color: $black;
        }
      }
    }

    background-image: var(--background);
    margin-top: 0.1rem;
    display: block;

    .messaging {
      padding: 0 2rem 0 1rem;

      h2 {
        color: $white;
        text-transform: uppercase;
        font-size: 0.8rem;
        margin-bottom: 2rem;

        span.tagpill {
          text-transform: capitalize;
          margin-right: 1rem;
          padding: 0.3rem 0.7rem 0.3rem;
          border-radius: 5rem;
          background-color: $red-3;
          font-size: 0.7rem;
          color: $white;
          vertical-align: text-top;
        }
      }

      h3 {
        color: $white;
        line-height: 1.1;
        font-size: 1.9rem;
      }

      p,
      .paragraph-wrapper {
        margin-bottom: 0;
      }

      .call-to-action {
        padding: 2rem 0 1rem;
      }

      @media (min-width: 67.5rem) {
        max-width: 50%;
      }
    }

    img {
      margin: 0 auto;
    }

    .messaging + img {
      max-width: 48%;
    }
  }
}
