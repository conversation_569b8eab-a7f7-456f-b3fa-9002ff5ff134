.modal-container {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: $black-opacity-80;
  z-index: 5;
  display: flex;
  .inactivity-modal {
    background-color: $white;
    width: 24rem;
    padding: 1.5rem 0;
    position: absolute;
    border-radius: 0.25rem;
    text-align: center;
    margin-top: 10%;
    left: 50%;
    transform: translateX(-50%);
    h2 {
      margin: 0 0 1rem 0;
      padding: 0 4rem;
      font-size: 1.25rem;
      line-height: 1.5rem;
    }
    .inactivity-content {
      margin: 0.5rem 0 1rem 0;
      .material-symbols-outlined {
        vertical-align: middle;
        font-size: 1.25rem;
      }
      h2 {
        margin-bottom: 0.5rem;
        padding: 0;
        font-size: 1.25rem;
      }
      p {
        margin: 0;
      }
    }

    .call-to-action {
      padding: 1rem 0 1rem 0;
      a,
      button {
        padding: 0.65rem 1.5rem;
        &.primary {
          margin-left: 0.25rem;
        }
        &.tertiary {
          border: thin solid $black;
          color: $black;
        }
        &.tertiary:hover {
          background-color: $black;
          color: $white;
        }
      }
    }
  }
  &.with-page-loader {
    background-color: rgba($color: #fff, $alpha: 0.95);
    .loader {
      position: absolute;
      top: 40%;
    }
  }
}
