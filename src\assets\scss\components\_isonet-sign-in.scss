.isonet-sign-in-container {
    margin: 8rem auto;
    max-width: 28rem;
    min-height: 28rem;
    padding: 1rem;
    form {
        padding: 2rem;
        h1 {
            font-weight: 400;
            font-size: 1.5rem;
        }
        p {
            margin: 0;
            font-size: .8rem;
            font-weight: 400;
        }
        input {
            border: none;
            border-bottom: thin solid $border-md-grey;
            background-color: $background-lt-grey;
            outline: none;
            width:100%;
            margin: 1rem 0;
            &.isonet-user-id, &[type="password"]{
                padding: .8rem 0;
                font-style: normal;
                font-size: .8rem;
            }
        }
        label.checkbox-wrapper {
            display: flex;
            align-items: center;
            margin-top: 2rem;
            input[type="checkbox"] {
               width:unset;
            }
            span {
                padding-left: .5rem;
                font-size: .8rem;
            }
        }
        .call-to-action {
            padding: 1rem 0;
            a {
                box-shadow: 0 .1rem .2rem 0 $border-md-grey;
                &.primary {
                    padding: 0.75rem 9.4rem;
                }
            }
        }
    }
}
@media (max-width: 31.25rem) {
    .isonet-sign-in-container {
        form {
            .call-to-action {
                a {
                    &.primary {
                        padding: 0.75rem 2rem;
                    }
                }
            }
        }
    }
}