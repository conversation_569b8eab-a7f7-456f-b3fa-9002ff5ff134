.loader {
    position: relative;
    width: 100%;
    text-align: center;
    margin-bottom: 2rem;
    transform: translate3d(0, 100%, 0);
  }
  .dot {
    width: .7rem;
    height: .7rem;
    margin: .25rem;
    background: $background-dk-blue;
    border-radius: 100%;
    display: inline-block;
    animation: slide 1s infinite;
  }
  .dot:nth-child(1) {
    animation-delay: 0.1s;
    background: $background-blue;
  }
  .dot:nth-child(2) {
    animation-delay: 0.2s;
    background: $background-md-blue-1;
  }
  .dot:nth-child(3) {
    animation-delay: 0.3s;
    background: $background-md-blue-2;
  }
  .dot:nth-child(4) {
    animation-delay: 0.4s;
    background: $inverse-link-hover;
  }
  .dot:nth-child(5) {
    animation-delay: 0.5s;
    background: $background-md-blue-3;
  }
  @-moz-keyframes slide {
    0% {
      transform: scale(1);
    }
    50% {
      opacity: 0.3;
      transform: scale(1.75);
    }
    100% {
      transform: scale(1);
    }
  }
  @-webkit-keyframes slide {
    0% {
      transform: scale(1);
    }
    50% {
      opacity: 0.3;
      transform: scale(1.75);
    }
    100% {
      transform: scale(1);
    }
  }
  @-o-keyframes slide {
    0% {
      transform: scale(1);
    }
    50% {
      opacity: 0.3;
      transform: scale(1.75);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes slide {
    0% {
      transform: scale(1);
    }
    50% {
      opacity: 0.3;
      transform: scale(1.75);
    }
    100% {
      transform: scale(1);
    }
  }