main {
    flex-grow: 2;
    min-width: 0;
    min-height: calc(100vh - 19rem);
	&.legislative-monitoring.hub{
		margin-top: .5rem; 
	}

    .no-padding {
        padding: 0;

        a {
            display: inline-block;
        }
    }

    .site.flex-wrapper {
        align-items: flex-start;

        .content-wrapper {
            flex-grow: 2;
            max-width: 70%;
            padding: 1.875rem;
        }

        aside {
            padding: 1.875rem 0;

            section {
                width: 19rem;
                padding: 1rem;
                margin: 0;

                // Internal changes
                .non-underline {
                    p {
                        a {
                            text-decoration: none;

                            &:hover {
                                color: #002d61;
                            }
                        }
                    }
                }

                div {
                    font-size: 0.9rem;
                }

                p {
                    font-size: .9rem;

                    a {
                        text-decoration: underline;
                        color: #004eaa;
                    }

                    &:hover {
                        color: #002d61;
                    }
                }

                .link-list {
                    h3 {
                        font-size: .9rem;
                        margin: .25rem 0 0;
                    }
                }

                &:not(:first-of-type) {
                    margin-top: 2rem;
                }
            }

            &.filter {
                h2.filter-toggle-mobile span {
                    display: none;
                }
            }

            &.thin {
                width: 17rem;

                section {
                    width: 15rem;
                }
            }

            .contact-team {
                .with-cta {
                    padding: 0 0 0.5rem;
                }
            }

            .media-container {
                .media-player {
                    aspect-ratio: 16 / 9;

                    iframe {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }

    &.dashboard {
        margin-top: 0;

        .dynamic-product-banner {
            padding: 2rem;
            background-image: linear-gradient(to left, rgba(5, 20, 110, 0.7), rgba(5, 20, 110, 0.2)),
                url("../assets/GettyImages-1214224199.jpg");
            background-repeat: no-repeat;
            background-size: cover;

            .messaging {
                h1 {
                    margin-top: .5rem;
                    color: $white;
                }

                p {
                    color: $white;
                    font-size: 1.5rem;
                }
            }
        }

        .configure-dashboard {
            text-align: right;
            padding-bottom: 1rem;
        }

        .widgets {
            .flex-wrapper {
                flex-wrap: nowrap;
                gap: 1.5rem;
                padding: .75rem 0;
            }

            .widget {
                background-color: $background-lt-grey;
            }
        }
    }

    &.global-search {
        .site.flex-wrapper {
            gap: 0;

            aside.filter {
                section {
                    width: 17rem;
                }
            }

            .content-wrapper {
                flex-grow: 2;
                width: unset;
                max-width: unset;
                padding-right: 1.2rem;

                .select-wrapper {
                    display: inline-block;
                }

                h2 {
                    font-size: 1.25rem;
                    line-height: 1.2;
                    margin-top: 0;
                    padding-bottom: .5rem;
                }

                .results-meta-sort.flex-wrapper {
                    flex-wrap: wrap;
                    margin-top: 1rem;

                    form {
                        margin-left: auto;

                        label b {
                            display: inline-block;
                            margin-right: 1rem;
                        }

                        .select-wrapper {
                            display: inline-block;
                        }
                    }
                }
            }
        }
    }

    &.library {
        .site.flex-wrapper {
            .content-wrapper {
                max-width: 100%;
            }
        }

        .no-content {
            align-items: center;

            .message {
                flex-grow: 2;
                max-width: 50%;

                h2 {
                    font-size: 1.75rem;
                }
            }

            .image-wrapper {
                position: relative;
                min-width: 20rem;
                margin-top: 2rem;
                width: fit-content;
                height: 15rem;
                background-image: url(../assets/pfas.png);
                background-position: bottom center;
                overflow: visible;

                .guidance-box {
                    position: absolute;
                    font-size: .9rem;
                    bottom: -7rem;
                    left: 11rem;
                    padding: 1rem 0;
                    width: 10rem;
                    margin: 0 auto;

                    .material-icons {
                        display: block;
                    }
                }
            }
        }

        .share-save {
            justify-content: flex-start;
            padding: 4rem;
            background-color: rgba(0, 53, 114);
            width: fit-content;

            #share {
                filter: brightness(70%);
            }
        }
    }

    &.system-messages-page {
        h2 {
            font-size: 1.5rem;
            font-weight: 400;
        }

        p {
            max-width: 70rem;
        }

        .flex-wrapper {
            align-items: flex-start;
            flex-wrap: wrap;
        }

        .contact-info {
            padding: 0;
            list-style: none;
            margin: 1.5rem 4rem 0 0;

            strong {
                display: inline-block;
                font-size: 1.05rem;
                font-weight: 500;
                padding-bottom: .25rem;
                border-bottom: thin solid $border-md-grey;
                margin-bottom: .5rem;
            }

            li {
                line-height: 2.5;

                .material-icons {
                    vertical-align: middle;
                    padding-right: .5rem;
                    line-height: .9;
                }
            }
        }
    }
}