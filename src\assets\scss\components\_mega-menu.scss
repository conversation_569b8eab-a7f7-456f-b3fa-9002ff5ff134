.mega-menu {
    padding: 2rem 4rem;
    background-color: $white;
    box-shadow: 0 0 0.15rem 0 rgb(0 0 0 / 20%);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

    gap: 2rem;


    b[role="heading"] {
        display: inline-block;
        width: 100%;
        border-bottom: thin solid $border-md-grey;
        margin-bottom: 1rem;
    }

    .flex-wrapper {
        grid-column: span 1;

    }

    [role="menu-item"] {
        padding: 0 .25rem .25rem;
        font-size: 1rem;
        display: inline-flex;
        width: 100%;
    }

    .heading {
        font-size: 1.2rem;
        font-weight: 500;
    }

    p {
        font-size: 1rem;
    }

    .sub-group {
        padding-top: 2rem;
        grid-column: span 1;

    }

    &.one,
    &.two,
    &.three {
        div:not(.sub-group) {
            width: 100%;

        }

        .group2-content,
        .group1-content {
            width: 100% !important;

        }
    }
    [role=group] div [role=menu-item] {
        padding-bottom: 0;
    }
    [role=group] span[role=note] {
        color: #4d4d4d;
        font-size: 0.8888888889rem;
        display: inline-flex;
        padding: 0 0.25rem 0.25rem;
    }
}