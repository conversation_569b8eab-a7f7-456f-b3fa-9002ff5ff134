.mobile-navigation {
    #mobile_navigation {
        display:none;
        position: absolute;
        padding: 2rem;
        top:5rem;
        left: -1rem;
        width: auto;
        margin-top: 0;
        background-color: $white;
        box-shadow: 0 0 0.15rem 0 rgba(0, 0, 0, 0.2);
        z-index: 2; //Internal changes
        li {
            list-style: none;
            color: $default-link;
            a {
                display:block;
                font-size: 1rem;
                padding:.2525rem;
                &[role="heading"] {
                    color: $body-text;
                    font-weight: 700;
                    cursor: text;
                }
            }
        }
        hr {
            margin: 1rem 0;
        }
        .mobile-view.side-nav{
            //padding: 0; future prototype
            .heading{
                color: $body-text;
            }
        }
        .heading {
            font-weight: 700;
            .material-icons.expand_more {
                font-size: 1rem;
                font-weight: 700;
            }

            .material-symbols-outlined.expand_less {
                font-size: 1rem;
                font-weight: 700;    
            }

        }

        li .heading {
            padding: 0.2525rem;
        }

        .menu-group {
            margin-bottom: 1rem;
            padding: 0 0 0 .5rem;

            li {
                .sub-heading {
                    font-weight: 500;
                }

                ul {
                    padding-left: .5rem;
                }
            }
            &.expand-always {
                display:block;
            }
        }
    }

    #closeMenu {
        display: none;
    }
}

