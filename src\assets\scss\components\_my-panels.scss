.panels-container .breadcrumbs-container {
  padding-top: 0.8888888889rem;
  padding-bottom: 0.3888888889rem;
}
.panels-container .breadcrumbs-container .breadcrumbs nav {
  flex-wrap: wrap;
  align-items: baseline;
}

@media (min-width: 67.5rem) {
  .panels-container .tabs .nav-wrapper {
    display: flex;
    align-items: flex-end;
  }
}
 
@media (max-width: 67.5rem) {
  .panels-container .tabs-content .tabbed .glossary {
    display: none;
  }
}
.panels-container .tabs-content .tabbed .glossary a {
  align-items: center;
  display: inline-flex;
  font-size: 0.8888888889rem;
  margin-bottom: 0.9444444444rem;
}

.panels-container .tabs-content .tabbed .glossary a span {
  margin-right: 0.2222222222rem;
  font-size: 1.1111111111rem;
}
.panels-container .panels-details-header {
  align-items: flex-start;
  flex: 1;
}
.panels-container .panels-details-header .content {
  align-items: flex-start;
  flex-direction: column;
  width: 100%;
  min-width: 100%;
  padding-right: 0;
}
@media (min-width: 67.5625rem) {
  .panels-container .panels-details-header .content {
    padding-right: 2.6666666667rem;
    width: 70%;
    min-width: 70%;
  }
}
.panels-container .panels-details-header .content .label {
  font-weight: 500;
  font-size: 0.8888888889rem;
}
.panels-container .panels-details-header .content h1 {
  font-size: 2.2222222222rem;
  margin-bottom: 0.6666666667rem;
  margin-top: 0;
  color: #1a1a1a;
}
.panels-container .panels-details-header .content p {
  font-size: 0.8888888889rem;
  margin-bottom: 0.6666666667rem;
  margin-top: 0;
}
.panels-container .panels-details-header .content .meta-info {
  width: 100%;
  gap: 0 0.6666666667rem;
  flex-wrap: wrap;
  margin-bottom: 1.3333333333rem;
}
.panels-container .panels-details-header .content .meta-info .meta {
  color: #4d4d4d;
  display: inline-flex;
  font-size: 0.8888888889rem;
}
.panels-container .panels-details-header .content .meta-info .meta strong {
  font-size: inherit;
  font-weight: 500;
  margin-right: 0.2222222222rem;
}
.panels-container .panels-details-header .content .meta-info .divider {
  color: #b2b2b2;
}
.panels-container .panels-details-header .content .ctas {
  flex-wrap: wrap;
  gap: 0.4444444444rem;
}
.panels-container .panels-details-header .content .ctas button {
  font-size: 0.8888888889rem;
  font-weight: 500;
}
.panels-container .panels-details-header img {
  display: none;
  max-width: 30%;
  width: 100%;
}
@media (min-width: 67.5625rem) {
  .panels-container .panels-details-header img {
    display: inline-block;
  }
}
.panels-container .hub-header .site.flex-wrapper {
  flex-direction: row;
}
.panels-container .hub-header a {
  margin-left: unset;
  color: #ffffff;
  font-size: 0.8888888889rem;
  text-decoration: underline;
}
.panels-container .panels-blue-bg {
  background-color: #e6ebf4;
  padding-top: 0.8888888889rem;
  padding-bottom: 2.2222222222rem;
}
.panels-container .panels-blue-bg .breadcrumbs-container {
  background-color: transparent;
  padding: 0;
}
.panels-container .panels-blue-bg .flex-wrapper {
  background-color: transparent;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}
.panels-container .panels-blue-bg .panels-details-header .content .meta-info {
  margin-bottom: 0;
}
.panels-container .panels-blue-bg .panels-details-header .content .lobs {
  margin: 0.8888888889rem 0;
}
.panels-container .panels-blue-bg .panels-details-header .content .topics {
  margin: 0.8888888889rem 0;
  flex-wrap: wrap;
}
.panels-container .panels-blue-bg .panels-details-header .content .topics a {
  display: inline-flex;
}
.panels-container .panels-blue-bg .panels-details-header .content .panel-hero-cta {
  display: flex;
  align-items: center;
}
.panels-container .panels-blue-bg .panels-details-header .content .topics > span {
  font-weight: 500;
  margin-right: 0.4444444444rem;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  margin-bottom: 0.4444444444rem;
}
@media (min-width: 37.5rem) {
  .panels-container .panels-blue-bg .panels-details-header .content .topics > span {
    margin-bottom: 0;
  }
}
.panels-container .panels-blue-bg .panels-details-header .content .topics > span span {
  font-size: 1rem;
  color: #00358e;
  margin-left: 0.2222222222rem;
}
.panels-container .panels-blue-bg .panels-details-header .content .topics .flex-wrapper {
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  width: 100%;
  flex-wrap: wrap;
}
@media (min-width: 37.5rem) {
  .panels-container .panels-blue-bg .panels-details-header .content .topics .flex-wrapper {
    align-items: center;
    flex-direction: row;
    width: unset;
  }
}
.panels-container .panels-blue-bg .panels-details-header .content .topics .chip {
  border-radius: 0.8888888889rem;
  border: thin solid #00358e;
  color: #00358e;
  font-size: 0.8888888889rem;
  padding: 0.2222222222rem 0.6666666667rem;
  margin-right: 0.4444444444rem;
  margin-bottom: 0.4444444444rem;
}
@media (min-width: 37.5rem) {
  .panels-container .panels-blue-bg .panels-details-header .content .topics .chip {
    margin-bottom: 0;
    margin-top: 0;
  }
}
.panels-container .panels-blue-bg .panels-details-header .content .topics .chip:last-child {
  margin-right: 0;
  margin-top: 0;
  margin-bottom: 0;
}
.panels-container .panels-blue-bg .panels-details-header .content .topics .chip:hover {
  background-color: #e6ebf4;
  color: #002665;
  cursor: pointer;
}
.info-banner.flex-wrapper {
  background-color: rgba(42, 125, 225, 0.15);
  border-radius: 0.25rem;
  border-bottom: none;
  text-align: left;
  padding: 1.25rem 1.5rem 1.5rem;
  margin-bottom: 2rem;
}

.panels-tab { 	
  aside.filter details .options {
    label.date-range {
      -ms-flex-align: center;
      align-items: center;
      input[type="date"] {
        margin-left: auto;
        width: 74%;
      }
	  #end-error {
	   	border: 0.063rem solid $red-4;
	  }
    }
	.error-message-container {
	  padding-left: 4.5rem;
	  .error-message {
		font-size: 0.75rem;
		display: -ms-flexbox;
		display: flex;
		color: $red-4;
		-ms-flex-align: center;
		align-items: center;
		gap: 0.125rem;
	  }
	}
  }
}
main .site.flex-wrapper aside section h2 p a{
	text-decoration: none;
	&:hover{
	  color: $background-dk-blue;
	}
}
.panels-container section.tabs-content .nav-wrapper .glossary{
	position: absolute;
    right: 0;
}

.panels-container .panel-history {
  display: flex;
}
.panels-container .panel-history .site.flex-wrapper {
  max-width: 100%;
  flex: 1;
  flex-wrap: wrap;
  width: 100%;
}
.panels-container .panel-history .site.flex-wrapper > section {
  width: 100%;
}
@media (min-width: 67.5625rem) {
  .panels-container .panel-history .site.flex-wrapper > section {
    max-width: 70%;
  }
}
.panels-container .panel-history .site.flex-wrapper aside.thin {
  position: unset;
  border-radius: 0.2222222222rem;
  margin-left: 0;
  padding-top: 0;
}
@media (max-width: 67.5rem) {
  .panels-container .panel-history .site.flex-wrapper aside.thin {
    width: 100%;
    margin: 0;
  }
}
@media (min-width: 67.5625rem) {
  .panels-container .panel-history .site.flex-wrapper aside.thin {
    position: sticky;
    top: 0.8888888889rem;
  }
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile {
  align-items: center;
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile h2 {
  display: inline-flex;
  align-items: center;
  padding: 0;
  margin: 0;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile h2 a {
  display: inline-flex;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile h2 span {
  color: #00358e;
  margin-left: 0.2222222222rem;
  font-size: 1rem;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile p {
  font-size: 0.7777777778rem;
  color: #4d4d4d;
  margin: 0;
  font-weight: 400;
  padding-right: 1.7777777778rem;
  width: 100%;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile a {
  font-size: 0.7777777778rem;
}
@media (min-width: 67.5625rem) {
  .panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile a[aria-label="Filter icon button"] {
    display: none;
  }
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile span {
  transition: all 0.25s;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .filter-toggle-mobile span.open {
  transform: rotate(180deg);
}
.panels-container .panel-history .site.flex-wrapper aside.thin #search-filters {
  padding-bottom: 0;
}
@media (max-width: 67.5rem) {
  .panels-container .panel-history .site.flex-wrapper aside.thin.fixed-at-top {
    border-bottom: thin solid #dbdbdb;
    box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 999;
  }
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters h2 {
  font-size: 0.8888888889rem;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters hr {
  border-width: 0;
  border-bottom: 0.1111111111rem solid #dbdbdb;
  margin: 0.8888888889rem 0;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul {
  padding-left: 0;
  padding-bottom: 1.7777777778rem;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul li {
  list-style-type: none;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul li a {
  color: #00358e;
  font-size: 0.8888888889rem;
  font-weight: 500;
  min-height: 0;
  display: inline-flex;
  align-items: center;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul li .accordionTab:hover {
  background-color: transparent;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul li .accordionContent {
  padding: 0 0.6666666667rem;
  margin-bottom: 0;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul li .accordionContent ul {
  padding-bottom: 0;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters ul li .accordionContent ul li a {
  display: inline-flex;
  font-weight: 400;
  padding: 0.2222222222rem;
  margin-bottom: 0.2222222222rem;
  min-height: unset;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .with-show-more-filters > ul {
  max-height: calc(75vh - 11.6666666667rem);
  overflow-y: auto;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer {
  flex-wrap: wrap;
  gap: 0.4444444444rem;
  align-items: flex-start;
}
@media (min-width: 48rem) {
  .panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer {
    align-items: center;
  }
}
.panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer > div {
  width: 100%;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer > div.hide {
  display: none;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer hr {
  border-width: 0;
  border-bottom: 0.0555555556rem solid #dbdbdb;
  margin: 0.8888888889rem 0 0.4444444444rem;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer a {
  display: inline-flex;
  align-items: center;
  font-size: 0.6666666667rem;
  position: relative;
}
.panels-container .panel-history .site.flex-wrapper aside.thin .aside-footer .separator {
  align-self: center;
  background-color: #636363;
  height: 0.6666666667rem;
  width: 0.0555555556rem;
}
.panels-container .panel-history .site.flex-wrapper .info-banner {
  background-color: #bdd7f6;
  padding: 0.8888888889rem;
  z-index: 0;
}
.panels-container .panel-history .site.flex-wrapper .info-banner .info-banner-content p {
  font-size: 0.7777777778rem;
  color: #4d4d4d;
}
.panels-container .panel-history section {
  flex: 1;
  padding-top: 0;
}
.panels-container .panel-history section .content h3 {
  font-size: 1.5555555556rem;
}
.panels-container .panel-history section .content h4 {
  font-size: 1.3333333333rem;
  margin-top: 0;
  margin-bottom: 0.8888888889rem;
}
.panels-container .panel-history section .content h5 {
  font-size: 1.1111111111rem;
  margin: 0;
  margin-bottom: 0.8888888889rem;
}
.panels-container .panel-history section .content p {
  margin-top: 0;
  margin-bottom: 0.8888888889rem;
}
.panels-container .panel-history section .content p:last-child {
  margin-bottom: 0;
}
.panels-container .panel-history section .content .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin-bottom: 1.3333333333rem;
  flex-wrap: wrap;
}
.panels-container .panel-history section .content .section-header h2 {
  color: #1a1a1a;
  font-size: 1.7777777778rem;
  margin: 0;
  padding-right: 0.8888888889rem;
}
.panels-container .panel-history section .content .section-header a {
  display: flex;
  align-items: center;
  font-size: 0.7777777778rem;
  font-weight: 500;
  margin: 0.4444444444rem 0;
}
.panels-container .panel-history section .content .section-header a span {
  font-size: 0.8888888889rem;
  margin-right: 0.4444444444rem;
}
.panels-container .panel-history section .content .gray-box {
  color: #4d4d4d;
  background-color: #f4f4f4;
  display: flex;
  border-radius: 0.2222222222rem;
  border: 0.1111111111rem solid #dbdbdb;
  padding: 1.3333333333rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 1.3333333333rem;
}
@media (min-width: 67.5625rem) {
  .panels-container .panel-history section .content .gray-box {
    width: 50%;
  }
}
.panels-container .panel-history section .content .attachment-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.4444444444rem;
}
.panels-container .panel-history section .content .attachment-item span {
  margin-right: 0.4444444444rem;
  margin-top: 0.1111111111rem;
}
.panels-container .panel-history section hr {
  border-width: 0;
  border-bottom: 0.0555555556rem solid #dbdbdb;
  margin: 2.6666666667rem 0;
}
.panels-container .panel-history section hr.double {
  border-bottom: 0.1111111111rem solid #dbdbdb;
}
.panels-tab {
  width: 100%;
}
.panels-tab .site.flex-wrapper {
  flex-wrap: wrap;
}
.panels-tab .site.flex-wrapper.overview {
  gap: 0;
}
.panels-tab .site.flex-wrapper .upcoming-panels {
  padding-top: 0;
}
.panels-tab .site.flex-wrapper .past-panels {
  padding-left: 1.875rem;
  padding-right: 1.875rem;
}
.panels-tab .site.flex-wrapper aside.thin {
  border-radius: 0.2222222222rem;
  margin-left: 0;
  padding-top: 0;
}

.panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile {
  align-items: center;
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile h2 {
  padding: 0;
  margin: 0;
}
.panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile p {
  font-size: 0.7777777778rem;
  color: #4d4d4d;
  margin: 0;
  font-weight: 400;
  padding-right: 1.7777777778rem;
  width: 100%;
}
.panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile a {
  font-size: 0.7777777778rem;
}
@media (min-width: 67.5625rem) {
  .panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile a[aria-label="Filter icon button"] {
    display: none;
  }
}
.panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile span {
  transition: all 0.25s;
}
.panels-tab .site.flex-wrapper aside.thin .filter-toggle-mobile span.open {
  transform: rotate(180deg);
}
.panels-tab .site.flex-wrapper aside.thin #search-filters {
  padding-bottom: 0;
}
.panels-tab .site.flex-wrapper aside.thin .aside-footer {
  flex-wrap: wrap;
  gap: 0.4444444444rem;
  align-items: flex-start;
}
@media (min-width: 48rem) {
  .panels-tab .site.flex-wrapper aside.thin .aside-footer {
    align-items: center;
  }
}
.panels-tab .site.flex-wrapper aside.thin .aside-footer > div {
  width: 100%;
}
.panels-tab .site.flex-wrapper aside.thin .aside-footer > div.hide {
  display: none;
}
.panels-tab .site.flex-wrapper aside.thin .aside-footer hr {
  border-width: 0;
  border-bottom: 0.0555555556rem solid #dbdbdb;
  margin: 0.8888888889rem 0 0.4444444444rem;
}
.panels-tab .site.flex-wrapper aside.thin .aside-footer a {
  display: inline-flex;
  align-items: center;
  font-size: 0.6666666667rem;
  position: relative;
}
.panels-tab .site.flex-wrapper aside.thin .aside-footer .separator {
  align-self: center;
  background-color: #636363;
  height: 0.6666666667rem;
  width: 0.0555555556rem;
}
.panels-tab .site.flex-wrapper .hero {
  border-radius: 0.2222222222rem;
  padding: 2.2222222222rem 1.3333333333rem;
  margin-bottom: 2.2222222222rem;
  width: 100%;
}
.panels-tab .site.flex-wrapper .hero h1 {
  font-size: 2.2222222222rem;
}

.panels-tab .site.flex-wrapper .hero p {
  font-size: 1rem;
}
.panels-tab .site.flex-wrapper .hero button {
  font-size: 1rem;
  font-weight: 500;
}
@media (max-width: 67.5rem) {
  .panels-tab .site.flex-wrapper .hero .messaging {
    max-width: 100%;
    padding: 0;
    order: 1;
  }
}
.panels-tab .site.flex-wrapper .hero img {
  max-width: 100%;
  padding-bottom: 0.8888888889rem;
}
.panels-tab section {
  padding-top: 0;
  padding-bottom: 0;
}
.panels-tab section.results-listing, .panels-tab main.state-filings section.updates, main.state-filings .panels-tab section.updates {
  flex: 1;
  padding-top: 0;
}
@media (max-width: 67.5rem) {
  .panels-tab section.results-listing, .panels-tab main.state-filings section.updates, main.state-filings .panels-tab section.updates {
    max-width: 100%;
  }
}
.panels-tab section.results-listing .info-banner, .panels-tab main.state-filings section.updates .info-banner, main.state-filings .panels-tab section.updates .info-banner {
  padding: 0.8888888889rem;
  margin-bottom: 0.8888888889rem;
}
.panels-tab section.results-listing .info-banner p, .panels-tab main.state-filings section.updates .info-banner p, main.state-filings .panels-tab section.updates .info-banner p {
  font-size: 0.7777777778rem;
  margin: 0;
}
.panels-tab section.results-listing .results-meta-sort, .panels-tab main.state-filings section.updates .results-meta-sort, main.state-filings .panels-tab section.updates .results-meta-sort {
  font-size: 0.8888888889rem;
  justify-content: space-between;
  margin: 0.8888888889rem 0;
  flex-wrap: wrap;
}
.panels-tab section.results-listing .results-meta-sort > span, .panels-tab main.state-filings section.updates .results-meta-sort > span, main.state-filings .panels-tab section.updates .results-meta-sort > span {
  margin-top: 0.4444444444rem;
}
.panels-tab section.results-listing .results-meta-sort .sort-by, .panels-tab main.state-filings section.updates .results-meta-sort .sort-by, main.state-filings .panels-tab section.updates .results-meta-sort .sort-by {
  margin-top: 0.4444444444rem;
}
.panels-tab section.results-listing .results-meta-sort .sort-by label, .panels-tab main.state-filings section.updates .results-meta-sort .sort-by label, main.state-filings .panels-tab section.updates .results-meta-sort .sort-by label {
  font-weight: 500;
  font-size: 0.8888888889rem;
  margin-right: 0.4444444444rem;
}
.panels-tab section.results-listing .results-meta-sort .sort-by select, .panels-tab main.state-filings section.updates .results-meta-sort .sort-by select, main.state-filings .panels-tab section.updates .results-meta-sort .sort-by select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='24px' viewBox='0 0 24 24' width='24px' fill='%234D4D4D'%3E%3Cpath d='M24 24H0V0h24v24z' fill='none' opacity='.87'/%3E%3Cpath d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6-1.41-1.41z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center right 0.4444444444rem;
  border-color: #8c8c8c;
  border-radius: 0.2222222222rem;
  padding: 0.3333333333rem 1.7777777778rem 0.3333333333rem 0.6666666667rem;
  position: relative;
}
.panels-tab section.results-listing .card h3, .panels-tab main.state-filings section.updates .card h3, main.state-filings .panels-tab section.updates .card h3 {
  font-size: 1rem;
  margin-top: 0;
  margin-bottom: 0.4444444444rem;
}
.panels-tab section.results-listing .card .badge.panels, .panels-tab main.state-filings section.updates .card .badge.panels, main.state-filings .panels-tab section.updates .card .badge.panels {
  color: #4d4d4d;
  display: inline-flex;
  border: thin solid #8c8c8c;
  font-weight: 500;
  font-size: 0.7777777778rem;
  align-self: flex-start;
  margin: 0 0 0.4444444444rem 0;
}
.panels-tab section.results-listing .card p, .panels-tab main.state-filings section.updates .card p, main.state-filings .panels-tab section.updates .card p {
  margin-bottom: 0.2222222222rem;
  margin-top: 0;
  padding: 0;
}
.panels-tab section.results-listing .card .meta-info, .panels-tab main.state-filings section.updates .card .meta-info, main.state-filings .panels-tab section.updates .card .meta-info {
  width: 100%;
  gap: 0 0.6666666667rem;
  flex-wrap: wrap;
}
.panels-tab section.results-listing .card .meta-info .meta, .panels-tab main.state-filings section.updates .card .meta-info .meta, main.state-filings .panels-tab section.updates .card .meta-info .meta {
  color: #4d4d4d;
  display: inline-flex;
  font-size: 0.7777777778rem;
}
.panels-tab section.results-listing .card .meta-info .meta strong, .panels-tab main.state-filings section.updates .card .meta-info .meta strong, main.state-filings .panels-tab section.updates .card .meta-info .meta strong {
  font-size: inherit;
  font-weight: 500;
  margin-right: 0.2222222222rem;
}
.panels-tab section.results-listing .card .meta-info .divider, .panels-tab main.state-filings section.updates .card .meta-info .divider, main.state-filings .panels-tab section.updates .card .meta-info .divider {
  color: #b2b2b2;
}
.panels-tab section.results-listing .card .no-img, .panels-tab main.state-filings section.updates .card .no-img, main.state-filings .panels-tab section.updates .card .no-img {
  border-radius: 0.2222222222rem;
  background-color:$background-lt-blue-2;
  min-height: 7.5rem;
  min-width: 7.5rem;
  -o-object-fit: cover;
     object-fit: cover;
  order: unset;
  display: none;
}
@media (min-width: 67.5rem) {
  .panels-tab section.results-listing .card .no-img, .panels-tab main.state-filings section.updates .card .no-img, main.state-filings .panels-tab section.updates .card .no-img {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
@media (min-width: 67.5rem) {
  .panels-tab section.results-listing .card .no-img span, .panels-tab main.state-filings section.updates .card .no-img span, main.state-filings .panels-tab section.updates .card .no-img span {
      color: #8aa2cb;
      font-size: 3.5555555556rem;
  }
}
.panels-tab section.results-listing .card .panel-label, .panels-tab main.state-filings section.updates .card .panel-label, main.state-filings .panels-tab section.updates .card .panel-label {
  color: #1a1a1a;
  font-weight: 500;
  font-size: 0.7777777778rem;
}
.panels-tab section.results-listing .page-results-wrapper, .panels-tab main.state-filings section.updates .page-results-wrapper, main.state-filings .panels-tab section.updates .page-results-wrapper, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper, .panels-tab main.state-filings section.updates .update-pagination-wrapper, main.state-filings .panels-tab section.updates .update-pagination-wrapper {
  justify-content: center;
}
@media (min-width: 67.5625rem) {
  .panels-tab section.results-listing .page-results-wrapper, .panels-tab main.state-filings section.updates .page-results-wrapper, main.state-filings .panels-tab section.updates .page-results-wrapper, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper, .panels-tab main.state-filings section.updates .update-pagination-wrapper, main.state-filings .panels-tab section.updates .update-pagination-wrapper {
    justify-content: space-between;
  }
}
.panels-tab section.results-listing .page-results-wrapper nav, .panels-tab main.state-filings section.updates .page-results-wrapper nav, main.state-filings .panels-tab section.updates .page-results-wrapper nav, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav {
  display: flex;
  justify-content: center;
  margin: 0;
  width: 100%;
}
@media (min-width: 67.5625rem) {
  .panels-tab section.results-listing .page-results-wrapper nav, .panels-tab main.state-filings section.updates .page-results-wrapper nav, main.state-filings .panels-tab section.updates .page-results-wrapper nav, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav {
    width: unset;
  }
}
.panels-tab section.results-listing .page-results-wrapper nav .pagination, .panels-tab main.state-filings section.updates .page-results-wrapper nav .pagination, main.state-filings .panels-tab section.updates .page-results-wrapper nav .pagination, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav .pagination, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav .pagination, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav .pagination, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav .pagination {
  align-items: center;
  display: flex;
  justify-content: space-evenly;
  padding: 0;
  gap: 0.6125rem;
}
@media (min-width: 67.5625rem) {
  .panels-tab section.results-listing .page-results-wrapper nav .pagination, .panels-tab main.state-filings section.updates .page-results-wrapper nav .pagination, main.state-filings .panels-tab section.updates .page-results-wrapper nav .pagination, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav .pagination, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav .pagination, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav .pagination, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav .pagination {
    justify-content: unset;
    width: unset;
    gap: unset;
  }
}
.panels-tab section.results-listing .page-results-wrapper nav .pagination li, .panels-tab main.state-filings section.updates .page-results-wrapper nav .pagination li, main.state-filings .panels-tab section.updates .page-results-wrapper nav .pagination li, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav .pagination li, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav .pagination li, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav .pagination li, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav .pagination li {
  margin: 0;
}
@media (min-width: 67.5625rem) {
  .panels-tab section.results-listing .page-results-wrapper nav .pagination li, .panels-tab main.state-filings section.updates .page-results-wrapper nav .pagination li, main.state-filings .panels-tab section.updates .page-results-wrapper nav .pagination li, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav .pagination li, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav .pagination li, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav .pagination li, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav .pagination li {
    margin: 0 0.5rem;
  }
}
.panels-tab section.results-listing .page-results-wrapper nav .pagination li a, .panels-tab main.state-filings section.updates .page-results-wrapper nav .pagination li a, main.state-filings .panels-tab section.updates .page-results-wrapper nav .pagination li a, .panels-tab section.results-listing main.state-filings .updates .update-pagination-wrapper nav .pagination li a, main.state-filings .updates .panels-tab section.results-listing .update-pagination-wrapper nav .pagination li a, .panels-tab main.state-filings section.updates .update-pagination-wrapper nav .pagination li a, main.state-filings .panels-tab section.updates .update-pagination-wrapper nav .pagination li a {
  align-items: center;
  display: flex;
}
.panels-tab .upcoming-panels {
  flex-wrap: wrap;
  width: 100%;
}
.panels-tab .upcoming-panels-heading {
  flex: 1;
  margin-bottom: 1.7777777778rem;
}
.panels-tab .upcoming-panels-heading h2 {
  font-size: 1.7777777778rem;
  padding-bottom: 0.2222222222rem;
  line-height: 1.2;
  color: #1a1a1a;
}
.panels-tab .upcoming-panels-heading p {
  color: #4d4d4d;
}
.panels-tab .upcoming-panels .cards {
  width: 100%;
}
.panels-tab .upcoming-panels .cards.empty {
  margin-left: 0rem;
}
.panels-tab .upcoming-panels .cards.empty h3 {
  color: #1a1a1a;
  text-align: center;
}
.panels-tab .upcoming-panels .cards .card {
  min-width: calc(25% - 0.4444444444rem);
  flex: 0;
  padding: 1rem;
}
@media (max-width: 67.5rem) {
  .panels-tab .upcoming-panels .cards .card {
    min-width: calc(50% - 0.4444444444rem);
  }
}
@media (max-width: 47.9375rem) {
  .panels-tab .upcoming-panels .cards .card {
    min-width: 100%;
  }
}
.panels-tab .upcoming-panels .cards .card * {
  order: unset;
}
.panels-tab .upcoming-panels .cards .card h3 {
  font-size: 1.3333333333rem;
  margin-bottom: 0.4444444444rem;
}
.panels-tab .upcoming-panels .cards .card p {
  color: #4d4d4d;
}
.panels-tab .upcoming-panels .cards .card .meta-date {
  font-size: 0.8888888889rem;
  margin-bottom: 0.4444444444rem;
}
.panels-tab .upcoming-panels .cards .card .meta-date time {
  font-style: normal;
}
.panels-tab .upcoming-panels .cards .card .meta-date time strong {
  padding: 0;
  font-size: 0.8888888889rem;
  font-weight: 500;
}
.panels-tab .upcoming-panels .cards .card .meta-lob {
  font-size: 0.7777777778rem;
  color: #4d4d4d;
  margin-bottom: 0.2222222222rem;
}
.panels-tab .upcoming-panels .cards .card.empty {
  border: thin solid #dbdbdb;
  border-radius: 0.2222222222rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 1.3333333333rem;
  width: 100%;
  margin: 0;
}
.panels-tab .upcoming-panels .cards .card.empty h3 {
  font-size: 1.1111111111rem;
}
.panels-tab .upcoming-panels .cards .card.empty p {
  font-size: 0.7777777778rem;
  text-align: center;
}
.panels-tab .past-panels {
  align-items: flex-start;
  flex: 1;
  flex-wrap: wrap;
  margin-left: -1.875rem;
  margin-right: -1.875rem;
}
.panels-tab .past-panels-heading {
  justify-content: space-between;
  flex: 1;
  margin-bottom: 1.7777777778rem;
}
@media (max-width: 67.5rem) {
  .panels-tab .past-panels-heading {
    flex-wrap: wrap;
  }
  .panels-tab .past-panels-heading a {
    margin-top: 0.4444444444rem;
    padding-top: 0;
  }
}
.panels-tab .past-panels-heading h2 {
  font-size: 1.7777777778rem;
  line-height: 1.2;
  padding-bottom: 0.2222222222rem;
  color: #1a1a1a;
}
.panels-tab .past-panels-heading p {
  color: #4d4d4d;
}
.panels-tab .past-panels-heading > div {
  padding-right: 1.3333333333rem;
}
.panels-tab .past-panels-heading a {
  font-weight: 500;
  font-size: 1rem;
  display: none;
  align-self: flex-start;
  padding-top: 0.4444444444rem;
}
@media (min-width: 67.5625rem) {
  .panels-tab .past-panels-heading a {
    display: inline-flex;
  }
}
.panels-tab .past-panels .cards {
  justify-content: space-evenly;
  width: 100%;
}
.panels-tab .past-panels .cards .card {
  min-width: calc(25% - 1rem);
  flex: 1;
  padding: 1rem;
}
@media (max-width: 67.5rem) {
  .panels-tab .past-panels .cards .card {
    min-width: calc(50% - 0.4444444444rem);
  }
}
@media (max-width: 47.9375rem) {
  .panels-tab .past-panels .cards .card {
    min-width: 100%;
  }
}
.panels-tab .past-panels .cards .card * {
  order: unset;
}
.panels-tab .past-panels .cards .card h3 {
  font-size: 1.3333333333rem;
  margin-bottom: 0.4444444444rem;
}
.panels-tab .past-panels .cards .card p {
  color: #4d4d4d;
}
.panels-tab .past-panels .cards .card .meta-date {
  font-size: 0.8888888889rem;
  margin-bottom: 0.4444444444rem;
}
.panels-tab .past-panels .cards .card .meta-date time {
  font-style: normal;
}
.panels-tab .past-panels .cards .card .meta-date time strong {
  padding: 0;
  font-size: 0.8888888889rem;
  font-weight: 500;
}
.panels-tab .past-panels .cards .card .meta-lob {
  font-size: 0.7777777778rem;
  color: #4d4d4d;
  margin-bottom: 0.2222222222rem;
}
.panels-tab .view-all {
  font-weight: 500;
  font-size: 1rem;
  display: inline-flex;
  align-items: flex-start;
  margin-top: 0.8888888889rem;
  white-space: nowrap;
}
@media (min-width: 67.5625rem) {
  .panels-tab .view-all {
    display: none;
  }
}
.panels-tab.agenda aside, .panels-tab.minutes aside, .panels-tab.contact-info aside, .panels-tab.panel-participants aside {
  border-radius: 0.2222222222rem;
  position: unset;
}
@media (min-width: 67.5625rem) {
  .panels-tab.agenda aside, .panels-tab.minutes aside, .panels-tab.contact-info aside, .panels-tab.panel-participants aside {
    position: sticky;
    top: 0.8888888889rem;
  }
}
@media (max-width: 67.5rem) {
  .panels-tab.agenda aside.fixed-at-top, .panels-tab.minutes aside.fixed-at-top, .panels-tab.contact-info aside.fixed-at-top, .panels-tab.panel-participants aside.fixed-at-top {
    border-bottom: thin solid #dbdbdb;
    box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 999;
  }
}
.panels-tab.agenda aside .with-show-more-filters h2, .panels-tab.minutes aside .with-show-more-filters h2, .panels-tab.contact-info aside .with-show-more-filters h2, .panels-tab.panel-participants aside .with-show-more-filters h2 {
  font-size: 0.8888888889rem;
}
.panels-tab.agenda aside .with-show-more-filters hr, .panels-tab.minutes aside .with-show-more-filters hr, .panels-tab.contact-info aside .with-show-more-filters hr, .panels-tab.panel-participants aside .with-show-more-filters hr {
  border-width: 0;
  border-bottom: 0.1111111111rem solid #dbdbdb;
  margin: 0.8888888889rem 0;
}
.panels-tab.agenda aside .with-show-more-filters ul, .panels-tab.minutes aside .with-show-more-filters ul, .panels-tab.contact-info aside .with-show-more-filters ul, .panels-tab.panel-participants aside .with-show-more-filters ul {
  padding-left: 0;
  padding-bottom: 1.7777777778rem;
}
.panels-tab.agenda aside .with-show-more-filters ul li, .panels-tab.minutes aside .with-show-more-filters ul li, .panels-tab.contact-info aside .with-show-more-filters ul li, .panels-tab.panel-participants aside .with-show-more-filters ul li {
  list-style-type: none;
}
.panels-tab.agenda aside .with-show-more-filters ul li a, .panels-tab.minutes aside .with-show-more-filters ul li a, .panels-tab.contact-info aside .with-show-more-filters ul li a, .panels-tab.panel-participants aside .with-show-more-filters ul li a {
  color: #00358e;
  font-size: 0.8888888889rem;
  font-weight: 500;
  min-height: 0;
  display: inline-flex;
  align-items: center;
}
.panels-tab.agenda aside .with-show-more-filters ul li .accordionTab, .panels-tab.minutes aside .with-show-more-filters ul li .accordionTab, .panels-tab.contact-info aside .with-show-more-filters ul li .accordionTab, .panels-tab.panel-participants aside .with-show-more-filters ul li .accordionTab {
  border-bottom: transparent;
  color: #00358e;
  font-size: 0.8888888889rem;
  padding: 0;
  justify-content: space-between;
  width: 100%;
  padding-top: 4px;
  padding-bottom: 4px;
}
.panels-tab.agenda aside .with-show-more-filters ul li .accordionTab:hover, .panels-tab.minutes aside .with-show-more-filters ul li .accordionTab:hover, .panels-tab.contact-info aside .with-show-more-filters ul li .accordionTab:hover, .panels-tab.panel-participants aside .with-show-more-filters ul li .accordionTab:hover {
  background-color: transparent;
}
.panels-tab.agenda aside .with-show-more-filters ul li .accordionContent, .panels-tab.minutes aside .with-show-more-filters ul li .accordionContent, .panels-tab.contact-info aside .with-show-more-filters ul li .accordionContent, .panels-tab.panel-participants aside .with-show-more-filters ul li .accordionContent {
  padding: 0 0.6666666667rem;
  margin-bottom: 0;
}
.panels-tab.agenda aside .with-show-more-filters ul li .accordionContent ul, .panels-tab.minutes aside .with-show-more-filters ul li .accordionContent ul, .panels-tab.contact-info aside .with-show-more-filters ul li .accordionContent ul, .panels-tab.panel-participants aside .with-show-more-filters ul li .accordionContent ul {
  padding-bottom: 0;
}
.panels-tab.agenda aside .with-show-more-filters ul li .accordionContent ul li a, .panels-tab.minutes aside .with-show-more-filters ul li .accordionContent ul li a, .panels-tab.contact-info aside .with-show-more-filters ul li .accordionContent ul li a, .panels-tab.panel-participants aside .with-show-more-filters ul li .accordionContent ul li a {
  display: inline-flex;
  font-weight: 400;
  padding: 0.2222222222rem;
  margin-bottom: 0.2222222222rem;
  min-height: unset;
}
.panels-tab.agenda aside .with-show-more-filters > ul, .panels-tab.minutes aside .with-show-more-filters > ul, .panels-tab.contact-info aside .with-show-more-filters > ul, .panels-tab.panel-participants aside .with-show-more-filters > ul {
  max-height: calc(100vh - 11.6666666667rem);
  overflow-y: auto;
}
.panels-tab.agenda .site.flex-wrapper > section, .panels-tab.minutes .site.flex-wrapper > section, .panels-tab.contact-info .site.flex-wrapper > section, .panels-tab.panel-participants .site.flex-wrapper > section {
  max-width: 100%;
  flex: 1;
}
@media (min-width: 67.5625rem) {
  .panels-tab.agenda .site.flex-wrapper > section, .panels-tab.minutes .site.flex-wrapper > section, .panels-tab.contact-info .site.flex-wrapper > section, .panels-tab.panel-participants .site.flex-wrapper > section {
    max-width: 70%;
  }
}
.panels-tab.agenda section, .panels-tab.minutes section, .panels-tab.contact-info section, .panels-tab.panel-participants section {
  flex: 1;
  padding-top: 0;
}
.panels-tab.agenda section .content h3, .panels-tab.minutes section .content h3, .panels-tab.contact-info section .content h3, .panels-tab.panel-participants section .content h3 {
  font-size: 1.3333333333rem;
  margin-top: 0;
  margin-bottom: 0.8888888889rem;
}
.panels-tab.agenda section .content h4, .panels-tab.minutes section .content h4, .panels-tab.contact-info section .content h4, .panels-tab.panel-participants section .content h4 {
  font-size: 1.1111111111rem;
  margin: 0;
}
.panels-tab.agenda section .content .panel-id-label, .panels-tab.minutes section .content .panel-id-label, .panels-tab.contact-info section .content .panel-id-label, .panels-tab.panel-participants section .content .panel-id-label{
  display: inline-block;
  font-size: 0.7777777778rem;
  font-weight: 500;
  margin-bottom: 0.4444444444rem;
}
.panels-tab.agenda section .content p, .panels-tab.minutes section .content p, .panels-tab.contact-info section .content p, .panels-tab.panel-participants section .content p {
  margin-top: 0;
  margin-bottom: 0.8888888889rem;
}
.panels-tab.agenda section .content p:last-child, .panels-tab.minutes section .content p:last-child, .panels-tab.contact-info section .content p:last-child, .panels-tab.panel-participants section .content p:last-child {
  margin-bottom: 0;
}
.panels-tab.agenda section .content .section-header, .panels-tab.minutes section .content .section-header, .panels-tab.contact-info section .content .section-header, .panels-tab.panel-participants section .content .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  margin-bottom: 1.3333333333rem;
  flex-wrap: wrap;
}
.panels-tab.agenda section .content .section-header h2, .panels-tab.minutes section .content .section-header h2, .panels-tab.contact-info section .content .section-header h2, .panels-tab.panel-participants section .content .section-header h2 {
  color: #1a1a1a;
  font-size: 1.5555555556rem;
  margin: 0;
  padding-right: 0.8888888889rem;
}
.panels-tab.agenda section .content .section-header .panel-id-label, .panels-tab.minutes section .content .section-header .panel-id-label, .panels-tab.contact-info section .content .section-header .panel-id-label, .panels-tab.panel-participants section .content .section-header .panel-id-label{
  font-size: 0.8888888889rem;
  font-weight: 500;
  margin-bottom: 0;
}
.panels-tab.agenda section .content .section-header a, .panels-tab.minutes section .content .section-header a, .panels-tab.contact-info section .content .section-header a, .panels-tab.panel-participants section .content .section-header a {
  display: flex;
  align-items: center;
  font-size: 0.7777777778rem;
  font-weight: 500;
  margin: 0.4444444444rem 0;
}
.panels-tab.agenda section .content .section-header a span, .panels-tab.minutes section .content .section-header a span, .panels-tab.contact-info section .content .section-header a span, .panels-tab.panel-participants section .content .section-header a span {
  font-size: 0.8888888889rem;
  margin-right: 0.4444444444rem;
}
.panels-tab.agenda section .content .gray-box, .panels-tab.minutes section .content .gray-box, .panels-tab.contact-info section .content .gray-box, .panels-tab.panel-participants section .content .gray-box {
  color: #4d4d4d;
  background-color: #f4f4f4;
  display: flex;
  border-radius: 0.2222222222rem;
  border: 0.1111111111rem solid #dbdbdb;
  padding: 1.3333333333rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: 1.3333333333rem;
}
@media (min-width: 67.5625rem) {
  .panels-tab.agenda section .content .gray-box, .panels-tab.minutes section .content .gray-box, .panels-tab.contact-info section .content .gray-box, .panels-tab.panel-participants section .content .gray-box {
    width: 50%;
  }
}
.panels-tab.agenda section .content .attachment-item, .panels-tab.minutes section .content .attachment-item, .panels-tab.contact-info section .content .attachment-item, .panels-tab.panel-participants section .content .attachment-item {
  display: inline-flex;
  align-items: center;
}
.panels-tab.agenda section .content .attachment-item span, .panels-tab.minutes section .content .attachment-item span, .panels-tab.contact-info section .content .attachment-item span, .panels-tab.panel-participants section .content .attachment-item span {
  margin-right: 0.4444444444rem;
}
.panels-tab.agenda section hr, .panels-tab.minutes section hr, .panels-tab.contact-info section hr, .panels-tab.panel-participants section hr {
  border-width: 0;
  border-bottom: 0.1111111111rem solid #dbdbdb;
  margin: 2.6666666667rem 0;
}
.panels-tab.contact-info aside {
  position: unset;
}
.panels-tab.contact-info aside h2 {
  margin-bottom: 0;
  padding-bottom: 0;
}
.panels-tab.contact-info .site.flex-wrapper > section {
  max-width: 37.7777777778rem;
}
.panels-tab.panel-participants .site.flex-wrapper > section {
  max-width: 37.7777777778rem;
}
.panels-tab.panel-participants .panel-member strong {
  display: block;
}
.panels-tab.panel-participants .panel-member .panel-jobtitle {
  font-size: 1rem;
  color: #4d4d4d;
}
.panels-tab.panel-participants .panel-member-placeholder {
  background-color: #dbdbdb;
  width: 100%;
  min-height: 27.7777777778rem;
  margin-top: 0.4444444444rem;
}

section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .upcoming-panels .card,
section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .past-panels .card {
  margin-left: -1rem;
  padding: 1rem;
}

.panels-container {
  .breadcrumbs {
    a {
      align-items: center;
      display: inline-flex;
    }
  }
  .panels-blue-bg{
    .panels-details-header{
      .badge{
        &.panels{
          color: $neutral-800;
          display: inline-flex;
          border: thin solid $border-neutral;
          font-weight: 500;
          font-size: 0.7777777778rem;
          align-self: flex-start;
          margin: 0 0 0.6666666667rem 0;
        }
      }
    }
  }

  .panel {
    &.hero {
      h2.no-margin {
        margin-bottom: 0.6666666667rem;
        font-size: 2.2222222222rem;
        color: $neutral-1000;
      }

      p {
        font-size: 0.8888888889rem;
        margin-bottom: 0.6666666667rem;
        margin-top: 0;
      }

      small {
        .bold {
          font-weight: 500;
          color: $neutral-800;
        }

        span:nth-child(2) {
          border-right: thin solid;
          margin-right: 0.5rem;
          padding-right: 0.5rem;
        }
      }

      img {
        border-radius: 0.25rem;
      }

      .call-to-action {
        display: flex;
        gap: 0.5rem;
      }
    }
  }

  section.panels-tab {
    .with-cta {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      h2 {
        font-size: 1.7777777778rem;
      }

      a {
        font-size: 0.9rem;
        font-weight: 500;
        margin-left: auto;

        span.material-icons {
          vertical-align: bottom;
        }
      }
    }

    .cards {
      align-items: stretch;
      @media (max-width: 67.5rem) {
        flex-wrap: wrap;
        justify-content: flex-start
      }
      .card{
        *{
          order: unset;
        }
        &.four{
          width: 24.3%;
        }
        
        @media (max-width: 67.5rem) {
          min-width: calc(50% - 0.4444444444rem);
        }
        @media (max-width: 47.9375rem) {
          min-width: 100%;
        }
      }
      article a {
        h3{
          font-size: 1.3333333333rem;
          margin-bottom: 0.4444444444rem;
        }
        .meta-date {
          font-size: 0.8888888889rem;
          margin-bottom: 0.4444444444rem;
          time{
            font-style: normal;
            strong{
              padding: 0;
              font-size: 0.8888888889rem;
              font-weight: 500;
            }
          }
        }

        .meta-lob {
          font-size: 0.7777777778rem;
          color: #4d4d4d;
          align-items: center;
          display: inline-flex;
          strong{
            padding: 0;
            font-size: 0.7777777778rem;
            color: #4d4d4d;
            margin-bottom: 0.2222222222rem;
            margin-bottom: 0;
            margin-right: 0.2222222222rem;
          }
        }

        p {
          color: $neutral-800;
        }

        img {
          margin-bottom: 1.3333333333rem;
        }
      }
    }

    &.background-blue{
      margin-top: 0.1rem;
      h2,h3,p{
        color: $white;
      }
      .cards{
        .card{
          h3{
            color: $white;
          }
          small, span{
            color: $white;
          }
        }
      }
    }
  }

  .empty {
    border: thin solid $neutral-100;
    border-radius: 0.2222222222rem;
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: center;
    justify-content: center;
    padding: 1.3333333333rem;
    width: 100%;
    margin: 0;
    span{
      color: $primary-200;
      font-size: 3.5555555556rem;
    }

    @media (max-width: 50rem) {
      padding: 1.3333333333rem;
    }

    .empty-message {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      h3 {
        font-size: 1.1111111111rem;
      }

      p {
        font-size: 0.7777777778rem;
        text-align: center;
        color: $neutral-800;
      }
    }
  }
}

.panels-tab {
  .site.flex-wrapper {
    aside {
      &.thin {
        width: 19.125rem;
        @media (max-width: 67.5rem) {
          width: 100%;
          margin: 0;
      }
      .keyword-class-codes {
        label {
          color: $neutral-1000;
        }
      }

        section {
          width: 100%;
        }

        details {
          .options {
            label {
              .clear-all-btn {
                margin-left: auto;
                font-size: 0.78rem;
                cursor: pointer;
                color: $default-link;
                padding-right: 0;
              }
            }
          }

          .options-children {
            padding-left: 1.5rem;
          }
        }
      }
    }
  }
  .results-listing{
    .cards{
      &.flex-wrapper{
        align-items: stretch;
      }
    }
  }
}
