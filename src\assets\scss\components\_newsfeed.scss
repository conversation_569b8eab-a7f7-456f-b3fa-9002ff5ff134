.newsfeed {
    article {
        padding-bottom: 1rem;
        time {
            font-size: .875rem;
        }
        address {
            display: inline-block;
            font-style: normal;
            font-weight: 700;
            padding-right: .5rem;
        }
        span {
            font-size: .8rem;
        }
    }
    &.panel {
        .date-selector {
            padding-bottom: 1rem;
            margin-bottom: 2rem;
            border-bottom: thin solid $border-md-grey;
            & * {
                vertical-align: middle;
                font-size: 1.25rem;
            }
            time {
                margin: 0 1rem;
            }
            span.material-icons {
                font-size: 2.5rem;
            }
        }

        .flex-wrapper {
            flex-wrap: wrap;
            gap: 1rem 2rem;
            align-content: flex-start;
            align-items: flex-start;

            article {
                width: 48%;
                font-size: 1.2rem;
            }
        }
        
    }
}