.notification-preferences-page {
  width: 100%;
}

.pop-up {
  height: 12rem;

  @media (max-width: 768px) {
    height: 18rem;
  }

}


.notification-preferences-container {
  .main-wrapper {
    width: 100%;
    max-width: 75rem;
    margin: 0 auto;
    padding: 1.25rem;
  }

  .notification-preferences-header {
    margin: 0 0 rem(33) 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: rem(24);
    align-self: stretch;

    .notification-header-title {
      color: $neutral-1000;
      font-size: 1.9rem;
      font-weight: 500;
      line-height: rem(32);
      margin: 0;
    }

    .notification-header-desc {
      line-height: rem(24);
    }

    .email-notifications {
      display: flex;
      padding: rem(24);
      flex-direction: column;
      gap: rem(10);
      border-radius: rem(4);
      border: thin solid $neutral-100;
      background: $neutral-50;
      align-self: stretch;

      .email-notifications-title {
        color: $neutral-1000;
        font-weight: 500;
        line-height: rem(24);
        margin: 0;
      }

      .email-notifications-desc {
        margin-top: rem(-4);
      }

      .notification-checkbox {
        padding: 0 rem(16);
        margin: 0;
      }
    }
  }

  .desc-text {
    color: $text-secondary;
    font-weight: 400;
    line-height: rem(21);
    margin: 0;
  }

  .checkbox-text {
    font-size: 0.9rem;
  }

  .notification-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .notify-about {
    margin-bottom: 1rem;
  }

  .notification-separator {
    padding: rem(32) rem(24);

    .notification-separator-line {
      border-bottom: thin solid $neutral-100;
    }
  }

  .notification-product-selection {
    .notification-product-selection-wrapper {
      display: flex;
      align-items: flex-start;
      gap: rem(24);
      margin-bottom: rem(16);
    }
  }

  .selected-product {
    border-top: thin solid $neutral-100;
    background: #fff;

    .selected-product-wrapper {
      padding: rem(12) 0 rem(24) 0;
      gap: rem(10);
      align-self: stretch;
      background: #f4f4f4;
      margin-bottom: 5rem;
    }

    .options {
      display: flex;
      gap: 0.6rem;
      padding: rem(16) rem(24);
      margin: rem(16) rem(32);
      border: thin solid $theDs;
      border-radius: rem(4);
      background-color: $white;
      flex-wrap: wrap;
      width: -webkit-fill-available;

      h5 {
        grid-column: span 3;
        margin-bottom: rem(8);
        margin: 0;
      }

      label {
        display: flex;
        align-items: start;
        gap: rem(8);
        padding-left: rem(24);
        width: 20rem;
      }

      .main-label {
        width: 100%;
        padding: 0;
      }

      ul {
        padding: 0;
        margin: 0;

        li {
          padding: 0.3rem 0;
          list-style: none;
          border: none;

          &:hover {
            background-color: transparent;
          }

          label {
            display: flex;
            align-items: center;
            gap: rem(8);
            width: auto;
          }
        }
      }
    }

    label {
      color: $text-secondary;
      font-size: 0.9rem;
      font-weight: 400;
      line-height: rem(20);
    }
  }

  .selected-product-header {
    padding: rem(16) rem(24) rem(16) rem(48);
    align-self: stretch;

    .selected-product-title {
      color: #00358e;
      font-weight: 500;
      line-height: rem(24);
      text-transform: capitalize;
      margin: 0;
    }

    .selected-product-desc {
      margin-top: rem(4);
    }
  }
}

.notification-checkbox {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: rem(8);
  margin-top: rem(10);
}

.label-text {
  color: $neutral-1000;
  font-size: 1rem;
  font-weight: 500;
}

.profile-dropdown {
  display: none;
}

.notification-label {
  font-size: 1.3rem;
  line-height: rem(32);
}

.notification-select {
  width: 100%;
  display: flex;
  align-items: center;
  border: thin solid #8c8c8c;
  padding: rem(8) rem(12) rem(8) rem(12);
  border-radius: rem(4);
  background: #fff;
  gap: rem(10);
  align-self: stretch;
  overflow: hidden;
  color: $neutral-1000;
  text-overflow: ellipsis;
  font-size: rem(16);
  height: fit-content;
  font-weight: 400;
  line-height: rem(24);
  box-sizing: border-box;
  appearance: auto;

  &:hover {
    border: thin solid #00358e;
  }

  & {
    &:focus {
      outline: rem(2) solid #00358e;
      outline-offset: rem(-1);
    }

    &:disabled {
      border: thin solid $neutral-100;
    }
  }
}

.preferences-bottom {
  position: fixed;
  bottom: 1.333rem;
  border: none;

}

.preferences-above-footer {
  position: absolute;
  bottom: 1.333rem;
  opacity: 1;
}

.selected-product-container {
  display: none;
}

.preferences-footer-container {
  display: none;
}

.preferences-footer {
  display: flex;
  // position: fixed;
  z-index: 99999;
  bottom: 0;
  width: 100%;
  padding: rem(24);
  align-items: center;
  gap: rem(10);
  background: #fff;
  box-shadow: 0 rem(-8) rem(16) 0 rgba(0, 0, 0, 0.12);

  .btn-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: rem(8);

    .setting-txt {
      margin-left: rem(24);
      color: $text-secondary;
      font-size: rem(16);
      font-weight: 500;
      line-height: rem(24);
    }
  }
}

.toggle-container {
  padding: 0.5rem;

  .switch {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
  }
}

.switch {
  position: relative;
  display: inline-block;
  height: 1.25rem;

  input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    width: 2.5rem;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $the9s;
    transition: 0.4s;
    border-radius: 1.25rem;

    &:before {
      position: absolute;
      content: '';
      height: 1rem;
      width: 1rem;
      left: 0.125rem;
      bottom: 0.125rem;
      background-color: $white;
      transition: 0.4s;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Material Icons';
    }
  }

  input:checked+.slider {
    background-color: $background-blue;
  }

  input:checked+.slider:before {
    transform: translateX(1.25rem);
    content: 'check';
    font-size: 0.75rem;
  }

  input:checked+.slider:hover {
    background-color: $background-secondary-hover;
  }

  &.disabled .slider {
    background-color: $the9s;
    cursor: not-allowed;
  }

  &.disabled .slider:before {
    background-color: $white;
  }
}

.custom-checkbox,
.email-notif-checkbox {
  input[type='checkbox'] {
    position: absolute;
    opacity: 0;
    cursor: pointer;

    &:checked+.checkmark {
      background-color: $background-blue;
      border-color: $background-blue;

      &::after {
        display: block;
      }
    }

    &:not(:checked):disabled+.checkmark {
      background-color: transparent;
      border-color: $neutral-100;
      opacity: 0.6;
    }

    &:disabled+.checkmark {
      background-color: $neutral-100;
      border-color: $neutral-100;
      opacity: 0.6;

      &::after {
        border-color: $white;
      }
    }
  }

  .checkmark {
    position: relative;
    display: inline-block;
    width: rem(16);
    height: rem(16);
    min-width: rem(16);
    background-color: $white;
    border-radius: rem(4);
    border: thin solid $border-neutral;
    background: $white;
    transition: background-color 0.3s, border-color 0.3s;

    &::after {
      content: '';
      position: absolute;
      display: none;
      top: 42%;
      left: 50%;
      width: rem(4);
      height: rem(8);
      border: solid $white;
      border-width: 0 rem(1) rem(1) 0;
      transform: translate(-50%, -50%) rotate(45deg);
    }
  }
}

.content-toggle {
  display: none;
}

.toggle-text {
  margin-left: 3rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  pointer-events: none;
  color: $text-secondary;
  font-size: rem(14);
  font-weight: 500;
}

.warning-message {
  display: flex;
  align-items: center;
  font-size: 0.8rem;
  color: #c10017;
  margin-top: 0.5rem;
  clear: both;
  width: 100%;

  .material-icons {
    margin-right: 0.2rem;
    font-size: rem(18);
  }
}

.material-symbols-outlined {
  font-variation-settings: 'FILL' 1, 'wght' 400, 'GRAD' 0, 'opsz' 20;
  font-size: rem(18);
}

@media (max-width: 56.25rem) {
  .notification-product-selection-wrapper {
    flex-wrap: wrap;
  }
}

@media (max-width: 37.5rem) {
  .notification-preferences-container {
    .selected-product-header {
      padding: rem(16) rem(12) rem(16) rem(24);
      align-self: stretch;
    }

    .notification-select {
      width: 80%;
    }
  }
}