.panels-modal {
  .modal {
    background-color: #ffffff;
    border-radius: 0.5rem;
    min-width: 80vw;
    height: -moz-fit-content;
    height: fit-content;
    box-shadow: 0 0.5rem 1rem 0 rgba(0, 0, 0, 0.12);
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    opacity: 1;
    transition: all 0.25s;
  }
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.875rem 1.5rem 0.875rem 1.5rem;
    border-bottom: thin solid #dbdbdb;

    h3 {
      margin: 0;
    }
    button {
      height: 2.75rem;
      width: 2.75rem;
      padding: 0;
    }
  }

  .modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    .react-pdf__Page {
      canvas {
        margin: auto;
      }
    }
  }

  .flex-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  @media (min-width: 67.5625rem) {
    .modal {
      min-width: 40vw;
    }
  }
  .agenda-pdf {
    height: calc(100vh - 2.6666666667rem);
    width: 70vw;
    .modal-header {
      align-items: flex-start;
      & > div {
        display: flex;
        flex-direction: column;
        h3 {
          font-size: 1.1111111111rem;
        }
      }
      .flex-wrapper {
        padding-top: 0.4444444444rem;
        flex: 1;
        flex-wrap: wrap;
        button {
          padding: 0.5555555556rem 0.8888888889rem;
          display: inline-flex;
          height: auto;
          width: auto;
          font-weight: 500;
          margin: 0.3333333333rem 0.4444444444rem 0.3333333333rem 0;
          span {
            margin-right: 0.2222222222rem;
          }
        }
      }
    }
    .modal-body {
      height: calc(-171px + 100vh);
    }
  }
}
