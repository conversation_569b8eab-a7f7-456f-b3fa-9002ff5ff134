.panels-tab,
.panels-container {
  section.toc-content,
  section.panel-history,
  section.results-listing {
    .content {
      &.ContactInfo {
        .panelItemContent {
          table {
            padding-left: 40px;

            tbody {
              tr {
                td {
                  padding: 0;

                  .tabletext11 {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }

      &.PanelParticipants {
        .panelItemContent {
          table {
            tbody {
              tr {
                td {
                  padding: 0;
                  border-bottom: none;

                  .cvrhead1 {
                    margin-bottom: 0;

                    span {
                      font-weight: 600;
                    }
                  }

                  .cvrtext {
                    margin-bottom: 0;
                  }
                }
              }
            }
          }
        }
      }

      ul:has(li:nth-child(2)) li:last-child {
        margin-bottom: 10px;
      }

      .PanelBullet1 {
        padding-bottom: 0;
      }

      .PanelBullet2 {
        margin-left: 1rem;
        margin-bottom: 0;
      }

      .PanelBullet3 {
        margin-left: 3rem;
        margin-bottom: 0;
      }

      .blockhd1 {
        margin-left: 1rem;
        margin-bottom: 0;
        span {
          font-weight: bold;
        }
      }

      .blocktext1 {
        margin-left: 1rem;
        margin-bottom: 0;
      }

      .blocktext2 {
        margin-left: 2rem;
        margin-bottom: 0;
      }

      .blocktext3 {
        margin-left: 3rem;
        margin-bottom: 0;
      }

      .blocktext4 {
        margin-left: 4rem;
        margin-bottom: 0;
      }

      .blocktext8 {
        margin-left: 3rem;
      }

      .outlinetxt1 {
        margin-left: 1rem;
        margin-bottom: 0;
      }

      p.outlinetxt2 {
        margin-left: 2rem;
        margin-bottom: 0;
      }

      .outlinetxt3 {
        margin-left: 3rem;
        margin-bottom: 0;
      }

      .outlinetxt4 {
        margin-left: 4rem;
        margin-bottom: 0;
      }

      .outlinetxt5 {
        margin-left: 5rem;
        margin-bottom: 0;
      }

      .outlinehd1 {
        margin-left: 1rem;
        margin-bottom: 0;
      }

      .outlinehd3 {
        margin-left: 3rem;
        margin-bottom: 0;
      }

      .isonormal {
        margin-left: 3rem;
        margin-bottom: 0;
      }

      .text1 {
        margin-left: 0rem;
      }

      .outlinetxt3:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }

      .outlinetxt2:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }

      .blocktext3:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }

      .outlinetxt1:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }

      .outlinetxt2:has(+ .blockhd1) {
        margin-bottom: 0.8888888889rem;
      }

      .isonormal:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }

      .outlinetxt4:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }

      .blocktext2:has(+ .outlinehd1) {
        margin-bottom: 0.8888888889rem;
      }

      .blocktext3:has(+ .outlinehd1) {
        margin-bottom: 0.8888888889rem;
      }

      .blocktext2:has(+ .text) {
        margin-bottom: 0.8888888889rem;
      }
    }

    .card {
      ul {
        margin: 0;
      }
    }
  }
}
