$color_1: #00358e;
$color_2: #4d4d4d;
$color_3: #1a1a1a;
$background-color_1: transparent;
$background-color_2: #636363;
$background-color_3: #bdd7f6;
$background-color_4: #f4f4f4;
$background-color_5: #dbdbdb;

.panels-container {
  .panel-history {
    display: flex;
    @media (max-width: 575px) {
      flex-direction: column;
    }
    .site.flex-wrapper {
      max-width: 100%;
      flex: 1;
      flex-wrap: wrap;
      width: 100%;
      > section {
        width: 100%;
      }
      aside.thin {
        position: unset;
        border-radius: 0.2222222222rem;
        margin-left: 0;
        padding-top: 0;
        .filter-toggle-mobile {
          align-items: center;
          display: flex;
          justify-content: space-between;
          width: 100%;
          h2 {
            display: inline-flex;
            align-items: center;
            padding: 0;
            margin: 0;
            a {
              display: inline-flex;
            }
            span {
              color: $color_1;
              margin-left: 0.2222222222rem;
              font-size: 1rem;
            }
          }
          p {
            font-size: 0.7777777778rem;
            color: $color_2;
            margin: 0;
            font-weight: 400;
            padding-right: 1.7777777778rem;
            width: 100%;
          }
          a {
            font-size: 0.7777777778rem;
          }
          span {
            transition: all 0.25s;
          }
          span.open {
            transform: rotate(180deg);
          }
        }
        #search-filters {
          padding-bottom: 0;
        }
        .with-show-more-filters {
          h2 {
            font-size: 0.8888888889rem;
          }
          hr {
            border-width: 0;
            border-bottom: 0.1111111111rem solid #dbdbdb;
            margin: 0.8888888889rem 0;
          }
          ul {
            padding-left: 0;
            padding-bottom: 1.7777777778rem;
            li {
              list-style-type: none;
              a {
                color: $color_1;
                font-size: 0.8888888889rem;
                font-weight: 500;
                min-height: 2.4444444444rem;
                display: inline-flex;
                align-items: center;
              }
              .accordionTab {
                border-bottom: transparent;
                color: $color_1;
                font-size: 0.8888888889rem;
                justify-content: space-between;
                width: 100%;
                &:hover {
                  background-color: $background-color_1;
                  cursor: pointer;
                }
              }
              .accordionContent {
                padding: 0 0.6666666667rem;
                margin-bottom: 0;
                max-height: 0;
                overflow: hidden;
                opacity: 0;
                transition: max-height 0.4s ease, opacity 0.4s ease;
                ul {
                  padding-bottom: 0;
                  li {
                    a {
                      display: inline-flex;
                      font-weight: 400;
                      padding: 0.2222222222rem;
                      margin-bottom: 0.2222222222rem;
                      min-height: unset;
                    }
                  }
                }
              }
              .accordionContent.active {
                max-height: 200px; /* Adjust to a high enough value to accommodate content */
                opacity: 1;
              }
            }
          }
          > ul {
            max-height: calc(75vh - 11.6666666667rem);
            overflow-y: auto;
          }
        }
        .aside-footer {
          flex-wrap: wrap;
          gap: 0.4444444444rem;
          align-items: flex-start;
          > div {
            width: 100%;
          }
          > div.hide {
            display: none;
          }
          hr {
            border-width: 0;
            border-bottom: 0.0555555556rem solid #dbdbdb;
            margin: 0.8888888889rem 0 0.4444444444rem;
          }
          a {
            display: inline-flex;
            align-items: center;
            font-size: 0.6666666667rem;
            position: relative;
          }
          .separator {
            align-self: center;
            background-color: $background-color_2;
            height: 0.6666666667rem;
            width: 0.0555555556rem;
          }
        }
      }
      .info-banner {
        background-color: $background-color_3;
        padding: 0.8888888889rem;
        z-index: 0;
        p {
          font-size: 0.7777777778rem;
          color: $color_2;
          margin: 0.5rem 0;
        }
      }
    }
    section {
      flex: 1;
      padding-top: 0;
      .content {
        .panelItemContent {
          display: flex;
          flex-direction: column;
          gap: 0.556rem;
        }

        h2 {
          margin: 0;
        }
        h3 {
          font-size: 1.5555555556rem;
        }
        h4 {
          font-size: 1.3333333333rem;
          margin-top: 0;
          margin-bottom: 0.8888888889rem;
        }
        h5 {
          font-size: 1.1111111111rem;
          margin: 0;
          margin-bottom: 0.8888888889rem;
        }
        p {
          margin-top: 0;
          margin-bottom: 0.8888888889rem;
          &:last-child {
            margin-bottom: 0;
          }
        }
        .section-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex: 1;
          margin-bottom: 1.3333333333rem;
          flex-wrap: wrap;
          h2 {
            color: $color_3;
            font-size: 1.7777777778rem;
            margin: 0;
            padding-right: 0.8888888889rem;
          }
          a {
            display: flex;
            align-items: center;
            font-size: 0.7777777778rem;
            font-weight: 500;
            margin: 0.4444444444rem 0;
            span {
              font-size: 0.8888888889rem;
              margin-right: 0.4444444444rem;
            }
          }
          &.with-cta {
            border-bottom: thin solid $background-color_5;
            padding-bottom: 1.7777777778rem;
          }
        }
        .gray-box {
          color: $color_2;
          background-color: $background-color_4;
          display: flex;
          border-radius: 0.2222222222rem;
          border: 0.1111111111rem solid #dbdbdb;
          padding: 1.3333333333rem;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          margin-top: 1.3333333333rem;
        }
        .attachment-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 0.4444444444rem;
          span {
            margin-right: 0.4444444444rem;
            margin-top: 0.1111111111rem;
          }
        }
      }
      hr {
        border-width: 0;
        border-bottom: 0.0555555556rem solid #dbdbdb;
        margin: 2.6666666667rem 0;
      }
      hr.double {
        border-bottom: 0.1111111111rem solid #dbdbdb;
      }
    }
  }
}
.tabNav.tabContent.panels-tab {
  display: none;
}
.tabNav.tabContent.panels-tab.active {
  display: inline-block;
}
.panels-tab {
  width: 100%;
  .site.flex-wrapper {
    flex-wrap: wrap;
  }
  .site.flex-wrapper.overview {
    gap: 0;
  }
}
.panels-tab.agenda {
  aside {
    border-radius: 0.2222222222rem;
    position: unset;
    .with-show-more-filters {
      h2 {
        font-size: 0.8888888889rem;
      }
      hr {
        border-width: 0;
        border-bottom: 0.1111111111rem solid #dbdbdb;
        margin: 0.8888888889rem 0;
      }
      ul {
        padding-left: 0;
        padding-bottom: 1.7777777778rem;
        li {
          list-style-type: none;
          a {
            color: $color_1;
            font-size: 0.8888888889rem;
            font-weight: 500;
            min-height: 2.4444444444rem;
            display: inline-flex;
            align-items: center;
          }
          .accordionTab {
            border-bottom: transparent;
            color: $color_1;
            font-size: 0.8888888889rem;
            padding: 0;
            padding-top: 4px;
            padding-bottom: 4px;
            justify-content: space-between;
            width: 100%;
            &:hover {
              background-color: $background-color_1;
            }
          }
          .accordionContent {
            padding: 0 0.6666666667rem;
            margin-bottom: 0;
            ul {
              padding-bottom: 0;
              li {
                a {
                  display: inline-flex;
                  font-weight: 400;
                  padding: 0.2222222222rem;
                  margin-bottom: 0.2222222222rem;
                  min-height: unset;
                }
              }
            }
          }
        }
      }
      > ul {
        max-height: calc(100vh - 11.6666666667rem);
        overflow-y: auto;
      }
    }
  }
  .site.flex-wrapper {
    > section {
      max-width: 100%;
      flex: 1;
    }
  }
  section {
    flex: 1;
    padding-top: 0;
    .content {
      h3 {
        font-size: 1.3333333333rem;
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
      }
      h4 {
        font-size: 1.1111111111rem;
        margin: 0;
      }
      p {
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        margin-bottom: 1.3333333333rem;
        flex-wrap: wrap;
        h2 {
          color: $color_3;
          font-size: 1.5555555556rem;
          margin: 0;
          padding-right: 0.8888888889rem;
        }
        a {
          display: flex;
          align-items: center;
          font-size: 0.7777777778rem;
          font-weight: 500;
          margin: 0.4444444444rem 0;
          span {
            font-size: 0.8888888889rem;
            margin-right: 0.4444444444rem;
          }
        }
      }
      .gray-box {
        color: $color_2;
        background-color: $background-color_4;
        display: flex;
        border-radius: 0.2222222222rem;
        border: 0.1111111111rem solid #dbdbdb;
        padding: 1.3333333333rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-top: 1.3333333333rem;
      }
      .attachment-item {
        display: inline-flex;
        align-items: center;
        span {
          margin-right: 0.4444444444rem;
        }
      }
    }
    hr {
      border-width: 0;
      border-bottom: 0.1111111111rem solid #dbdbdb;
      margin: 2.6666666667rem 0;
    }
  }
}
.panel-tooltip-content {
  & > div {
    display: flex;
    flex-direction: column;
    h2 {
      font-size: 0.9rem;
      margin: 0;
      margin-bottom: 0.8888888889rem;
    }
    div.flex-wrapper.flex-wrapper {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      div.label {
        font-weight: 500;
      }
      p {
        margin-top: 0;
        font-size: 1rem;
      }
    }
  }
}
.panels-tab.minutes {
  aside {
    border-radius: 0.2222222222rem;
    position: unset;
    .with-show-more-filters {
      h2 {
        font-size: 0.8888888889rem;
      }
      hr {
        border-width: 0;
        border-bottom: 0.1111111111rem solid #dbdbdb;
        margin: 0.8888888889rem 0;
      }
      ul {
        padding-left: 0;
        padding-bottom: 1.7777777778rem;
        li {
          list-style-type: none;
          a {
            color: $color_1;
            font-size: 0.8888888889rem;
            font-weight: 500;
            min-height: 2.4444444444rem;
            display: inline-flex;
            align-items: center;
          }
          .accordionTab {
            border-bottom: transparent;
            color: $color_1;
            font-size: 0.8888888889rem;
            padding: 0;
            padding-top: 4px;
            padding-bottom: 4px;
            justify-content: space-between;
            width: 100%;
            &:hover {
              background-color: $background-color_1;
            }
          }
          .accordionContent {
            padding: 0 0.6666666667rem;
            margin-bottom: 0;
            ul {
              padding-bottom: 0;
              li {
                a {
                  display: inline-flex;
                  font-weight: 400;
                  padding: 0.2222222222rem;
                  margin-bottom: 0.2222222222rem;
                  min-height: unset;
                }
              }
            }
          }
        }
      }
      > ul {
        max-height: calc(100vh - 11.6666666667rem);
        overflow-y: auto;
      }
    }
  }
  .site.flex-wrapper {
    > section {
      max-width: 100%;
      flex: 1;
    }
  }
  section {
    flex: 1;
    padding-top: 0;
    .content {
      h3 {
        font-size: 1.3333333333rem;
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
      }
      h4 {
        font-size: 1.1111111111rem;
        margin: 0;
      }
      p {
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        margin-bottom: 1.3333333333rem;
        flex-wrap: wrap;
        h2 {
          color: $color_3;
          font-size: 1.5555555556rem;
          margin: 0;
          padding-right: 0.8888888889rem;
        }
        a {
          display: flex;
          align-items: center;
          font-size: 0.7777777778rem;
          font-weight: 500;
          margin: 0.4444444444rem 0;
          span {
            font-size: 0.8888888889rem;
            margin-right: 0.4444444444rem;
          }
        }
      }
      .gray-box {
        color: $color_2;
        background-color: $background-color_4;
        display: flex;
        border-radius: 0.2222222222rem;
        border: 0.1111111111rem solid #dbdbdb;
        padding: 1.3333333333rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-top: 1.3333333333rem;
      }
      .attachment-item {
        display: inline-flex;
        align-items: center;
        span {
          margin-right: 0.4444444444rem;
        }
      }
    }
    hr {
      border-width: 0;
      border-bottom: 0.1111111111rem solid #dbdbdb;
      margin: 2.6666666667rem 0;
    }
  }
}
.panels-tab.contact-info {
  aside {
    border-radius: 0.2222222222rem;
    position: unset;
    position: unset;
    .with-show-more-filters {
      h2 {
        font-size: 0.8888888889rem;
      }
      hr {
        border-width: 0;
        border-bottom: 0.1111111111rem solid #dbdbdb;
        margin: 0.8888888889rem 0;
      }
      ul {
        padding-left: 0;
        padding-bottom: 1.7777777778rem;
        li {
          list-style-type: none;
          a {
            color: $color_1;
            font-size: 0.8888888889rem;
            font-weight: 500;
            min-height: 2.4444444444rem;
            display: inline-flex;
            align-items: center;
          }
          .accordionTab {
            border-bottom: transparent;
            color: $color_1;
            font-size: 0.8888888889rem;
            padding: 0;
            padding-top: 4px;
            padding-bottom: 4px;
            justify-content: space-between;
            width: 100%;
            &:hover {
              background-color: $background-color_1;
            }
          }
          .accordionContent {
            padding: 0 0.6666666667rem;
            margin-bottom: 0;
            ul {
              padding-bottom: 0;
              li {
                a {
                  display: inline-flex;
                  font-weight: 400;
                  padding: 0.2222222222rem;
                  margin-bottom: 0.2222222222rem;
                  min-height: unset;
                }
              }
            }
          }
        }
      }
      > ul {
        max-height: calc(100vh - 11.6666666667rem);
        overflow-y: auto;
      }
    }
    h2 {
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
  .site.flex-wrapper {
    > section {
      max-width: 100%;
      flex: 1;
      max-width: 37.7777777778rem;
    }
  }
  section {
    flex: 1;
    padding-top: 0;
    .content {
      h3 {
        font-size: 1.3333333333rem;
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
      }
      h4 {
        font-size: 1.1111111111rem;
        margin: 0;
      }
      p {
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        margin-bottom: 1.3333333333rem;
        flex-wrap: wrap;
        h2 {
          color: $color_3;
          font-size: 1.5555555556rem;
          margin: 0;
          padding-right: 0.8888888889rem;
        }
        a {
          display: flex;
          align-items: center;
          font-size: 0.7777777778rem;
          font-weight: 500;
          margin: 0.4444444444rem 0;
          span {
            font-size: 0.8888888889rem;
            margin-right: 0.4444444444rem;
          }
        }
      }
      .gray-box {
        color: $color_2;
        background-color: $background-color_4;
        display: flex;
        border-radius: 0.2222222222rem;
        border: 0.1111111111rem solid #dbdbdb;
        padding: 1.3333333333rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-top: 1.3333333333rem;
      }
      .attachment-item {
        display: inline-flex;
        align-items: center;
        span {
          margin-right: 0.4444444444rem;
        }
      }
    }
    hr {
      border-width: 0;
      border-bottom: 0.1111111111rem solid #dbdbdb;
      margin: 2.6666666667rem 0;
    }
  }
}
.panels-tab.panel-participants {
  aside {
    border-radius: 0.2222222222rem;
    position: unset;
    .with-show-more-filters {
      h2 {
        font-size: 0.8888888889rem;
      }
      hr {
        border-width: 0;
        border-bottom: 0.1111111111rem solid #dbdbdb;
        margin: 0.8888888889rem 0;
      }
      ul {
        padding-left: 0;
        padding-bottom: 1.7777777778rem;
        li {
          list-style-type: none;
          a {
            color: $color_1;
            font-size: 0.8888888889rem;
            font-weight: 500;
            min-height: 2.4444444444rem;
            display: inline-flex;
            align-items: center;
          }
          .accordionTab {
            border-bottom: transparent;
            color: $color_1;
            font-size: 0.8888888889rem;
            padding: 0;
            padding-top: 4px;
            padding-bottom: 4px;
            justify-content: space-between;
            width: 100%;
            &:hover {
              background-color: $background-color_1;
            }
          }
          .accordionContent {
            padding: 0 0.6666666667rem;
            margin-bottom: 0;
            ul {
              padding-bottom: 0;
              li {
                a {
                  display: inline-flex;
                  font-weight: 400;
                  padding: 0.2222222222rem;
                  margin-bottom: 0.2222222222rem;
                  min-height: unset;
                }
              }
            }
          }
        }
      }
      > ul {
        max-height: calc(100vh - 11.6666666667rem);
        overflow-y: auto;
      }
    }
  }
  .site.flex-wrapper {
    > section {
      max-width: 100%;
      flex: 1;
      max-width: 37.7777777778rem;
    }
  }
  section {
    flex: 1;
    padding-top: 0;
    .content {
      h3 {
        font-size: 1.3333333333rem;
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
      }
      h4 {
        font-size: 1.1111111111rem;
        margin: 0;
      }
      p {
        margin-top: 0;
        margin-bottom: 0.8888888889rem;
        &:last-child {
          margin-bottom: 0;
        }
      }
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        margin-bottom: 1.3333333333rem;
        flex-wrap: wrap;
        h2 {
          color: $color_3;
          font-size: 1.5555555556rem;
          margin: 0;
          padding-right: 0.8888888889rem;
        }
        a {
          display: flex;
          align-items: center;
          font-size: 0.7777777778rem;
          font-weight: 500;
          margin: 0.4444444444rem 0;
          span {
            font-size: 0.8888888889rem;
            margin-right: 0.4444444444rem;
          }
        }
      }
      .gray-box {
        color: $color_2;
        background-color: $background-color_4;
        display: flex;
        border-radius: 0.2222222222rem;
        border: 0.1111111111rem solid #dbdbdb;
        padding: 1.3333333333rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin-top: 1.3333333333rem;
      }
      .attachment-item {
        display: inline-flex;
        align-items: center;
        span {
          margin-right: 0.4444444444rem;
        }
      }
    }
    hr {
      border-width: 0;
      border-bottom: 0.1111111111rem solid #dbdbdb;
      margin: 2.6666666667rem 0;
    }
  }
  .panel-member {
    strong {
      display: block;
    }
    .panel-jobtitle {
      font-size: 1rem;
      color: $color_2;
    }
  }
  .panel-member-placeholder {
    background-color: $background-color_5;
    width: 100%;
    min-height: 27.7777777778rem;
    margin-top: 0.4444444444rem;
  }
}
@media (min-width: 67.5625rem) {
  .panels-container {
    .panel-history {
      .site.flex-wrapper {
        > section {
          max-width: 70%;
        }
        aside.thin {
          position: sticky;
          top: 0.8888888889rem;
          .filter-toggle-mobile {
            a[aria-label="Filter icon button"] {
              display: none;
            }
          }
        }
      }
    }
  }
  section {
    .content {
      .gray-box {
        width: 50%;
      }
    }
  }

  .panels-tab.agenda {
    aside {
      position: sticky;
      top: 0.8888888889rem;
    }
    .site.flex-wrapper {
      > section {
        max-width: 70%;
      }
    }
    section {
      .content {
        .gray-box {
          width: 50%;
        }
      }
    }
  }
  .panels-tab.minutes {
    aside {
      position: sticky;
      top: 0.8888888889rem;
    }
    .site.flex-wrapper {
      > section {
        max-width: 70%;
      }
    }
    section {
      .content {
        .gray-box {
          width: 50%;
        }
      }
    }
  }
  .panels-tab.contact-info {
    aside {
      position: sticky;
      top: 0.8888888889rem;
    }
    .site.flex-wrapper {
      > section {
        max-width: 70%;
      }
    }
    section {
      .content {
        .gray-box {
          width: 50%;
        }
      }
    }
  }
  .panels-tab.panel-participants {
    aside {
      position: sticky;
      top: 0.8888888889rem;
    }
    .site.flex-wrapper {
      > section {
        max-width: 70%;
      }
    }
    section {
      .content {
        .gray-box {
          width: 50%;
        }
      }
    }
  }
}
@media (max-width: 67.5rem) {
  .panels-container {
    .panel-history {
      .site.flex-wrapper {
        aside.thin {
          width: 100%;
          margin: 0;
        }
        aside.thin.fixed-at-top {
          border-bottom: thin solid #dbdbdb;
          box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
          position: fixed;
          left: 0;
          top: 0;
          right: 0;
          z-index: 999;
        }
      }
    }
  }
  .panels-tab.agenda {
    aside.fixed-at-top {
      border-bottom: thin solid #dbdbdb;
      box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      z-index: 999;
    }
  }
  .panels-tab.minutes {
    aside.fixed-at-top {
      border-bottom: thin solid #dbdbdb;
      box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      z-index: 999;
    }
  }
  .panels-tab.contact-info {
    aside.fixed-at-top {
      border-bottom: thin solid #dbdbdb;
      box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      z-index: 999;
    }
  }
  .panels-tab.panel-participants {
    aside.fixed-at-top {
      border-bottom: thin solid #dbdbdb;
      box-shadow: 0 0.4444444444rem 0.8888888889rem 0 rgba(0, 0, 0, 0.12);
      position: fixed;
      left: 0;
      top: 0;
      right: 0;
      z-index: 999;
    }
  }
}
@media (min-width: 48rem) {
  .panels-container {
    .panel-history {
      .site.flex-wrapper {
        aside.thin {
          .aside-footer {
            align-items: center;
          }
        }
      }
    }
  }
}
