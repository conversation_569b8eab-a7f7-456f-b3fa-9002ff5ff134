@function px-to-rem($px) {
  $rem-value: calc($px / 18);
  @return calc(round($rem-value * 1000) / 1000) * 1rem;
}

aside.toc-wrapper.toc-wrapper.toc-wrapper {
  padding-top: 0; // to eliminate toc padding until we follow prototype css
}
.toc-wrapper {
  padding-top: px-to-rem(16);
  .panels-toc {
    background: #f4f4f4;
    padding: px-to-rem(16);
    padding-top: 0;
    border-radius: px-to-rem(4);
    position: sticky;
    top: px-to-rem(16);

    .table-of-content-heading {
      padding: px-to-rem(24) px-to-rem(12) px-to-rem(8) px-to-rem(12);
      align-items: center;
      display: flex;
      justify-content: space-between;
      width: 100%;
      h2 {
        font-size: 0.8888888889rem;
        display: inline-flex;
        align-items: center;
        padding: 0;
        margin: 0;
        border: none;
        span {
          color: #00358e;
          margin-left: 0.2222222222rem;
          font-size: 1rem;
        }
        a {
          display: inline-flex;
          font-size: 0.7777777778rem;
        }
      }
      border-bottom: 2px solid #dbdbdb;
    }

    ul {
      list-style-type: none;
      padding-left: 0; // Remove default padding
      margin: 0; // Remove default margin
    }

    .panel-item {
      padding: px-to-rem(8) 0 px-to-rem(8) 0;
      li.toc-main-heading {
        display: flex;
        justify-content: space-between;
        a {
          font-weight: 500;
        }
      }

      li {
        padding: px-to-rem(8) px-to-rem(12);
      }
      a {
        font-size: px-to-rem(16);
        line-height: px-to-rem(24);
        text-align: left;
        color: #00358e;
        font-weight: 400;
        &.active {
          font-weight: 500;
        }
      }

      ul {
        padding-left: px-to-rem(16);
        li {
          padding: px-to-rem(4) px-to-rem(12);
        }
      }
      .material-icons {
        cursor: pointer;
        color: #00358e;
      }
    }
  }
}

.panels-toc.scrollable-section {
  flex: 1;
  overflow-y: auto;
  max-height: 100vh;
}

.hidden {
  display: none;
}

.visible {
  display: block;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollable-section::-webkit-scrollbar {
  width: px-to-rem(0);
  background: transparent; /* Optional: just to ensure the background is transparent */
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollable-section {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.dynamic-tab-content {
  padding-top: 0;
}
.panels-container > * {
  flex: 0 0 100%;
  max-width: 100%;
}

.col-xs-12 {
  flex: 0 0 100%;
  max-width: 100%;
  &.toc-wrapper {
    order: 1;
  }
  &.toc-content {
    order: 2;
  }
}

@media (min-width: 576px) {
  .panels-container.toc-right {
    .toc-wrapper {
      order: 2;
    }
    .toc-content {
      order: 1;
    }
  }
}
.col-sm-4 {
  @media (min-width: 576px) {
    flex: 0 0 33.3333%;
    max-width: 33.3333%;
  }
}

.col-md-3 {
  @media (min-width: 768px) {
    flex: 0 0 25%;
    max-width: 25%;
  }
}

.col-sm-8 {
  @media (min-width: 576px) {
    flex: 0 0 66.6667%;
    max-width: calc(66.6667% - 4rem);
  }
}

.col-md-9 {
  @media (min-width: 768px) {
    flex: 0 0 75%;
    max-width: calc(75% - 4rem);
  }
}

.checkGap {
  display: flex;
  flex-direction: column;
  gap: px-to-rem(24);
}

$background-color_1: #636363;
$toc-background-color_2: #dbdbdb;
$toc-background-color_3: #e5e5e5;

.panels-container {
  .panel-history {
    .site.flex-wrapper {
      aside.thin {
        width: 19.125rem;
        section {
          width: 100%;
        }
        .aside-footer {
          flex-wrap: wrap;
          gap: 0.4444444444rem;
          align-items: flex-start;
          > div {
            width: 100%;
          }
          > div.hide {
            display: none;
          }
          hr {
            border-width: 0;
            border-bottom: 0.0555555556rem solid $toc-background-color_2;
            margin: 0.8888888889rem 0 0.4444444444rem;
          }
          a {
            display: inline-flex;
            align-items: center;
            font-size: 0.6666666667rem;
            position: relative;
          }
          .separator {
            align-self: center;
            background-color: $background-color_1;
            height: 0.6666666667rem;
            width: 0.0555555556rem;
          }
        }
      }
    }
  }
}
.panels-tab {
  .site.flex-wrapper {
    aside.thin {
      width: 19.125rem;
      section {
        width: 100%;
      }
      section.with-show-more-filters.background-lt-grey {
        ul {
          padding-bottom: 0;
        }
      }
      .aside-footer {
        flex-wrap: wrap;
        gap: 0.4444444444rem;
        align-items: flex-start;
        > div {
          width: 100%;
        }
        > div.hide {
          display: none;
        }
        hr {
          border-width: 0;
          border-bottom: 0.0555555556rem solid #dbdbdb;
          margin: 0.8888888889rem 0 0.4444444444rem;
        }
        a {
          display: inline-flex;
          align-items: center;
          font-size: 0.6666666667rem;
          position: relative;
        }
        .separator {
          align-self: center;
          background-color: $background-color_1;
          height: 0.6666666667rem;
          width: 0.0555555556rem;
        }
      }
    }
  }
}
.panels-container
  .panel-history
  .site.flex-wrapper
  aside.thin
  .with-show-more-filters.background-lt-grey
  ul {
  padding-bottom: 0;
}

@media (min-width: 48rem) {
  .panels-container {
    .panel-history {
      .site.flex-wrapper {
        aside.thin {
          .aside-footer {
            align-items: center;
          }
        }
      }
    }
  }
  .panels-tab {
    .site.flex-wrapper {
      aside.thin {
        .aside-footer {
          align-items: center;
        }
      }
    }
  }
}
.accordionTab {
  color: #00358e;
  display: flex;
  align-items: flex-start;
  font-weight: 500;
  border-bottom: thin solid $toc-background-color_3;
  padding: 0.25rem 0.5rem;
  min-height: 2.4444444444rem;
  margin-bottom: 0;
  padding-top: 4px;
  padding-bottom: 4px;
  span.material-icons {
    cursor: pointer;
  }
}
  .panels-tab.contact-info aside.thin section p a{
  text-decoration: none;
}

  .hide-back-to-top{
  display: none;
}

  aside.thin{
    #search-filters{
      .accordion{
      .panelnav {
        &-item {
          display: flex;
          align-items: center;
          font-size: 0.8888888889rem;
          font-weight: 500;
          padding: 0.5555555556rem 0.6666666667rem;

          &.active {
            background-color: $neutral-100;
            color: $neutral-1000;
          }
        }
      }
    }
  }
}
