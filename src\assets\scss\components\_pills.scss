.pills {
    a, button {
        font-size: .9rem;
        padding: .25rem .5rem;
        margin: .25rem;
        border-radius: 1.5rem;
        // Internal changes
        // white-space: nowrap;
        // display: inline-block;
        &:hover {
            color: $default-link-hover;
            border-color: $border-dk-blue;
        }
    }
    &.removable {
         display: flex;
         flex-wrap: wrap;
        strong {
            color: $body-text;
            font-size: .95rem;
            font-weight: 400;
            padding: .25rem .5rem;
            margin: .25rem;
            border-radius: 1.5rem;
            border: thin solid $border-md-grey;
            /* white-space: nowrap; */
            display: flex;
            a {
                padding: 0 .25rem 0 1rem;
                margin: 0;
                span.material-icons {
                    vertical-align: middle;
                    font-size: 1rem;
                }

                &.clear-all {
                    padding-left: 2rem;
                }
            }

        }
    }
}