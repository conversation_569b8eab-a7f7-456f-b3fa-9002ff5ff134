$padding: 1.7778rem;
$max-width: 42rem;
$min-height: 30rem;
$background-lt-white: #ffffff;

.reimagine {
  .padding-left {
    padding-left: $padding !important;
    padding-right: 0 !important;
  }

  .padding-right {
    padding-right: $padding !important;
    padding-left: 0 !important;
  }

  .microsite-img-width {
    width: 50% !important;
    max-width: $max-width;
  }

  &.major-content.background-lt-white.rich-text-component {
    padding: 0 !important;
  }

  .microsite {
    flex-wrap: wrap;
  }

  .microsite-header {
    background-image: url("https://core.verisk.com/-/media/microsite/HeaderImage.jpg");
    background-repeat: no-repeat;
    background-size: 100%;
    min-height: $min-height;
    position: relative;
    width: 100%;

    &::after {
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0) 0%,
        $background-lt-white 63%
      );
      bottom: 0;
      content: "";
      height: 200px;
      position: absolute;
      width: 100%;
    }
  }

  .microsite-header .content-wrapper {
    margin: 6.6667rem auto 0 !important;
  }

  .microsite .content-wrapper {
    border-radius: 0.6667rem;
    display: flex;
    margin: 0 auto;
    flex-wrap: wrap;
    padding: $padding !important;
    box-shadow: 0px -30px 20px rgba(0, 0, 0, 0.05);
    max-width: calc(100% - 17rem) !important;
    position: relative;
    z-index: 1;
  }

  .microsite-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding-right: $padding;

    a.primary {
      align-self: flex-start;
      margin-top: 1.3333rem;
      font-weight: 500;
    }

    h2 {
      border: 0 !important;
      font-size: 2.222rem;
      line-height: 1.25;
      margin-bottom: 1.3333rem;

      .highlight {
        color: #00358e;
        font-size: 2.222rem !important;
      }
    }
  }

  .marketing-banner {
    padding: $padding 0;
    width: 100%;

    .content-wrapper {
      background-color: transparent;
      margin-top: 0 !important;
    }

    h2,
    p {
      background-color: transparent !important;
      color: white !important;
    }
  }

  .roadmap .content-wrapper {
    margin-top: $padding;
    margin-bottom: $padding;
    box-shadow: none !important;
  }

  .roadmap .microsite-content {
    padding-left: $padding;
  }

  @media (max-width: 1081px) {
    .microsite .content-wrapper {
      flex-direction: column;
      max-width: 100% !important;
    }

    .microsite-header .microsite-content,
    .marketing-banner .microsite-content,
    .roadmap .microsite-content {
      padding: 0;
      order: 1;
    }

    .microsite-img-width {
      margin-bottom: $padding;
      width: 100% !important;
      max-width: 100%;
      order: 0;
    }

    .padding-left,
    .padding-right {
      padding: 0 !important;
    }
  }
}
