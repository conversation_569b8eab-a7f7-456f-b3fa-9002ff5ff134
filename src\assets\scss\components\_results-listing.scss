.results-listing {
    .cards {
        flex-direction: column;
        gap: 0;
        margin-left: 0;
        .card {
            border-bottom: thin solid $border-lt-grey;
            margin-bottom: 0.25rem;
            padding-left: 0;
            a {
                flex-direction: row;
                flex-wrap: nowrap;
                display: flex;
                gap: 1rem;
                // Internal changes
                span.material-icons {
                    font-size: .9rem;
                }
                p {
                    color: $body-text;
                    font-size: .95rem;
                    margin: 0;
                    padding: .25rem 0;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;  
                    overflow: hidden;
                }
            }
            img {
                height: 7.5rem;
                width: 7.5rem;
                object-fit: cover;
                order: unset;
            }
            small {
                color: $lt-body-text;
                span + span {
                    padding-left: .5rem;
                    margin-left: .5rem;
                    border-left: thin solid;
                }
            }
            strong {
                padding: 0;
                font-size: 1rem;
                b {
                    color: $body-text;
                }
            }
            &:hover {
                box-shadow: unset;
            }
        }
    }

    .results-table {
        
        &.scrollable {
            position: relative;
            width: 100%;
            overflow: auto;
            z-index: 1;
            border: thin solid $border-lt-grey;
            table {
                width: 100%;
                min-width: 1280px;
                border-collapse: separate;
                border-spacing: 0;
                thead th {
                    position: -webkit-sticky;
                    position: sticky;
                    top: 0;
                }
                th:first-child {
                    position: -webkit-sticky;
                    position: sticky;
                    width: 5rem;
                    left: 0;
                    z-index: 2; 
                }
                td:nth-of-type(1) {
                    position: -webkit-sticky;
                    position: sticky;
                    background-color: $white;
                    left: 5rem;
                    z-index: 2;
                    box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
                }
                tbody th {
                    border-bottom: thin solid $border-lt-grey;
                    &:not(.active-sort) {
                        background-color: $white;
                    }
                    
                }
                thead th:first-child {
                    z-index: 5;
                }
                thead th:nth-of-type(2) {
                    z-index: 5;
                    left: 5rem;
                    box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
                    @media (max-width: 50rem) {
                        width: 5rem;
                    }
                }
                tbody {
                    .active-sort {
                        background-color: $background-lt-grey-2;
                    }
                }
            }
            &::-webkit-scrollbar {
                -webkit-appearance: none;
            }
            &::-webkit-scrollbar-thumb {
                border-radius: .25rem;
                background-color: rgba(0,0,0,.2);
                box-shadow: 0 0 .05rem rgba(255,255,255,.1);
            }
        }
    }

    .page-results-wrapper {
        padding-top: 2rem;
        display: flex;
        align-items: center;
        font-size: .9rem;
        flex-wrap: wrap;
        select {
            padding: .25rem 1rem .25rem .5rem;
        }
        nav {
            margin-left: auto;
            ul {
                display: flex;
                align-items: center;
                li {
                    list-style: none;
                    margin: 0 .5rem;
                    &.page-number {
                        a.page-link {
                            padding:0 .5rem;
                            background-color: $white;
                            border: thin solid $default-link;
                            &:hover {
                                background-color: $default-link;
                                color: $white;
                            }

                            &.active {
                                background-color: $default-link-hover;
                                color: $inverse-link;
                                pointer-events: none;
                            }
                        }
                        
                    }

                    /* a.disabled {
                        span {
                            color: $lt-body-text;
                            pointer-events:none;
                        }  
                    } */
                }
            }
        }
        .select-wrapper {
            display: inline-block;
        }
        .select-wrapper:after {
            top: 0.3rem;
            right: 0.3rem;
        }
    }
}