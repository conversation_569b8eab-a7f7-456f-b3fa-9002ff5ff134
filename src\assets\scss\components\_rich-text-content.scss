.major-content.rich-text-component {
  .site {
    .table-wrapper {
      position: relative;
      width: 100%;
      overflow: auto;
      z-index: 1;
      border: thin solid $border-lt-grey;
      table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        th,
        td {
          min-width: 5rem;
          background-color: $white;
          border-bottom: thin solid $border-lt-grey;
        }
        th:first-child,
        td:first-child {
          position: -webkit-sticky;
          position: sticky;
          min-width: 5rem;
          left: 0;
          z-index: 2;
          background-color: $white;
          box-shadow: 5px 0 5px -5px rgba(0, 0, 0, 0.1);
          border-bottom: thin solid $border-lt-grey;
        }
        thead th {
          background-color: $background-lt-grey;
          text-wrap: nowrap;
          &:first-child {
            background-color: $background-lt-grey;
          }
        }
        tbody th {
          background-color: $background-lt-grey-2;
          text-wrap: nowrap;
          &:first-child {
            background-color: $background-lt-grey-2;
          }
        }
      }
      &::-webkit-scrollbar {
        -webkit-appearance: none;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 0.25rem;
        background-color: rgba(0, 0, 0, 0.2);
        box-shadow: 0 0 0.05rem rgba(255, 255, 255, 0.1);
      }
    }
  }
}
