.commercial-lines {
  padding-top: 0;
  width: 100%;
  max-width: 110rem;

  .site.site.flex-wrapper .container {
    width: 100%;
  }

  .container-header {
    h2 {
      font-size: 1.5555555556rem;
      margin: 0 0 0.6666666667rem;
    }

    p {
      font-size: 0.8888888889rem;
      margin: 0;
    }
  }

  .table-top-info {
    display: flex;
    justify-content: space-between;
    margin: 0.8888888889rem 0;
    flex-wrap: wrap;

    .legend {
      display: flex;
      align-items: center;
      font-size: 0.7777777778rem;
      font-weight: 500;
      margin-right: 1rem;

      * {
        font-size: 0.7777777778rem;
      }

      a p {
        color: #4d4d4d;
        border-bottom: thin dashed #4d4d4d;
      }

      ul {
        display: inline-flex;
        padding-left: 0;
        margin: 0;
        margin-left: 0.6666666667rem;
        gap: 0.6666666667rem;

        li {
          list-style-type: none;
        }
      }
    }

    button {
      align-items: center;
      font-size: 0.7777777778rem;
      font-weight: 500;
      display: flex;

      span {
        margin-right: 0.4444444444rem;
      }
    }
  }

  .table-container {
    overflow: auto;
    max-width: 100%;

    table {
      border: 1px solid #dbdbdb;
      border-left: 0;
      border-bottom: 0;
      border-right: 0;
      border-spacing: 0;
      min-width: 1200;

      thead {
        position: sticky;
        top: 0;
        z-index: 9999;
      }

      th {
        padding: 0;
      }

      thead tr {
        &:first-child th {
          &:first-child {
            border-right: thin solid #a3a3a3;
            padding: 0.7777777778rem 0.8888888889rem;
            vertical-align: middle;
            font-size: 0.8888888889rem;
          }

          &:not(:first-child) {
            border-right: thin solid #a3a3a3;
            border-bottom: thin solid #dbdbdb;
            text-align: center;
            vertical-align: middle;
            min-width: 160px;
            width: 160px;
          }

          &:last-child {
            border-right: thin solid #dbdbdb;
          }
        }

        &:last-child th {
          border: 1px solid #dbdbdb;
          border-left: 0;
          border-top: 0;
          text-align: center;
          font-size: 0.6666666667rem;
          font-weight: 400;
          vertical-align: middle;
          width: 2.2222222222rem;

          &:first-child {
            border-left: 0;
          }

          &:nth-child(4n) {
            border-right: thin solid #a3a3a3;
          }

          &:nth-child(4n + 1) {
            border-left: 0;
          }

          &:last-child {
            border-right: thin solid #dbdbdb;
          }
        }
      }

      tbody {
        tr {
          &.section-row {
            background-color: #e9e9e9;

            .section-row-title {
              background-color: #e9e9e9;
              font-weight: 500;
              font-size: 0.7777777778rem;
              box-shadow: 0.2777777778rem 0 0.2777777778rem 0 rgba(0, 0, 0, 0.1);
              padding: 0.6666666667rem 0.8888888889rem 0.6666666667rem;
              border-left: thin solid #dbdbdb;
            }

            td {
              border-right: thin solid #a3a3a3;

              &:last-child {
                border-right: thin solid #dbdbdb;
              }
            }
          }

          .impact {
            border-radius: 0.8888888889rem;
            height: 1.7777777778rem;
            display: flex;
            align-items: center;
            position: absolute;
            top: 50%;
            margin-top: -0.8888888889rem;
            font-size: 0.7777777778rem;
            background-color: #e6ebf4;
            padding: 0 0.5555555556rem;
            font-weight: 500;
          }

          .impact-start {
            position: relative;
          }

          .impact-prior .impact {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }

          .impact-after .impact {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }

          .impact {
            .has-tooltip * {
              font-size: 0.6666666667rem;
              color: #4d4d4d;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .indicator {
              border-radius: 100%;
              height: 0.6666666667rem;
              width: 0.6666666667rem;
              margin-left: 0.5555555556rem;
              margin-right: 0.3333333333rem;
            }
          }

          &:first-child .section-row-title {
            border-top: thin solid #dbdbdb;
          }

          @for $i from 1 through 16 {
            .timeline-bar-width-#{$i} {
              width: calc(#{$i} * 90px - 16px);
            }
          }
        }

        td {
          border: 1px solid #dbdbdb;
          border-left: 0;
          border-top: 0;
          vertical-align: middle;
          width: 5rem;
          min-width: 5rem;

          &:first-child {
            padding: 0;
            border-right: thin solid #a3a3a3;
            position: sticky;
            left: 0;
            z-index: 999;

            &:not(.section-row-title) {
              background-color: #ffffff;
            }
          }

          &:not(:first-child) {
            padding: 0;
            padding-left: 0.4444444444rem;
            padding-right: 0.4444444444rem;
          }

          &:nth-child(5),
          &:nth-child(9),
          &:nth-child(13) {
            border-right: thin solid #a3a3a3;
          }

          &:nth-child(5n-1) {
            border-left: 0;
          }

          .row-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-between;
            padding: 0.6666666667rem 0.8888888889rem 0.6666666667rem;
            width: 100%;

            .badge {
              font-size: 0.6666666667rem;
              border: thin solid #dbdbdb;
              font-weight: 500;
              margin: 0.4444444444rem 0;
            }

            a {
              font-size: 0.7777777778rem;
              font-weight: 500;
              margin: 0;
              width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;

              &.has-tooltip {
                width: unset;
                margin-right: 0;
              }
            }

            span {
              font-size: 0.7777777778rem;
              font-weight: 500;
              margin: 0;
              width: -moz-fit-content;
              width: fit-content;
              overflow: hidden;
              margin-right: 0.6666666667rem;
              text-overflow: ellipsis;
            }

            .subscription {
              display: inline-flex;
              align-items: center;
              border: 0;
              padding-left: 0;
              padding-right: 0;

              p {
                color: #4d4d4d;
                font-weight: 500;
                font-size: 0.6666666667rem;
                margin: 0;
              }

              .commercial-lines .table-container table tbody td .row-item span span {
                color: #4d4d4d;
                font-size: 0.8888888889rem;
                margin-right: 0.2222222222rem;
              }
            }
          }
        }
      }

      .row-label {
        border-left: thin solid #dbdbdb;
        position: sticky;
        left: 0;
        z-index: 999;
        max-width: 11.1111111111rem;
        box-shadow: 0.2777777778rem 0 0.2777777778rem 0 rgba(0, 0, 0, 0.1);
      }
    }
  }

  .has-tooltip {
    display: inline-block;
    width: unset;
    overflow: hidden;

    p {
      border-bottom: thin dashed #4d4d4d;
    }
  }
}

.info-banner.flex-wrapper {
  background-color: rgba(42, 125, 225, 0.15);
  border-radius: 0.25rem;
  border-bottom: none;
  text-align: left;
  padding: 1.25rem 1.5rem 1.5rem;
  margin-bottom: 2rem;
}

main.lob .info-banner {
  margin-bottom: 0;
  flex-direction: column;
  align-items: flex-start;

  h3 {
    font-size: 1.1111111111rem;
    margin-bottom: 0.4444444444rem;
  }

  p {
    font-size: 0.8888888889rem;
    margin-top: 0;
    margin-bottom: 0.4444444444rem;
  }

  ul {
    font-size: 0.8888888889rem;
    padding-left: 0.4444444444rem;
    list-style-position: inside;
    margin: 0 0 0.4444444444rem;
  }
}

@media (min-width: 113.5625rem) {
  .commercial-lines .table-container table tbody tr.section-row .section-row-title {
    box-shadow: none;
  }
}

@media (min-width: 48rem) {
  .commercial-lines .table-container table tbody td .row-item .badge {
    font-size: 0.7777777778rem;
  }
}

@media (min-width: 67.5625rem) {
  .commercial-lines .table-container table tbody td .row-item {
    a {
      white-space: nowrap;
      font-size: 0.8888888889rem;
      width: 75%;
      margin-right: 0.4444444444rem;
    }

    span {
      white-space: nowrap;
      font-size: 0.8888888889rem;
    }
  }
}

@media (min-width: 48rem) {
  .commercial-lines .table-container table tbody td .row-item .subscription p {
    font-size: 0.7777777778rem;
  }
}

@media (min-width: 113.5625rem) {
  .commercial-lines .table-container table .row-label {
    box-shadow: none;
  }
}

@media (min-width: 37.5rem) {
  .commercial-lines .table-container table .row-label {
    min-width: 16.6666666667rem;
  }
}

.tippy-box {
  &[data-theme~="verisk"] {
    background: #ffffff;
    color: #3f3f3f;
    box-shadow: 0.0555555556rem 0 0.8888888889rem rgba(0, 0, 0, 0.1);
    padding: 0.25rem 1rem;

    &[data-placement^="top"]>.tippy-arrow::before {
      border-top-color: white;
    }

    &[data-placement^="bottom"]>.tippy-arrow::before {
      border-bottom-color: white;
    }

    &[data-placement^="left"]>.tippy-arrow::before {
      border-left-color: white;
    }

    &[data-placement^="right"]>.tippy-arrow::before {
      border-right-color: white;
    }
  }

  &[data-theme~="verisk-light"] {
    border-radius: 0.6666666667rem;
    background: #ffffff;
    color: #3f3f3f;
    box-shadow: 0 0.2222222222rem 0.6666666667rem 0 rgba(0, 0, 0, 0.25);
    padding: 1.3333333333rem;

    &[data-placement^="top"]>.tippy-arrow::before {
      border-top-color: white;
    }

    &[data-placement^="bottom"]>.tippy-arrow::before {
      border-bottom-color: white;
    }

    &[data-placement^="left"]>.tippy-arrow::before {
      border-left-color: white;
    }

    &[data-placement^="right"]>.tippy-arrow::before {
      border-right-color: white;
    }

    .tippy-content {
      padding: 0;
    }
  }

  &[data-theme~="paas-genai"] {
    background: #e6ebf4;
    color: #1a1a1a;
    box-shadow: none;
    padding: 0.2777777778rem 0.5rem;
    border-radius: 0.2777777778rem;

    &[data-placement^="top"]>.tippy-arrow::before {
      border-top-color: #e6ebf4;
    }

    &[data-placement^="bottom"]>.tippy-arrow::before {
      border-bottom-color: #e6ebf4;
    }

    &[data-placement^="left"]>.tippy-arrow::before {
      border-left-color: #e6ebf4;
    }

    &[data-placement^="right"]>.tippy-arrow::before {
      border-right-color: #e6ebf4;
    }

    .tippy-content {
      padding: 0;

      ul {
        margin: 0rem;
        padding-left: 1rem;

        li {
          font-size: 0.85rem;
        }
      }

      strong,
      span {
        display: block;
        font-size: 0.85rem;
      }
    }
  }

  .tippy-content {
    padding: 0.5rem 0.75rem;

    ul {
      margin: 0rem;
      padding-left: 1rem;

      li {
        font-size: 0.85rem;
      }
    }

    strong,
    span {
      display: block;
      font-size: 0.85rem;
    }
  }

  &[data-theme~="verisk-light"] .tippy-content {
    font-size: 0.7777777778rem;

    .tooltip-content {
      display: flex;
      flex-direction: column;
    }

    h3 {
      font-size: 0.8888888889rem;
      margin: 0;
      margin-bottom: 0.4444444444rem;
    }

    .impact-tooltip-label {
      font-size: 0.7777777778rem;
      font-weight: 500;
    }

    .impact-tooltip-text {
      font-size: 0.7777777778rem;
    }

    div {
      display: inline-flex;
    }

    p {
      font-size: 0.7777777778rem;
    }
  }
}