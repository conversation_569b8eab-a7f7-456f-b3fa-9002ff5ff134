section {
    padding: 1.875rem;
    background-color: $white;
    // Internal changes
    &.background-lt-grey {
        background-color: $background-lt-grey;
    }
    &.background-blue {
        background-color: $background-blue;
        color: $white;
        h2 {
            color: $white;
        }
    }
    &.background-dk-blue {
        background-color: $background-dk-blue;
        color: $white;
        h2 {
            color: $white;
        }
    }

    .flex-wrapper {
        &.with-cta {
            p {  // Internal changes
                width: 100%;
            }
            .call-to-action {
                text-align: right;
            }
        }
        .messaging + img {
            margin-left: auto;
            max-width: 40%;
            object-fit: cover;
            /* flex-grow: 2; */
        }
    }
}