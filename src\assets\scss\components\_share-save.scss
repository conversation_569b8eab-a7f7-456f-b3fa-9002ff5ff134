.share-save {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
    justify-content: flex-start;
    position: relative;

    div.popup {
        display: none;
    }

    a {

        &.secondary,
        &.tertiary {
            padding: 0.25rem 0.5rem;
            font-size: .85rem;
            width: 5rem;
            gap: 0.2rem;
            display: flex;
        }

        span.material-icons {
            font-size: 1.2rem;
            vertical-align: middle;
        }

        &#share.sharing,
        &#bookmark.saved {
            &+div.popup {
                display: block;
                position: absolute;
                overflow: visible;
                color: $body-text;
                z-index: 2;
                background-color: $white;
                border-radius: .25rem;
                box-shadow: 0 0 0.75rem 0 rgba(0, 0, 0, 0.2);
                padding: 0.5rem 1rem;

                a {
                    font-size: .85rem;

                    img,
                    i {
                        width: .85rem;
                        vertical-align: middle;
                        margin-right: 0.75rem;
                        opacity: .8;
                        display: inline-block;
                    }

                    &.close {
                        position: absolute;
                        right: 0.25rem;
                        top: 0;

                        span.material-icons {
                            font-size: 1rem;
                        }
                    }
                }

                p {
                    font-size: .85rem;
                    margin: .25rem;
                }

                &::before {
                    content: '';
                    position: absolute;
                    width: 0;
                    height: 0;
                    bottom: 100%;
                    left: 1.5em; // offset should move with padding of parent
                    border: .75rem solid transparent;
                    border-top: none;
                    border-bottom-color: #fff;
                    filter: drop-shadow(0 -0.0625rem 0.0625rem rgba(0, 0, 0, 0.1));
                }

                &.sharing {
                    padding: 1.25rem 1.25rem .5rem;
                    top: 2.75rem;

                    a {
                        display: block;
                        line-height: 2.5;

                        &.close {
                            line-height: 1.5;
                        }
                    }
                }

                &.saved {
                    top: 2.75rem;
                    right: -2rem;
                }
            }
        }

        &#bookmark {
            span.saved {
                display: none;
            }

            &.saved {
                span.save {
                    display: none;
                }

                span.saved {
                    display: inline-flex;
                }
            }
        }
    }
}