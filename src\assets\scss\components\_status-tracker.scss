.status-tracker-container {
  overflow-x: auto;

  .status-tracker {
    position: relative;
    display: flex;
    margin: 1.25rem 0;
    @media (max-width: 48rem) {
      min-width: 33.33333rem;
    }
    @media (min-width: 50.0625rem) and (max-width: 55.37rem) {
      max-width: 350px;
    }
    .step {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: auto;
      flex: 1;

      &::before {
        position: absolute;
        content: "";
        border-bottom: 0.188rem solid $border-lt-grey;
        width: 100%;
        top: 0.5rem;
        left: 0;
        z-index: 2;
      }
      &::after {
        position: absolute;
        content: "";
        width: 100%;
        top: 0.5rem;
        left: 0;
        z-index: 2;
      }
      .step-counter {
        position: relative;
        z-index: 5;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 50%;
        background: $white;
        border: thin solid $border-md-grey;
        margin-bottom: 0.375rem;
      }
      .step-name {
        font-size: 0.9rem;
        font-weight: 700;
        margin-top: 0.5rem;
        text-align: center;
        padding: 0 5px;
      }
      &.completed {
        .step-counter {
          border: 2px solid $body-text;
          background-color: $body-text;
          .material-icons {
            color: $white;
            font-size: 0.95rem;
            font-weight: 700;
          }
        }
        .step-dt {
          color: $the6s;
          display: block;
          font-size: 0.8rem;
          font-weight: 500;
        }
        &::before {
          border-bottom: none;
        }
        &::after {
          position: absolute;
          content: "";
          border-bottom: 0.188rem solid $body-text;
          width: 100%;
          top: 0.5rem;
          left: 0;
          z-index: 3;
        }
      }
      &:first-of-type {
        align-items: flex-start;
        .step-name {
          text-align: left;
        }
      }
      &:last-of-type {
        align-items: flex-end;
        .step-name {
          text-align: right;
        }
      }
    }
  }
}
