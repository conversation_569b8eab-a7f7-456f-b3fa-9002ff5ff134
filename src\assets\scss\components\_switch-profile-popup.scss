.modal-container-loader {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  opacity: 0.95;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-container-switch-popup {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: $black-opacity-80;
  z-index: 4;
  display: flex;
  &.media-model {
    background-color: #000000e0;
  }
  .profile-modal {
    background-color: $white;
    width: 50%;
    position: absolute;
    border-radius: 0.25rem;
    margin-top: 5%;
    margin-left: auto;
    margin-right: auto;
    left: 50%;
    max-height: 90vh; /* 80% of the viewport height */
    @media only screen and (max-width: 62.5rem) {
      max-height: 75vh; /* 75% of the viewport height */
    }

    overflow-y: auto;
    transform: translateX(-50%);
    h2 {
      font-size: 1rem;
      line-height: 1rem;
      margin: 0;
    }
    .profile-content {
      margin: 0;
      .link-list {
        margin: 0;
        padding: 0;
        li {
          padding: 0.25rem 0 0.25rem 1rem;
          border-bottom: thin solid #eee;
          .heading {
            font-weight: 600;
            padding: 0.75rem 0;
            margin: 0;
          }
          .description {
            font-size: 0.75rem;
            padding: 0.05rem 0;

            margin: 0;
          }
        }
        .profile {
          color: black;
          font-size: 0.75rem;

          .profile-link {
            color: inherit;
            font-size: inherit;
            font-family: inherit;
            padding: 0;
            text-align: start;
            width: 100%;
            height: 100%;
          }
        }
        .choosen-profile {
          color: black;
          font-size: 0.75rem;
          background-color: #dddddd;
          font-weight: 600;
          .profile-link {
            color: inherit;
            font-size: inherit;
            font-family: inherit;
            padding: 0;
            font-weight: inherit;
            text-align: start;
            width: 100%;
            height: 100%;
          }
        }
      }
      .scrollable-list {
        max-height: 19rem;
        overflow-y: auto;
        @media only screen and (max-width: 62.5rem) {
          max-height: 15rem;
        }
      }
    }

    .call-to-action {
      padding: 1rem 1rem 1rem 0;
      text-align: right;

      a,
      button {
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.5rem 1rem;
        &.primary {
          margin-left: 0.25rem;
        }
        &.tertiary {
          border: thin solid $black;
          color: $black;
        }
        &.tertiary:hover {
          background-color: $black;
          color: $white;
        }
      }
    }
  }
  &.with-page-loader {
    background-color: rgba($color: #fff, $alpha: 0.95);
    .loader {
      position: absolute;
      top: 40%;
    }
  }
}
