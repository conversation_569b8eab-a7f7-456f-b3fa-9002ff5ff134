.switch {
  position: relative;
  display: inline-block;
  height: 1.25rem;
  input {
    &:checked {
      + {
        .slider {
          background-color: #00358e;
          &:before {
            transform: translateX(1.25rem);
            content: '\e5ca';
            font-size: rem(18);
            font-family: 'Material Symbols Outlined';
          }
        }
      }
    }
  }

  .slider {
    position: absolute;
    width: 2.5rem;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #a3a3a3;
    transition: 0.4s;
    border-radius: 1.25rem;
    &:before {
      position: absolute;
      content: '';
      height: 1rem;
      width: 1rem;
      left: 0.125rem;
      bottom: 0.125rem;
      background-color: #ffffff;
      transition: 0.4s;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Material Icons';
    }
  }
}
