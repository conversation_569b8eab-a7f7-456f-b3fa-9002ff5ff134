Section.tabs-content {
  padding: 1.875rem;
  .tabs-description{
    padding-bottom: 1.5rem;
  }
  .tabs {
    width: 100%;

    &.responsive-tabs {
      display: flex;
      position: relative;
      border-bottom: thin dotted #bbbbbb;

      .tabbed {
        display: flex;
        inline-size: auto;
        overflow-x: hidden;
        scroll-behavior: smooth;
        scrollbar-width: none;
        will-change: scroll-position;
        padding: 1.5rem 0 0 0;
        gap: 0.5rem;
        border-bottom: 0;

        align-items: flex-end;
        margin-bottom: -2px;

        nav {
          padding: 1.5rem 0 0 0;
          display: flex;
          gap: 0.5rem;

          .tab-container {
            display: flex;
            a:focus {
              margin: 0.125rem 0;
              &.active {
                margin: -0.00005rem 0;
              }
            }
          }

          .tab {
            flex-grow: unset;
            padding: 1rem 1.5rem;
            background-color: #f7fbfe;
            font-size: 0.9rem;
            font-weight: 700;
            border-radius: 0.25rem;
            text-align: center;
            // flex-grow: 1;
            white-space: nowrap;
            // border-bottom: thin dotted #bbbbbb;
            margin: 0.125rem 0;
          }

          .tab.active {
            background-color: white;
            border: thin solid #dddddd;
            border-radius: 0;
            border-top: 0.2rem solid;
            border-bottom: none;
            color: #3f3f3f;
            margin: -0.00005rem 0;
          }
        }
      }
    }

    .nav-wrapper {
      width: 100%;
    }

    .prev.hidden {
      display: none;
    }

    .prev {
      left: 0;
      position: absolute;
      padding: 0.75rem 1rem;
      border: 1px solid #00358e;
      background-color: #ffffff;
      color: #00358e;
      border-radius: 0.25rem;
      vertical-align: middle;
      bottom: 0.125rem;
      span {
        font-weight: 600;
      }
      font-size: 0.9rem;
    }
    .next.hidden {
      display: none;
    }

    .next {
      right: 0;
      padding: 0.75rem 1rem;
      background-color: #ffffff;
      border: 1px solid #00358e;
      color: #00358e;
      border-radius: 0.25rem;
      vertical-align: middle;
      position: absolute;
      bottom: 0.125rem;
      span {
        font-weight: 600;
      }
    }
  }
}
button.tabNav.next,
button.tabNav.prev {
  &:hover {
    background-color: #edf2fa;
  }
}

// If tabsbase is used within the page and not at top, then vertical padding from top is removed ISOALY-4016
main > section.tabs-content:nth-child(n + 2) {
  padding-top: 1.875rem;
  .tabs.responsive-tabs {
    .tabbed {
      padding: 0;
      nav {
        padding: 0;
      }
    }
  }
}
.left-rail-section {
  section.tabs-content {
    padding: 0 0 1.875rem;
    .tabs.responsive-tabs {
      .tabbed {
        padding: 0;
        nav {
          padding: 0;
        }
      }
    }
  }
}

.tab-container {
  overflow: auto;
  gap: 0.5rem;
  scroll-behavior: smooth;
  scrollbar-width: none;
  will-change: scroll-position;
}

.tab-content {
  section.events {
    div.call-to-action {
      text-align: center;
    }
  }
  .tab-item{
    .references{
      padding-bottom: 0px;
      *{
        font-size: 0.9rem;
      }
    }
  }
  .major-content.rich-text-component{
    padding-bottom: 0px;
    h2{
      font-size: 1.7rem;
    }
  }
  .contact-team{
    margin-top: 1.875rem;
  }
}

.left-rail-section {
  .tabs-content:first-of-type .title {
    margin-top: 0px;
  }
  &.content-wrapper {
    .tab-content {
      .tab-item {
        section.events {
          @media (max-width: 67.5rem) {
            &:not(.explore-insights):not(.hot-topics):not(.carousel) {
              .cards {
                flex-wrap: wrap;
                justify-content: flex-start;

                &.min-width-cards {
                  flex-direction: column;

                  .card {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    min-width: unset;
                    max-width: unset;
                    padding-right: 0;

                    img {
                      width: 15rem;
                    }

                    h3 {
                      flex-grow: 2;
                    }
                  }
                }
              }
            }
          }
          @media (max-width: 50rem) {
            &:not(.explore-insights):not(.hot-topics):not(.carousel) {
              .cards {
                margin-left: 0;
                .card {
                  width: 100%;
                  padding: 1rem 0;
                  img {
                    height: 10rem;
                  }
                }
                .call-to-action {
                  margin-bottom: 2rem;
                }
              }
            }
          }

          .card {
            &.four {
              width: 50%;
            }
          }
        }
      }
    }
  }
}
.tab-content .tab-item:last-child .references {
  padding-bottom: 1.875rem;
}
