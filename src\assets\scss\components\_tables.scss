table {
    width: 100%;
    font-size: .9rem;
    tr {
        vertical-align: text-top;
    }
    th {
        text-align: left;
        font-weight: 500;
        padding: .5rem;
        background-color: $background-lt-grey;
        span.material-icons {
            vertical-align: middle;
            font-size: .85rem;
        }
        &[aria-sort] {
            background-color: darken($background-lt-grey, 2%);
        }
    }
    td {
        padding: 1rem .5rem;
        border-bottom: thin solid $border-lt-grey;
        span.material-icons {
            font-size: 1.2rem;
            vertical-align: middle;
        }
        &.active-sort {
            background-color: $background-lt-grey-2;
        }
    }
    &.library-results {
        th {
            &.title {
                width: 50%;
                input {
                    width: 100%;
                }
            }
            &.action {
                width: 5rem;
            }
        }
    }
}