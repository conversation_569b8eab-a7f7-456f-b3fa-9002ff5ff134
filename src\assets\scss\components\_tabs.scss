.tabs {
    width: 100%;
    small {
        display: block;
        font-size: .75rem;
        font-weight: 400;
        color: $body-text;
    }
    .nav-wrapper {
        width: 100%;
		// display: flex;
		// align-items: flex-end;
    }
    .tabbed {
        border-bottom: thin dotted $border-md-grey;
        align-items: flex-end;
        nav {
            padding: 1.5rem 0 0 0;
            display: flex;
            gap: 0.5rem;
            .tab {
                padding: 1rem 1.5rem;
                background-color: $background-lt-blue;
                font-size: .9rem;
                font-weight: 700;
                border-radius: .25rem;
                text-align: center;
                flex-grow: 1;
                &.active {
                    background-color: white;
                    border: thin solid $theDs;
                    border-radius: 0;
                    border-top: 0.2rem solid;
                    border-bottom: none;
                    color: $body-text;
                    margin-bottom: -0.1rem;
                }
            }
        }
        &.no-dotted-bottom-border {
            border-bottom: none;
        }
        &.dynamic-tab {
            overflow-x: hidden; /*hide the radio inputs */
            margin: 1rem 0 4rem;
            border-bottom: none;
            label {
                padding: 1rem .2rem;
                font-size: .9rem;
                cursor: pointer;
                color: $default-link;
                margin: 0 2rem 0 0;
                display: inline-block;
                &:has(input:checked){
                    color: $black;
                    font-weight: 700;
                    border-bottom: medium solid $black;
                }
            }
            label:nth-of-type(1):has(input:checked) ~ .sub-tab-content:nth-of-type(1),
            label:nth-of-type(2):has(input:checked) ~ .sub-tab-content:nth-of-type(2),
            label:nth-of-type(3):has(input:checked) ~ .sub-tab-content:nth-of-type(3) {
                display: block;
            }
            .sub-tab-content {
                display: none;
            }
            @media (max-width: 27rem) {
                label {
                    margin: 0;
                }
            }
        }
    }
    .tabbed [type="radio"] {
        /* hiding the inputs */
        display: none;
    }
    &.underline {
        .tabbed {
            border-bottom: none;
            nav {
                .tab.active {
                    border: none;
                    border-bottom: .2rem solid;
                }
            }
        }
    }
    &.no-background {
        .tabbed {
            nav {
                .tab {
                    background-color: transparent;
                }
            }
        }
    }
}