.timeline {
    margin-top: 2rem;
    .flex-wrapper {
        align-items: flex-start;
        justify-content: stretch;

        time {
            font-size: .9rem;
            font-weight: 700;
            line-height: 1;
            width: 8rem;
            text-align: right;
            margin-right: -0.5rem;
            background-color: white;
            z-index: 1;
            span {
                display: inline-flex;
                position: relative;
                vertical-align: middle;
                z-index: 1;
                height: 1rem;
                width: 1rem;
                background-color: $white;
                border: thin solid $border-md-grey;
                border-radius: 50%;
                margin-left: .5rem;
                &.current {
                    background-color: $black;
                    border-color: $black;
                }
            }
        }

        div {
            border-left: thin solid $theBs;
            padding: 0 0 1rem 1rem;
            width: calc(100% - 8rem);
            h3 {
                font-weight: 400;
                line-height: 1;
                margin: 0;
            }
            p {
                margin: .5rem 0;
                &:last-of-type {
                    margin-bottom: 1rem;
                }
            }
        }
    } 
}