.toast-example {
  display: flex;
  justify-content: center;
  height: 100vh;
  .notifications {
    position: fixed;
    top: 13.5rem;
    right: 1.25rem;
    li {
      background-color: $white;
    }
    .toast {
      width: 28rem;
      position: relative;
      display: flex;
      gap: 1rem;
      overflow: hidden;
      list-style: none;
      padding: 1rem 1.0625rem;
      margin-bottom: .625rem;
      box-shadow: 0 .1rem .2rem 0 $border-md-grey;
      justify-content: space-between;
      animation: show_toast 0.3s ease forwards;
      .material-icons {
        align-self: flex-start;
      }
      .column {
          display: grid;
          span {
              max-width: 20rem;
          }
      }
      &.success{    
        border: thin solid $green-2;
        border-left-width: .5rem;
        .material-icons {
          color: $green-2;
        }
      }
      &.error {
        border: thin solid $red-1;
        border-left-width: .5rem;
        .material-icons {
          color: $red-1;
        }
      }
      &.warning {
        border: thin solid $background-primary;
        border-left-width: .5rem;
        .material-icons {
          color: $background-primary;
        }
      }
      &.info {
        border: thin solid $background-md-blue-2;
        border-left-width: .5rem;
        .material-icons {
          color: $background-md-blue-2;
        }
      }
      span.material-icons:last-child {
        color: $body-text;
        font-weight: 400;
        cursor: pointer;
        border: thin solid transparent;
      }
      span.material-icons:last-child:hover {
        border-radius: 100%;
        border: thin solid $border-md-grey;
      }
    }
  }
  .notifications :where(.toast, .column) {
    align-items: normal;
  }
  @keyframes show_toast {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(0%);
    }
  }
  .notifications .toast.hide {
    animation: hide_toast 0.3s ease forwards;
  }
  @keyframes hide_toast {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(calc(100% + 25px));
    }
  }
  .buttons{
    .btn {
      box-shadow: 0 0.1rem 0.2rem 0 $border-md-grey;
    }
  }
  .call-to-action {
    text-align: left;
  }
  @media (max-width: 67.5rem) {
    & {
      align-items: center;
    }
  }
  @media screen and (max-width: 45rem) {
    .notifications {
      width: 95%;
      .toast {
        width: 100%;
        font-size: 1rem;
        margin-left: 1.25rem;
      }
    }
    .buttons {
      .btn {
        margin: .5rem 0 .0625rem;
        padding: .5rem .9375rem;
      }
    }
    .call-to-action {
      text-align: center;
    }
  }
}
