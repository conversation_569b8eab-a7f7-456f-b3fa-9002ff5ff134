@function px-to-rem($px) {
  $rem-value: calc($px / 18);
  @return calc(round($rem-value * 1000) / 1000) * 1rem;
}

@mixin flex-colum-wise-gap($gap) {
  display: flex;
  flex-direction: column;
  gap: px-to-rem($gap);
}

.toc-content {
  @include flex-colum-wise-gap(0);
  padding-top: px-to-rem(24);
  color: #4d4d4d;
  * {
    margin: 0;
  }
  // .digestContent{
  //   padding-bottom: 2rem;
  //   border-bottom: 0.1111111111rem solid #dbdbdb;
  // }

  h1,
  h1 > span {
    font-size: px-to-rem(28);
    font-weight: 500;
    line-height: px-to-rem(35);
    text-align: left;
    color: #1a1a1a;
  }
  span,
  p,
  a {
    font-size: px-to-rem(18);
    font-weight: 400;
    line-height: px-to-rem(28);
    text-align: left;
  }
  ul {
    @include flex-colum-wise-gap(0);
  }

  blockquote {
    @include flex-colum-wise-gap(10);
    padding: px-to-rem(24);
    border-radius: px-to-rem(4);
    border: 2px solid #dbdbdb;
    background: #f4f4f4;
    font-size: px-to-rem(16);
    line-height: px-to-rem(24);
    text-align: left;

    li {
      font-weight: 700;
    }
    li span {
      font-weight: 400;
    }
  }

  //every panel item under it:
  .digest {
    @include flex-colum-wise-gap(24);
    border-bottom: 2px solid #dbdbdb;
    padding-bottom: px-to-rem(48);

    //content will be from BE
    .digestContent {
      @include flex-colum-wise-gap(16);
      h3,
      h3 > span {
        font-size: px-to-rem(20);
        font-weight: 500;
        line-height: px-to-rem(24);
        text-align: left;
      }
    }
  }

  .panelItem {
    @include flex-colum-wise-gap(0);
    // border-bottom: 2px solid #dbdbdb;
    // padding-bottom: px-to-rem(48);
    &:last-child {
      border-bottom: none;
      padding-bottom: none;
    }
    &.ContactInfo {
      table {
        td {
          border: none;
        }
      }
    }
    .panelItemContent {
      @include flex-colum-wise-gap(10);
      div {
        @include flex-colum-wise-gap(0);
      }
      h2,
      h2 > span {
        font-size: px-to-rem(24);
        font-weight: 500;
        line-height: px-to-rem(32);
        text-align: left;
      }

      h3,
      h3 > span {
        font-size: px-to-rem(18);
        font-weight: 500;
        line-height: px-to-rem(28);
        text-align: left;
      }
      .attachment-link {
        color: #00358e;
        cursor: pointer;
      }
    }
    // .section-header {
    //   display: flex;
    //   align-items: center;
    //   justify-content: space-between;
    //   flex: 1;
    //   margin-bottom: 1.3333333333rem;
    //   flex-wrap: wrap;
    // }
    a.secondary.small {
      display: flex;
      align-items: center;
      font-size: 0.7777777778rem;
      font-weight: 500;
      margin: 0.4444444444rem 0;
      padding: 0.4444444444rem 0.8888888889rem;
      span {
        font-size: 0.8888888889rem;
        margin-right: 0.4444444444rem;
      }
    }
  }
}
