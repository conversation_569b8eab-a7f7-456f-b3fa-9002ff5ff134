.video-container {
    width: 100%;
    max-width: 1200px;
    padding: 16px;
    text-align: left;
    // overflow: hidden; // Prevent overflow on smaller screens

    h2 {
        font-size: 1.5rem;
        margin-bottom: 10px;

        @media (min-width: 768px) {
            font-size: 2rem;
        }

        @media (min-width: 1024px) {
            font-size: 2.5rem;
        }
    }

    p {
        font-size: 1rem;
        color: #666;

        @media (min-width: 768px) {
            font-size: 1.2rem;
        }

        @media (min-width: 1024px) {
            font-size: 1.4rem;
        }
    }

    // Target dynamically generated Kaltura player
    div[id^="kaltura_player_"] {
        width: 100%;
        height: auto;
        aspect-ratio: 16 / 9;
        background: #000;
        display: block;
        // max-width: 100%; // Ensure the player does not exceed the container width

        @media (max-width: 1024px) {
            max-width: 80%;
        }

        @media (max-width: 768px) {
            max-width: 100%;
        }
    }
}