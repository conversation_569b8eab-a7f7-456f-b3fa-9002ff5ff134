// class youtube added to increase specificity on article page. ticket:3683
section.media-container.youtube {
    padding: 3rem 2rem;
    .site {
        .media-player {
            width: 100%;
            aspect-ratio: 16/9;
            iframe {
                display: block;
                width: 100%;
                height: 100%;
            }
        }

        .messaging {
            align-self: center;
			width:100%;
            @media (min-width: 67.5rem) {
                max-width: 50%;
            }

            h2 {
                margin: 0;
            }

            p {
                margin-top: 0.5rem;
            }
        }
    @supports not (aspect-ratio: 16/9) {
        .media-player {
            position: relative;
            padding-bottom: 56.25%;
            padding-top: 30px;
            height: 0;
            overflow: hidden;
        }

        .media-player>iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    @media (max-width: 67.5rem) {
        &.flex-wrapper {
            flex-wrap: wrap;
        }
    }
}
}

main{
    aside{
        .media-container.youtube{
            h3{
                margin-top: 0;
            }
        }
    }
} 

//styling according to ticket 3867 to enhance thumbnail quality

    .media-player {
      .aspect-ratio-container {
        position: relative;
        width: 100%;
        padding-top: 56.25%; // 16:9 Aspect Ratio
  
        .thumbnail-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-size: cover;
          background-position: center;
          cursor: pointer;
  
          .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: #ff0000;
            
          }
        }
  
        .react-player {
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }
  
/* reserve for emergency overrides across platform */