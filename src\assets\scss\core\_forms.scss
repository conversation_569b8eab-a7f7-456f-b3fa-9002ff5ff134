select {
    cursor: pointer;
    position:relative;
    width:fit-content;
}

input[type="text"],
input[type="date"],
select {
    border: thin solid $default-link;
    padding: .5rem 1rem;
    font-size: .9rem;
    &[disabled] {
        color: $the9s;
        border-color: $border-md-grey;
        pointer-events: none;
    }
}


textarea {
    border: thin solid $default-link;
    padding: .5rem 1rem;
    min-height: 8rem;
    &[disabled] {
        color: $the9s;
        border-color: $border-md-grey;
        pointer-events: none;
    }
}

::-webkit-calendar-picker-indicator {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="15" viewBox="0 0 24 24"><path fill="%23004eaa" d="M20 3h-1V1h-2v2H7V1H5v2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 18H4V8h16v13z"/></svg>');
  }

input[type="date" i] {
    font-family: 'Roboto';
}

input[type="checkbox"] {
    accent-color: $default-link;
}

::placeholder {
    font-style: italic;
}

.select-wrapper {
    position: relative;
    &:after {
        font-family: 'Material Icons';
        position: absolute;
        pointer-events: none;
        content:'\e5cf';
        top: .5rem;
        right: 0.5rem;
        color: $default-link;
    }
    select {
        padding-right: 2rem;
    }
}

label, .pseudo-label {
    b {
        font-size: .85rem;
        display:block;
        padding-bottom: .25rem;
    }
}
