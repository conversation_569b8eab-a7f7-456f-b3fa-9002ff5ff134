.main-content-wrapper {
    display: flex;
    z-index: 0;
    position: relative;    
    .desktop-view {
        display: block;
    }
    .mobile-view {
        display: none;
    }
    #slide, .toggle {
        display:none;
    }
    
}

.site {
    max-width: 80rem;
    margin: 0 auto;
    position: relative;

    &.flex-wrapper {
        gap: 2rem;
        section {
            padding: 1.875rem 0;
            &.poll {
                padding: 1rem;
            }
        }
        .call-to-action {
            text-align: left;
        }
    }
}

.flex-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.no-margin {
    margin: 0;
}
.no-margin-top {
    margin-top: 0;
}
.no-margin-bottom {
    margin-bottom: 0;
}

.margin-bottom {
    margin-bottom: 1rem;
}

.margin-top {
    margin-top: 1rem;
}

.background-lt-grey {
    background-color: $background-lt-grey;
}
.background-lt-grey-2 {
    background-color: $background-lt-grey-2;
}
.background-lt-blue {
    background-color: $background-lt-blue;
}
.background-lt-blue-2 {
    background-color: $inverse-link-hover;
    color: $black;
}
.background-blue {
    background-color: $background-blue;
    color: $white;
    h1, h2 {
        color: $white;
    }
}
.background-md-blue-2 {
    background-color: $background-md-blue-2;
    color: $white;
    h1, h2 {
        color: $white;
    }
}
.background-dk-blue {
    background-color: $background-dk-blue;
    color: $white;
    h1, h2 {
        color: $white;
    }
}
.background-lt-yellow {
    background-color: rgba(255, 198, 0, 0.5);
    color: $black;
}
.background-yellow {
    background-color: $background-primary;
    color: $black;
}
.background-dk-yellow {
    background-color: $background-primary-hover;
    color: $black;
}

.sr-only {
    clip: rect(0 0 0 0);
    clip-path: inset(50%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    white-space: nowrap;
    width: 1px;
}


