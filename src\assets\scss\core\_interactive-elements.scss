a {
    color: $default-link;
    text-decoration: none;
    cursor: pointer;
    &:hover {
        color: $default-link-hover;
    }
    &[aria-disabled="true"] {
        pointer-events: none;
        &:not(.primary):not(.secondary):not(.tertiary) {
            color: $lt-body-text;
        }
    }

    &.primary {
        padding: .75rem 2rem;
        background-color: $background-primary;
        color: $primary-link;
        border-radius: .25rem;
        &:hover {
            background-color: $background-primary-hover;
        }
        &[aria-disabled="true"] {
            background-color: $theDs;
        }
        &.medium {
            padding: 0.4444444444rem 0.8888888889rem;;
        }
    }
    &.secondary {
        padding: .75rem 2rem;
        background-color: $default-link;
        color: $white;
        border-radius: .25rem;
        &:hover {
            background-color: $default-link-hover;
        }
        &[aria-disabled="true"] {
            background-color: $theDs;
        }
        &.medium {
            padding: 0.4444444444rem 0.8888888889rem;;
        }
    }
    &.tertiary {
        padding: .75rem 2rem;
        background-color: $white;
        color: $default-link;
        border-radius: .25rem;
        border: thin solid $default-link;
        &:hover {
            color: $default-link-hover;
        }
        &[aria-disabled="true"] {
            color: $theDs;
            border-color: $border-md-grey;
        }
        &.medium {
            padding: 0.4444444444rem 0.8888888889rem;;
        }
    }
    &.inverse {
        color: $inverse-link;
        &:hover {
            color: $inverse-link-hover;
        }
        &[aria-disabled="true"] {
            color: $theDs;
        }
    }
}

button {
    &.primary {
        padding: .75rem 2rem;
        background-color: $background-primary;
        color: $primary-link;
        border-radius: .25rem;
        &:hover {
            background-color: $background-primary-hover;
        }
        &[disabled] {
            background-color: $theDs;
        }
    }
    &.secondary {
        padding: .75rem 2rem;
        background-color: $default-link;
        color: $white;
        border-radius: .25rem;
        &:hover {
            background-color: $default-link-hover;
        }
        &[disabled] {
            background-color: $theDs;
        }
    }
    &.tertiary {
        padding: .75rem 2rem;
        background-color: $white;
        color: $default-link;
        border-radius: .25rem;
        border: thin solid $default-link;
        &:hover {
            color: $default-link-hover;
        }
        &[disabled] {
            color: $theDs;
            border-color: $border-md-grey;
        }
    }
}

.call-to-action {
    padding: 2rem 0;
    text-align: center;
    width: 100%;
}