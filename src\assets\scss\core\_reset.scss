body {
    min-height: 100vh;
    margin: 0;
    display: flex;
    flex-direction: column;
    background-color: white;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

button {
    display: inline-block;
    border: none;
    margin: 0;
    text-decoration: none;
    background-color: transparent;
    color: black;
    font-size: 1rem;
    cursor: pointer;
    text-align: center;
    transition: background 250ms ease-in-out, 
                transform 150ms ease;
    -webkit-appearance: none;
    -moz-appearance: none;
}

select {
    // A reset of styles, including removing the default dropdown arrow
    appearance: none;
    // Additional resets for further consistency
    background-color: transparent;
    border: none;
    padding: 0 1em 0 0;
    margin: 0;
    width: 100%;
    font-family: inherit;
    font-size: inherit;
    cursor: inherit;
    line-height: inherit;
    &::-ms-expand {
        display: none;
      }
  }

