html {
    font-size: 18px;
}

body {
    font-family: '<PERSON><PERSON>', sans-serif;
    font-size: 1rem;
    font-variant: no-common-ligatures;
    line-height: 1.5;
    text-rendering: optimizelegibility;
    text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    color: $body-text;
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 500;
    color: $black;
}

h1 {
    font-size: 2.4rem;
    line-height: 1.3;
}

h2 {
    font-size: 1.9rem;
    margin: 1rem 0;
    &.with-cta {
        display: flex;
        flex-wrap: wrap;
    }
    a {
        margin-left: auto;
        font-size: .9rem;
    }
}

h3 {
    font-size: 1.3rem;
    span.material-icons {
        font-size: .9rem;
    }
}

h4 {
    font-size: 1.25rem;
}

h5 {
    font-size: 1.2rem;
}

h6 {
    font-size: 1.15rem;
}

p {
    font-size: 1rem;
}
// Internal changes
.paragraph-wrapper {
    @extend p;
    margin: 1rem 0;
}
small {
    font-size: .875rem;
}