/*Colors*/
$baseFontSize: 18px;
$theEs: #eeeeee;
$theDs: #dddddd;
$theBs: #bbbbbb;
$the9s: #999999;
$the6s: #666666;

$the6s-hover-20: #66666633;

$white: #ffffff;
$black: #000000;

$black-opacity-80: #000000cc;

$body-text: #3f3f3f;
$header-text: $black;
$lt-body-text: #737373;

$default-link: #004eaa;
$default-link-hover: #002d61;
$inverse-link: $white;
$inverse-link-hover: #d8ebef;
$primary-link: $black;

$background-blue: #00358e;
$background-dk-blue: #002D61;
$background-lt-blue: #f7fbfe;
$background-lt-blue-2: rgba(42, 125, 225, 0.15);
$background-lt-grey: #f8f8f8;
$background-lt-grey-2: #fcfcfc;
$background-md-blue-1: #A4C8F0;
$background-md-blue-2: #2672CD;
$background-md-blue-3: #1D6AC9;
$background-primary: #FFC600;
$background-primary-hover: #F4AF2D;
$background-primary-40: #ffe899;
$background-secondary: #004EAA;
$background-secondary-hover: #002d61;
$background-tertiary: $white;
$background-tertiary-hover: $background-lt-blue;

$border-md-grey: $theBs;
$border-lt-grey: #efefef;
$border-lt-grey-2: #e5e5e5;
$border-blue: #00358e;
$border-dk-blue: #002D61;
$border-dk-grey: #707070;

$border-neutral: #8c8c8c;
$neutral-100: #dbdbdb;
$neutral-800: #4d4d4d;
$neutral-1000: #1a1a1a;
$neutral-50: #f4f4f4;
$neutral-200: #cacaca;
$neutral-300: #b2b2b2;
$neutral-400: #a3a3a3;
$neutral-500: #8c8c8c;
$neutral-700: #636363;

$primary-200: #8aa2cb;
$text-secondary: $neutral-800;

$red-1: #d40019;
$red-2: #d10077;
$red-3: #CF5792;
$red-4: #C10017;

$yellow-1: #FFC600;
$yellow-2: #F4AF2D;
$yellow-3: #CD792D;

$green-1: #6ABF4B;
$green-2: #006A35;
$yellow-3: #CD792D;

$lt-grey-1: #a3a3a3;
$dk-grey-1: #4A4A4A;


/*Dimensions*/

$max-site-width: 90rem;


$alert-banner-yellow: #ffe899;
$alert-banner-grey: #f2f2f266;

$tab-border-color: #ebebeb