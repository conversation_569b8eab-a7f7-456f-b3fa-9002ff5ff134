@media (max-width: 67.5rem) {
// Internal changes
    .desktop-view {
        display: none;
    }
    .mobile-view {
        display: flex;
    }
    header {
        nav {
           .non-mobile {
            display: none;
           }
           .mobile-navigation {
                display: inline-flex;
                a {
                    font-size: 2rem;
                    &.material-icons {
                        padding: 1.5rem 0.75rem 1.5rem 0;
                    }
                }
            }
        }
    }
    .newsfeed {
        &.panel {
            .flex-wrapper {
                article {
                    width: 100%;
                }
            }
        }
    }

    section {
        padding: 1.875rem;
        &.hero, &.hero.topic-detail, &.ei-weekly-digest, &.mileage-confirm, &.paas-monthly-newsletter {
            .messaging {
                max-width: 100%;
                width: 100%;
                padding: 0;
            }
            img {
               display:none;
            }
            .call-to-action {
                text-align: center;
            }
        }
        &.hero.topic-detail {
            padding: 1.875rem;
        }
        /* &.explore-lobs {
            .cards {
                justify-content: center;
                .card {
                    max-width: 25%;
                    min-width: 15rem;

                     img {
                        height: 10rem;
                    }

                    strong {
                        padding-top: .75rem;
                    }
                }
            }
        } */

        .box-container {
            flex-wrap: wrap;
            justify-content: flex-start;

            .box {
                width: auto;
                display: block;
            }
        }


        &:not(.explore-insights):not(.hot-topics):not(.carousel) {
            .cards {
                flex-wrap: wrap;
                justify-content: flex-start;
                .card {
                    /* width: auto; */
                }
                &.min-width-cards {
                    flex-direction: column;
                    .card {
                        width: 100%;
                        display:flex;
                        align-items: center;
                        gap: 1rem;
                        min-width: unset;
                        max-width: unset;
                        padding-right: 0;
                        img {
                            width: 15rem;
                        }
                        h3 {
                            flex-grow: 2;
                        }
                    }
                }
            }
        }

        &.explore-insights {
            .cards {
                flex-direction: column;
                gap: 0;

                article {
                    max-width: 100%;
                    min-width: 100%;
                    margin: 0 auto;

                    img {
                        height: 15rem;
                    }
                }
            }
            .call-to-action {
                text-align: left;
            }
        }
        &.all-topics-list {
            text-align: left;
        }

        .carousel-cards {
            flex-wrap: nowrap;
            justify-content: space-between;
        }
    }

    .signed-in {
        .main-content-wrapper {
            #slide, .toggle {
               display: none;
            }
        }

        aside {
            &.workspace-nav {
                display: none;
            }
        }
    }

    main.global-search {
        .site.flex-wrapper {
            flex-direction: column;
            aside.filter {
                width: 100%;
                margin-left: 0;
                section {
                    width: 100%;
                }
            }
        }
    }

    main.library {
        .no-content.flex-wrapper {
            flex-direction: column;
            align-items: flex-start;
            padding-bottom: 10rem;
            .message {
                max-width: 100%;
            }
            .image-wrapper {
                margin-top: 0;
            }
        }
    }

    footer {
        .copyright-footer-links {
            small {
                font-size: .75rem;
                a {
                    display: block;
                    &:not(:first-of-type) {
                        border-left: 0;
                    }
                }
            }
        }
    }

    main {
        .site.flex-wrapper {
            aside {
                &.filter, &.filter.thin {
                    padding: 0;
                    h2.filter-toggle-mobile {
                        display: flex;
                        align-items: flex-end;
                        margin-bottom: 0;
                        width: 100%;
                        padding: 0 0.9375rem;

                        span {
                            display: inline-block;
                            vertical-align: middle;
                        }
                    }
                    .link-list {
                        padding: 0 0.8rem 0 0.9375rem;
                    }

                    .link-list-closed {
                        display: none;
                        margin-top: 0;

                    }
                }
            }
        }
    }
}

@media (max-width: 50rem) {
    header {
        .top-header.flex-wrapper {
            display: block;
            position: relative;
            .logo {
                position: absolute;
                top: -0.5rem;
            }
            .search-box {
                min-width: 100%;
                padding-top: 2rem;
            }

            a:last-of-type {
                position: absolute;
                top: -0.5rem;
                right: 0;
                font-size: .85rem;
            }
        }

        nav.account-alerts-support {
            .user-account {
                .menu-dropdown {
                    .support {
                        margin-right: 1.5rem;
                    }
                    .account {
                        margin-left: 0;
                        a {
                            margin-left: 0;
                            padding: 1.5rem 0 1.5rem 1rem;
                        }
                    }
                    .dropdown-content {
                        width: auto;
                    }
                }
            }
        }
    }
    main {
        .site.flex-wrapper {
            flex-direction: column;
            .site.flex-wrapper {
                aside, aside.thin {
                    width: 100%;
                    section {
                        width: 100%;
                    }
                }
            }
            .content-wrapper {
                max-width: 100%;
                width: 100%;
            }
            aside {
                width: 100%;
                section {
                    width: 100%;
                }
            }
        }
    }
    section {
        .flex-wrapper {
            &.with-cta {
                flex-direction: column;
                .call-to-action {
                    text-align: left;
                }
            }
        }
        &:not(.explore-insights):not(.hot-topics):not(.carousel) {
            .cards {
                margin-left: 0;
                .card {
                    width: 100%;
                    padding: 1rem 0;
                    img {
                        height: 10rem;

                    }
                }
                .call-to-action {
                    margin-bottom: 2rem;
                }
            }

            a.primary {
                display: block;
                width: fit-content;
            }
        }
        &.explore-insights {
            h2 {
                flex-direction: column;
                .tabs {
                    margin-left: 0;
                }
            }
            .cards {
                article {
                    img {
                        height: 10rem;
                    }
                }
            }
        }
    }
    footer {
        .site.flex-wrapper {
            flex-direction: column;
            gap: 0;
            .copyright-footer-links {
                padding: 1rem 2rem;
            }
        }
    }
}

@media (max-width: 30rem) {
    header {
        .top-header.flex-wrapper {
            .search-box {
                display: flex;
                flex-direction: column;
                select, input {
                    width: 100%;
                    margin-bottom: .5rem;
                }
                #mainSearch {
                    padding: 0.4rem 0.6rem;
                    border-radius: .25rem;
                    width: 100%;
                    margin-left: .2rem;
                }
            }
        }
    }
    main.legislative-monitoring{
        &.hub{
            .content-wrapper{
                .lmon-table-view{
                    .results-listing{
                        .page-results-wrapper{
                            nav{
                                ul{
                                    padding: 0;
                                }
                                li:first-of-type{
                                    margin-left: 0;
                                }
                                li:last-child{
                                    margin-right: 0;
                                }
                            } 
                        }
                    }
                }
            }
        }
    }

}

@media (max-width: 40rem) {
    header {
        .top-header.flex-wrapper {
            .search-box {
                .select-wrapper:after {
                    top: .2rem;
                }
                select, input {
                    font-size: .8rem;
                }
                button {
                    padding: .1rem .6rem;
                    font-size: .9rem;
                }
            }
        }
    }
    section {
        overflow-wrap: break-word;
        .bio {
            flex-direction: column;
        }
        &:not(.explore-insights):not(.hot-topics) {
            .cards {
                &.min-width-cards {
                    .card {
                        flex-direction: column;
                        align-items: flex-start;
                        img {
                            width: 100%;
                        }
                    }
                }
            }
        }
    }
    main.global-search {
        .site.flex-wrapper {
            .content-wrapper {
                width: 100%;
                h2 + .results-meta-sort.flex-wrapper {
                    align-items: flex-start;
                    width: 100%;
                    form {
                        margin-left: 0;
                        margin-top: .5rem;
                    }
                }
                .results-listing {
                    .cards {
                        width: 100%;
                        margin-left: 0;
                        .card {
                            img {
                                display: none;
                            }
                        }
                    }
                    .page-results-wrapper {
                        flex-direction: column;
                        nav {
                            margin-top: 1rem;
                            margin-left: -1.5rem;
                            ul {
                                padding-left: 0;
                            }
                        }
                    }
                }

            }
        }
    }
    main.legislative-monitoring{
        &.search{
            .site.flex-wrapper{
                .content-wrapper{
                    .results-listing{
                        .page-results-wrapper{
                            flex-direction: column;
                            nav{
                                margin-top: 1rem;
                                margin-left: -4.5rem; 
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (min-width: calc(92.5rem + 1px)) {

    body:not(.signed-in) {
        main:not([class*="search"]) {
            .site.flex-wrapper {
                .content-wrapper {
                    padding-left: 0;
                }
            }
        }
    }
}
@media (max-width: 92.5rem) {
    .timeline {
        border-left: thin solid $border-md-grey;
        .flex-wrapper {
            flex-direction: column;
            time {
                width: auto;
                margin-left: -.5rem;
                padding-bottom: .5rem;
                background-color: transparent;
                span {
                    margin-left: 0;
                    float: left;
                    margin-right: 0.5rem;
                }
            }
            div {
                border-left: none;
                width: calc(100% - 1rem);
            }
        }
    }
    p.no-results {
        margin-left: 1.875rem;
    }
}


body:not(.signed-in) {
    main.emerging-issues > .site.flex-wrapper {
        @media (max-width: 92.5rem) {
            max-width: 80rem;
            margin: 0;
        }
    }

    @media (min-width: 67.5rem) and (max-width: 91.325rem) {
        main.global-search {
            .site.flex-wrapper {
                aside.filter {
                   margin-left: 1rem;
                }
            }
        }
    }

    .hero {
        &.topic-detail {
            @media (max-width: 92.5rem) {
                padding-left: 1.875rem;
            }
            @media (min-width: calc(92.5rem + 1px)) {
                padding-left: 0.5rem;
            }
        }
    }

    @media (max-width: 93.75rem) {
        .hub-page.lob {
            .hero {
                &.topic-detail {
                    padding-left: 1.875rem;
                }
            }
        }
    }
}

/* @media (min-width: 123.75rem) {

     .cards {
            .card {
                img {
                    height: 12rem;
                }
            }
        }
} */