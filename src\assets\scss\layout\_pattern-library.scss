$blue-1: #00358E;
$blue-2: #004EAA;
$blue-3: #1D6AC9;
$blue-4: #002D61;
$blue-5: #d8ebef;
$blue-6: #f7fbfe;
$blue-7: #A4C8F0;
$blue-8: #2A7DE1;
$grey-1: #3f3f3f;
$grey-2: #666666;
$grey-3: #999999;
$grey-4: #bbbbbb;
$grey-5: #dddddd;
$grey-6: #eeeeee;
$grey-7: #efefef;
$grey-8: #f2f2f2;
$grey-9: #66666633;
$red-1: #d40019;
$red-2: #d10077;
$gold-1: #ffc600;
$gold-2: #f4af2d;
$gold-3: #ffe899;
$green-1: #6abf4b;
$green-2: #006a35;



.pattern-library {

    header {
        width: 100%;
        background-color: $white;
        .top-header {
            .logo {
                img {
                    height: 2rem;
                }
            }
            h1 {
                text-align: center;
                font-weight: 400;
                margin-top: .5rem;
            }
        }
        &.demo-signed-in, &.demo-guest {
            position: unset;
            margin-bottom: 15rem;
        }
        &.demo-guest {
            .account-alerts-support {
                display: none;
            }
            nav + .primary {
                display: inline-flex;
                align-self: center;
            }
        }
    }
    aside.brand-patterns {
        nav {
            width: 100%;
            padding-right: 1rem;
        }
        @extend .workspace-nav;
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        max-height: 48rem;
        overflow-y: hidden;
        display: inline-block;
        border-right: none;
        padding-top: 0;
        ul {
            padding-left: 1rem;
        }
        a {
            line-height: 1.8;
            padding: .25rem 0.2rem; 
        }
        h2 {
            margin-top: 1rem;
        }
    }
    aside.brand-patterns:hover {
        overflow-y: auto;
    }
    main.patterns {
        border-left: 0.25rem double $border-lt-grey;
        scroll-behavior: smooth;
        padding: 1.875rem;
        .intro {
            margin: 1rem 0;
        }
        pre {
            text-align: left;
            white-space: pre-line;
            padding: 1rem;
            background-color: $background-lt-grey;
            border: thin solid $border-md-grey;
        }
        section {
            h2 {
                font-size: 2.75rem;
                font-weight: 400;
                margin: 2rem 0;
                padding: 6rem 0 1rem;
                border-top: 1rem dotted $border-lt-grey;
            }
            h3 {
                font-size: 2rem;
                font-weight: 400;
                width: 100%;
                margin-top: 1rem;
            }
           &.typography, &.iconography, &.header, &.hero-component, &.interactive-elements {
                .flex-wrapper {
                    flex-direction: column;
                    align-items: flex-start;
                    h4 {
                        font-size: 1.5rem;
                        font-weight: 400;
                    }
                    &.cards {
                        flex-direction: row;
                        flex-wrap: wrap;
                        gap: 1rem 2rem;
                        .card {
                            padding: 1rem;
                            min-width: 15rem;
                            text-align: center;
                        }

                        .material-icons {
                            font-size: 3rem;
                        }
                    }
                }
                pre {
                    white-space: pre-wrap;       /* css-3 */
                    white-space: -moz-pre-wrap;  /* Mozilla, since 1999 */
                    & + {
                        h2 {
                            border: none;
                            padding: 0;
                            margin: 1rem 0;
                        }
                    }
                }
           }
           &.typography {
            h1,h2,h3,h4,h5,h6 {
                margin: 2rem 0;
            }
            .flex-wrapper:first-of-type {
                h3:first-of-type {
                    margin-top: 0;
                }
            }
           }
           &.colors {
                p {
                    margin: .25rem 0 0;
                    &:first-of-type {
                        margin-top: .5rem;
                    }
                }
                h2 {
                    border-top: none;
                    padding-top: 0;
                    margin-top: 0;
                }
           }
           &.header, &.hero-component {
                .section-child {
                    margin-bottom: 2rem;
                    p {
                        margin-top: 2rem;
                    }
                    img {
                        width: 100%;
                        box-shadow: 0 0 0.15rem 0 $border-md-grey;
                    }
                    &.mobile-view {
                        img {
                            width: 50%;
                            display: block;
                        }
                    }   
                }              
            }
            &.interactive-elements {
                aside {
                    &.workspace-nav {
                        box-shadow: 0 0 0.15rem 0 $border-md-grey;
                        border:none;
                        min-height: 25rem;
                        display: inline-flex;
                    }
                }
                .call-to-action {
                    text-align: left;
                }
                .glossary {
                    .material-icons {
                        vertical-align: middle;
                    }
                }
            }
        }

        .cards {
            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 2rem;
            margin-left: 0;
            .card {
                .color {
                    height: 5rem;
                    width: 12rem;
                    border: thin solid $border-md-grey;
                    &.black {
                        background-color: $black;
                    }
                    &.black-2 {
                        background-color: $black-opacity-80;
                    }
                    &.grey-1 {
                        background-color: $grey-1;
                    }
                    &.grey-2 {
                        background-color: $grey-2;
                    }
                    &.grey-3 {
                        background-color: $grey-3;
                    }
                    &.grey-4 {
                        background-color: $grey-4;
                    }
                    &.grey-5 {
                        background-color: $grey-5;
                    }
                    &.grey-6 {
                        background-color: $grey-6;
                    }
                    &.grey-7 {
                        background-color: $grey-7;
                    }
                    &.grey-8 {
                        background-color: $grey-8;
                    }
                    &.grey-9 {
                        background-color: $grey-9;
                    }
                    &.white {
                        background-color: $white;
                    }
                    &.blue-1 {
                        background-color: $blue-1;
                    }
                    &.blue-2 {
                        background-color: $blue-2;
                    }
                    &.blue-3 {
                        background-color: $blue-3;
                    }
                    &.blue-4 {
                        background-color: $blue-4;
                    }
                    &.blue-5 {
                        background-color: $blue-5;
                    }
                    &.blue-6 {
                        background-color: $blue-6;
                    }
                    &.blue-7 {
                        background-color: $blue-7;
                    }
                    &.blue-8 {
                        background-color: $blue-8;
                    }
                    &.red-1 {
                        background-color: $red-1;
                    }
                    &.red-2 {
                        background-color: $red-2;
                    }
                    &.gold-1 {
                        background-color: $gold-1;
                    }
                    &.gold-2 {
                        background-color: $gold-2;
                    }
                    &.gold-3 {
                        background-color: $gold-3;
                    }
                    &.green-1 {
                        background-color: $green-1;
                    }
                    &.green-2 {
                        background-color: $green-2;
                    }
                }

            }
        }
        h4 {
            & + .sub-header {
                margin-bottom: 3rem;
            }
        }
    }
}