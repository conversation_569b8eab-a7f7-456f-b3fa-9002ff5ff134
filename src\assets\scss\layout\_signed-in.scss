.signed-in {
    header {
        a.primary {
            display: none;
        }
    }
    .site {
        max-width: unset;
    }
    aside.workspace-nav {
        display:inline-flex;
    }
    .main-content-wrapper {
        .toggle {
            display: block;
            text-decoration: none;
            color: $default-link;
            background-color:white;
            box-shadow: 0 0 0.75rem 0 rgb(0 0 0 / 20%);
            transform: rotate(-90deg);
            position: fixed;
            top: 13rem;
            left: 15.15rem;            
            -webkit-transition: all .5s ease-out;
            -moz-transition: all .5s ease-out;
            transition: all .5s ease-out;
            z-index: 1;
            cursor: pointer;
            span.material-icons {
                font-size: 1.5rem;
                display:block;
                &.more {
                    display: none;
                }
            }
            &:hover {
                color: $default-link-hover;
            }
        }

        #slide:checked ~ aside.workspace-nav {
            margin-left: -15.5rem;
             }

        #slide:checked + .toggle {
        left: 0;
            span.material-icons {
                &.less {
                    display:none;
                }
                &.more {
                    display: block;
                }

            }
        }


        #slide {
        display: none;
        }
    }

    .account-alerts-support {
        display:flex;
    }
    main {
        .hero {
            &.topic-detail {
                padding-left: 1.875rem;
            }
        }
        .site.flex-wrapper {
            .filter {
                margin-left: 1rem;
            }
            .content-wrapper {
                padding: 1.875rem;
            }
            p.no-results {
                margin-left: 1.875rem;
            }
        }
        .panels-tab {
            .site.flex-wrapper
            {
                aside{
                    margin-left: 0;
                }
            } 
        }
    }
}