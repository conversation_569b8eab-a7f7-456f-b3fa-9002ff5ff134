body:not(.signed-in) {
    main {
        &.base-page {
            .major-content {
                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .references {

                // exclude references inside left-rail-section
                &:not(.left-rail-section .references) {
                    max-width: 80rem;
                    margin: 0 auto;
                    padding: 0;

                    details {
                        padding: 1.875rem;

                        @media (min-width: calc(92.5rem + 1px)) {
                            padding: 1.875rem 0;
                        }
                    }
                }
            }

            // .rich-text-component {
            //     &:not(.left-rail-section .rich-text-component) {
            //         max-width: 80rem;
            //         margin: 0 auto;
            //         padding: 1.875rem 0 1.875rem 1.875rem;

            //         @media (min-width: calc(92.5rem + 1px)) {
            //             padding: 1.875rem 0;
            //         }
            //     }
            // }
        }
    }
}