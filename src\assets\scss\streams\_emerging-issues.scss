main.emerging-issues {

     @import 'emerging-issues-partials/ei-variables';
     @import 'lob-partials/lob-components/project-status';

     .content-wrapper {
          &.guest-user {
               padding: 1.875rem;
          }
          flex-grow: 2;
          .content-wrapper.all-topics {
               max-width: 100%;
          }//
          &.all-topics {
               flex-wrap: wrap;
               align-items: flex-start;
               h2 {
                    font-size: 1.125rem;
                    padding-bottom: 1rem;
                    border-bottom: thin solid $border-md-grey;
                    font-weight: 700;
               }
               .link-list {
                    display: flex;
                    align-items: flex-start;
                    flex-wrap: wrap;
                    li {
                         width: 45%;
                         margin-right: 1rem;
                    }
                }           
          }
     }

     &.hub-page {
          // .hub-header {
          //      background-image: linear-gradient(45deg, rgba(0, 53, 142, 0.75), rgba(0, 53, 142, 0.55)), url(../assets/future-allocation-services.png);
          // }

          li {

               a {
                    &.list-content {
                         font-size: 1rem;
                         font-weight: 500;
                         word-break: break-word;
                         padding: 1.25rem 0;
                    }
               }
          }

          // .background-lt-grey {
          //      background-color: $faux-ghost-white;
          // } #New styles

          .no-padding-top {
               padding-top: 0rem;
          }

          .share-alignment {
               margin: auto;
          }

          ul {
               &.list-height {
                    max-height: 40.625rem;
                    overflow: auto;
               }
          }

          .verisk-activity .agenda-link-group {
               &.top-line {
                    border-top: thin solid $grey-4;
               }
          }

          .pills {
               &.tag-pills-alignment {
                    flex-wrap: wrap;
               }
          }
     }

     &.topic-detail-page {
          span {
               &.isonet-content {
                    justify-content: flex-end;
                    margin-left: auto;
                    margin-right: 0;
                    color: $white;
               }

               &.material-icons.size {
                    font-size: 1.125rem;
                    color: $white;
               }
          }

          .share-save a#share.sharing+div.popup.saved,
          .share-save a#bookmark.saved+div.popup.saved.popup-content-alignment {
               right: -4rem;
          }
     }

     .author {
          .author-details {
               border-bottom: none;
               padding-bottom: 0px;
          }
     }

     .newsfeed {
          .load-news {
               width: 70%;
          }
     }

     .main-container {
          display: flex;
          background-color: lightgrey;
          align-items: center;
          justify-content: center;
     }

     .text-container {
          margin: 1rem;
          padding: 1rem;
     }

     .vertical-container {
          display: flex;
          flex-direction: column;
          background-color: lightgrey;
          align-items: center;
          justify-content: center;
          margin: 1rem;
          padding: 1rem;
     }

     // .flex-wrapper{
     //      display: flex;
     //      align-items: center;
     //      justify-content: flex-start;
     //      gap: 2rem;
     // }
     .youtube-aside{
          width: 15rem;
     }
}