main.filing-intelligence {
    @import "filing-intelligence-partials/fi-variables";
    @import "filing-intelligence-partials/fi-list-section-common";
    @import "filing-intelligence-partials/fi-revision";
    @import "filing-intelligence-partials/fi-forms-tab";
    @import "filing-intelligence-partials/fi-rules-tab";
    @import "filing-intelligence-partials/fi-loss-costs-tab";
    @import "filing-intelligence-partials/fi-subject-tab";
    @import "filing-intelligence-partials/fi-list-section";
    @import "filing-intelligence-partials/fi-detail-section-tabs";
    @import "filing-intelligence-partials/fi-corresponding-forms";
    @import "filing-intelligence-partials/fi-corresponding-rules";
    @import "filing-intelligence-partials/fi-corresponding-loss-costs";
    @import "filing-intelligence-partials/fi-preview-documents";
    @import "filing-intelligence-partials/fi-download-dropdown";
    @import "filing-intelligence-partials/fi-read-full-content";
    @import "filing-intelligence-partials/fi-em-stylesheet";
    @import "filing-intelligence-partials/fi-timeline";
    @import "filing-intelligence-partials/fi-not-subscribed-message";
    @import "filing-intelligence-partials/fi-banner";
    @import "filing-intelligence-partials/fi-feedback";
    @import "filing-intelligence-partials/fi-cursor-types";
    @import "filing-intelligence-partials/fi-hub-header";
    @import "filing-intelligence-partials/fi-accordion";
    @import "filing-intelligence-partials/fi-disclaimer";
    @import "filing-intelligence-partials/fi-lob-filter";
    @import "filing-intelligence-partials/fi-classplan-tab";
    @import "filing-intelligence-partials/fi-alert-banner";

    * {
        font-family: "Roboto", sans-serif;
    }

    container-type: inline-size;

    .revision-seperator {
        display: none;

        @container (max-width: #{$sm}) {
            display: block;
            margin: 0;
            border: 0.0625rem solid $border-md-grey;
        }
    }

    details summary::after {
        transform: rotate(0);
        transition: transform 0.3s ease;
    }

    details[open] summary::after {
        content: '\e5cf';
        transform: rotate(180deg);
    }
}