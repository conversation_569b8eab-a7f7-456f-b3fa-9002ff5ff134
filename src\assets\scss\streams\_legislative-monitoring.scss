main.legislative-monitoring {
    &.glossary {
        .site.flex-wrapper .content-wrapper {
            max-width: unset;
        }
    }
    .site.flex-wrapper .content-wrapper {

        /* max-width: unset; */
        h1 {
            .source-link {
                font-size: 0.8rem;
                font-weight: normal;

                span {
                    vertical-align: middle;
                    font-size: 0.8rem;
                }
            }
        }
    }

    .disclaimer {
        font-size: .75rem;
        display: block;
        margin-top: 2rem;
    }

    aside {
        &.thin {
            display: flex;
            flex-direction: column;

            section {
                margin-left: auto;
            }
        }

        // Internal changes
        section.background-lt-grey {
            ul.link-list {
                li {
                    a {
                        small {
                            font-size: 0.813rem;
                        }
                    }
                }
            }
        }
    }

    &.hub {
        .site.flex-wrapper .content-wrapper {
            max-width: calc(100% - 17rem);

            @media (min-width: 67.5rem) {
                &.description {
                    .media-container {
                        .site {
                            .media-player {
                                width: 46%;
                            }
                        }
                    }
                }
            }

            .glossary-hub {
                justify-content: flex-end;

                a .material-icons {
                    vertical-align: middle;
                }
            }

            h2:first-of-type {
                border-bottom: thin solid $border-md-grey;
            }

            form {
                padding-bottom: 2rem;

                p {
                    font-size: 1.15rem;
                }

                .flex-wrapper {
                    gap: 2rem;
                    flex-wrap: wrap;

                    .tabs {
                        width: unset;
                        margin-left: auto;
                        gap: .5rem;
                        align-self: flex-end;

                        .tabbed {
                            nav {
                                padding-top: 0;

                                a {
                                    span {
                                        vertical-align: top;
                                    }

                                    &.active {
                                        span {
                                            color: $body-text;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .pseudo-label {
                        @media (max-width: 28rem) {
                            input {
                                margin-bottom: .5rem;
                            }

                            span {
                                display: none;
                            }
                        }
                    }
                }

                .select-wrapper {
                    &:after {
                        top: .5rem;
                        right: .5rem;
                        color: $default-link;
                    }
                }
            }

            .lmon-table-view {
                .results-listing {
                    .results-table {
                        &.scrollable {
                            margin-bottom: 2rem;
                            /* table {
                                min-width: 0;
                            } */
                        }
                    }

                    .page-results-wrapper {
                        padding-top: 1rem;
                    }
                }
            }

            /* .lmon-map-view, */
            .lmon-table-view {
                display: none;
            }

            /* .lmon-list-view.active, .lmon-map-view.active {
                display: block;
            } */
            /* &.width-100 {
                max-width:100%;
                width:100%;
            } */
            &:has(.table-view.active) {
                .lmon-table-view {
                    display: block;
                }

                .lmon-map-view {
                    display: none;
                }
            }
        }

        #usa-map {
            width: 100%;
            height: auto;
        }

        svg {
            .annotation {
                &-state-active {
                    fill: $default-link;

                    +.annotation-line {
                        stroke: $theBs;
                        opacity: 1;
                    }
                }

                &-line {
                    opacity: 0;
                }

                &-text {
                    text-anchor: start;
                    pointer-events: none;
                }
            }

            a[aria-disabled='false'] {
                pointer-events: unset;

                path {
                    fill: #f0f0f0;
                    &:hover {
                        fill: $the6s-hover-20;
                    }

                    &.annotation-line {
                        display: none;

                        &+path {
                            display: none;
                        }
                    }
                }

                text {
                    display: none;
                }
            }

            a:not([aria-disabled='false']) {
                path {
                    fill: $default-link;
                }

                &:hover {
                    +.annotation-state-active {
                        fill: $default-link-hover;
                    }

                    path {
                        fill: $default-link-hover;
                    }
                }

            }

            text {
                font-size: .9rem;
                font-style: normal;
                font-variant: normal;
                font-weight: 500;
                font-stretch: normal;
                text-align: center;
                line-height: 100%;
                writing-mode: lr-tb;
                text-anchor: middle;
                fill: $white;
                fill-opacity: 1;
                stroke: none;
                stroke-width: 1px;
                stroke-linecap: butt;
                stroke-linejoin: miter;
                stroke-opacity: 1;
            }
        }

        svg+.flex-wrapper {
            justify-content: space-between;
        }
        .info-banner.flex-wrapper {
            background-color: rgba(42, 125, 225, 0.15);
            border-radius: 0.25rem;
            border-bottom: none;
            text-align: left;
            padding: 1.25rem 1.5rem 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 5;
            a.close-icon {
                margin-left: 1.5rem;
                color: $body-text;
                align-self: flex-start;
                span {
                    font-size: 1.125rem;
                    padding: 0.15rem;
                }
            }
            .info-banner-content {
                width: 100%;
                font-size: 0.9rem;
                .banner-heading {
                    display: block;
                    font-size: 1.25rem;
                    margin-bottom: 0.5rem;
                }
                p {
                    margin: 0.5rem 0;
                }
                ul {
                    padding-left: 1.5rem;
                    margin: 0;
                    li {
                        text-align: left;
                    }
                }
            }

        }
        @media (max-width: 80rem) {
            .site.flex-wrapper {
                flex-wrap: wrap;

                .content-wrapper {
                    width: 100%;
                    max-width: 100%;
                }

                aside.thin {
                    flex-direction: row;
                    width: 100%;
                    flex-wrap: wrap;
                    align-items: flex-start;
                    gap: 2rem;

                    section {
                        margin: 0;
                    }
                }
            }
        }

        @media (max-width: 67.5rem) {
            .site.flex-wrapper .content-wrapper {
                width: 100%;
                max-width: 100%;

                form .flex-wrapper .tabs {
                    display: none;
                }

                &:has(.map-view.active) {
                    .lmon-table-view {
                        display: block;
                    }

                    .lmon-map-view {
                        display: none;
                    }
                }
            }
        }

        @media (max-width: 48rem) {
            .site.flex-wrapper {
                aside.thin {
                    flex-direction: column;

                    section {
                        width: 100%;
                    }
                }
            }
        }
    }

    &.search {
        h1 {
            margin-top: 1rem;
        }

        .site.flex-wrapper {
            gap: 0rem;

            aside.thin.filter {
                width: 15rem;

                details {
                    summary {
                        span {
                            max-width: 85%;
                        }
                    }
                }
            }

            .content-wrapper {
                max-width: calc(98% - 16rem);

                .glossary-search {
                    justify-content: flex-end;

                    a .material-icons {
                        vertical-align: middle;
                    }
                }

                .results-listing {
                    .results-table {
                        table {
                            th {
                                a.sortarrow {
                                    display: flex;
                                    width: max-content
                                }

                                span.material-icons {
                                    margin: auto;
                                }
                            }
                        }
                    }
                }
            }

            @media (max-width: 67.5rem) {
                flex-direction: column;

                aside.filter.thin {
                    width: 100%;
                    margin-left: 0;

                    section {
                        width: 100%;
                    }
                }

                .content-wrapper {
                    max-width: 100%;
                }
            }
        }

        form {
            label {
                display: flex;

                &.date-range {
                    align-items: center;

                    input[type="date"] {
                        margin-left: auto;
                        width: 74%;
                    }

                    #end-error {
                        border: 0.063rem solid #C10017;
                    }

                }
            }

            .error-message-container {
                padding-left: 3.4rem;

                .error-message {
                    font-size: 0.75rem;
                    display: flex;
                    color: #C10017;
                    align-items: center;
                    gap: 0.125rem;
                }
            }

        }

        .content-wrapper {
            .results-meta-sort {
                padding: 2rem 0 0;
                font-weight: 500;
                justify-content: space-between;
                gap: 0.5rem;
                flex-wrap: wrap;

                .download-results {
                    font-size: 0.9rem;

                    span {
                        display: inline-block;
                        vertical-align: middle;
                        font-weight: normal;
                    }
                }
            }
        }
    }

    &.detail {
        h1+section {
            padding-top: 0;
        }

        h1+.site.flex-wrapper {
            align-items: center;

            .glossary {
                margin-left: auto;

                .material-icons {
                    vertical-align: middle;
                }
            }
        }

        .material-icons {
            &.filed {
                color: $yellow-3;
            }

            &.under-review {
                color: $lt-body-text;
            }

            &.approved {
                color: $green-2;
                font-weight: 700;
            }

            &.withdrawn,
            &.disapproved {
                color: $red-1;
                font-weight: 700;
            }
        }

        .site.flex-wrapper .content-wrapper {
            max-width: unset;

            h2 {
                font-size: 1.7rem;
            }

            section:not(:first-of-type) {
                padding-top: 0;
            }
        }

        .overview-fields {
            align-items: flex-start;
            gap: 1rem;
            line-height: 1.8;
            justify-content: space-between;
            flex-wrap: wrap;

            .group {
                width: 30%;

                .pseudo-label {
                    width: 100%;

                    b {
                        font-size: 1rem;
                        display: inline-block;
                    }
                }
            }
        }

        .description {
            display: -webkit-box;
            // -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        #showMore {
            display: none;
        }

        .toggle {
            font-size: .9rem;
            transform: none;
            display: block;
            box-shadow: unset;
            position: unset;
            padding: 1rem 0 0 0;

            .less {
                display: none;
            }
        }

        section:has(#showMore:checked) {
            .description {
                display: block;
            }

            .toggle {
                .more {
                    display: none;
                }

                .less {
                    display: block;
                }
            }
        }

        .legend {
            font-size: .9rem;
            padding: 1rem 0;
            border-top: thin solid $border-md-grey;
            border-bottom: thin solid $border-md-grey;
            flex-wrap: wrap;

            b {
                padding-right: .5rem;
            }

            span {
                display: flex;
                padding: .25rem 0;

                i {
                    vertical-align: middle;
                    margin-right: .5rem;
                }

                &:not(:last-of-type) {
                    padding-right: 1rem;
                }

            }
        }

        form {
            margin-top: 1.5rem;

            .flex-wrapper {
                flex-direction: column;
                align-items: flex-start;

                &.timeline-controls {
                    flex-direction: row;
                    gap: 2rem;
                    padding-bottom: 1rem;

                    label {
                        display: flex;
                        align-items: flex-start;

                        input {
                            margin: .25rem .5rem 0 0;
                        }

                        b {
                            padding: 0;
                        }
                    }
                }

                &.filings {
                    label {
                        display: table;

                        input,
                        b,
                        a,
                        span.material-icons,
                        .element-wrapper {
                            display: table-cell;
                            padding: .5rem;
                            vertical-align: top;
                        }

                        input {
                            width: 1rem;
                            height: 1rem;
                            margin-top: .75rem;
                        }

                        b {
                            font-size: 1rem;
                            width: 18rem;
                        }

                        a {
                            width: 10rem;
                        }

                        span.material-icons {
                            width: 2.8rem;
                            font-size: 1.25rem;
                            text-align: center;
                        }

                        .element-wrapper {
                            width: 12rem;
                            font-weight: normal;

                            .badge {
                                font-size: .85rem;
                                padding: .25rem .5rem;
                                background-color: $the6s;
                                color: $white;
                                margin-left: 0.2rem;
                                border-radius: 0.25rem;
                                font-weight: 600;
                                vertical-align: middle;
                            }
                        }
                    }
                }
            }
        }

        textarea {
            width: 100%;
            margin-top: 1.5rem;
        }

        .contact-info-panel {
            .flex-wrapper {
                margin-top: 1.5rem;
                gap: 3rem;
                align-items: flex-start;
                flex-wrap: wrap;
            }

            img {
                height: 2rem;
            }

            ul {
                margin: 0;
                padding: 0;

                span {
                    display: block;
                    padding-bottom: .5rem;
                    font-weight: 700;
                }

                li {
                    list-style: none;
                    line-height: 1.8;

                    a {
                        font-weight: 500;
                    }
                }
            }
        }

        @media (max-width: 46.75rem) {
            form {
                .flex-wrapper {
                    &.filings {
                        label {
                            display: flex;
                            flex-wrap: wrap;

                            input,
                            b,
                            a,
                            span.material-icons,
                            .element-wrapper {
                                display: unset;
                            }

                            b {
                                width: calc(100% - 2rem);
                            }

                            a {
                                margin-left: 1.5rem;
                                width: 8.5rem;
                            }

                            span.material-icons {
                                padding-left: 0;
                                width: 2rem;
                            }

                            .element-wrapper {
                                width: auto;
                                padding: .5rem 0;
                            }
                        }
                    }
                }
            }
        }
    }

    &.glossary {
        h1 {
            font-size: 1.75rem;
            padding-bottom: 2.5rem;
        }

        h2 {
            font-size: 1.25rem;
            font-weight: 700;
            margin-top: 0;
        }

        h3 {
            font-size: 1.1rem;

            &:not(:first-of-type) {
                padding-top: .75rem;
            }
        }

        .content-wrapper {
            position: relative;
        }

        section {
            table {
                font-size: .8rem;
                max-width: 70rem;

                th {
                    font-weight: 700;

                    &.fieldname {
                        width: 15rem;
                    }

                    &.abbreviation {
                        width: 7rem;
                    }
                }

                td {
                    padding: .5rem;

                    &:first-of-type {
                        font-weight: 700;
                    }
                }

                p {
                    margin: .25rem 0;
                    font-size: .8rem;

                    &:first-of-type {
                        margin-top: 0;
                    }

                    &:last-of-type {
                        margin-bottom: 0;
                    }
                }

                @media (max-width: 92.5rem) {
                    max-width: 100%;
                }
            }

            &:not(:last-of-type) {
                padding-bottom: 0;
            }

            &.lob-abbreviations {
                table {
                    max-width: 50rem;
                }
            }
        }

        .disclaimer {
            max-width: 65rem;
        }

        @media (max-width: 79.99rem) {
            .lob-abbreviations {
                display: flex;
                flex-wrap: wrap;

                h2 {
                    width: 100%;
                }

                div {
                    width: 45%;

                    &:first-of-type {
                        margin-right: 2rem;
                    }
                }
            }
        }

        @media (max-width: 42.5rem) {
            .lob-abbreviations {
                flex-direction: column;

                div {
                    width: 100%;

                    &:first-of-type {
                        margin-right: 0;
                    }
                }
            }
        }

        @media (min-width: 80rem) {
            .content-wrapper {
                display: flex;
                flex-wrap: wrap;

                h1 {
                    width: 100%;
                }

                .field-definitions {
                    width: 60%;
                    padding-right: 1.5rem;
                    padding-top: 0;
                    margin-right: 1.5rem;
                    border-right: thin solid $border-lt-grey;
                }

                .lob-abbreviations {
                    padding-top: 0;
                    width: 33%;
                }
            }
        }

    }

    &:not(.search):not(.hub) {
        @media (max-width: 67.5rem) {
            .site.flex-wrapper {
                flex-direction: column;

                aside:not(.filter) {
                    display: flex;
                    flex-direction: row;
                    align-items: flex-start;
                    width: 100%;
                    gap: 1rem;

                    section {
                        margin-left: 0;

                        &:not(:first-of-type) {
                            margin-top: 0;
                        }
                    }
                }
            }

            h1+.site.flex-wrapper {
                align-items: flex-start;
                flex-direction: row;
            }
        }

        @media (max-width: 48rem) {
            .site.flex-wrapper {
                aside:not(.filter) {
                    flex-direction: column;

                    section {
                        width: 100%;
                    }
                }

                .overview-fields {
                    flex-direction: column;
                    gap: 0;

                    .group {
                        width: 100%;
                    }
                }
            }
        }
    }
}

main.right-dynamic-rail {
    aside {
        margin-left: auto;

        section {
            ul {
                li {
                    p {
                        font-size: 0.8rem;
                        font-weight: 500;
                    }

                    .bottom-links {
                        font-size: .8rem;

                        a:not(:first-of-type) {
                            padding-left: .5rem;
                            margin-left: .25rem;
                            border-left: thin solid $border-md-grey;
                        }

                        span.material-icons {
                            font-size: .8rem;
                            vertical-align: middle;
                            margin-left: 0.2rem;
                        }
                    }
                }
            }
        }
    }

}