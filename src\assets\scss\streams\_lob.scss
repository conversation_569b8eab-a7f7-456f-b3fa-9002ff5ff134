main.lob {
    @import "lob-partials/lob-components/hot-topics";
    @import "lob-partials/lob-components/upcoming-events";
    @import "lob-partials/lob-components/status-tracker";
    @import "lob-partials/lob-components/past-events";
    @import 'lob-partials/lob-components/project-status';

    .events {
      .cards.flex-wrapper {
        .card{
          .event-button {
            text-align: center;
            a.secondary {
              background-color: $white;
              color: $default-link;
              border-radius: 0.25rem;
              border: 1px solid  $background-secondary;
              &:hover{
                color: $default-link-hover;
                border-color: $default-link-hover;
              }
            }
          }
        }
      }
    }

    &.deep-dive {
        .site.flex-wrapper {
            .content-wrapper {
                section {
                    padding-bottom: 0;
                    h2 {
                        font-size: 1.7rem;
                    }
                    h3 {
                        font-size: 1.1rem;
                    }
                    p:last-of-type {
                        margin-bottom: 0;
                    }
                    ul {
                        margin-bottom: 0;
                        &.highlights {
                            list-style-type: none;
                            li {
                                padding-bottom: .5rem;
                                &::before {
                                    font-family: 'Material Icons';
                                    content: "square";
                                    -webkit-font-feature-settings: 'liga';
                                    vertical-align: middle;
                                    margin-right: .5rem;
                                }
                            }
                        }
                    }
                    &.pills {
                        border-bottom: none;
                    }
                    &.author {
                        border-top: thin solid #bbbbbb;
                        margin-top: 2.5rem;
                        padding-top: 0;

                        h2 {
                            margin-bottom: 0;
                        }

                        img.author-photo {
                            height: 5rem;
                        }
                    }
                }
            }

        }

        .bio {
            .social-icons {
                a:not(:nth-of-type(3)) {
                    display: none;
                }
            }

            p {
                display: none;
            }
        }
    }
    .sign-in-button {
        .lock-icon {
          font-size: 2rem;
          padding-right: 0.625rem;
          color:#004eaa;
        }
    
        font-weight: 500;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    
      .card-links {
        a {
          font-weight: 500;
          font-size: 1rem;
        }
      }
    
      .hero {
        .share-save {
          text-align: right;
    
          a {
            span.material-icons {
              font-size: 1rem;
              position: relative;
              bottom: 0.05rem;
              margin-right: .188rem;
            }
          }
        }
      }
    
      section.upcoming-events {
        padding: 0rem;
      }
    
      section.upcoming-events.scroll {
        overflow-y: scroll;
        height: 37rem;
      }
    
      section.past-events.scroll {
        overflow-y: scroll;
        height: 18rem;
      }
    
      .agenda-link-group {
       // margin-top: 0.5rem;
       padding-top: .5rem;
        font-size: .9rem;
    
        a:not(:first-of-type) {
          padding-left: .5rem;
          margin-left: .25rem;
          border-left: thin solid $border-md-grey;
        }
    
        span.material-icons {
          font-size: .8rem;
          vertical-align: middle;
        }
    
        &.top-line {
          border-top: thin solid $grey-4;
        }
      }
    
      .major-iso-link {
        margin-top: 1.5rem;
      }
      
      .content-wrapper.key-takeaways,
      .verisk-activity-container,
      section.general-liability {
        padding: unset !important;
      }
    
      .left-pane {
        width: 70%
      }
    
      span.material-icons.register {
        font-weight: 100 !important;
      }
  section.major-content.background-dk-blue {
    p,
    span,
    h2 {
      background-color: $background-dk-blue;
      color: $white;
    }
  }
  section.major-content.background-lt-white {
    p,
    span,
    h2 {
      color: $black;
      background-color: $white;
    }
  }
  section.major-content.background-lt-grey {
    p,
    span,
    h2 {
      background-color: $background-lt-grey;
      color: $black;
    }
  }
    }
    .contact-team {
      &.background-dk-blue {
        .contactTeam-content {
          a {
            color: $white;
            text-decoration: underline;
            font-weight: 500;
          }
        }        
      }
    }

