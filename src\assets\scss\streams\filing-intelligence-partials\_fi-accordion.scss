.fi-accordion-list {
    display: none;

    @container (max-width: #{$md}) {
        display: block;
    }
}

.fi-accordion-list {

    @container (max-width: #{$md}) {
        margin: 2rem 0;
    }

    @container (max-width: #{$sm}) {
        margin: 2rem 0;
    }
}

.fi-accordion-data {
    max-height: 26.75rem;
    overflow-y: scroll;
    overflow: overlay;

    &:hover {
        &::-webkit-scrollbar-thumb {
            display: block;
        }
    }

    &::-webkit-scrollbar {
        width: 0.625rem;
        background-color: transparent;
    }

    &::-webkit-scrollbar-track {
        margin-top: 2.5rem;
        background-color: transparent;
        width: 0.25rem;
    }

    &::-webkit-scrollbar-thumb {
        background-color: $the-Ds;
        border-radius: 0.3125rem;
        width: 0.25rem;
        border: 0.1875rem solid transparent;
        background-clip: padding-box;
        display: none;
    }
}

.fi-accordion-wrapper,
.fi-accordion-item .references {
    position: relative;
    border-left: 0.1625rem solid $red-1;
    border-bottom: 0.0625rem solid $border-md-grey;
    border-top: 0.0625rem solid $border-md-grey;
    border-right: 0.0625rem solid $border-md-grey;
    padding: 0;
    font-size: 0.9375rem;
    line-height: 1.25rem;
    margin-bottom: 2rem;

    @container (max-width: #{$md}) {
        section {
            padding: 0;
        }
    }

    @container (max-width: #{$sm}) {
        section {
            padding: 0;
        }
    }

    .active {
        max-height: 15.9375rem;
    }

    summary {
        font-size: 0.9375rem;
        border: 0;
        transition: 0.4s;
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-items: flex-start;
        color: $red-1;
        font-weight: 500;
        padding: 1rem 1.315rem;


        &::after {
            color: $default-link;
            font-size: 1.5rem;
            font-weight: 100;
        }
    }

    .reference-listing {
        padding: 0 1.315rem 1rem 1.315rem;
    }

    .accordion-content {
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.3125rem;
        overflow-y: scroll;
        overflow: overlay;
        max-height: 15.9375rem;
        transition: max-height 0.2s ease-out;

        &:hover {
            &::-webkit-scrollbar-thumb {
                display: block;
            }
        }

        &::-webkit-scrollbar {
            width: 0.625rem;
            background-color: transparent;
        }

        &::-webkit-scrollbar-track {
            background-color: transparent;
            width: 0.25rem;
        }

        &::-webkit-scrollbar-thumb {
            background-color: $the-Ds;
            border-radius: 0.3125rem;
            width: 0.25rem;
            border: 0.1875rem solid transparent;
            background-clip: padding-box;
            display: none;
        }

        .rules-filing-id:nth-child(n+2) {
            padding-top: 1rem;
        }

        .rules-detail-value {
            font-weight: 700;
            padding-right: 0.5rem;
        }

        .fi-accordion-filing-key-message {
            font-weight: 700;
            margin-bottom: 1rem;

            .rules-detail-value {
                font-weight: 400;
            }
        }

        .fi-accordion-filing-text {
            font-weight: 700;
        }

        .circular-download-btn,
        .circular-preview-btn {
            color: $default-link;
            text-transform: capitalize;
            padding: 0;
            min-width: auto;
            margin-bottom: 0.2rem;

            .critical-pdf-preview-icon {
                margin-left: 0;

                .list-section-preview {
                    display: flex;

                    svg {
                        font-size: 1.4rem;
                        margin-left: 0.3125rem;
                    }
                }
            }

            span {
                margin-left: 0.3125rem;
            }

            &:not(:last-of-type) {
                margin-right: 0.9375rem;
            }

            &:hover {
                color: $dark-blue-hover;
                background-color: unset;
            }
        }

        .fi-accordion-filing-history {
            line-height: 2.2;
        }
    }
}

.fi-accordion-basic .references {
    border-left: 0.0625rem solid $border-md-grey;
    transition: background-position 0.3s ease, color 0.3s ease;
    position: relative;

    &:hover {
        background-color: none;
    }

    summary:hover {
        background-color: $background-lt-cyan;
    }

    .accordion-content {
        max-height: none;
    }

    &:first-of-type {
        margin-top: 0;
    }

    summary {
        color: $default-link;
    }
}