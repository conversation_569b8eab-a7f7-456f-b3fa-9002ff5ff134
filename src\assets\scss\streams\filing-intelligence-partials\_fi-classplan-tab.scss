.classplan-tab-content {
    border-top: 0.0625rem dashed $border-md-grey;
    top: -0.0625rem;
    position: relative;

    .classplan-wrapper {
        display: flex;
        column-gap: 2.5rem;

        .classplan-list-pane {
            width: 50%;

            .list-section-filing-id {
                margin-bottom: 1rem;
            }

            @container (max-width: #{$md}) {
                width: 100%;
            }
        }

        .classplan-detail {
            width: 50%;
            margin-top: 1.125rem;
            box-sizing: border-box;

            @container (max-width: #{$md}) {
                display: none;
            }

            .fi-accordion-item {
                .references:first-of-type {
                    margin-top: 2.5rem;
                }
            }

            .fi-accordion-basic {
                .references:first-of-type {
                    margin-top: 0;
                }
            }

            .classplan-default-text {
                margin-top: 2rem;

                .classplan-description {
                    span {
                        font-weight: 700;
                    }
                }
            }

            .selected-classplan-item {
                font-size: 0.9375rem;
                color: $body-text;
                margin-bottom: 1rem;

                .selected-classplan-item-text {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .tab-name {
                padding-left: 1.125rem;
            }

            .lob-icon-hidden {
                visibility: unset;
                display: none;
            }
        }

        .fi-accordion-data {
            display: flex;

            @container (max-width: #{$md}) {
                display: none;
            }

            .classplan-default-text {
                margin-top: 2rem;

                .classplan-description {
                    span {
                        font-weight: 700;
                    }
                }
            }

            .selected-classplan-item {
                font-size: 0.9375rem;
                color: $body-text;
                margin: 0 0 1.25rem 0;

                .selected-classplan-item-text {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .classplan-default-text {
                margin-top: 2rem;
            }

            .fi-accordion-item {
                .references {
                    margin-bottom: 1rem;
                }
            }
        }
    }
}

.classplan-detail-content {
    padding-top: 1rem;
    line-height: 1.4375rem;
    color: $body-text;
    font-size: 0.875rem;
    max-height: 21rem;
    overflow-y: scroll;

    @container (max-width: #{$xl}) {
        max-height: 19rem;
    }

    &:hover {
        &::-webkit-scrollbar-thumb {
            display: block;
        }
    }

    &::-webkit-scrollbar {
        width: 0.625rem;
        background-color: transparent;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
        width: 0.25rem;
    }

    &::-webkit-scrollbar-thumb {
        background-color: $the-Ds;
        border-radius: 0.3125rem;
        width: 0.25rem;
        border: 0.1875rem solid transparent;
        background-clip: padding-box;
        display: none;
    }

    .classplan-detail-header {
        font-weight: 700;
    }
}