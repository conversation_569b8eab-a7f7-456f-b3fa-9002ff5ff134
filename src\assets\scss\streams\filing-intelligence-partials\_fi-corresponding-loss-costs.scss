.corresponding-loss-costs {
    padding-top: 1.0625rem;
    line-height: 1.4375rem;
    color: $body-text;
    font-size: 0.875rem;

    .hint-text {
        font-size: 0.8125rem;
        color: $body-text;
    }

    .select-corresponding-loss-costs {
        font-size: 0.9375rem;
        font-weight: 500;
        color: $default-link;
        padding-bottom: 0.6875rem;
        border-bottom: 0.0625rem dotted $the-Ds;
        margin-bottom: 1.0625rem;

        // unselected and hovered option in dropdown list
        .css-d7l1ni-option {
            background-color: $white;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;

            &:hover {
                background-color: $background-lt-blue;
                color: $dark-blue-hover;
                cursor: pointer;
            }
        }

        // unselected options in dropdown list
        .css-10wo9uf-option {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        // selected option in dropdown list
        .css-tr4s17-option {
            background-color: $default-link;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
        }

        // color for the dropdown svg icon
        .css-1xc3v61-indicatorContainer,
        // color for the dropdown svg icon after selecting dropdown
        .css-15lsz6c-indicatorContainer {
            color: $default-link;

            &:hover {
                color: $dark-blue-hover;
                cursor: pointer;
            }
        }

        // text color for the selected value
        .css-1dimb5e-singleValue {
            color: $default-link;

            &:hover {
                color: $dark-blue-hover;
                cursor: pointer;
            }
        }

        // selected option - initial
        .css-1gxfbx6-control,
        // selected option - after selected
        .css-tzexq5-control {
            cursor: pointer;
        }
    }

    .loss-costs-selected-value {
        font-weight: 500;
    }

    .loss-costs-detail {
        padding-top: 1rem;

        .loss-costs-value {
            font-weight: 500;
        }

        .loss-costs-download {
            padding-top: 1rem;
        }

        .loss-costs-circular-list {
            font-weight: 500;
            padding-right: 0.5rem;
        }

        .circular-download-btn {
            color: $default-link;
            text-transform: capitalize;
            padding: 0;
            min-width: auto;

            span {
                margin-left: 0.3125rem;
            }
        }

        .circular-download-btn:not(:last-of-type) {
            margin-right: 0.9375rem;
        }

        .circular-download-btn:hover {
            color: $dark-blue-hover;
            background-color: unset;
        }
    }

    .loss-costs-text-value {
        font-weight: 500;
    }
}