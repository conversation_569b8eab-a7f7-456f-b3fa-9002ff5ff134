.topical-sections {
    display: flex;
    border-bottom: 0.0625rem dashed $border-md-grey;
    overflow: hidden;
    justify-content: space-between;

    // @container (max-width: #{$xl}) {
    //     height: 4.25rem;
    //     justify-content: space-between;
    // }

    // @container (max-width: #{$md}) {
    //     height: 3.5rem;
    //     justify-content: space-between;
    // }

    // @container (max-width: #{$sm}) {
    //     height: 4.25rem;
    //     justify-content: space-between;
    // }

    .tab-name {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
    }

    // .tab-no-lock-icon {
    //     display: flex;
    //     flex-wrap: wrap;
    //     align-items: center;
    //     justify-content: center;
    // }

    .lob-subscription-icon {
        width: 1rem;
        height: 1.125rem;
        vertical-align: middle;
        padding-bottom: 0.1875rem;
    }

    .lob-icon-hidden {
        visibility: hidden;
    }

    .topical-nav-tab {
        position: relative;
        display: flex;
        align-items: baseline;
        padding-bottom: 0.875rem;
    }

    .topical {
        box-sizing: border-box;
        font-size: 0.875rem;
        font-weight: 500;
        color: $default-link;
        text-align: center;
        line-height: normal;
        background: $white;
        z-index: 1;

        &:hover {
            color: $dark-blue-hover;
        }
    }

    .selected-detail {
        color: $body-text;
        font-weight: 700;

        &:hover {
            text-decoration: none;
        }
    }

    .overview-hr {
        width: 0.0625rem;
        height: 100%;
        border: none;
        background-color: $border-md-grey;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        will-change: transform;
    }
}

.section-one {
    justify-content: flex-start;
    margin-top: 0.9375rem;
}

.preview-document-sections {
    height: 2.125rem;
}