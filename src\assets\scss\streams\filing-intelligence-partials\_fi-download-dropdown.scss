.download-dropdown {
    position: relative;

    .dropdown-btn {
        font-size: 0.8125rem;
        color: $default-link;
        white-space: nowrap;
        font-weight: 500;

        &:hover {
            color: $dark-blue-hover;
        }

        // size of the drop down arrow at smaller resolution
        .css-i4bv87-MuiSvgIcon-root,
        // size of the drop down arrow at smaller resolution
        .css-vubbuv {
            @container (max-width: #{$md}) {
                font-size: 1.2rem;
            }
        }
    }

    .dropdown-btn-disabled {
        font-size: 0.8125rem;
        color: $border-md-grey;
        white-space: nowrap;
        font-weight: 500;
        pointer-events: none;

        &:hover {
            text-decoration: none;
        }

        // size of the drop down arrow at smaller resolution
        .css-i4bv87-MuiSvgIcon-root,
        // size of the drop down arrow at smaller resolution
        .css-vubbuv {
            @container (max-width: #{$md}) {
                font-size: 1.2rem;
            }
        }
    }

    .pointer-events-visible {
        pointer-events: visible;
    }

    .pointer-events-none {
        pointer-events: none;
        opacity: 0.5;
    }

    .dropdown-menu {
        position: absolute;
        top: 1.8rem;
        right: 0;
        background: $white;
        box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 $the-box-shadow;
        padding: 0.4375rem 0 0 0;
        z-index: 12;

        .dropdown-item {
            color: $the-6s;
            font-weight: 400;
            height: 1.575rem;
            padding: 0 0.5625rem 0 0.75rem;
            width: 100%;
            min-width: 10rem;

            &:hover {
                background: $background-lt-blue;
            }

            .dropdown-item-checkbox {
                display: none;
            }

            .dropdown-item-checkmark {
                height: 1.15rem;
                width: 1.15rem;
                overflow: hidden;
                border: 0.0625rem solid $the-6s;
                background-color: $white;
            }

            input:checked~.dropdown-item-checkmark:after {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                font-size: 0.875rem;
                font-weight: 700;
                content: '✓';
            }

            .dropdown-item-checkmark:after {
                display: none;
                color: $white;
                background-color: $default-link;
            }

            .dropdown-item-text {
                padding: 0 0.3125rem 0 0.5625rem;
                white-space: nowrap;
                font-size: 0.78rem;
            }
        }

        .dropdown-item-selected {
            font-weight: 500;
        }

        .dropdown-options-seperator {
            height: 0.0625rem;
            margin-block-start: 0.25rem;
            margin-block-end: 0.25rem;
            margin-inline-start: 0.625rem;
            margin-inline-end: 0.625rem;
            border: 0.0625rem dotted $the-Ds;
        }

        .download-now-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: auto;
            font-size: 1rem;
            color: $default-link;
            height: 2.1875rem;
            width: 100%;
            font-size: 0.8275rem;

            &:hover {
                background: $background-lt-blue;
            }
        }

        .download-dropdown-visible {
            color: $default-link;
        }

        .download-dropdown-disabled {
            color: $the-9s;
        }
    }
}