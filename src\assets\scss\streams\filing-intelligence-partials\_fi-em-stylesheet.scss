// start - this section of code is to override the HTML issue from the JSON data
// p {
//     text-indent: 0pt !important;
// }

// end - this section of code is to override the HTML issue from the JSON data

.Copyright {
    font-family: Arial;
    font-size: 9pt
}

.EMTableofContents {
    margin-top: 12pt;
    margin-bottom: 6pt;
    page-break-before: always;
    page-break-inside: avoid;
    page-break-after: avoid;
    border-bottom: 1.5pt solid #000000;
    padding-bottom: 1pt;
    font-family: Arial;
    font-size: 20pt;
    font-weight: bold
}

.EMbaseheading {
    font-family: Arial
}

.EMbasetext {
    font-family: Arial;
    font-size: 11pt
}

.EMbody1 {
    // margin-left: 36pt;
    margin-bottom: 6pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMbody2 {
    // margin-left: 45pt;
    margin-bottom: 6pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMbody3 {
    // margin-left: 54pt;
    margin-bottom: 6pt;
    line-height: 15pt;
    font-family: Aria<PERSON>;
    font-size: 11pt
}

.EMbody4 {
    // margin-left: 63pt;
    margin-bottom: 6pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMheader {
    font-family: Arial
}

.EMheading1 {
    margin-top: 12pt;
    margin-bottom: 6pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    border-bottom: 1.5pt solid #000000;
    padding-bottom: 1pt;
    font-family: Arial;
    font-size: 20pt;
    font-weight: bold
}

.EMheading2 {
    margin-top: 8pt;
    margin-bottom: 6pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 20pt;
    font-family: Arial;
    // font-size: 16pt;
    font-size: 11pt; // this section of code is to override the HTML issue from the JSON data
    font-weight: bold
}

.EMheading3 {
    margin-top: 8pt;
    // margin-left: 36pt;
    margin-bottom: 6pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    font-family: Arial;
    // font-size: 14pt
}

.EMlanguage {
    margin-right: 36pt;
    margin-left: 54pt;
    margin-bottom: 6pt;
    text-align: justify;
    line-height: 11pt;
    font-family: Arial;
    font-size: 10pt
}

.EMlist1 {
    margin-left: 54pt;
    margin-bottom: 6pt;
    text-indent: -18pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMlist2 {
    margin-left: 63pt;
    margin-bottom: 6pt;
    text-indent: -18pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMlist3 {
    margin-left: 72pt;
    margin-bottom: 6pt;
    text-indent: -18pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMquote {
    margin-right: 36pt;
    margin-left: 54pt;
    margin-bottom: 6pt;
    font-family: Arial;
    font-size: 11pt
}

.EMsectionreference {
    font-family: Arial;
    font-weight: bold
}

.EMsectiontext {
    margin-left: 0pt;
    margin-bottom: 6pt;
    line-height: 15pt;
    font-family: Arial;
    font-size: 11pt
}

.EMsectiontitle {
    margin-top: 12pt;
    margin-bottom: 6pt;
    text-align: center;
    page-break-before: always;
    page-break-inside: avoid;
    page-break-after: avoid;
    border-bottom: 1.5pt solid #000000;
    padding-bottom: 16pt;
    font-family: Arial;
    font-size: 22pt;
    font-weight: bold
}

.EMtitle {
    margin-top: 10pt;
    margin-bottom: 10pt;
    text-align: center;
    page-break-after: avoid;
    font-family: Arial;
    font-size: 24pt;
    font-weight: bold
}

.EMtocheading {
    margin: 8pt 36pt 2pt 14.4pt;
    text-indent: -14.4pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    font-family: Arial;
    font-size: 14pt
}

.EMtoctext1 {
    margin-top: 4pt;
    margin-right: 36pt;
    margin-left: 14.4pt;
    text-indent: -14.4pt;
    font-family: Arial;
    font-size: 11pt
}

.EMtoctext2 {
    margin-right: 36pt;
    margin-left: 32.4pt;
    text-indent: -14.4pt;
    font-family: Arial;
    font-size: 11pt
}

.EMtoctext3 {
    margin-right: 36pt;
    margin-left: 50.4pt;
    text-indent: -14.4pt;
    font-family: Arial;
    font-size: 11pt
}

.FilingHeader {
    margin-top: 0pt;
    line-height: normal;
    font-family: Arial
}

.Footer {
    font-family: 'Times New Roman';
    font-size: 10pt
}

.Header {
    font-family: 'Times New Roman';
    font-size: 10pt
}

.Revision {
    font-family: 'Times New Roman';
    font-size: 12pt
}

.TOC1 {
    margin: 8pt 36pt 2pt 14.4pt;
    text-indent: -14.4pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    font-family: Arial;
    font-size: 14pt
}

.TOC2 {
    margin-top: 4pt;
    margin-right: 36pt;
    margin-left: 14.4pt;
    text-indent: -14.4pt;
    font-family: Arial;
    font-size: 11pt
}

.TOC3 {
    margin-right: 36pt;
    margin-left: 32.4pt;
    text-indent: -14.4pt;
    font-family: Arial;
    font-size: 11pt
}

.TOC4 {
    margin-right: 36pt;
    margin-left: 50.4pt;
    text-indent: -14.4pt;
    font-family: Arial;
    font-size: 11pt
}

.TOC5 {
    margin-left: 48pt;
    font-family: Calibri;
    font-size: 9pt
}

.TOC6 {
    margin-left: 60pt;
    font-family: Calibri;
    font-size: 9pt
}

.TOC7 {
    margin-left: 72pt;
    font-family: Calibri;
    font-size: 9pt
}

.TOC8 {
    margin-left: 84pt;
    font-family: Calibri;
    font-size: 9pt
}

.TOC9 {
    margin-left: 96pt;
    font-family: Calibri;
    font-size: 9pt
}

.TOCHeading {
    margin-top: 12pt;
    margin-bottom: 3pt;
    page-break-after: avoid;
    font-family: 'Calibri Light';
    font-size: 16pt;
    font-weight: bold
}

.blockhd1 {
    margin-top: 4pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd2 {
    margin-top: 4pt;
    margin-left: 15.1pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd3 {
    margin-top: 4pt;
    margin-left: 30.25pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd4 {
    margin-top: 4pt;
    margin-left: 45.35pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd5 {
    margin-top: 4pt;
    margin-left: 59.75pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd6 {
    margin-top: 4pt;
    margin-left: 74.9pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd7 {
    margin-top: 4pt;
    margin-left: 90pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd8 {
    margin-top: 4pt;
    margin-left: 105.1pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blockhd9 {
    margin-top: 4pt;
    margin-left: 120.25pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.blocktext1 {
    margin-top: 4pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext2 {
    margin-top: 4pt;
    margin-left: 15.1pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext3 {
    margin-top: 4pt;
    margin-left: 30pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext4 {
    margin-top: 4pt;
    margin-left: 45.35pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext5 {
    margin-top: 4pt;
    margin-left: 59.75pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext6 {
    margin-top: 4pt;
    margin-left: 74.9pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext7 {
    margin-top: 4pt;
    margin-left: 90pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext8 {
    margin-top: 4pt;
    margin-left: 105.1pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.blocktext9 {
    margin-top: 4pt;
    margin-left: 120.25pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial
}

.center {
    margin-top: 4pt;
    text-align: center;
    line-height: 11pt;
    font-family: Arial
}

.colline {
    text-align: justify;
    line-height: 4pt;
    border-bottom: 0.75pt solid #000000;
    font-family: Arial
}

.columnheading {
    text-align: center;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.isonormal {
    margin-top: 4pt;
    line-height: 11pt;
    font-family: Arial
}

.outlinehd1 {
    margin-top: 4pt;
    margin-left: 15.1pt;
    text-indent: -15.1pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd2 {
    margin-top: 4pt;
    margin-left: 30.25pt;
    text-indent: -30.25pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd3 {
    margin-top: 4pt;
    margin-left: 45.35pt;
    text-indent: -45.35pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd4 {
    margin-top: 4pt;
    margin-left: 59.75pt;
    text-indent: -59.75pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd5 {
    margin-top: 4pt;
    margin-left: 74.9pt;
    text-indent: -74.9pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd6 {
    margin-top: 4pt;
    margin-left: 90pt;
    text-indent: -90pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd7 {
    margin-top: 4pt;
    margin-left: 105pt;
    text-indent: -105pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd8 {
    margin-top: 4pt;
    margin-left: 120pt;
    text-indent: -120pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinehd9 {
    margin-top: 4pt;
    margin-left: 135.35pt;
    text-indent: -135.35pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinetxt1 {
    margin-top: 4pt;
    margin-left: 15pt;
    text-indent: -15pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinetxt2 {
    margin-top: 4pt;
    margin-left: 30pt;
    text-indent: -30pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinetxt3 {
    margin-top: 4pt;
    margin-left: 45pt;
    text-indent: -45pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold;
    // text-indent: 0pt !important; // this section of code is to override the HTML issue from the JSON data
}

.outlinetxt4 {
    margin-top: 4pt;
    margin-left: 60pt;
    text-indent: -60pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold;
    // font-size: 11pt; // this section of code is to override the HTML issue from the JSON data
    // text-indent: 0pt !important; // this section of code is to override the HTML issue from the JSON data
}

.outlinetxt5 {
    margin-top: 4pt;
    margin-left: 75pt;
    text-indent: -75pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold;
    // font-size: 11pt; // this section of code is to override the HTML issue from the JSON data
    // text-indent: 0pt !important; // this section of code is to override the HTML issue from the JSON data
}

.outlinetxt6 {
    margin-top: 4pt;
    margin-left: 90pt;
    text-indent: -90pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinetxt7 {
    margin-top: 4pt;
    margin-left: 105pt;
    text-indent: -105pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinetxt8 {
    margin-top: 4pt;
    margin-left: 120pt;
    text-indent: -120pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.outlinetxt9 {
    margin-top: 4pt;
    margin-left: 135pt;
    text-indent: -135pt;
    text-align: justify;
    page-break-inside: avoid;
    line-height: 11pt;
    font-family: Arial;
    font-weight: bold
}

.sectiontitlecenter {
    margin-top: 4pt;
    text-align: center;
    page-break-inside: avoid;
    page-break-after: avoid;
    border-top: 0.75pt solid #000000;
    padding-top: 3pt;
    font-family: Arial;
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase
}

.sectiontitleflushleft {
    margin-top: 4pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    border-top: 0.75pt solid #000000;
    padding-top: 3pt;
    font-family: Arial;
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase
}

.tablecenter {
    margin-top: 3pt;
    text-align: center;
    line-height: 11pt;
    font-family: Arial
}

.tablejust {
    margin-top: 3pt;
    text-align: justify;
    line-height: 11pt;
    font-family: Arial
}

.tableleft {
    margin-top: 3pt;
    line-height: 11pt;
    font-family: Arial
}

.tableright {
    margin-top: 3pt;
    text-align: right;
    line-height: 11pt;
    font-family: Arial
}

.tabletext {
    margin-top: 4pt;
    line-height: 11pt;
    font-family: Arial
}

.tabletxtdecpage {
    margin-top: 3pt;
    line-height: 11pt;
    font-family: Arial;
    font-size: 9pt
}

.titleflushleft {
    margin-top: 4pt;
    page-break-inside: avoid;
    page-break-after: avoid;
    border-top: 0.75pt solid #000000;
    padding-top: 3pt;
    font-family: Arial;
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase
}

.title12 {
    text-align: center;
    page-break-inside: avoid;
    page-break-after: avoid;
    font-family: Arial;
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase
}

.title18 {
    text-align: center;
    line-height: 18pt;
    font-family: Arial;
    font-size: 18pt;
    font-weight: bold;
    text-transform: uppercase
}

span.EMbasetextChar {
    font-family: Arial;
    font-size: 11pt
}

span.EMbody1Char {
    font-family: Arial;
    font-size: 11pt
}

span.FooterChar {
    font-family: 'Times New Roman'
}

span.HeaderChar {
    font-family: 'Times New Roman'
}

span.Heading1Char {
    font-family: Arial;
    font-size: 16pt;
    font-weight: bold
}

span.Heading2Char {
    font-family: Arial;
    font-size: 14pt;
    font-weight: bold;
    font-style: italic
}

span.Heading3Char {
    font-family: Arial;
    font-size: 13pt;
    font-weight: bold
}

span.Hyperlink {
    text-decoration: underline;
    color: #0563c1;
    -aw-style-name: hyperlink
}

// EOC - awlist style for numbering list
.awlist1 {
    list-style: none;
    counter-reset: awlistcounter6_0
}

.awlist1>li:before {
    content: '(' counter(awlistcounter6_0) ')';
    counter-increment: awlistcounter6_0
}

.awlist2 {
    list-style: none;
    counter-reset: awlistcounter7_0
}

.awlist2>li:before {
    content: '(' counter(awlistcounter7_0, lower-latin) ')';
    counter-increment: awlistcounter7_0
}

.awlist3 {
    list-style: none;
    counter-reset: awlistcounter6_0 3
}

.awlist3>li:before {
    content: '(' counter(awlistcounter6_0) ')';
    counter-increment: awlistcounter6_0
}

.awlist44 {
    list-style: none;
    counter-reset: awlistcounter19_0
}

.awlist44>li:before {
    content: '(' counter(awlistcounter19_0, lower-latin) ')';
    counter-increment: awlistcounter19_0
}

.awlist45 {
    list-style: none;
    counter-reset: awlistcounter20_0 1
}

.awlist45>li:before {
    content: '(' counter(awlistcounter20_0, lower-latin) ')';
    counter-increment: awlistcounter20_0
}