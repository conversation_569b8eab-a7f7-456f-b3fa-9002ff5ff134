// formsDetails css
.forms-detail-content {
    padding-top: 1rem;
    line-height: 1.4375rem;
    color: $body-text;
    font-size: 0.875rem;
    max-height: 21rem;
    overflow-y: scroll;

    @container (max-width: #{$xl}) {
        max-height: 19rem;
    }

    &:hover {
        &::-webkit-scrollbar-thumb {
            display: block;
        }
    }

    &::-webkit-scrollbar {
        width: 0.625rem;
        background-color: transparent;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
        width: 0.25rem;
    }

    &::-webkit-scrollbar-thumb {
        background-color: $the-Ds;
        border-radius: 0.3125rem;
        width: 0.25rem;
        border: 0.1875rem solid transparent;
        background-clip: padding-box;
        display: none;
    }

    .forms-action {
        color: $red-2;
    }

    .forms-filing-id {
        padding-top: 1rem;
    }

    .forms-detail-value {
        font-weight: 500;
    }

    .forms-detail-circular-list {
        font-weight: 500;
        padding-right: 0.5rem;
    }

    .forms-preview-document {
        float: right;
        color: $default-link;

        &:hover {
            color: $dark-blue-hover;
        }
    }

    .circular-download-btn {
        color: $default-link;
        text-transform: capitalize;
        padding: 0;
        min-width: auto;

        span {
            margin-left: 0.3125rem;
        }
    }

    .circular-download-btn:not(:last-of-type) {
        margin-right: 0.9375rem;
    }

    .circular-download-btn:hover {
        color: $dark-blue-hover;
        background-color: unset;
    }

    .forms-summary-hr {
        >div:not(:last-child) {
            border-bottom: 0.05rem solid $border-md-grey;
        }
    }

    .summary-text {
        font-size: 0.875rem;
        font-weight: 700;
        margin-top: 1.4rem;
    }

    .no-summary-text {
        font-size: 0.875rem;
        // margin-top: 1.4rem;
    }
}

// main css
.forms-tab-content {
    border-top: 0.0625rem dashed $border-md-grey;
    top: -0.0625rem;
    position: relative;

    .forms-wrapper {
        display: flex;
        column-gap: 2.5rem;

        .forms-list-pane {
            width: 50%;
            min-width: calc(50% - 1.25rem);

            @container (max-width: #{$md}) {
                width: 100%;
            }
        }

        .forms-detail {
            width: 50%;
            max-width: calc(50% - 1.25rem);
            margin-top: 1.125rem;
            box-sizing: border-box;

            @container (max-width: #{$xl}) {
                margin-top: 2.5rem;
            }


            @container (max-width: #{$md}) {
                display: none;
            }

            .fi-accordion-item {
                .references:first-of-type {
                    margin-top: 2.5rem;
                }
            }

            .fi-accordion-basic {
                .references:first-of-type {
                    margin-top: 0;
                }
            }

            .forms-default-text {
                margin-top: 4.5rem;

                .forms-description {
                    span {
                        font-weight: 700;
                    }
                }
            }

            .selected-forms-item {
                font-size: 0.9375rem;
                color: $body-text;
                margin: 0 0 1.25rem 0;

                .selected-forms-item-text {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }
        }

        .fi-accordion-data {
            .forms-default-text {
                margin-top: 0;
            }

            .fi-accordion-item {
                .references {
                    margin-bottom: 1rem;
                }
            }

            .critical-update-hr {
                padding-top: 1rem;
                border-top: 0.0625rem dashed $border-md-grey;
            }

            .critical-update {
                .references {
                    margin-bottom: 0.5rem;

                    &:last-child {
                        margin-bottom: 2rem;
                    }
                }
            }
        }
    }
}