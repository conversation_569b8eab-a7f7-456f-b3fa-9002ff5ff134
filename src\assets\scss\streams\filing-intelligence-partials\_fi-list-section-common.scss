%list-section-list {
  margin-top: 0;
  margin-bottom: 0;
  border: 0.0625rem solid $border-md-grey;
  box-sizing: border-box;
  border-top: 0.125rem solid $default-link;
  box-shadow: 0 0.125rem 0.1875rem 0 $border-md-grey;
  color: $body-text;
  font-size: 0.9375rem;
  line-height: 1.25rem;
  max-height: 22rem;
  overflow-y: scroll;
  overflow: overlay;
  padding-left: 0;

  @container (max-width: #{$xl}) {
    max-height: 21rem;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      display: block;
    }
  }

  &::-webkit-scrollbar {
    width: 0.625rem;
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
    width: 0.25rem;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $the-Ds;
    border-radius: 0.3125rem;
    width: 0.25rem;
    border: 0.1875rem solid transparent;
    background-clip: padding-box;
    display: none;
  }
}

%wrapper-all-list-section {
  color: $default-link;
  line-height: normal;
  font-weight: 500;
  font-size: 0.8125rem;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0.9375rem 0 0 0;
  align-items: flex-end;
  column-gap: 1rem;

  @container (max-width: #{$md}) {
      padding-top: 0;
  }

  @container (max-width: #{$sm}) {
      padding-top: 0;
  }

  .all-list-section {
      text-align: left;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      column-gap: 0.3125rem;

      .list-section-type-text {
          color: $body-text;
          white-space: nowrap;
      }

      .select-filter-type {

          // unselected and hovered option in dropdown list
          .css-d7l1ni-option {
              background-color: $white;

              &:hover {
                  cursor: pointer;
                  background-color: $background-lt-cyan;
                  color: $dark-blue-hover;
                  font-weight: 500;
              }
          }

          // unselected and unhovered option in dropdown list
          .css-10wo9uf-option {
              &:hover {
                  color: $dark-blue-hover;
              }
          }

          // selected option in dropdown list
          .css-tr4s17-option {
              background-color: $default-link;

              &:hover {
                  cursor: pointer;
              }
          }

          // selected value container 
          .css-1fdsijx-ValueContainer,
          .react-select__value-container {
              padding: 0.125rem 0 0.125rem 0;
          }

          // dropdown arrow
          .css-1xc3v61-indicatorContainer,
          .css-15lsz6c-indicatorContainer {
              color: $default-link;
              padding: 0.125rem 0.25rem 0.125rem 0.25rem;
          }

          // dropdown control for the selected value
          .css-1gxfbx6-control,
          // dropdown control for the selected value
          .css-tzexq5-control {
              background-color: transparent;
              cursor: pointer;

              &:hover {
                  color: $dark-blue-hover;
              }
          }

          // text color for the selected value
          .css-1dimb5e-singleValue {
              color: $default-link;
              margin-left: 0;

              &:hover {
                  color: $dark-blue-hover;
              }
          }

          // menu list
          .css-1nmdiq5-menu {
              width: 10rem;
              margin-top: 0;
              border-radius: 0;
          }

          // size of the drop down arrow at smaller resolution
          .css-i4bv87-MuiSvgIcon-root,
          // size of the drop down arrow at smaller resolution
          .css-vubbuv {
              @container (max-width: #{$md}) {
                  font-size: 1.2rem;
              }
          }
      }
  }

  .list-section-download-options {
      padding-bottom: 0.33rem;

      @container (max-width: #{$md}) {
          padding-bottom: 0.45rem;
      }

  }

  .pointer-event-disabled {
      pointer-events: none;
  }

  .pointer-event-enabled {
      pointer-events: visible;
  }
}