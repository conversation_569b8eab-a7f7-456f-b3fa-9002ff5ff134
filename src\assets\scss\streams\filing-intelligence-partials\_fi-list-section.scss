.list-section-list-wrapper {

    @container (max-width: #{$md}) {
        width: auto;
    }

    .list-section-filing-id {
        font-size: 0.8125rem;
        color: $body-text;
        font-weight: 500;
        padding-top: 0.9375rem;
        display: flex;
        flex-direction: column;
        gap: 0.3rem;

        .about-filing {
            color: $default-link;
            margin-left: 0.5rem;

            &:hover {
                color: $dark-blue-hover;
                cursor: pointer;
            }

            .list-section-preview {
                display: inline-flex;

                // .eye-icons {
                //     padding-top: 0.15rem;
                //     padding-left: 0.15rem;
                // }
            }
        }
    }

    .fi-accordion-list {
        .critical-update-hr {
            padding-top: 1rem;
            border-top: 0.0625rem dashed $border-md-grey;
        }

        .critical-update {
            .fi-accordion-wrapper {
                margin-bottom: 0.5rem;

                &:last-child {
                    margin-bottom: 1rem;
                }
            }
        }
    }

    .list-section-notfiled-text {
        font-weight: 700;
        font-size: 0.90rem;
        color: $body-text;
        padding-top: 0.9375rem;
    }

    .wrapper-all-list-section {
        @extend %wrapper-all-list-section;
    }

    .list-section-list {
        @extend %list-section-list;

        summary {
            cursor: pointer;
        }

        summary::after {
            margin-right: 0.625rem;
        }

        .list-group {
            .list-grouping-header {
                border-top: none;
                height: 3rem;
                padding-left: 0.6575rem;
                font-weight: 500;

                &:hover {
                    background-color: $background-lt-cyan;
                }
            }

            .list-section-info {
                padding-left: 1.315rem;
                border-bottom: 0.0625rem solid $border-md-grey;
            }
        }

        .list-group[open] {
            border-bottom: 0.0625rem solid $border-md-grey;
        }

        .list-section-info {
            border-bottom: 0.0625rem solid $border-md-grey;
            padding: 0 0 0 0.6575rem;
            height: 3rem;

            &:hover {
                background-color: $background-lt-cyan;
                cursor: pointer;

                &:hover .list-section-number {
                    font-weight: 700;
                }

            }

            .list-section-number-title {
                flex-grow: 1;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;

                .list-section-number {
                    font-weight: 700;
                }
            }

            .list-section-preview,
            .list-section-download {
                margin: 0.4375rem;
                padding: 0.25rem;
                color: $default-link;

                &:hover {
                    color: $dark-blue-hover;
                    cursor: pointer;
                }
            }

            .list-section-hide-download {
                margin: 0.4375rem;
                padding: 0.25rem;
                visibility: hidden;
            }
        }

        .list-section-info:is(:last-child) {
            border-bottom: 0;
        }

        .selected-cursor {
            cursor: pointer;
        }

        .list-section-info.selected-item {
            background-color: $background-lt-cyan;

            .list-section-number {
                font-weight: 700;
            }

        }

        .list-section-topical {
            border-bottom: 0.0625rem solid $border-md-grey;
            padding: 0 0 0 0.6575rem;
            cursor: default;
            height: 3rem;

            .list-section-number-title {
                flex-grow: 1;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;

                .list-section-number {
                    font-weight: 700;
                }
            }

            .list-section-preview,
            .list-section-download {
                margin: 0.4375rem;
                padding: 0.25rem;
                color: $default-link;

                &:hover {
                    color: $dark-blue-hover;
                    cursor: pointer;
                }
            }

            .list-section-hide-download {
                margin: 0.4375rem;
                padding: 0.25rem;
                visibility: hidden;
            }
        }

        .list-section-topical:is(:last-child) {
            border-bottom: 0;
        }

        .list-section-topical.selected-item {
            background-color: $background-lt-cyan;

            .list-section-number {
                font-weight: 500;
            }

        }
    }

    .selected-item-content {
        padding: 1.875rem;
        border-bottom: 0.0625rem dashed $border-md-grey;

        @container (max-width: #{$sm}) {
            padding: 0.5rem;
        }

        @container (min-width: #{$md}) {
            display: none;
        }
    }
}

.list-section-not-filed {
    border-top: 0.0625rem dashed $border-md-grey;
    top: -0.0625rem;
    position: relative;
}

.empty-subject-section {
    padding-top: 1.0625rem;
    font-size: 0.9375rem;
    font-weight: 500;
    color: $body-text;
}