.revision-filter-wrapper {
    border: 0.0625rem solid $border-md-grey;
    border-left: 0;
    border-right: 0;
    background-color: $white;
    box-sizing: border-box;
    box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 $the-box-shadow-1;
    margin-bottom: 2.5rem;
    padding: 0 2.5%;

    @container (max-width: #{$sm}) {
        border-radius: 0;
        border: none;
        padding: 1rem 0 1rem 0;
        margin: 0;
        box-shadow: none;
    }

    .revision-filter-header {
        font-weight: 600;
        margin: 1rem 0 1rem 1rem;

        @container (max-width: #{$sm}) {
            margin-top: 0.5rem;
        }

        @container (max-width: 42rem) {
            flex-wrap: wrap;
        }

        .content-tab-arrow-wrapper {
            width: 10%;
            display: flex;
            margin-top: 0.15rem;
            margin-left: 0.5rem;

            .content-tab-arrow {
                display: flex;
                justify-content: center;
                margin: auto;
                width: 4.25rem;

                .expand-less-icon {
                    width: 1.25rem;
                    height: 1.25rem;
                }

                .expand-more-icon {
                    width: 1.25rem;
                    height: 1.25rem;
                }
            }
        }
    }

    .revision-filter-section {
        margin: 0 1rem 2rem 1rem;
        justify-content: space-between;

        .revision-filter-dropdown {

            .revision-filter-selectdropdown {
                display: flex;
                flex-wrap: wrap;
                width: 100%;
            }

            .revision-filter-dropdownsubmit {
                display: flex;
                flex-direction: column-reverse;
                margin-top: 1rem;
            }

            .revision-filter-label {
                margin-bottom: 0.15rem;
                font-weight: 500;
            }

            .revision-filter-subLabel {
                font-size: 0.75rem;
                color: $the-9s;
                margin-bottom: 0.15rem;
            }

            // unselected and hovered option in dropdown list
            .css-d7l1ni-option {
                background-color: $white;

                &:hover {
                    color: $dark-blue-hover;
                    cursor: pointer;
                    background-color: $background-lt-blue;
                    font-weight: 500;
                }
            }

            .css-1nmdiq5-menu {
                color: $default-link;
            }

            // selected option in dropdown list
            .css-tr4s17-option {
                background-color: $default-link;
            }

            // padding of each state
            .css-1fdsijx-ValueContainer {
                padding: 0.125rem 0.25rem 0.125rem 0.25rem;
            }

            // selected state at the top
            .css-1gxfbx6-control,
            // selected state at the top
            .css-tzexq5-control {
                background-color: transparent;
                cursor: pointer;
            }

            // text color of the selected value
            .css-1dimb5e-singleValue {
                z-index: 1;
                color: $default-link;

                &:hover {
                    color: $dark-blue-hover;
                    cursor: pointer;
                }
            }

            // color for input text
            .css-qbdosj-Input,
            // color for input text
            .css-166bipr-Input {
                color: $default-link;

                &:hover {
                    cursor: pointer;
                    color: $dark-blue-hover;
                }
            }

            // dropdown list for states
            .css-syji7d-Group {
                padding-top: 0;
                padding-bottom: 0;
            }

            // height of dropdown menu
            .css-tjinjn-MenuList {
                max-height: 10.25rem;

                // color of disabled lob
                .css-nkt5xy-option {
                    color: $the-9s;
                }

                // color and size of the text for non disabled lob
                .css-i4bv87-MuiSvgIcon-root {
                    font-size: 1rem;
                    position: relative;
                    top: 0.075rem;
                    right: 0.0625rem;
                    margin-right: 0.1875rem;
                }
            }

            //to fix lock icon size and height of dropdown in test environment
            .css-9dakgz {
                max-height: 10.3rem;
                padding-top: 0;
                padding-bottom: 0;

                // color and size of the text for non disabled lob
                .css-1s9izoc:nth-child(2) {
                    border-top: 0.0625rem solid $border-md-grey;
                    padding-top: 0;
                }

                // color of disabled lob
                .css-nkt5xy-option {
                    color: $the-9s;
                }

                //to fix the postion of lock icon in baseline
                .css-vubbuv {
                    font-size: 1rem;
                    position: relative;
                    top: 0.07rem;
                    right: 0.0625rem;
                    margin-right: 0.1875rem;
                }
            }

            //the scrollbar on hover
            :hover {
                &::-webkit-scrollbar-thumb {
                    display: block;
                }
            }

            //css for the scrollbar
            ::-webkit-scrollbar {
                width: 0.625rem;
                background-color: transparent;
            }

            //css for the scrollbar-width
            ::-webkit-scrollbar-track {
                background-color: transparent;
                width: 0.25rem;
            }

            //css for the scrollbar-moveable color
            ::-webkit-scrollbar-thumb {
                background-color: $the-Ds;
                border-radius: 0.3125rem;
                width: 0.25rem;
                border: 0.1875rem solid transparent;
                background-clip: padding-box;
            }
        }

        .revision-filter-button {
            width: 50%;
            margin-top: 3rem;
        }

        .lob-filter {
            min-width: 16rem;
            margin-right: 2rem;

            @container (max-width: #{$md}) {
                min-width: 18rem;
            }
        }

        .revision-filter-show-result {
            background: $yellow-1;
            color: $black;
            font-family: Roboto;
            font-weight: 500;
            font-size: 0.9375rem;
            height: 2.25rem;
            vertical-align: middle;
            border: none;
            border-radius: 0.125rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.149);
            letter-spacing: .05em;
            text-decoration: none;
            padding: 1.25rem;
            text-align: center;
            line-height: 0;
        }

        .revision-filter-reult-disabled {
            background: $bg-grey-disabled;
            color: $the-6s;
            font-family: Roboto;
            font-weight: 500;
            font-size: 0.9375rem;
            height: 2.25rem;
            vertical-align: middle;
            border: none;
            border-radius: 0.125rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.149);
            letter-spacing: .05em;
            text-decoration: none;
            padding: 1.25rem;
            text-align: center;
            line-height: 0;
        }

        .revision-filter-description {
            border-radius: 0.3125rem;
            border: 0.0625rem solid $border-md-grey;
            background-color: $white;
            box-sizing: border-box;
            box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 $the-box-shadow-1;
            margin: 0 2%;
            width: 50%;
            padding: 1.5rem;

            .filing-set-info-header {
                font-weight: 500;
            }

            @container (max-width: #{$md}) {
                display: none;
            }
        }
    }
}