// lossCostsDetails css
.loss-costs-detail-content {
    padding-top: 1rem;
    line-height: 1.4375rem;
    color: $body-text;
    font-size: 0.875rem;
    max-height: 21rem;
    overflow-y: scroll;

    @container (max-width: #{$xl}) {
        max-height: 19rem;
    }

    &:hover {
        &::-webkit-scrollbar-thumb {
            display: block;
        }
    }

    &::-webkit-scrollbar {
        width: 0.625rem;
        background-color: transparent;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
        width: 0.25rem;
    }

    &::-webkit-scrollbar-thumb {
        background-color: $the-Ds;
        border-radius: 0.3125rem;
        width: 0.25rem;
        border: 0.1875rem solid transparent;
        background-clip: padding-box;
        display: none;
    }

    .loss-costs-action {
        color: $red-2;
    }

    .loss-costs-filing-id {
        padding-top: 1rem;
    }

    .loss-costs-detail-value {
        font-weight: 500;
    }

    .loss-costs-detail-circular-list {
        font-weight: 500;
        padding-right: 0.5rem;
    }

    .circular-download-btn {
        color: $default-link;
        text-transform: capitalize;
        padding: 0;
        min-width: auto;

        span {
            margin-left: 0.3125rem;
        }
    }

    .circular-download-btn:not(:last-of-type) {
        margin-right: 0.9375rem;
    }

    .circular-download-btn:hover {
        color: $dark-blue-hover;
        background-color: unset;
    }

    .summary-text {
        font-size: 0.875rem;
        font-weight: 700;
        margin-top: 1.4rem;
    }

    .no-summary-text {
        font-size: 0.875rem;
        // margin-top: 1.4rem;
    }
}

// main css
.loss-costs-tab-content {
    border-top: 0.0625rem dashed $border-md-grey;
    top: -0.0625rem;
    position: relative;

    .loss-costs-wrapper {
        display: flex;
        column-gap: 2.5rem;

        .loss-costs-list-pane {
            width: 50%;

            @container (max-width: #{$md}) {
                width: 100%;
            }
        }

        .loss-costs-detail {
            width: 50%;
            margin-top: 0.5375rem;
            box-sizing: border-box;

            @container (max-width: #{$xl}) {
                margin-top: 1.875rem;
            }

            @container (min-width: #{$md}) and (max-width: $lxl) {
                margin-top: 1.875rem;
            }

            @container (max-width: #{$md}) {
                display: none;
            }

            .fi-accordion-item {
                .references:first-of-type {
                    margin-top: 2.5rem;
                }
            }

            .fi-accordion-basic {
                .references:first-of-type {
                    margin-top: 0;
                }
            }

            .loss-costs-default-text {
                margin-top: 4.5rem;

                .loss-costs-description {
                    span {
                        font-weight: 700;
                    }
                }
            }

            .selected-loss-costs-item {
                font-size: 0.9375rem;
                color: $body-text;
                margin: 0 0 1.25rem 0;

                .selected-loss-costs-item-text {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }
        }

        .fi-accordion-data {
            @container (max-width: #{$md}) {
                display: none;
            }

            .loss-costs-default-text {
                margin-top: 4.5rem;

                .loss-costs-description {
                    span {
                        font-weight: 700;
                    }
                }
            }

            .selected-loss-costs-item {
                font-size: 0.9375rem;
                color: $body-text;
                margin: 0 0 1.25rem 0;

                .selected-loss-costs-item {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .loss-costs-default-text {
                margin-top: 0;
            }

            .fi-accordion-item {
                .references {
                    margin-bottom: 1rem;
                }
            }

            .critical-update-hr {
                padding-top: 1rem;
                border-top: 0.0625rem dashed $border-md-grey;
            }

            .critical-update,
            .fi-accordion-item {
                .references {
                    margin-bottom: 0.5rem;

                    &:last-child {
                        margin-bottom: 2rem;
                    }
                }
            }
        }
    }
}