.entitlement-subscription-container {
    .not-subscribed {
        display: flex;
        column-gap: 1.25rem;
        flex-direction: row;

        @container (max-width: #{$md}) {
            flex-direction: column;
        }

        .not-subscribed-first {
            width: 50%;

            p {
                font-size: 0.85rem;
            }

            @container (max-width: #{$md}) {
                flex-direction: column;
                width: 100%;
            }

            .not-subscribed-title {
                color: $body-text;
                font-weight: 500;
                margin-top: 0.625rem;
                margin-bottom: 1.125rem;
                font-size: 0.9375rem;
            }
        }

        .not-subscribed-last {
            width: 50%;

            p {
                font-size: 0.85rem;
            }

            @container (max-width: #{$md}) {
                flex-direction: column;
                width: 100%;
            }
        }

        .no-split {
            width: 100%;
            margin-top: 0.625rem;

            p {
                font-size: 0.85rem;
            }

            @container (max-width: #{$md}) {
                flex-direction: column;
                width: 100%;
            }

            .not-subscribed-title {
                color: $body-text;
                font-weight: 500;
                margin-top: 0.625rem;
                margin-bottom: 1.125rem;
                font-size: 0.9375rem;
            }
        }
    }
}