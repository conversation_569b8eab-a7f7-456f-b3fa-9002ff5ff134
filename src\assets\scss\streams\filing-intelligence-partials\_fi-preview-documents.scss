&.modal {
    position: fixed;
    left: 0;
    top: 0;
    min-width: 100vw;
    width: 100%;
    min-height: 100vh;
    height: 100vh;
    background-color: $black-opacity-75;
    transition-property: background-color;
    transition-duration: 300ms;
    z-index: 10;
    display: flex;
    align-items: flex-start;
    padding-top: 0.9375rem;
    padding-bottom: 0.9375rem;
    box-sizing: border-box;
    justify-content: center;
    margin: 0;
    //TO DO figure out width and height based on design
    overflow-y: auto;
    overflow-x: hidden;

    @container (min-height: 650px) {
        align-items: center;
    }

    @container (max-width: #{$md}) {
        padding: 0.5rem;
    }

    .modal-content {
        background-color: $white;
        padding: 0.5rem 1rem;
        animation-name: translateY;
        animation-duration: 0.3s;
        animation-timing-function: ease-in;
        border-radius: 0.25rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 91.67%;
        //TO DO figure out width and height based on design
        overflow-y: auto;
        overflow-x: hidden;
        max-width: 48rem;
        height: -webkit-fill-available;
        flex-grow: 1;
        flex: 1;

        .linkAnnotation {
            display: none;
        }

        /* 768px */
        .header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.25rem 0.75rem;
        }

        .header-close {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            padding: 0.25rem 0;

            @container (max-width: #{$sm}) {
                padding-right: 0;
            }

            // button.close {
            //     margin-right: 0.5rem;

            //     @container (max-width: #{$sm}) {
            //         margin-right: 0;
            //     }
            // }
        }

        button.close {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 625rem;
            /* full rounded 9999px */
            padding: 0.25rem;
            transition-property: color, background-color, border-color, text-decoration-color, fill,
                stroke, opacity, box-shadow, transform, filter, backdrop-filter;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 300ms;
            color: $light-black;
            background-color: $white;
            border-color: transparent;

            &:hover {
                background-color: rgb(249 250 251 / 0.8);
            }

            svg {
                width: 1.188rem;
                height: 1.188rem;
            }

            @container (max-width: #{$sm}) {
                padding: 0;
            }
        }

        .preview-tab-wrapper {
            position: relative;
            width: 100%;

            .download-wrapper {
                position: absolute;
                right: 0;
                top: 0.3125rem;

                .dropdown-btn {
                    @container (max-width: #{$sm}) {
                        font-size: 0.7rem;
                        top: 0.25rem;
                    }

                    // size of the drop down arrow at smaller resolution
                    .css-i4bv87-MuiSvgIcon-root,
                    // size of the drop down arrow at smaller resolution
                    .css-vubbuv {
                        @container (max-width: #{$sm}) {
                            font-size: 1.2rem;
                        }
                    }
                }

                .dropdown-btn-disabled {
                    @container (max-width: #{$sm}) {
                        font-size: 0.7rem;
                    }
                }

                .dropdown-menu {
                    @container (max-width: #{$sm}) {
                        width: 100%;
                    }

                    .dropdown-item-checkmark {
                        @container (max-width: #{$sm}) {
                            height: 1rem;
                            width: 1rem;
                        }
                    }

                    .dropdown-item-text {
                        @container (max-width: #{$sm}) {
                            font-size: 0.7rem;
                        }

                    }

                    .dropdown-item {
                        @container (max-width: #{$sm}) {
                            width: auto;
                        }
                    }

                    .download-now-btn {
                        @container (max-width: #{$sm}) {
                            height: 1.875rem;
                            font-size: 0.7rem;
                        }
                    }
                }
            }

            .tabs {
                width: 100%;

                .topical-nav-tab {
                    align-items: flex-end;
                }

                .topical-sections {
                    width: 100%;
                    justify-content: flex-start;
                    gap: 0.75rem;

                    .topical {
                        @container(max-width: #{$sm}) {
                            font-size: 0.7rem;
                        }
                    }
                }

                a {
                    cursor: pointer;
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.25rem;
                }
            }
        }

        .document-container {
            width: 100%;
            margin-top: 0.75rem;
            overflow-y: auto;
            padding-right: 0.5rem;
            height: -webkit-fill-available;

            .filing-id {
                font-size: 0.875rem;
                font-weight: 400;
                margin-top: 1rem;
            }

            .filing-title {
                font-size: 1.5rem;
                font-weight: 500;
                margin-bottom: 1rem;
            }

            .circular-download-btn {
                color: $default-link;
                text-transform: capitalize;
                padding: 0;
                min-width: auto;

                span {
                    margin-left: 0.3125rem;
                }
            }

            .circular-download-btn:not(:last-of-type) {
                margin-right: 0.9375rem;
            }

            .circular-download-btn:hover {
                color: $dark-blue-hover;
                background-color: unset;
            }

            .document {
                width: 100%;

                .page-container {
                    border-radius: 0.25rem;
                    border: 0.125rem solid rgb(209 213 219);
                    box-shadow: 0 0.0625rem 0.1875rem 0 rgb(0 0 0 / 0.1), 0 0.0625rem 0.125rem -0.0625rem rgb(0 0 0 / 0.1);
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    .page {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        // .loader {
        //     top: 40%;
        // }

        .fi-loader {
            width: auto;
            min-height: auto;
            height: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 40%;
            bottom: 30%;
            position: relative;

            span {
                border-radius: 625rem;
                /* full rounded 9999px */
                border-width: 0;
                border-top-width: 0.125rem;
                border-style: double;
                border-color: $default-link;
                height: 3rem;
                width: 3rem;
                animation: spin 1s ease-in-out infinite;
                animation-delay: 0.3s;
            }
        }

        .error {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;

            .message {
                color: $light-red;
            }
        }
    }
}

@keyframes translateY {
    from {
        transform: translateY(50%);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}