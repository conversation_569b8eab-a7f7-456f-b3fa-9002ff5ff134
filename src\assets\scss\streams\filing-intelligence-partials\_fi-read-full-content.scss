// common styles
.read-full-wrapper {
    padding-bottom: 1rem;
    padding-top: 0.5rem;

    .read-full-content {
        .read-full-eoc-label {
            display: block;
        }

        .read-full-label {
            font-weight: 700;
            font-size: 0.875rem;
        }

        td {
            border-bottom: none;
        }
    }

    .read-full-clamp {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .read-full-btn {
        color: $default-link;
        text-transform: capitalize;
        padding: 0;
        display: flex;
        flex-wrap: nowrap;
        text-align: left;

        span {
            margin-left: 0.125rem;
        }
    }

    .read-full-btn:focus {
        background: $light-blue;
    }

    .read-full-btn:hover {
        color: $dark-blue-hover;
        background-color: unset;
    }
}

.read-full-wrapper:last-child {
    border-bottom: none;
}

// details section > summary styles
.read-full-wrapper.summary {

    .read-full-content {

        p:last-of-type {
            margin-bottom: 0;
        }

        p:empty {
            display: none;
        }

        a {
            color: $body-text;
            cursor: initial;

            &:hover {
                color: $body-text;
            }
        }

        // this section of code is to override the HTML issue from the JSON data
        table {
            width: auto !important;
        }

        // this section of code is to override the HTML issue from the JSON data
        // p {
        //     margin-left: 0 !important;
        // }
    }

    .read-full-clamp {

        // this section of code is to override the HTML issue from the JSON data
        table {
            display: block;

            tbody {
                display: block;

                tr {
                    display: block;
                    height: auto !important;

                    td {
                        display: block;
                        padding-bottom: 0;
                    }
                }
            }
        }
    }
}

// details section > summary and amendment styles
.read-full-wrapper.summary-amendment {
    border-bottom: none;
    padding-bottom: 0.5rem;
    padding-top: 0.5rem;
}

// formstab > timeline styles
.read-full-wrapper.fi-timeline {
    border-bottom: none;
    padding: 0rem;

    .read-full-btn {
        font-size: 0.875rem;
    }
}