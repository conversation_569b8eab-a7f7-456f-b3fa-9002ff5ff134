.revision-wrapper {
    border-radius: 0.3125rem;
    border: 0.0625rem solid $border-md-grey;
    background-color: $bg-lt-grey;
    box-sizing: border-box;
    box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 $the-box-shadow-1;
    margin: 2.5rem 3% 2.5rem 3%;

    @container (max-width: #{$sm}) {
        border-radius: 0;
        border: none;
        padding: 1rem 0 1rem 0;
        margin: 0;
        box-shadow: none;
    }

    .revision-header {
        margin: 1.75rem 1.4375rem 0 1.4375rem;

        @container (max-width: #{$sm}) {
            margin-top: 0.5rem;
        }

        @container (max-width: 42rem) {
            flex-wrap: wrap;
        }

        .revision-title {
            font-size: 1.5rem;
            white-space: nowrap;
            color: $body-text;

            span {
                font-weight: 500;
            }
        }

        .revision-title-vertical-line {
            width: 0.0625rem;
            height: 2.3125rem;
            background-color: $border-md-grey;
            margin: 0 0.875rem 0 1.125rem;

            @container (max-width: 42rem) {
                display: none;
            }
        }

        .choose-state {
            font-size: 0.75rem;
            color: $body-text;
        }

        .state-multistate-wrapper {
            column-gap: 0.8125rem;
            row-gap: 0.625rem;

            @container (max-width: #{$md}) {
                flex-wrap: wrap;
            }

            @container (max-width: 42rem) {
                flex-wrap: nowrap;
            }

            .select-state {
                color: $default-link;
                font-weight: 500;
                min-width: 11.25rem;
                border-bottom: 0.0625rem solid $default-link;
                box-shadow: none;
                z-index: 2;

                .select-state-placeholder {
                    color: $default-link;
                }

                // unselected and hovered option in dropdown list
                .css-d7l1ni-option {
                    background-color: $white;

                    &:hover {
                        background-color: $background-lt-blue;
                        color: $dark-blue-hover;
                        cursor: pointer;
                        font-weight: 500;
                    }
                }

                // unselected and unhovered option in dropdown list
                .css-10wo9uf-option {
                    // font-weight: 700;
                }

                // selected option in dropdown list
                .css-tr4s17-option {
                    background-color: $default-link;
                }

                // padding of each state
                .css-1fdsijx-ValueContainer {
                    padding: 0.125rem 0.25rem 0.125rem 0.25rem;
                }

                // color for the dropdown svg icon
                .css-1xc3v61-indicatorContainer,
                // color for the dropdown svg icon after selecting dropdown
                .css-15lsz6c-indicatorContainer {
                    color: $default-link;
                    padding: 0.125rem 0.25rem 0.125rem 0.25rem;

                    &:hover {
                        color: $dark-blue-hover;
                        cursor: pointer;
                    }
                }

                // selected state at the top
                .css-1gxfbx6-control,
                // selected state at the top
                .css-tzexq5-control {
                    background-color: transparent;
                    cursor: pointer;
                }

                // text color of the selected value
                .css-1dimb5e-singleValue {
                    z-index: 1;
                    color: $default-link;

                    &:hover {
                        color: $dark-blue-hover;
                        cursor: pointer;
                    }
                }

                // color for input text
                .css-qbdosj-Input,
                // color for input text
                .css-166bipr-Input {
                    color: $default-link;

                    &:hover {
                        cursor: pointer;
                        color: $dark-blue-hover;
                    }
                }

                // dropdown list for states
                .css-syji7d-Group {
                    padding-top: 0;
                    padding-bottom: 0;
                }

                // height of dropdown menu
                .css-tjinjn-MenuList {
                    max-height: 10.25rem;

                    // margin line which differentiate entitled and non-entitled states
                    .css-syji7d-Group:nth-child(2) {
                        border-top: 0.0625rem solid $border-md-grey;
                    }

                    // color of nonEntitled states
                    .css-nkt5xy-option {
                        color: $the-9s;
                    }

                    // color and size of the text for entitled states
                    .css-i4bv87-MuiSvgIcon-root {
                        font-size: 1rem;
                        position: relative;
                        top: 0.075rem;
                        right: 0.0625rem;
                        margin-right: 0.1875rem;
                    }
                }

                //to fix lock icon size and height of dropdown in test environment
                .css-9dakgz {
                    max-height: 10.3rem;
                    padding-top: 0;
                    padding-bottom: 0;

                    //margin line which differentiate entitled and non-entitled states
                    .css-1s9izoc:nth-child(2) {
                        border-top: 0.0625rem solid $border-md-grey;
                        padding-top: 0;
                    }

                    //the color of the text for non-entitled states
                    .css-nkt5xy-option {
                        color: $the-9s;
                    }

                    //to fix the postion of lock icon in baseline
                    .css-vubbuv {
                        font-size: 1rem;
                        position: relative;
                        top: 0.07rem;
                        right: 0.0625rem;
                        margin-right: 0.1875rem;
                    }
                }

                //the scrollbar on hover
                :hover {
                    &::-webkit-scrollbar-thumb {
                        display: block;
                    }
                }

                //css for the scrollbar
                ::-webkit-scrollbar {
                    width: 0.625rem;
                    background-color: transparent;
                }

                //css for the scrollbar-width
                ::-webkit-scrollbar-track {
                    background-color: transparent;
                    width: 0.25rem;
                }

                //css for the scrollbar-moveable color
                ::-webkit-scrollbar-thumb {
                    background-color: $the-Ds;
                    border-radius: 0.3125rem;
                    width: 0.25rem;
                    border: 0.1875rem solid transparent;
                    background-clip: padding-box;
                    display: none;
                }
            }

            .multistate-text {
                color: $body-text;
                font-size: 0.75rem;
                width: auto;
            }
        }

        .lob-subscription {
            display: flex;
            align-items: center;

            .lob-subscription-icon {
                width: 1rem;
                height: 1.125rem;
                vertical-align: middle;
                padding-bottom: 0.1rem;
            }
        }
    }

    .revision-footnote {
        border-radius: 0;
        box-sizing: border-box;
        margin: 0 3% 1rem 12.5%;
        color: $body-text;
        font-size: 0.75rem;

        @container(max-width: #{$md}) {
            margin: 0 3% 1rem 4.5%;
        }

        @container(max-width: #{$lg}) {
            margin: 0 3% 1rem 3.5%;
        }
    }

    .content-wrapper {
        margin: 1.75rem 1.5rem 0.75rem 1.5rem;
        display: flex;

        @container (max-width: #{$lg}) {
            flex-wrap: wrap;
        }

        @container(max-width: #{$sm}) {
            margin: 1.75rem 1rem 0.75rem 1rem;
        }

        .content-date-effective {
            display: flex;
            align-items: end;
            flex-direction: column;
            row-gap: 0.375rem;
            padding-top: 0.375rem;
            min-width: 11%;

            @container (max-width: #{$lg}) {
                display: contents;
                flex-direction: row;
                gap: 0.3125rem;
                padding-top: 0;
            }

            @container(max-width: #{$md}) {
                display: contents;
            }

            @container (min-width: #{$xl}) {
                padding-top: 0;
            }

            .not-filed-state {
                font-weight: 500;
                margin-right: 0.5625rem;
                margin-left: 0.6rem;
                margin-top: 0.25rem;
                font-size: 0.875rem;
                align-items: center;
                text-align: end;
                line-height: normal;

                @container (max-width: #{$lg}) {
                    display: flex;
                    gap: 0.25rem;
                    margin: 0;
                    padding-bottom: 0.375rem;
                    padding-left: 1.3rem;
                    width: auto;
                }

                @container (max-width: #{$sm}) {
                    padding-left: 0.5rem;
                }
            }

            .content-date {
                font-weight: 500;
                margin-right: 0.5625rem;
                margin-top: 0.25rem;
                font-size: 0.875rem;
                align-items: center;
                text-align: end;
                line-height: normal;

                @container (max-width: #{$lg}) {
                    display: flex;
                    gap: 0.25rem;
                    margin: 0;
                    padding-bottom: 0.375rem;
                }
            }

            .content-effective {
                font-weight: 500;
            }

            .status-tooltip {
                position: relative;
                display: inline-block;
                border-bottom: 0.0625rem dashed $black;

                .tooltip-text {
                    visibility: hidden;
                    width: 12.25rem;
                    background-color: $white;
                    text-align: start;
                    border: 0.0625rem solid $border-md-grey;
                    font-weight: 500;
                    font-size: 0.75rem;
                    border-radius: 0.3125rem;
                    padding: 0.75rem;
                    position: absolute;
                    z-index: 100;
                    top: 170%;
                    left: 60%;
                    margin-left: -3.75rem;
                }

                .tooltip-text::before {
                    content: "";
                    position: absolute;
                    bottom: 100%;
                    left: 1.5625rem;
                    border: 0.625rem solid transparent;
                    border-bottom-color: $border-md-grey;
                }

                .tooltip-text::after {
                    content: "";
                    position: absolute;
                    bottom: 100%;
                    left: 1.625rem;
                    border: 0.5625rem solid transparent;
                    border-bottom-color: $white;
                }
            }

            .status-pending {
                border-bottom: none;
                line-height: normal;
            }

            .status-tooltip:hover .tooltip-text {
                visibility: visible;
            }

            .ellipse-wrapper {
                height: 2.3125rem;

                .content-ellipse {
                    width: 2.3125rem;
                    height: 2.3125rem;
                    padding: 0.125rem 0.125rem 0.125rem 0.125rem;
                    border: 0.0625rem solid $border-md-grey;
                    background-color: $white;
                    box-sizing: border-box;
                    box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 $the-box-shadow-1;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    @container (max-width: #{$lg}) {
                        width: 1.625rem;
                        height: 1.625rem;
                    }

                    .status-approved-icon {
                        color: $green-2;

                        @container (max-width: #{$lg}) {
                            width: 1.25rem;
                            height: 1.125rem;
                        }
                    }

                    .status-pending-icon {
                        color: $yellow-2;

                        @container (max-width: #{$lg}) {
                            width: 1.25rem;
                            height: 1.125rem;
                        }
                    }

                    .status-disapproved-icon {
                        color: $red-2;

                        @container (max-width: #{$lg}) {
                            width: 1.25rem;
                            height: 1.125rem;
                        }
                    }

                    .status-withdrawn-icon {
                        color: $red-2;

                        @container (max-width: #{$lg}) {
                            width: 1.25rem;
                            height: 1.125rem;
                        }
                    }
                }

                hr {
                    width: 1rem;
                    height: 0.0625rem;
                    background-color: $border-md-grey;
                    border: none;

                    @container (max-width: #{$lg}) {
                        width: 0.0625rem;
                        height: 0.3125rem;
                        margin: 0 auto 0 auto;
                    }
                }

                @container (max-width: #{$lg}) {
                    display: block;
                    justify-content: center;
                    height: 1.9375rem;
                }
            }
        }

        .content-filed {
            padding-top: 0.8rem;

            @container(max-width: #{$lg}) {
                display: contents;
            }

            @container(max-width: #{$md}) {
                display: contents;
            }

            .not-filed-state {
                @container (max-width: #{$lg}) {
                    padding-left: 1.3rem;
                }

                @container(max-width: #{$sm}) {
                    padding-left: 0.5rem;
                }
            }
        }

        .bureau-state-status {
            display: block;
            padding-top: 0.8rem;

            @container(max-width: #{$lg}) {
                display: contents;
            }

            @container(max-width: #{$md}) {
                display: contents;
            }

            @container(max-width: #{$sm}) {
                padding-bottom: 0;
            }

            .not-filed-state {
                @container (max-width: #{$lg}) {
                    padding-left: 1.3rem;
                }

                @container(max-width: #{$sm}) {
                    padding-left: 0.5rem;
                }
            }
        }

        .content-filing-topics {
            font-weight: 500;
            font-size: 0.875rem;
            line-height: normal;
            min-width: 11%;

            @container(min-width:#{$lg}) {
                // width: 5.625rem;
                text-align: right;
                padding-right: 0.5625rem;
                padding-top: 0.8125rem;
                // margin-right: 0.1rem;
            }


            @container (max-width: #{$lg}) {
                padding-left: 1.4rem;
                padding-bottom: 0.375rem;
            }

            @container(max-width: #{$sm}) {
                padding-left: 0.5rem;
                padding-bottom: 0.375rem;
            }
        }

        .outer-content {
            border-radius: 0.3125rem;
            border: 0.0625rem solid $border-md-grey;
            background-color: $white;
            box-sizing: border-box;
            box-shadow: 0.0625rem 0.0625rem 0.3125rem 0 $the-box-shadow-1;
            padding: 1rem 1.375rem 1rem 1.375rem;
            max-height: 45.3125rem;
            flex-grow: 1;
            min-width: 89%;

            @container (max-width: #{$md}) {
                max-height: none;
            }

            @container (max-width: #{$sm}) {
                max-height: none;
                padding: 1rem 0.5rem 1rem 0.5rem;
            }

            .content {
                display: flex;
                column-gap: 1.25rem;
                row-gap: 1rem;
                flex-direction: row;

                @container (max-width: #{$md}) {
                    flex-direction: column-reverse;
                }

                &:before {
                    content: "";
                    align-self: stretch;
                    border: 0.0625rem dotted $border-md-grey;

                    @container (min-width: #{$md}) {
                        height: 4.375rem;
                    }
                }

                .content-type-tabs {
                    order: -1;
                    width: 50%;
                    // overflow-x: scroll;
                    // overflow: overlay;

                    @container (max-width: #{$md}) {
                        width: auto;
                    }

                    .content-type-text {
                        font-size: 0.75rem;
                        font-weight: 500;
                        color: $body-text;
                    }

                    .arrowNav.prev,
                    .arrowNav.next {
                        position: absolute;
                        display: inline-block;
                        cursor: pointer;
                        background-color: $white;
                        border: thin solid $background-blue;
                        outline: none;
                        border-radius: 0.25rem;
                        height: 4rem;
                        z-index: 2;

                        &:hover {
                            background-color: $light-blue-hover;
                            color: $background-blue;
                        }
                    }

                    .prev {
                        left: 0;
                        margin-left: 0.15rem;
                    }

                    .next {
                        right: 0;
                        margin-right: 0.15rem;
                    }

                    .material-symbols-outlined {
                        font-family: 'Material Symbols Outlined';
                        font-weight: normal;
                        font-style: normal;
                        font-size: 1.5rem;
                        line-height: 1;
                        letter-spacing: normal;
                        text-transform: none;
                        display: inline-block;
                        white-space: nowrap;
                        word-wrap: normal;
                        direction: ltr;
                        -webkit-font-feature-settings: 'liga';
                        -webkit-font-smoothing: antialiased;
                    }

                    .content-tabs {
                        display: flex;
                        gap: 0.25rem;
                        margin-top: 1rem;
                        overflow: auto;
                        scroll-behavior: smooth;
                        scrollbar-width: none;
                        will-change: scroll-position;
                        -webkit-overflow-scrolling: touch;

                        @container (max-width: #{$sm}) {
                            height: 4rem;
                        }

                        .tab {
                            height: 2.8125rem;
                            border-radius: 0.1875rem;
                            background-color: $background-lt-cyan;
                            color: $default-link;
                            font-size: 0.9375rem;
                            text-align: center;
                            letter-spacing: 0.03125rem;
                            box-sizing: border-box;
                            white-space: nowrap;
                            flex-grow: 1;
                            padding: 0.875rem 0 1.875rem 0;

                            @container (max-width: #{$sm}) {
                                padding: 0.475rem 0.6rem 0.575rem 0.6rem;
                                text-wrap: wrap;
                                height: 100%;
                            }

                            &:hover {
                                border-top: 0.1875rem solid $default-link;
                                border-radius: 0;
                                padding: 0.75rem 0 1.875rem 0;

                                @container (max-width: #{$sm}) {
                                    padding: 0.385rem 0.5rem 0.485rem 0.5rem;
                                    height: 100%;
                                }
                            }

                            .tab-lock-icon {
                                @container (max-width: #{$sm}) {
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    flex-wrap: wrap;
                                }
                            }
                        }

                        .temp-tab {
                            background-color: $the-Ds;
                            color: $the-9s;

                            &:hover {
                                padding: none;
                            }
                        }

                        .business-owner {
                            white-space: wrap;

                            @container (min-width: 60.625rem) and (max-width: 75rem) {
                                padding: 0.225rem 0.225rem;

                                &:hover {
                                    padding: 0rem 0.225rem 0rem 0.225rem;
                                }
                            }

                        }

                        .selected-tab {
                            border-top: 0.1875rem solid $default-link;
                            border-left: 0.0625rem solid $border-md-grey;
                            border-right: 0.0625rem solid $border-md-grey;
                            border-radius: 0;
                            color: $body-text;
                            background-color: $white;
                            text-align: center;
                            font-size: 0.9375rem;
                            font-weight: bold;
                            z-index: 1;
                            position: relative;
                            height: 3.5rem;
                            padding: 0.75rem 0 1.875rem 0;

                            @container (max-width: #{$sm}) {
                                padding: 0.385rem 0.5rem 0.485rem 0.5rem;
                                height: 100%;
                            }

                            &.business-owner {

                                @container (min-width: 60.625rem) and (max-width: 75rem) {
                                    padding: 0.225rem;

                                    &:hover {
                                        padding: 0rem 0.225rem 0rem 0.225rem;
                                    }
                                }
                            }
                        }

                        .disabled-tab {
                            height: 2.8125rem;
                            border-radius: 0.1875rem;
                            background-color: $the-Ds;
                            color: $the-9s;
                            font-size: 0.9375rem;
                            text-align: center;
                            letter-spacing: 0.03125rem;
                            box-sizing: border-box;
                            white-space: nowrap;
                            flex-grow: 1;
                            padding: 0.875rem 0 1.875rem 0;
                        }

                        .lob-subscription-icon {
                            width: 1rem;
                            height: 1.125rem;
                            vertical-align: middle;
                            padding-bottom: 0.1875rem;
                        }
                    }
                }

                .content-description {
                    display: flex;
                    color: $body-text;
                    gap: 0.75rem;
                    width: 50%;

                    @container (min-width: #{$md}) {
                        height: 4.7em;
                    }

                    @container (max-width: #{$md}) {
                        width: 100%;
                    }

                    .content-description-item {
                        width: 90%;
                        display: flex;
                        align-items: center;

                        .content-description-item-text {
                            display: -webkit-box;
                            -webkit-line-clamp: 4;
                            -webkit-box-orient: vertical;
                            overflow: hidden;

                            span {
                                font-weight: 500;
                            }
                        }
                    }

                    .content-tab-arrow-wrapper {
                        width: 10%;
                        display: flex;

                        .content-tab-arrow {
                            display: flex;
                            justify-content: center;
                            margin: auto;
                            width: 4.25rem;

                            .expand-less-icon {
                                color: $default-link;
                                width: 2.1875rem;
                                height: 2.1875rem;
                            }
                        }
                    }
                }
            }

            .bureau-content-wrapper {
                display: flex;
                flex-direction: column;

                .bureau-content-hr {
                    border: none;
                    border-top: 0.0625rem dashed $border-md-grey;
                    width: 100%;
                    margin-top: 0.5rem;
                }

                .bureau-content {
                    display: flex;
                    width: 50%;

                    @container (max-width: #{$md}) {
                        width: 100%;
                    }

                    .bureau-content-description {
                        color: $body-text;

                        span {
                            font-weight: 500;
                        }
                    }
                }
            }
        }
    }
}