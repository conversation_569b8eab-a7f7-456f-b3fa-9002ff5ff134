.subject-tab-content {
    padding-bottom: 0.9375rem;
    border-top: 0.0625rem dashed $border-md-grey;
    top: -0.0625rem;
    position: relative;

    .subject-tab-text {
        font-weight: 500;
        color: $body-text;
        font-size: 0.8125rem;
    }

    .subject-wrapper {
        display: flex;
        padding-top: 0.9375rem;
        column-gap: 2.5rem;

        .subject-list {
            width: 50%;

            @container (max-width: #{$md}) {
                width: 100%;
            }

            .subject-all-list-section {
                @extend %wrapper-all-list-section;
            }

            .subject-section-list {
                @extend %list-section-list;

                .subject-info {
                    font-size: 0.9375rem;
                    border-bottom: 0.0625rem solid $border-md-grey;
                    padding: 0.625rem 0 0.625rem 0.4375rem;
                    font-weight: 500;

                    .subject-name {
                        color: $default-link;
                    }

                    .subject-types {
                        color: $body-text;
                        font-weight: 400;
                    }

                    &:hover {
                        background-color: $background-lt-blue;

                        .subject-name {
                            color: $dark-blue-hover;
                        }
                    }
                }

                .subject-info-selected {
                    background-color: $background-lt-blue;

                    .subject-name {
                        color: $body-text;
                        font-weight: 700;
                    }
                }

                .detail-pane {
                    padding: 1.875rem;
                    border-bottom: 0.0625rem dashed $border-md-grey;

                    @container (max-width: #{$sm}) {
                        padding: 0.5rem;
                    }

                    &:hover {
                        &::-webkit-scrollbar-thumb {
                            display: block;
                        }
                    }

                    &::-webkit-scrollbar {
                        width: 0.625rem;
                        background-color: transparent;
                    }

                    &::-webkit-scrollbar-track {
                        background-color: transparent;
                        width: 0.25rem;
                    }

                    &::-webkit-scrollbar-thumb {
                        background-color: $the-Ds;
                        border-radius: 0.3125rem;
                        width: 0.25rem;
                        border: 0.1875rem solid transparent;
                        background-clip: padding-box;
                        display: none;
                    }

                    @container (min-width: #{$md}) {
                        display: none;
                    }
                }

                .topics-list-group {
                    .topics-list-grouping-header {
                        border-top: none;
                        height: 3rem;
                        padding-left: 0.6575rem;
                        font-weight: 500;

                        &:hover {
                            background-color: $background-lt-cyan;
                        }
                    }

                    .subject-info {
                        font-weight: 400;
                        padding-left: 1.5rem;

                        .subject-name {
                            color: $body-text;
                        }
                    }

                }

                .topics-list-group[open] {
                    border-bottom: 0.0625rem solid $border-md-grey;
                }

                .topics-list-group-disabled {
                    pointer-events: none;

                    .topics-list-grouping-header {
                        color: $body-text;
                    }
                }
            }
        }

        .subject-detail {
            width: 50%;
            box-sizing: border-box;

            @container (max-width: #{$md}) {
                display: none;
            }

            .subject-default-text {
                padding-top: 4.5rem;
            }

            .selected-topic-item {
                font-size: 0.9375rem;
                color: $body-text;
                font-weight: 700;
                margin: 1rem 0 1.25rem 0;
                height: 1rem;

                .selected-topic-item-text {
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }
        }

        .subject-background-container {
            padding-top: 1.0625rem;

            .no-summary-available {
                font-size: 0.9375rem;
                color: $body-text;
                font-weight: 700;
            }

            @container (min-width: #{$md}) {
                max-height: 24rem;
                overflow-y: scroll;

                &:hover {
                    &::-webkit-scrollbar-thumb {
                        display: block;
                    }
                }

                &::-webkit-scrollbar {
                    width: 0.625rem;
                    background-color: transparent;
                }

                &::-webkit-scrollbar-track {
                    background-color: transparent;
                    width: 0.25rem;
                }

                &::-webkit-scrollbar-thumb {
                    background-color: $the-Ds;
                    border-radius: 0.3125rem;
                    width: 0.25rem;
                    border: 0.1875rem solid transparent;
                    background-clip: padding-box;
                    display: none;
                }
            }
        }
    }
}