.timeline-section {
    margin-top: 1.25rem;
    max-height: 21.5rem;
    padding-right: 1rem;
    overflow-y: scroll;
    overflow: overlay;

    @container (max-width: #{$sm}) {
        padding-right: 0.5rem;
    }

    &:hover {
        &::-webkit-scrollbar-thumb {
            display: block;
        }
    }

    &::-webkit-scrollbar {
        width: 0.625rem;
        background-color: transparent;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
        width: 0.25rem;
    }

    &::-webkit-scrollbar-thumb {
        background-color: $the-Ds;
        border-radius: 0.3125rem;
        width: 0.25rem;
        border: 0.1875rem solid transparent;
        background-clip: padding-box;
        display: none;
    }

    .timeline-no-data {
        font-size: 0.875rem;

        .timeline-no-data-highlighted {
            font-weight: 500;
        }
    }

    .timeline-item {
        display: flex;
        column-gap: 1.3125rem;
        font-size: 0.875rem;

        .timeline-date {
            font-weight: 500;
        }

        .timeline-line-wrapper {
            position: relative;
            display: flex;
            justify-content: center;

            .timeline-pointer {
                position: absolute;
                min-width: 1.125rem;
                height: 1.125rem;
                border-radius: 50%;
                border: 0.125rem solid $default-link;
                background-color: $white;
            }

            .timeline-line {
                width: 0.0938rem;
                height: 100%;
                background-color: $the-9s;
            }

            .timeline-arrow {
                display: none;
            }
        }

        .timeline-content {
            margin-bottom: 2.1875rem;
            row-gap: 0.625rem;

            .timeline-download-section {
                padding-top: 0.5rem;

                .timeline-download {
                    color: $default-link;
                    display: flex;
                    align-items: center;
                    padding: 0.375rem 0 0.125rem 0;
                    column-gap: 0.3125rem;

                    &:hover {
                        color: $dark-blue-hover;
                    }
                }

                .timeline-panel-text {
                    font-weight: 500;
                }
            }
        }
    }

    .timeline-item:first-child {

        .timeline-line-wrapper {

            .timeline-pointer {
                background-color: $default-link;
            }
        }
    }

    .timeline-item:last-child {

        .timeline-line-wrapper {

            .timeline-line {
                height: calc(100% - 0.625rem);
            }

            .timeline-arrow {
                display: flex;
                position: absolute;
                height: 100%;
                align-items: flex-end;

                .timeline-arrow-icon {
                    color: $the-9s;
                }
            }
        }
    }
}