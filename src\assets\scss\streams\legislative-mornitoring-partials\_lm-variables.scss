main.legislative-monitoring {
    .site.flex-wrapper .content-wrapper {
        max-width: unset;
    }
    .disclaimer {
        font-size: .75rem;
        display: block;
        margin-top: 2rem;
    }
    aside {
        &.thin {
            display:flex;
            flex-direction: column;
            section {
                margin-left: auto;
            }
        }
    }
    &.hub {
        .content-wrapper {

            h2:first-of-type {
                border-bottom: thin solid $border-md-grey;
            }
            form {
                padding-bottom: 2rem;
                p {
                    font-size: 1.15rem;
                }
                .flex-wrapper {
                    gap: 2rem;
                }
                .select-wrapper {
                    &:after {
                        top: .5rem;
                        right: .5rem;
                        color: $default-link;
                    }
                }
            }
        }
        #usa-map {
            width: 100%;
            height: auto;
        }
        svg {
            .annotation {
                &-state-active {
                    fill: $default-link;
                    + .annotation-line {
                        stroke: $theBs;
                        opacity: 1;
                    }
                }
                &-line {
                   opacity: 0;
                }
                &-text {
                    text-anchor: start;
                }
            }
            a[disabled="true"] {
                pointer-events: none;

                    path {
                        fill: #f0f0f0;
                        &.annotation-line {
                            display: none;
                            & + path {
                                display: none;
                            }
                        }
                    }
                    text {
                        display: none;
                    }
            }
            a:not([disabled="true"]) {
                path {
                    fill: $default-link;
                }

                &:hover {
                    + .annotation-state-active {
                        fill: $default-link-hover;
                    }
                    path {
                        fill: $default-link-hover;
                    }
                }
                
            }
            text {
                font-size:.9rem;
                font-style:normal;
                font-variant:normal;
                font-weight:500;
                font-stretch:normal;
                text-align:center;
                line-height:100%;
                writing-mode:lr-tb;
                text-anchor:middle;
                fill:$white;
                fill-opacity:1;
                stroke:none;
                stroke-width:1px;
                stroke-linecap:butt;
                stroke-linejoin:
                miter;stroke-opacity:1;
            }
        }

        svg + .flex-wrapper {
            justify-content: space-between;
        }
    }
    &.search {
        h1 {
            margin-top: 1rem;
        }
        .site.flex-wrapper {
            gap: 1rem;
            aside.thin.filter {
                width: 15rem;
            }
            .content-wrapper {
                max-width:  calc(98% - 15rem);
            }
            @media (max-width: 67.5rem) {
                flex-direction: column;
                aside.filter.thin {
                    width: 100%;
                    margin-left: 0;
                    section {
                        width: 100%;
                    }
                }
                .content-wrapper {
                    max-width: 100%;
                }
            }
        }
        
        form {
            label {
                display:flex;
                align-items: center;
                &.date-range {
                    input[type="date"] {
                        margin-left: auto;
                    }
                }
            }
        }
        .content-wrapper {
            .results-meta-sort {
                padding: 2rem 0 0;
                font-weight: 500;
            }
        }
    }
    &.detail {
        h1 + section {
            padding-top: 0;
        }
        h1 + .site.flex-wrapper {
            align-items: center;

            .glossary {
                margin-left: auto;
                .material-icons {
                    vertical-align: middle;
                }
            }
        }
        .content-wrapper {
            h2 {
                font-size: 1.7rem;
            }
            section:not(:first-of-type) {
                padding-top: 0;
            }
        }
        .overview-fields {
            align-items: flex-start;
            gap: 1rem;
            line-height: 1.8;
            justify-content: space-between;
            flex-wrap: wrap;
            .group {
                width: 30%;
                .pseudo-label {
                    width: 100%;
                }
                &:first-of-type {

                }
            }
        }
        .description {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        #showMore {
            display:none;
        }

        .toggle {
            font-size: .9rem;
            transform: none;
            box-shadow: unset;
            position: unset;
            padding:1rem 0 1.5rem 0;
            .less {
                display: none;
            }
        }

        section:has(#showMore:checked) {
            .description {
                display: block;
            }
            .toggle {
                .more {
                    display: none;
                }
                .less {
                    display:block;
                }
            }
        }

        .legend {
            font-size: .9rem;
            padding: 1rem 0;
            border-top: thin solid $border-md-grey;
            border-bottom: thin solid $border-md-grey;
            flex-wrap: wrap;
            b {
                padding-right: .5rem;
            }
            span {
                display: flex;
                padding: .25rem 0;
                i {
                    vertical-align: middle;
                    margin-right: .5rem;
                    &.filed {
                        color: #FFC600;
                    }
                    &.under-review {
                        color: $lt-body-text;
                    }
                    &.approved {
                        color: $green-1;
                    }
                    &.withdrawn, &.disapproved {
                        color: $red-1;
                    }
                }
                &:not(:last-of-type) {
                    padding-right: 1rem;
                }

            }
        }
        form {
            margin-top: 1.5rem;
            .flex-wrapper {
                flex-wrap: wrap;
                gap: 0 2rem;
                label {
                    display:flex;
                    width: 23rem;
                    padding-bottom: .5rem;
                    b {
                        font-size: 1rem;
                    }
                    a {
                        padding-left: .5rem;
                    }
                    span {
                        padding-left: .5rem;
                        &.material-icons {
                            color: $green-1;
                        }
                        &:not(.material-icons) {
                            color: $white;
                            font-weight: 500;
                            font-size: .8rem;
                            padding: .1rem .5rem;;
                            margin-left: auto;
                            background-color: $the6s;
                            line-height: 1.2;
                            align-self: center;
                            position: relative;
                        }
                    }
                    input[type="checkbox"] {
                        width: 1rem;
                        margin: 0 .5rem 0 0;
                    }
                }
                &:first-of-type {
                    gap: 1rem;
                    label {
                        width: auto;
                        margin-right: 3rem;
                        padding-bottom: 1rem;
                        b {
                            font-size: .9rem;
                        }
                    }
                }
            }
        }
        textarea {
            width: 100%;
            margin-top: 1.5rem;
        }

        .contact-info-panel {
            .flex-wrapper {
                margin-top: 1.5rem;
                gap: 3rem;
                align-items: flex-start;
            }
            img {
                height: 2rem;
            }
            ul {
                margin: 0;
                padding: 0;
                span {
                    display: block;
                    padding-bottom: .5rem;
                    font-weight: 700;
                }
                li {
                    list-style: none;
                    line-height: 1.8;
                    a {
                        font-weight: 500;
                    }
                }
            }
        }
    }
    &.glossary {
        h1 {
            font-size: 1.75rem;
            padding-bottom: 2.5rem;
        }
        h2 {
            font-size: 1.25rem;
            font-weight: 700;
            margin-top: 0;
        }
        h3 {
            font-size: 1.1rem;
            &:not(:first-of-type) {
                padding-top: .75rem;
            }
        }
        .content-wrapper {
            position: relative;
        }
        section {
            table {
                font-size: .8rem;
                max-width: 70rem;
                th {
                    font-weight: 700;
                    &.fieldname {
                        width: 15rem;
                    }
                    &.abbreviation {
                        width: 7rem;
                    }
                }
                td {
                    padding: .5rem;
                    &:first-of-type {
                        font-weight: 700;
                    }
                }
                p {
                    margin: .25rem 0;
                    font-size: .8rem;
                    &:first-of-type {
                        margin-top: 0;
                    }
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                }
                @media (max-width: 92.5rem) {
                    max-width: 100%;
                }
            }
            &:not(:last-of-type) {
                padding-bottom: 0;
            }
            &.lob-abbreviations {
                table {
                    max-width: 50rem;
                }
            }
        }
        .disclaimer {
            max-width: 65rem;
        }
        
        @media (max-width: 79.99rem) {
            .lob-abbreviations {
                display: flex;
                flex-wrap: wrap;
                h2 {
                    width: 100%;
                }
                div {
                    width: 45%;
                    &:first-of-type {
                        margin-right: 2rem;
                    }
                }
            }
        }
        @media (max-width: 42.5rem) {
            .lob-abbreviations {
                flex-direction: column;
                div {
                    width: 100%;
                    &:first-of-type {
                        margin-right: 0;
                    }
                }
            }
        }
        @media (min-width: 80rem) {
            .content-wrapper {
                display: flex;
                flex-wrap: wrap;
                h1 {
                    width: 100%;
                }
                .field-definitions {
                    width: 60%;
                    padding-right: 1.5rem;
                    padding-top: 0;
                    margin-right: 1.5rem;
                    border-right: thin solid $border-lt-grey;
                }
                .lob-abbreviations {
                    padding-top: 0;
                    width: 33%;
                }
            }
        }
        
    }

    &:not(.search) {
        @media (max-width: 67.5rem) {
            .site.flex-wrapper {
                flex-direction: column;
                aside:not(.filter) {
                    display: flex;
                    flex-direction: row;
                    align-items: flex-start;
                    width: 100%;
                    gap: 1rem;
                    section {
                        margin-left: 0;
                        &:not(:first-of-type) {
                            margin-top: 0;
                        }
                    }
                }
            }
            h1 + .site.flex-wrapper {
                align-items: flex-start;
                flex-direction: row;
            }
        }
        @media (max-width: 48rem) {
            .site.flex-wrapper {
                aside:not(.filter) {
                    flex-direction: column;
                    section {
                        width: 100%;
                    }
                }
                .overview-fields {
                    flex-direction: column;
                    gap: 0;
                    .group {
                        width: 100%;
                    }
                }
            }
        }
    }

}