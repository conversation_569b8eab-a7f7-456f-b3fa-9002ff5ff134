// Internal changes  
.hot-topics ,.major-projects {
    .cards {
        flex-wrap: wrap; // Internal
        .card {
            overflow-wrap: anywhere;
            .flex-wrapper {
                align-items: flex-start;
                gap: 1rem;
            }
            img {
                width: 10rem;
                height: 10rem;
            }
            h3 {
                padding-bottom: .25rem;
            }
            small {
                text-transform: uppercase;
                font-size: .8rem;
                font-weight: 500;
            }
            p {
                padding-top: .25rem;
                font-size: 1rem;
            }
            // Internal changes
            .paragraph-wrapper {
                padding-top: .25rem;
            }
            .card-links {
                padding-top: 1rem;
                font-size: .9rem;
                a:not(:first-of-type) {
                    padding-left: .5rem;
                    margin-left: .25rem;
                    border-left: thin solid $border-md-grey;
                }
                span.material-icons {
                    font-size: .8rem;
                    vertical-align: middle;
                }
            }
        }
    }

    @media (max-width: 78.125rem) {
        .cards {
            .card {
                .card-links {
                    a {
                        line-height: 2;
                        display:block;
                        &:not(:first-of-type) {
                            padding-left: 0;
                            margin-left: 0;
                            border-left: 0;
                        }
                    }
                }
            }
        }
    }

    @media (max-width: 48rem) {
        .cards {
            flex-direction: column;
            .card {
                width: 100%;
                img {
                    display: none;
                }
                // Internal changes
                .card-links {
                    a {
                        display: inline-block;
                        &:not(:first-of-type) {
                            padding-left: 0.5rem;
                            margin-left: 0.25rem;
                            border-left: thin solid $border-md-grey;
                        }
                    }
                }
            }
        }
    }

}