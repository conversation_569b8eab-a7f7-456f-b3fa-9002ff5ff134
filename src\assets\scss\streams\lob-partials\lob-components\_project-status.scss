section.project-status {
  padding: 0 0 1rem;

  details {
    summary {
      border: none;
      &:after {
        margin-left: 0;
      }
    }
    table {
      border-collapse: separate;
      border-spacing: 0.75rem 0;
      margin-left: -0.75rem;
      th,
      td {
        background-color: $white;
        left: 0;
        &:first-of-type {
          padding-left: 0;
        }
      }
      th {
        font-size: 0.85rem;
        border-bottom: thin dotted;
        padding: 0.44rem;
        &.status,
        &.date {
          width: 5rem;
        }
      }
      td {
        border-bottom: none;
        padding: 0.9rem 0.44rem;
        &.status,
        &.date {
          font-size: 0.85rem;
          font-weight: 500;
        }
        .material-icons {
          font-size: 0.7rem;
          font-weight: 700;
          padding: 0.1rem;
          background-color: $body-text;
          color: white;
          border-radius: 50%;
          vertical-align: middle;
          margin-right: 0.25rem;
        }
        p {
          width: 100%;
          font-size: 0.9rem;
        }
      }
    }
  }
  border-bottom: thin solid $border-md-grey;
}
