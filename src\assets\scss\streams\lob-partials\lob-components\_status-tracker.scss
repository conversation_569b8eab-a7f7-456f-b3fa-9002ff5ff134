.progress_bar {
  display: flex;

  .status {
    margin-bottom: 28px;
  }

  .statusdetails {
    font-size: 1rem;
  }

  .stepper-wrapper {
    position: relative;
    right: 7%;
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.25rem;
    min-width: 65rem;
  }

  .stepper-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    @media (max-width: 48rem) {
      font-size: 0.75rem;
    }
  }

  .stepper-item::before {
    position: absolute;
    content: "";
    border-bottom: 0.188 solid #ccc;
    width: 100%;
    top: 1rem;
    left: -50%;
    z-index: 2;
  }

  .stepper-item::after {
    position: absolute;
    content: "";
    border-bottom: 0.125rem solid #ccc;
    width: 100%;
    top: 1rem;
    left: 50%;
    z-index: 2;
  }

  .stepper-item .step-counter {
    position: relative;
    z-index: 5;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 1.875rem;
    height: 1.875rem;
    border-radius: 50%;
    background: $white;
    border: 0.125rem solid $border-md-grey;
    margin-bottom: 0.375rem;
  }

  .stepper-item.active {
    font-weight: 700;
  }

  .material-icons {
    color: $white;

    font-weight: 700;
  }

  .stepper-item.completed .step-counter {
    border: 2px solid $default-link;
    background-color: $default-link;
  }

  .stepper-item.completed .step-name {
    font-weight: 700;
  }

  .stepper-item.completed::after {
    position: absolute;
    content: "";
    border-bottom: 0.188rem solid #2480d5;
    width: 100%;
    top: 1rem;
    left: 50%;
    z-index: 3;
  }

  .step-dt {
    color: #ccc;
  }

  .stepper-item:first-child::before {
    content: none;
  }

  .stepper-item:last-child::after {
    content: none;
  }

  a {
    font-weight: 700;

    span {
      top: 6px;
      position: relative;
    }
  }
}