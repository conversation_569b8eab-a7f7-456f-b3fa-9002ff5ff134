.paas-board-bureau-topSection {
    padding-right: 0;
    width: 100%;
    max-width: 100%;

    .paas-board-bureau-title {
        color: $black;
        margin: 0.625rem 0;
        font-size: 1.25rem;
        line-height: 1.25;
        display: block;
    }

    .paas-board-bureau-details {
        font-size: 0.875rem;
    }

    .paas-board-bureau-details-item {
        margin-bottom: 0.5rem;

        .label {
            font-weight: 700;
        }

        .detail {
            display: inline-flex;
            align-items: flex-start;
            flex: 1;
            margin-left: 0.25rem;
        }
    }
}

.paas-board-bureau-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    max-width: 1140px;
    width: 100%;
}

.paas-board-bureau-content-leftCol {
    flex-basis: 100%;

    .MsoNoSpacing {
        margin: 0;
    }

    .MsoNormal {
        margin: 0;
    }

    .tabs {
        padding-bottom: 1.25rem;

        .tabbed {
            nav {
                border-bottom: 1px solid $tab-border-color;

                .tab {
                    font-weight: 400;
                    margin-bottom: -3px;
                    padding: 0.625rem;
                }

                .tab.active {
                    font-weight: 700;
                    color: $body-text;
                }
            }
        }
    }

    .tabContent {
        display: none;

        .content-label {
            margin-top: 0;
            margin-bottom: 0.375rem;
        }

        .linkLists {
            margin: 0 0 1.25rem;
            list-style: none;
            padding-left: 0;

            li {
                margin-bottom: 0.5rem;
            }
        }
    }

    .tabContent.active {
        display: inline-block;
    }
}

@media (min-width: 1081px) {
    .paas-board-bureau-topSection {
        padding-right: 1.25rem;
        max-width: 798px;
    }

    .paas-board-bureau-content {
        flex-wrap: nowrap;
    }

    .paas-board-bureau-content-leftCol {
        flex-basis: 70%;
    }
}