.paas-class-guides,
.paas-board-bureau,
.paas-educational,
.paas-legislative {
    padding-top: 0;
}

.paas-class-guides-topSection {
    padding-right: 0;
    width: 100%;
    max-width: 100%;

    .paas-class-guides-title {
        margin: 0.625rem 0;
        font-size: 1.25rem;
        line-height: 1.25;
        display: block;
    }

    .paas-class-guides-details {
        font-size: 0.875rem;
    }

    .paas-class-guides-details-item {
        margin-bottom: 0.5rem;

        .label {
            font-weight: 700;
        }

        .detail {
            display: inline-flex;
            align-items: flex-start;
            flex: 1;
            margin-left: 0.25rem;

            .has-tooltip {
                color: $body-text;
                border-bottom: thin dashed $default-link;
                position: relative;

                .tooltip {
                    padding: 0.25rem 1rem;
                    width: 12rem;
                    background: #ffffff;
                    color: $body-text;
                    box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
                    position: absolute;
                    top: 0.25rem;
                    left: 0;
                    transform: translate(calc(-50% + 2rem), 1.5rem);
                    display: none;
                    z-index: 3;
                }
            }

            .has-tooltip:hover {
                .tooltip {
                    display: block;
                }
            }

            a {
                display: inline-flex;
                align-items: center;
            }
        }
    }

    .paas-class-guides-details-item.flex {
        display: flex;
    }

    .paas-class-guides-actions {
        .share-save {
            gap: 0.625rem;
            justify-content: flex-end;
            margin-top: 1rem;
        }
    }
}

.paas-class-guides-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    max-width: 1140px;
    width: 100%;
}

.paas-class-guides-content-leftCol {
    flex-basis: 100%;

    .MsoNoSpacing {
        margin: 0;
    }

    .MsoNormal {
        margin: 0;
    }

    .tabs {
        padding-bottom: 1.25rem;

        .tabbed {
            nav {
                border-bottom: 1px solid $tab-border-color;

                .tab.active {
                    font-weight: 700;
                    color: $body-text;
                }

                .tab {
                    font-weight: 400;
                    margin-bottom: -3px;
                    padding: 0.625rem;
                }
            }
        }
    }

    .tabContent.active {
        display: inline-block;
    }

    .tabContent {
        display: none;
        width: 100%;

        .content-label {
            margin-top: 0;
            margin-bottom: 0.375rem;
        }

        .linkLists {
            margin: 0 0 1.25rem;
            list-style: none;
            padding-left: 0;

            .industry-mapping {
                &.BOPRating{
                    p{
                        align-items: end;
                        span{
                            flex-basis: 100%;
                        }
                    }
                }

                p {
                    display: flex;
                    gap: 1rem;
                    
                    span {
                        flex-basis: 20%;
                    }

                    div {
                        flex-basis: 80%;
                    }

                    span:last-child {
                        flex-basis: 80%;
                    }
                }

                a[target='_blank']:after {
                    display: none;
                }

                sup {
                    font-size: 0.8rem;
                }
            }

            li {
                margin-bottom: 0.5rem;
            }
        }

        table {
            width: unset;
        }
    }
}

.paas-class-guides-content-rightCol {
    flex-basis: 100%;
    padding-top: 1rem;

    aside {
        padding: 0;
        width: 100%;

        h2 {
            border-bottom-color: $border-lt-grey-2;
        }
    }

    aside.thin {
        section {
            margin-bottom: 1.25rem;
        }
    }
}

@media (min-width: 1081px) {
    .paas-class-guides-topSection {
        padding-right: 1.25rem;
        max-width: 798px;
    }

    .paas-class-guides-content {
        flex-wrap: nowrap;
    }

    .paas-class-guides-content-leftCol {
        flex-basis: 70%;
    }

    .paas-class-guides-content-rightCol {
        flex-basis: 30%;
    }
}