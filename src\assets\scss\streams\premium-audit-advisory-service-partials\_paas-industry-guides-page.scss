.hover-lift-cards.industry-guides {
    max-width: 80rem;

    .card {
        a {
            img {
                height: 12.5rem;
                width: 12.5rem;
            }

            p {
                -webkit-line-clamp: unset;
            }
        }

        h3 {
            margin-bottom: 0.5rem;
        }
    }

    .card:first-of-type {
        padding-left: 0;
    }

    ul {
        margin: 0;
        padding-left: 1rem;

        li {
            color: #3f3f3f;
            padding-bottom: 0.4rem;

            strong {
                font-weight: bold;
                display: inline;
            }
        }
    }
}

.industry-guides {
    flex-wrap: wrap;
    max-width: 80rem;

    .breadcrumbs {
        margin-left: 2rem;
        margin-bottom: 1.5rem;
    }

    aside.filter.thin {
        position: sticky;
        top: 0.4444444444rem;

        .radio-group {
            margin-top: 0.7777777778rem;

            fieldset {
                border: 0;
                padding: 0;

                ul {
                    margin: 0;
                    padding: 0;

                    li {
                        padding-left: 0;
                        list-style-type: none;
                        margin-bottom: 0.4444444444rem;

                        label {
                            font-size: 0.8888888889rem;
                            padding: 0.4444444444rem 0;
                        }

                        input {
                            margin-right: 0.4444444444rem;
                        }
                    }
                }
            }
        }
    }

    aside.filter {
        details {
            summary {
                margin-bottom: 0;
                padding-top: 0;

                .lines-item {
                    padding-left: 0.4rem;
                }
            }

            .btn-wrapper {
                padding: 0.2222222rem 0 0.222222rem 0.4rem;
            }

            form {
                fieldset {
                    border: none;
                    padding: 0;

                    ul {
                        list-style: none;
                        padding-left: 0;
                    }
                }
            }
        }

        .btn-wrapper {
            padding: 0.4444444444rem 0;
        }

        .btn-wrapper:hover {
            background-color: rgba(42, 125, 225, 0.15);
            font-weight: 700;
            cursor: pointer;
        }

        .btn-wrapper.selected {
            background-color: rgba(42, 125, 225, 0.15);
            font-weight: 700;
        }
    }

    .content-wrapper {
        .jurisdictional-info-content-header {
            position: sticky;
            top: -0.0555555556rem;
            background-color: #ffffff;
            padding-bottom: 0.8888888889rem;
            z-index: 2;
        }

        h2 {
            font-size: 1.9rem;
            margin: 0;
            width: 100%;
            padding-right: 1.1111111111rem;
        }

        .accordion {
            width: 100%;

            .accordionTab:first-of-type {
                border-bottom: unset;
                border-top: thin solid #efefef;
            }

            .accordionTab {
                color: #00358e;
                display: flex;
                align-items: center;
                font-weight: 500;
                border-bottom: thin solid #e5e5e5;
                padding: 0.25rem 0.5rem;
                min-height: 2.4444444444rem;
                margin: 0 0 1rem;
                margin-bottom: 0;
                font-size: 1.5rem;
                line-height: 1.25;
                width: 100%;

                h2 {
                    border-bottom: none;
                    color: #004eaa;
                    font-size: 1.5555555556rem;
                    display: inline-flex;
                    align-items: center;
                }

                span.icon {
                    transition: all 0.25s;
                    margin-left: 0.5rem;
                }
            }

            .accordionTab.active {
                span.icon {
                    transform: rotate(180deg);
                }
            }

            .accordionTab:hover {
                background-color: #e6ebf4;
                cursor: pointer;
            }

            .accordionContent {
                .flex-wrap {
                    width: 100%;

                    section {
                        h3 {
                            font-size: 1.3rem;
                        }

                        p {
                            font-size: 1rem;

                            strong {
                                p {
                                    padding: 0;
                                }
                            }
                        }

                        ul {
                            margin-left: 1rem;

                            li {
                                list-style-type: disc;
                            }
                        }

                        li {
                            font-size: 1rem;
                        }

                        .accordion {
                            width: 100%;
                            border-top: thin solid #efefef;

                            .accordionTab {
                                display: flex;
                                border-top: unset;

                                h3 {
                                    font-size: 1rem;
                                    color: #004eaa;
                                    padding-left: 0.5rem;
                                }

                                .material-symbols-outlined.icon {
                                    font-size: 1.5rem;
                                }
                            }

                            .accordionContent {
                                p {
                                    padding: 0 1rem;
                                    margin-top: 0.2rem;
                                }
                            }
                        }
                    }
                }

                section {
                    padding: 0.8888888889rem 0.5rem 0;
                    overflow-y: unset;
                    max-height: unset;

                    h3 {
                        font-size: 0.7777777778rem;
                        margin: 0 0 0.3333333333rem 0;
                    }

                    h3.bg-gray {
                        background-color: #f4f4f4;
                        padding: 0.6666666667rem 0.8888888889rem;
                    }

                    p {
                        font-size: 0.7777777778rem;
                        padding: 0 0.8888888889rem;
                    }

                    ul {
                        padding: 0 0.8888888889rem;
                    }
                }
            }
        }

        section {
            padding: 0;
            padding-right: 1.1111111111rem;

            h3:first-child {
                margin-top: 0;
            }

            h3 {
                margin: 0;
                margin-top: 1.1111111111rem;
                width: 100%;
            }

            ul {
                margin: 0;
                padding: 0;

                li {
                    font-size: 0.7777777778rem;
                    list-style-type: none;
                    margin: 0.5555555556rem 0;
                }
            }
        }
    }
}

.accordionContent {
    opacity: 0;
    max-height: 0;
    transition: all 0.3s ease-out;
    overflow: hidden;
}

.accordionContent.active {
    opacity: 1;
    display: flex;
    margin-bottom: 1rem;
    max-height: unset;
}

@media (max-width: 50rem) {

    section.hover-lift-cards {
        h2 {
            flex-direction: column;
        }

        .cards {
            article {
                img {
                    height: 10rem;
                }
            }
        }
    }

    section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards {
        margin-left: 0;

        .card {
            width: 100%;
            padding: 1rem 0;

            img {
                height: 10rem;
            }
        }
    }
}

@media (max-width: 67.5rem) {
    section {
        padding: 1.875rem;
    }

    section:not(.explore-insights):not(.hover-lift-cards):not(.hot-topics):not(.carousel) .cards {
        flex-wrap: wrap;
        justify-content: flex-start;

        .card {
            /* width: auto; */
        }
    }

    section.hover-lift-cards {
        .cards {
            flex-direction: column;
            gap: 0;

            article {
                max-width: 100%;
                min-width: 100%;
                margin: 0 auto;

                img {
                    height: 15rem;
                }
            }
        }
    }

}