.jurisdictional-info {
  flex-wrap: wrap;
  max-width: 80rem;

  aside.filter.thin {
    position: sticky;
    top: 0.4444444444rem;

        .jurisdictions input[type=text] {
      width: 100%;
      margin-bottom: 0.6666666667rem;
    }

    .jurisdictions {
      .clear-all-btn {
        margin-left: auto;
        font-size: 0.78rem;
        cursor: pointer;
        color: #004eaa;
        padding-right: 0;
      }
    }

    .radio-group {
      margin-top: 0.7777777778rem;

      fieldset {
        border: 0;
        padding: 0;

        ul {
          margin: 0;
          padding: 0;

          li {
            padding-left: 0;
            list-style-type: none;
            margin-bottom: 0.4444444444rem;

            label {
              font-size: 0.8888888889rem;
              padding: 0.4444444444rem 0;

              input:checked+span {
                font-weight: 700;
              }
            }

            input {
              margin-right: 0.4444444444rem;
            }
          }
        }

        legend {
          font-size: 0.8888888889rem;
          font-weight: 700;
          padding-top: 0.8888888889rem;
          padding-bottom: 0.4444444444rem;
        }
      }
    }

    .content-type {
      .radio-group {
        display: flex;

        label {
          flex: 0 1 75%;
        }

        input {
          flex: 0 1 25%;
        }
      }
    }

    form {
      li {
        padding-left: 1.5rem;
      }
    }

    .topic-subtopics {
      .radio-group {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        max-height: 16.9444444444rem;

        label {
          display: flex;
          gap: 0.5rem;
          padding-bottom: 0.5rem;
          align-items: flex-start;
          font-size: 0.8888888889rem;
        }

    }

            input[type=text] {
        width: 100%;
        margin-bottom: 0.6666666667rem;
      }

      .show-more-topic-subtopics {
        padding: 0.5rem 0 1rem;
        font-size: 0.9rem;
        display: block;
      }

      .show-more-topic-subtopics:target {
        display: none;
      }

      .show-less-topic-subtopics {
        padding: 0.5rem 0 1rem;
        font-size: 0.9rem;
        display: block;
        padding-top: 1rem;
      }

      .show-less-topic-subtopics {
        display: none;
      }

      .show-less-topic-subtopics:target {
        display: none;
      }

      .show-more-topic-subtopics:target+.show-less-topic-subtopics {
        display: block;
      }
    }

    .topic-subtopics:has(.show-more-topic-subtopics:target) {
      .options {
        max-height: 20rem;
        overflow-y: scroll;
      }
    }
  }

  .content-wrapper {
    .jurisdictional-info-content-header {
      position: sticky;
      top: -0.0555555556rem;
      background-color: #ffffff;
      padding-bottom: 0.8888888889rem;
      z-index: 2;

      .flex-wrapper {
        align-items: center;

        h2 {
          border-bottom: 0;
        }
      }

      .flex-wrapper:last-child {
        width: 100%;
        justify-content: flex-end;

        a {
          display: flex;
          font-size: 0.7777777778rem;
          align-items: center;

          .material-symbols-outlined {
            font-size: 1rem;
            margin-right: 0.2222222222rem;
          }

          .expand-label {
            font-size: 0.7777777778rem;
          }

          .collapse-label {
            font-size: 0.7777777778rem;
          }
        }

        a.open {
          .expand-label {
            display: none;
          }
        }

        a.closed {
          .collapse-label {
            display: none;
          }
        }
      }

      .flex-wrapper.rules-state {
        justify-content: flex-start;
        margin-top: 0.444444rem;
      }

      p {
        font-size: 0.7777777778rem;
        margin-bottom: 0.4444444444rem;
      }
    }

    .jurisdictional-info-content-header.is-pinned {
      border-bottom: thin solid #f4f4f4;
      padding: 1.7777777778rem 0 0.8888888889rem;
    }

    .rules-state-dropdown {
      width: 75%;
      margin-bottom: 0.888889rem;

      .dropdown-count {
        font-weight: 700;
        font-size: 0.666667rem;
        padding: 0.555556rem 0.666667rem;
      }

      .dropdown-content {
        padding-top: 0.666667rem;
        margin-top: 0px;
        border-top: thin solid rgb(244, 244, 244);
      }

      #rulesState {
        label {
          padding-bottom: 0.111111rem;
          margin-bottom: 0.222222rem;
          border-bottom: thin solid rgb(219, 219, 219);
        }

        ul {
          padding-left: 0.888889rem;
          margin: 0px;

          li {
            list-style-type: none;
            margin-bottom: 0.333333rem;
            font-size: 0.777778rem;
          }
        }
      }

    }

    .selected-rule.flex-wrapper {
      align-items: flex-start;
      justify-content: flex-start;
      flex-direction: column;
      padding-bottom: 0.8888888889rem;
      border-bottom: thin solid #dbdbdb;
      display: none;
      margin-bottom: 0.8888888889rem;

      a {
        font-size: 0.7777777778rem;
      }

      h4 {
        font-size: 0.8888888889rem;
        margin-top: 0;
        margin-bottom: 0.2222222222rem;
      }

      p {
        margin: 0;
      }

      .show-less {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .show-more {
        display: -webkit-box;
        -webkit-line-clamp: 15;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .effective-date {
        display: flex;
        gap: 0.5rem;

        .date-value {
          font-size: 0.8888888889rem;
          margin-top: 0;
          margin-bottom: 0.2222222222rem;
        }
      }
    }


    .selected-rule.flex-wrapper.show {
      display: flex;
    }

    h2 {
      font-size: 1.3333333333rem;
      margin: 0;
      width: 100%;
      padding-right: 1.1111111111rem;
    }

    .jurisdictional-info-skeleton {
      margin-bottom: 0.6666666667rem;

      .payroll-limitations {
        display: flex;
        flex-direction: column;
        margin-left: 0.8888888889rem;

        label {
          font-size: 0.7777777778rem;
          margin-bottom: 0.2222222222rem;
        }

        .select-wrapper {
          select {
            border: thin solid #004eaa;
            padding: 0.5rem 2rem 0.5rem 1rem;
            font-size: 0.7777777778rem;
          }
        }

        .select-wrapper::after {
          font-family: "Material Icons";
          position: absolute;
          pointer-events: none;
          content: "\e5cf";
          top: 0.5rem;
          right: 0.5rem;
          color: #004eaa;
        }
      }
    }

    .jurisdictional-info-skeleton-items {
      display: none;
      width: 100%;

      p {
        margin: 0;
        margin-bottom: 0.2222222222rem;
        width: 100%;
        font-size: 0.7777777778rem;
      }

      .selected-jurisdictions {
        align-items: flex-start;
        display: inline-flex;
        font-size: 0.7777777778rem;
        flex-wrap: wrap;

        a {
          display: flex;
          justify-content: center;
          align-items: center;
          border: thin solid transparent;
          color: #00358e;
          width: 1.7777777778rem;
          height: 1.7777777778rem;
          border-radius: 0.2222222222rem;
          margin-right: 0.4444444444rem;
        }

        a:hover {
          border: thin solid transparent;
          background-color: #e6ebf4;
          cursor: pointer;
          color: #002665;
        }

        a.focused {
          border: thin solid #00358e;
          color: #00358e;
        }
      }
    }

    .jurisdictional-info-skeleton-items.show {
      align-content: flex-start;
      display: inline-flex;
      flex-wrap: wrap;
    }

    .jurisdictional-info-skeleton-items-wrapper {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }

    section {
      padding: 0;
      padding-right: 1.1111111111rem;

      h3 {
        margin: 0;
        margin-top: 1.1111111111rem;
        width: 100%;
      }

      h3:first-child {
        margin-top: 0;
      }

      ul {
        margin: 0;
        padding: 0;

        li {
          font-size: 0.7777777778rem;
          list-style-type: none;
          margin: 0.5555555556rem 0;

          a {
            display: inline-flex;
            align-items: center;

            span {
              font-size: 0.7777777778rem;
              margin-left: 0.2222222222rem;
            }
          }
        }
      }
    }

    section.jurisdictional-state-links {
      ul {
        border-top: thin solid #dbdbdb;
      }
    }

    .accordion {

      .accordionTab {
        font-size: 1.5rem;
        line-height: 1.25;
        margin: 0 0 1rem;
        color: #00358e;
        display: flex;
        align-items: center;
        font-weight: 500;
        border-bottom: thin solid #e5e5e5;
        padding: 0.25rem 0.5rem;
        min-height: 2.4444444444rem;
        margin-bottom: 0;

        h2 {
          border-bottom: none;
          color: #004eaa;
          font-size: 1.5555555556rem;
          display: inline-flex;
          align-items: center;
        }

        span.icon {
          transition: all 0.25s;
          margin-left: 0.5rem;
        }

        .badge {
          margin-left: 0.4444444444rem;
        }

        .item-count {
          align-self: center;
          align-items: center;
          background-color: black;
          border-radius: 0.75rem;
          display: inline-flex;
          margin-left: 0.375rem;
          min-height: 0.7777777778rem;
          min-width: 0.6666666667rem;
          justify-content: center;

          small {
            color: white;
            font-size: 0.6666666667rem;
            font-weight: 700;
            padding: 0.2222222222rem 0.4444444444rem;
          }
        }
      }

      .accordionTab.active span.icon {
        transform: rotate(180deg);
      }


      .accordionTab:hover {
        background-color: #e6ebf4;
        cursor: pointer;
      }

      .accordionContent {
        opacity: 0;
        max-height: 0;
        transition: all 0.3s ease-out;
        overflow: hidden;
        display: none;

        .has-tooltip {
          border-bottom: thin dashed $body-text;
          position: absolute;

          li {
            margin-bottom: 1rem;
            white-space: normal;
          }

          .tooltip {
            padding: 0.25rem 1rem;
            overflow-wrap: break-word;
            width: 15rem;
            background: $white;
            color: $body-text;
            box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
            position: absolute;
            top: -0.5rem;
            left: 5rem;
            transform: translate(calc(-50% + 2rem), 1.5rem);
            display: none;
            z-index: 3;
            max-height: 10rem;
            overflow-y: scroll;
          }

          .tooltip::before {
            content: "";
            position: absolute;
            top: -0.625rem;
            left: calc(50% - 4.625rem);
            border-bottom: 0.625rem solid $white;
            border-left: 0.625rem solid transparent;
            border-right: 0.625rem solid transparent;
          }
        }

        .has-tooltip:hover {
          .tooltip {
            display: block;
            overflow: auto;
          }

          .tooltip::before {
            content: "";
            position: absolute;
            top: -0.625rem;
            left: calc(50% - 4.625rem);
            border-bottom: 0.625rem solid $white;
            border-left: 0.625rem solid transparent;
            border-right: 0.625rem solid transparent;
          }
        }


        .flex-wrap {
          width: 100%;
        }

        section {
          padding: 0.8888888889rem 0.5rem 0;
          overflow-y: unset;
          max-height: unset;

          ul {
            padding: 0 0.8888888889rem;
          }

          .notes {
            ul {
              padding-left: 0.8888888889rem;

              li {
                margin: 0;
                list-style-type: disc;
              }
            }
          }

          h3 {
            font-size: 0.7777777778rem;
            margin: 0 0 0.3333333333rem 0;

            .fw-normal {
              font-weight: 400;
            }
          }

          h3.bg-gray {
            background-color: #f4f4f4;
            padding: 0.6666666667rem 0.8888888889rem;
          }

          p {
            font-size: 0.7777777778rem;
            padding: 0 0.8888888889rem;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            border: thin solid #dbdbdb;
            margin-bottom: 0.4444444444rem;
            border-left: 0.1111111111rem solid #1a1a1a;
          }

          table:first-child {
            border-left: thin solid #dbdbdb;
          }

          th {
            padding: 0.4444444444rem;
            text-align: left;
            font-size: 0.7777777778rem;
            background-color: #f4f4f4;
          }

          td {
            padding: 0.4444444444rem;
            text-align: left;
            font-size: 0.7777777778rem;
          }

          thead {
            
            tr:first-child {
              th {
                font-weight: 400;
                padding: 0.4444444444rem 0.4444444444rem 0;
                border-left: thin solid #dbdbdb;
              }

              th:first-child {
                border-left: 0;
              }
            }

            tr:last-child {
              th {
                  padding: 0.4444444444rem 0.4444444444rem 0;
                  border-bottom: thin solid #dbdbdb;
                  width: 25%;
                  white-space: nowrap;
              }

              th:nth-child(odd) {
                font-weight: 700;
                border-left: thin solid #dbdbdb;
                white-space: nowrap;
              }

              th:first-child {
                border-left: 0;
              }

              th:nth-child(even) {
                font-weight: 400;
              }
            }
          }

          tbody {
            tr {
              td {
                padding-top: 0;
                padding-bottom: 0;
                border-bottom: 0;
                width: 25%;
                white-space: nowrap;
              }

              td:nth-child(odd) {
                border-left: thin solid #dbdbdb;
              }

              td:first-child {
                border-left: 0;
              }
            }

            tr:first-child {
              td {
                padding-top: 0.4444444444rem;
                white-space: nowrap;
              }
            }

            tr:nth-last-child(2) {
              td {
                padding-bottom: 0.4444444444rem;
                border-bottom: thin solid #dbdbdb;
              }
            }

            tr:last-child {
              td {
                padding: 0.2222222222rem 0.4444444444rem;
                height: 2rem;
              }
            }
          }

          .table-footnotes {
            font-size: 0.7777777778rem;
            padding: 0;
            margin: 0;
          }

          .notes {
            margin-top: 0.8888888889rem;

            p {
              margin: 0;
              padding: 0;
            }
          }
        }

        section.jurisdictional-audit-info {
          .liabilities{
            .table-wrapper {
              .table-wrapper {
                &:nth-child(1) {
                  table {
                    border-left:  0.1111111111rem solid #1a1a1a;
                  }
                }
                &:nth-child(2) {
                  table {
                    border-left: thin solid #dbdbdb;
                  }
                }
                &:nth-child(3) {
                  table {
                    border-left: thin solid #dbdbdb;
                  }
                }
              }
          }
        }
          margin-bottom: 0.8888888889rem;

          .payroll {
            margin-bottom: 1rem;
            overflow-x: auto;
            overflow-y: unset;
          }

          h3 {
            font-size: 0.8888888889rem;
            margin-bottom: 0.4444444444rem;
          }

          .table-wrapper {
            display: inline-flex;
            width: 100%;

            table {
              border-left: 0.1111111111rem solid #1a1a1a;
            }

            table:first-child {
              border-left: thin solid #dbdbdb;
            }
          }

          .table-wrapper {
            .table-wrapper {
              table {
                border-left: 0.1111111111rem solid #1a1a1a;
              }
            }

            .table-wrapper:first-child {
              table {
                border-left: thin solid #dbdbdb;
              }
            }
          }

          .table-wrapper:has(th:nth-child(6)) {
            table {
              thead {
                tr {
                  height: 4rem;
                }
              }
            }
          }

          .rate-changes {
            table {
              border-left: 0;
              border-right: 0;
            }

            table:first-child {
              border-left: thin solid #dbdbdb;
            }

            table:nth-child(2) {
              flex-basis: 100%;
            }

            table:last-child {
              border-right: thin solid #dbdbdb;
            }

            thead {
              tr:last-child {
                th {
                  height: 2rem;
                }
              }

              tr:first-child {
                th {
                  padding: 0.4444444444rem 0.4444444444rem 0;
                  border-bottom: thin solid #dbdbdb;
                  height: 3rem;
                }

                th:nth-child(odd) {
                  font-weight: 700;
                  border-left: thin solid #dbdbdb;
                }

                th:nth-child(even) {
                  font-weight: 400;
                  border-left: 0;
                }
              }
            }

            tbody {
              td:nth-child(odd) {
                border-left: thin solid #dbdbdb;
              }
            }
          }

          .table-wrapper:has(table:nth-child(2)) {
            table:nth-child(2) {
              flex-basis: 50%;
            }
          }

          .table-wrapper:has(table:nth-child(3)) {
            table:nth-child(2) {
              flex-basis: 100%;
            }
          }
        }
      }

      .accordionContent.active {
        opacity: 1;
        display: flex;
        margin-bottom: 1rem;
      }
    }

    .accordion.disabled {
      .accordionTab {
        h2 {
          color: #8c8c8c;

          .badge.info {
            background-color: #dbdbdb;
            color: #3f3f3f;
            border-radius: 0.6666666667rem;
            font-size: 0.6111111111rem;
            font-weight: 500;
          }
        }

        .icon {
          display: none;
        }
      }

      .accordionTab:hover {
        background-color: transparent;
        pointer-events: none;
      }
    }
  }

  .dropdown {
    .show {
      display: flex;
      flex-direction: column;
    }

    .dropdown-input {
      width: 100%;
      padding: 0px 0.666667rem;

      input {
        width: 100%;
        border-radius: 0.111111rem;
      }
    }
  }

  .dropdown.multiselect {
    position: relative;
    display: inline-block;
    width: 100%;
  }

  .dropdown-button {
    display: flex;
    font-size: 0.777778rem;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    min-height: 2.44444rem;
    padding: 0.222222rem 0.333333rem 0.222222rem 0.666667rem;
    border-width: thin;
    border-style: solid;
    border-color: rgb(0, 78, 170);
    border-image: initial;
  }

  .dropdown-button.is-open {
    border-bottom: transparent;

    .material-icons {
      transform: rotate(0deg);
    }
  }

  .dropdown-container {
    display: none;
    position: absolute;
    border-bottom-left-radius: 0.111111rem;
    border-bottom-right-radius: 0.111111rem;
    background-color: rgb(249, 249, 249);
    box-shadow: rgba(0, 0, 0, 0.12) 0px 0.444444rem 0.888889rem 0px;
    z-index: 1;
    max-height: 13.8889rem;
    width: 100%;
    left: 0px;
    right: 0px;
    border-width: 0px thin thin;
    border-style: solid;
    border-color: rgb(0, 78, 170);
    border-image: initial;
    padding: 0.666667rem 0px 0px;

    .dropdown-content {
      overflow-y: auto;
      margin: 0.666667rem 0px 0px;
      padding: 0px 0.666667rem;

      fieldset {
        border-width: 0px;
        border-style: initial;
        border-color: initial;
        border-image: initial;
        padding: 0px;
      }

      label {
        display: block;
        font-size: 0.777778rem;
        margin-bottom: 0.444444rem;
      }
    }
  }
}

@media (min-width: 67.5625rem) {
  .jurisdictional-info {
    .content-wrapper {
      .jurisdictional-info-skeleton-items {
        flex: 1;
      }
    }
  }
}

@media (max-width: 67.5rem) {
  &.search {
    .site.flex-wrapper {
      flex-direction: column;

      aside.filter {
        width: 100%;
        margin-left: 0;

        section {
          width: 100%;
        }
      }

      .content-wrapper {
        max-width: 100%;
        padding-top: 0;
      }
    }
  }
}

.order-1 {
order: 1;
}

.order-2 {
order: 2;
}

.order-3 {
order: 3;
}
