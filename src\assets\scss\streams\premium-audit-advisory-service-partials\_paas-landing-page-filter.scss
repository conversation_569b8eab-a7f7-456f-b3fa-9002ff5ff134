&.search {
    aside {
        section.with-show-more-filters {
            .topics-disabled {
                pointer-events: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                user-select: none;

                summary {
                    color: $lt-grey-1;
                }
            }

            .content-type {

                label:nth-of-type(n+4) {
                    display: none;
                }

                .show-less-content-type,
                .show-more-content-type {
                    padding: .5rem 0 1rem;
                    font-size: .9rem;
                    display: block;
                }

                .show-less-content-type {
                    padding-top: 1rem;
                }

                .show-less-content-type,
                .show-more-content-type:target {
                    display: none;
                }

                .show-more-content-type:target+.show-less-content-type {
                    display: block;
                    border: 0px;
                }

                &:has(.show-more-content-type:target) {
                    label:nth-of-type(n+4) {
                        display: flex;
                    }

                    .options {
                        max-height: 20rem;
                        overflow-y: scroll;
                    }

                }
            }
        }
    }

    .jurisdictional-info {
        aside.filter.thin {
            padding-top: 1.875rem;
        }
    }

    aside.filter.thin {
        margin-left: 2rem;
        padding-top: 0;
        min-width: 19.125rem;

        section {
            width: 100%;
        }

        .link-list {
            .suggestion-box {
                padding: 0.5rem 0.5rem;
                width: calc(100% - 0.30rem);
                background: #ffffff;
                box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
                position: absolute;
                z-index: 3;
                top: 5rem;
                border-bottom-left-radius: 0.5rem;
                border-bottom-right-radius: 0.5rem;
                max-height: 15rem;
                overflow-y: auto;

                .suggestion-item {
                    cursor: pointer;
                    margin-bottom: 1rem;
                }

                .suggestion-focused {
                    background-color: $border-lt-grey;
                }
            }
        }

        .link-list-disabled {
            cursor: not-allowed;
        }

        form {
            li {
                padding-left: 1.5rem;
            }
        }

        .keyword-class-codes {
            position: relative;

            .form {
                display: inline-flex;
                flex-wrap: wrap;
                width: 100%;
                position: relative;
            }

            label:first-child {
                color: $default-link;
                font-weight: 500;
                margin: 0.5rem 0;
                display: block;
            }

            input {
                padding: 0.625rem 2.5rem 0.625rem 1rem;
                border-radius: 0.5rem 0 0 0.5rem;
                border: 1px solid $border-dk-grey;
                border-right: none;
                min-height: 2.75rem;
                width: calc(100% - 2.75rem);
                color: $background-blue;
                font-size: unset;
                z-index: 4;
            }

            .suggestion-input {
                border-bottom-left-radius: 0;
                border: none;
                box-shadow: 0 0 16px rgba(0, 0, 0, 0.1);
            }

            button {
                font-size: 1.5rem;
                padding: 0.5625rem;
                color: $the6s;
                background-color: $white;
                border: 1px solid $border-dk-grey;
                border-radius: 0 0.5rem 0.5rem 0;
                margin-bottom: 1rem;
                max-width: 44px;
                min-height: 2.75rem;
                z-index: 4;
            }

            .suggestion-button {
                border-bottom-right-radius: 0;
                border: none;
                box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
            }
        }
    }
}