.results-listing {
    &.table-view {
        .scrollable {
            overflow: visible;
        }
    }

    .cards {
        .card {
            a {
                p {
                    display: block;
                }
            }
        }
    }
}

.has-tooltip {
    border-bottom: thin dashed $body-text;
    position: relative;

    .tooltip {
        padding: 0.25rem 1rem;
        overflow-wrap: break-word;
        width: 12rem;
        background: $white;
        color: $body-text;
        box-shadow: 0.08px 0 16px rgba(0, 0, 0, 0.1);
        position: absolute;
        top: 0.25rem;
        left: 0;
        transform: translate(calc(-50% + 2rem), 1.5rem);
        display: none;
        z-index: 3;
    }

    .tooltip::before {
        content: "";
        position: absolute;
        top: -0.625rem;
        left: calc(50% - 0.625rem);
        border-bottom: 0.625rem solid $white;
        border-left: 0.625rem solid transparent;
        border-right: 0.625rem solid transparent;
    }
}

.has-tooltip:hover {
    .tooltip {
        display: block;
    }

    .tooltip::before {
        content: "";
        position: absolute;
        top: -0.625rem;
        left: calc(50% - 0.625rem);
        border-bottom: 0.625rem solid $white;
        border-left: 0.625rem solid transparent;
        border-right: 0.625rem solid transparent;
    }
}

&.search {
    .no-class-code {
        font-size: 1rem;
        line-height: 1.375rem;
        font-weight: 400;

        b {
            font-size: 1rem;
            line-height: 1.375rem;
            font-weight: 600;
        }
    }

    .flex-wrapper {
        .content-wrapper {
            max-width: calc(98% - 22.125rem);
            padding-top: 1.875rem;
            width: 100%;

            .clearTags {
                display: inline-block;
                color: $dk-grey-1;
                font-family: Roboto;
                font-size: 0.95rem;
                font-style: normal;
                font-weight: 600;
                line-height: 1.5rem;
                cursor: pointer;
                margin: 0.25rem;
                padding: 0.25rem 0.5rem;
            }
        }
    }

    .results-meta-sort {
        padding: 2rem 0 0;
        gap: 2rem;
        font-size: 0.9rem;

        .tabs {
            width: unset;

            .tabbed {
                border-bottom: none;

                nav {
                    padding-top: 0;
                    margin-left: 0.5rem;

                    a.active {
                        span {
                            color: $body-text;
                            font-weight: 700;
                        }
                    }

                    span {
                        vertical-align: top;
                        color: $default-link;
                    }
                }
            }
        }
    }

    .results-listing.table-view {
        .results-table {
            table {
                min-width: unset;

                a {
                    color: $default-link;
                }

                a:hover {
                    text-decoration: none;
                }

                thead {
                    th {
                        cursor: pointer;
                    }

                    th:last-child {
                        cursor: auto;

                        a {
                            cursor: auto;
                        }
                    }

                    th:nth-of-type(2) {
                        box-shadow: none;
                        width: 40%;
                    }
                }

                tbody {
                    th {
                        font-weight: normal;
                    }

                    td:nth-of-type(1) {
                        box-shadow: none;
                    }
                }
            }
        }

    }

    .results-listing.list-view {
        border: thin solid $theEs;
        padding: 0 0.75rem;

        .card {
            p:first-child {
                color: $default-link;
            }

        }

        .card:last-child {
            border-bottom: none;
        }

    }
}