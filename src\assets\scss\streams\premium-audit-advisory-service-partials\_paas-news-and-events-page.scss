.hover-lift-cards {
    padding-top: 0;

    time {
        font-style: normal !important;
        display: block;
        color: $lt-body-text !important;
    }
}

.carousel {

    .cards {
        overflow: hidden;
        flex-wrap: nowrap;
    }

    .card {
        align-content: flex-start;
        display: flex;
        flex-wrap: wrap;
        width: 25%;
    }

    .carousel-nav {
        border-bottom: none;

        nav {
            flex-wrap: wrap;
            justify-content: flex-end;

            button {
                border: thin solid #00358e;
                color: #00358e;
                border-radius: 0.25rem;
                padding: 0.75rem 1rem;
                vertical-align: middle;
                font-weight: 600;
            }

            button:hover {
                background-color: #e6ebf4;
                color: #002665;
                border-color: #002665;
            }

            button:focus {
                outline: none;
                box-shadow: #ffffff 0 0 0 0.125rem, #002665 0 0 0 0.1875rem;
            }
        }
    }
}