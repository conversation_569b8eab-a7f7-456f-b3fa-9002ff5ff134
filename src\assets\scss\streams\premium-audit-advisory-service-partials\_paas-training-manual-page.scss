.training-manuals {
    padding-top: 0;

    .accordionTab {
        font-size: 1.5rem;
        line-height: 1.25;
        margin: 0 0 1rem;
        color: #00358e;
        display: flex;
        align-items: center;
        font-weight: 500;
        border-bottom: thin solid #e5e5e5;
        padding: 0.25rem 0.5rem;
        min-height: 2.4444444444rem;
        margin-bottom: 0;

        span.material-symbols-outlined {
            transition: all 0.25s;
            margin-left: 0.5rem;
        }
    }

    .accordionTab.active {
        span.material-symbols-outlined {
            transform: rotate(180deg);
        }
    }

    .accordionTab:hover {
        background-color: #e6ebf4;
        cursor: pointer;
    }

    .material-symbols-outlined {
        font-variation-settings: "FILL" 1, "wght" 400, "GRAD" 0, "opsz" 20;
        font-size: 1rem;
    }

    .content-wrapper {
        h2 {
            font-size: 1.9rem;
        }

        .content-container {
            margin-top: 2rem;
        }

        ul {
            padding-left: 1.5rem;

            li {
                padding-bottom: 0.5rem;
            }
        }

        .accordion {
            border-top: thin solid #efefef;
            border-bottom: thin solid #efefef;

            .accordionTab {
                padding: 0.25rem 0.5rem;
                width: 100%;
                border-bottom: unset;

                h4 {
                    font-size: 1.1rem;
                    color: #004eaa;
                    margin: 0.5rem 0;
                }
            }

            .accordionContent {
                padding-left: 0.5rem;

                .mobile {
                    display: flex;
                    margin-left: 0;
                    gap: 4rem;
                }
            }
        }
    }

    .site.flex-wrapper {
        aside {
            p {
                a {
                    text-decoration: none;
                }
            }
        }
    }
}