.paas-section-hero {
    display: flex;

    .paas-section-hero-content {
        width: 100%;

        h2 {
            font-size: 1.5rem;
            margin: 0 0 1.25rem;
        }

        p {
            margin-bottom: 0;
        }
    }

    img {
        width: 60%;
        height: auto;
    }
}

.hub-header {
    .site {
        &.flex-wrapper {
            gap: 1rem;

            span {
                text-transform: capitalize;
                padding: 0.3rem 1rem 0.3rem;
                border-radius: 5rem;
                background-color: $red-3;
                font-size: 0.7rem;
                color: $white;
                vertical-align: text-top;
                margin-top: 0.4rem;
            }
        }
    }
}

@media (min-width: 1080px) {
    .paas-section-hero {
        padding: 2.3333rem 0 1.7222rem 2rem;
        position: relative;
        overflow: hidden;
        width: 100%;
    }
}

@media (min-width: 67.5rem) {
    .paas-section-hero-content {
        width: 40%;
    }
}