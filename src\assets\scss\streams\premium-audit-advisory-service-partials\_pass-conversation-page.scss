.gen-ai-chatbox {
    display: flex;
    position: relative;
    border: 1px solid #8C8C8C;
    border-radius: 10px;
    height: 792px;
    width: 1323px;
    top: 50%;
    left: 67px;
}

.gen-ai {
    border: 1px solid #8C8C8C;
    border-radius: 10px;
}

.paas-ai-main {
    box-sizing: border-box;
    height: 800px;
    width: 120rem;
    max-width: 96%;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 10px;
    border: thin solid #8c8c8c;
    margin-left: 1.875rem;
    margin-right: 1.875rem;
    margin-bottom: 1.875rem;
    display: flex;
}

/* PaasAi Left Panel */
.paas-ai-left-panel {
    min-width: 18.75rem;
    width: 18.75rem;
    padding-top: 24px;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 679px;
    background: rgba(219, 219, 219, 0.30);
    height: inherit;
    display: inline-block;
    position: relative;
}

.paas-ai-new-conversation-button {
    display: flex;
    width: 88%;
    height: 52px;
    padding: var(--md, 12px) var(--2xl, 32px);
    justify-content: center;
    align-items: center;
    gap: var(--sm, 8px);
    border-radius: var(--xs, 4px);
    border: 1px solid var(--Button-Primary-Default, #FFC600);
    background: var(--Button-Primary-Default, #FFC600);
    margin-left: 6%;
    margin-right: 6%;
    margin-bottom: 6%;
}

.paas-ai-new-conversation-button:disabled {
    pointer-events: none !important; 
    cursor: initial !important;  
}

.paas-ai-conversation-button-plus {
    width: 25px;
    height: 28px;
    flex-shrink: 0;
    font-size: 24px;
}

.paas-ai-conversation-button-text {
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    white-space: nowrap;
}

.paas-ai-left-panel-body {
    min-width: 18.75rem;
    width: 18.75rem;
    height: 79%;    
    flex-shrink: 0;
    overflow: hidden;
    overflow-y: auto;
    position: relative;

    &::-webkit-scrollbar {
        width: 6px;
        height: 420px;
        flex-shrink: 0;
    }

    &::-webkit-scrollbar-thumb {
        width: 6px;
        height: 420px;
        flex-shrink: 0;
        background: var(--Background-Action-Disabled, #DBDBDB);
    }
}

.paas-ai-left-panel-footer {
    display: flex;
    justify-content: space-evenly;
    margin: 1rem;
}

.paas-ai-left-panel-footer-btn {
    color: var(--Text-Action-Default, #00358E);
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 200% */
}

.paas-ai-left-panel-footer-btn:disabled {
    cursor:initial;
    pointer-events: none;
    color: var(--Text-Secondary, #4D4D4D);
}

.paas-ai-tabs-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    white-space: nowrap;
    color: var(--Text-Secondary, #4D4D4D);
    font-family: Roboto, sans-serif;
    font-style: normal;
    line-height: 21px;
    padding-top: 6px;
    padding-left: 20px;
    padding-right: 25px;
    padding-bottom: 10px;    

    > div {
        font-size: 1rem;
        font-weight: 500;
    }

    > button {
        color: var(--Text-Action-Focus, #00358E);
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    > button:disabled {
        pointer-events: none; 
        cursor: initial;
        color: var(--Text-Secondary, #4D4D4D);
    }
}

.paas-ai-tabs-body {
    justify-content: space-between;
    width: 100%;
    white-space: nowrap;
    color: var(--Text-Secondary, #4D4D4D);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 21px;
    display: block;  
    position: relative;

    > div {
        margin-top: 10px;
    }
}

.avatar{
    &.user {
        font-weight: 500;
    }
}

.paas-ai-tab-container {
    > div.category {
        padding-left: 19px;
        padding-top: 5px;
    }

    > div.tab {
        height: 44px;
        flex-shrink: 0;
        background: var(--Background-Action-Disabled, #f2f2f2);
        display: flex;
        justify-content: space-between;
    
        &.active {
            background: var(--Background-Action-Disabled, #DBDBDB) !important;
            white-space: nowrap;
        }
    
        > button {
            > div {
                overflow: hidden;
                color: var(--Text-Primary, #1A1A1A);
                text-overflow: ellipsis;
                font-family: Roboto, sans-serif;
                font-size: 0.875rem;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                display: -webkit-box;
                width: 212px;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 1;
                text-align: left; 
                padding-left: 19px;
            }
        }
    
        > button:disabled {
            pointer-events: none; 
            cursor: initial;  
            color: var(--Text-Secondary, #4D4D4D);
        }

        > div.options {
            border: none;
            margin: 0;
            width: 66px;
            text-decoration: none;
            background-color: transparent;
            color: black;
            font-size: 1rem;
            text-align: center;
            transition: background 250ms ease-in-out, transform 150ms ease;
            display: inline-flex;
        
            >button {
                > span {
                    font-weight: 900 !important;
                    width: 2rem;
                    fill: #4d4d4d;
                    margin-top: 3px;
                }
            }
        
            > button:disabled {
                cursor: initial !important;
                pointer-events: none !important;
        
            }

        }
    }
}

.options-container{
    position: sticky;
    z-index: 2;
    display: inline-flex;
    padding: 11px 17px 11px 5px;    
    flex-direction: column;
    justify-content: center;
    align-items: flex-start; 
    gap: 10px;
    flex-shrink: 0;
    border-radius: 10px;
    border: 0.5px solid #DBDBDB;
    background: #FCFCFC;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.10);
    position: relative;
    right: 114px;
    width:fit-content;
    height:fit-content;

    
    &.show-below {
        top: 29px;
    }

    
    &.show-above {
        bottom: 76px;
    }

    >div {
        color: var(--Text-Primary, #1A1A1A);
        font-family: Roboto, sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        align-items: center;
        gap: 11px;
        flex-shrink: 0;
        display: inline-flex;

        >button:disabled {
            pointer-events: none; 
            cursor: initial;
        }
    }
}

/* PaasAi Middle Panel */
.paas-ai-middle-panel {
    width: 100%;
    position: relative;
    padding: 1.25rem 1.25rem 1.25rem 3.6666666667rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.paas-ai-middle-panel-conversation {
    height: 485px !important;
    width: -webkit-fill-available;
    overflow: auto;
    padding: 1rem 0;
    margin-top: 10px;
    flex-grow: 1;

    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        flex-shrink: 0;
    }

    &::-webkit-scrollbar-thumb {
        width: 6px;
        height: 420px;
        flex-shrink: 0;
        background: var(--Background-Action-Disabled, #DBDBDB);
    }
}


.paas-ai-spinner {
    height: fit-content;
    width: fit-content;
    position: relative;
    left: 50%;
    top: 60%;
}

.paas-ai-conversation-title {
    background-color: transparent;
    box-shadow: none;
    min-height: unset;
    padding: 0;
    justify-content: space-between;
    flex-wrap: wrap;
    align-content: flex-start;
    display: block;
    align-self: flex-start;
    align-items: center;
    width: 96%;
    height: 50px;
}

.paas-ai-title{
    color: #272727;
    font-size: 1rem;
    line-height: 1.25;
    margin-bottom: 5px;    
    font-weight: 500;
    height: 25px;
    overflow-y: hidden;
    overflow-x: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
}

.paas-ai-warning{
    font-size: 0.6666666667rem;
    line-height: 1.5;
    margin-block-start: 0;
}

.paas-ai-middle-panel-anchor {
    height: 1px;
}

.paas-ai-middle-panel-suggestion {
    flex-shrink: 0;

    .chip-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 10px;
    }

    .chip-button {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border: 1px solid #00358E; 
        border-radius: 16px; 
        background-color: transparent;
        color: #00358E;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        text-decoration: none;
        white-space: nowrap;

        &:hover {
            background-color: rgba(0, 74, 173, 0.1); 
        }

        &:active {
            background-color: rgba(0, 74, 173, 0.2);
        }
    }
}

.paas-ai-middle-panel-suggestion-title-container {
    display:flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 8px;

    > p {
        font-size: 16px;
        font-weight: 700;
        margin:0;
    }

    > a {
        margin-right: 22px;
        .material-icons{
            font-size: 20px;
    font-weight: 700;
        }
    }
}

.paas-ai-middle-panel-input-container-main {
    flex-shrink: 0;
}

.paas-ai-middle-panel-input-container {
    width: 98%;
    height: 44px;
    flex-shrink: 0;
    border-radius: 10px;
    border: 1px solid var(--Border-Primary, #00358E);
    background: #FFF;
    display: inline-flex;
    scroll-behavior: smooth;
    position: relative;
}

.avatar-style {
    width: 24px;
    height: 20px;
    flex-shrink: 0;
}

.paas-ai-message {
    margin-bottom: 30px;
}

.paas-ai-message.bot {
    margin-bottom: 0%;
}

.paas-ai-message-header {
    display: flex;
}

.paas-ai-message-sender {
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    display: inline-flex;
    margin-top: 3px;
    margin-bottom: 10px;
}

.paas-ai-message-sender.sources {
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    line-height: 24px;
    display: inline-flex;
    margin-bottom: 0px !important;
}

.paas-ai-message-sources{
    padding-left: 34px;
    margin-top: 15px;
}

.paas-ai-message-content {
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    padding-left: 34px;
    width: -webkit-fill-available;
    padding-top: 2px;
    padding-right: 60px;

    >div {
        color: var(--Text-Primary, #1A1A1A);
        font-family: Roboto, sans-serif;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
    }

    >li {
        color: var(--Text-Primary, #1A1A1A);
        font-family: Roboto, sans-serif;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
    }
}


@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.typing {
    opacity: 0.6;
    font-style: italic;
    animation: blink 1s infinite;
}

@keyframes blink {
    50% {
        opacity: 0.3;
    }
}


.paas-ai-message-content.greeting {
    >div {
        font-size: 40px;
        font-weight: 500;
        line-height: 50px;
        padding-top: 5px;
    }
}

.paas-ai-message-content.sources {
    padding-left: 0px;

    >div {
        font-weight: 400;
        line-height: 24px;
    }

    >a {
        color: var(--Text-Action-Focus, #00358E);
        /* Text md/Medium */
        font-family: Roboto, sans-serif;
        font-size: 1rem;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        display: table; 
    }
}

.paas-ai-conversation-starters {
    width: 600px;
    bottom: 25px;
    color: var(--Text-Primary, #1A1A1A);
    /* Text sm/Regular */
    font-family: Roboto, sans-serif;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 175%;
    margin-left: 32px;

    >li {
        margin-left:20px;
    }
}


.sub-bulletin {
    padding-left: 4%;
    list-style-type: circle;
    text-indent: -1.5em;
    padding-top: 1%;
}

.sources-header-style {
    color: var(--Text-Primary, #1A1A1A);

    /* Text md/Medium */
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding-left: 31px;
}

.paas-ai-ratings-container {
    margin-left: 31px;    
    margin-top: 20px;
}

.rating-button {
    color: #004eaa;
    text-decoration: none;
    cursor: pointer;
    padding: 0px;
    height: inherit;
    height: 24px;

    margin-right: 0.5rem;
    font-family: 'Material Symbols Outlined', sans-serif;
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
}

.rating-button:disabled{
    cursor:initial;
    pointer-events: none;
    color: var(--Text-Primary, #1A1A1A);
}

.rating-button:enabled:hover {
    color: #1A1A1A;
}

.rating-button.upvote-selected {
    color: #22b169;
}

.rating-button.downvote-selected {
    color: #e56c68;
}

.rating-button.clicked-button {
    padding: 0px;
    background: var(--Background-Tertiary, #c2c5cc) !important;
}

.paas-ai-tooltips-container {
    height: 30px;
}

.copy-tooltip {
    width: 40px;
    height: 33px;
    flex-shrink: 0;
    border-radius: 4px;
    background: var(--Text-Action-Inverse-Hover, #E6EBF4);
    box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.10);
    color: #000;
    font-family: Roboto, sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 200% */

    display: flex;
    position: relative;
    left: -8px;
    bottom: 7px;
    padding-top: 4px;
    z-index: 2;
    align-items: center;
    justify-content: center;

}

.copy-tooltip-active {
    width: 54px !important;
    // bottom: 57px !important;
}

.upvote-tooltip {
    width: 99px;
    height: 33px;
    flex-shrink: 0;
    border-radius: 4px;
    background: var(--Text-Action-Inverse-Hover, #E6EBF4);
    box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.10);
    color: #000;
    font-family: Roboto, sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 200% */

    display: flex;
    position: relative;
    left: -2px;
    bottom: 7px;
    padding-top: 2px;
    z-index: 2;
    align-items: center;
    justify-content: center;
}

.upvote-tooltip-active {
    width: 195px !important;
    height: 30px !important;
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 142.857% */
}

.downvote-tooltip {
    width: 99px;
    height: 33px;
    flex-shrink: 0;
    border-radius: 4px;
    background: var(--Text-Action-Inverse-Hover, #E6EBF4);
    box-shadow: 1px 1px 4px 0px rgba(0, 0, 0, 0.10);
    color: #000;
    font-family: Roboto, sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    /* 200% */

    display: flex;
    position: relative;
    left: 27px;;
    bottom: 8px;
    padding-top: 2px;
    z-index: 2;
    align-items: center;
    justify-content: center;
}

.downvote-tooltip-active {
    width: 195px !important;
    height: 30px !important;
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 142.857% */
}

.tooltip-feedback {
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    background-color: #e6ebf4;
    display: inline-block;
    width: 740px;
    margin-left: 20px;
    position: relative;
    padding-top: 7px;
    padding-left: 11px;
    padding-bottom: 18px;
    border-radius: 9px;
    z-index: 2;

    &.show-below {
        top: 7px;
    }

    
    &.show-above {
        bottom: 264px;
    }
}

.paas-ai-feedback-header{
    height: 30px;
    display: flex;
    align-items: center;
    gap: 58%;
    align-self: stretch;
    padding-left: 5px;
}

.paas-ai-feedback-radio {
    margin-bottom: 2px;
}

.paas-ai-feedback-input {
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    width: 706px;
    height: 40px;
    flex-shrink: 0;
    background: #FFF;
    padding-left: 10px;
    margin: 7px;    
    border-radius: 5px;
    border-color: #adbedb;
    margin-bottom: 13px;
}

.paas-ai-feedback-submit{
    display: flex;
    height: 38px;
    width: 78px;
    padding: var(--sm, 8px) var(--xl, 24px);
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: var(--xs, 4px);
    border: 1px solid var(--Border-Action-Default, #00358E);
    background: var(--Button-Secondary-Default, #00358E);
    color: var(--Text-Inverse, #FFF);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    margin-left: 7px;
}

.paas-ai-feedback-x-button{
    display: flex;
    width: 44px;
    height: 16px;
    padding: var(--sm, 8px) var(--lg, 16px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--xs, 4px);
}


.paas-ai-middle-panel-input-text {
    width: 92%;
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 142.857% */
    height: 38px;
    flex-shrink: 0;
    border: 0px;
    background: #FFF;
    outline: none;
    margin-top: 1px;

    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}

.paas-ai-middle-panel-input-buttons {
    justify-content: center;
    align-items: center;
    display: flex;
    gap: var(--sm, 8px);

    :disabled {
        cursor: not-allowed;
        pointer-events: none;
    }
}

.paas-ai-warning-text {
    font-size: 0.75rem;
    text-align: center;
    width: 100%;
    height: 18px;
    opacity: 0px;
    font-family: Roboto, sans-serif;
    font-weight: 400;
    line-height: 18px;
    color: var(--Text-Secondary, #4D4D4D);
    margin-top: 25px;
}

.material-symbols-outlined{
    font-family: 'Material Symbols Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.paas-genai-display-input{
    display: flex;
    justify-content: flex-start;
    align-self: flex-end;
    flex-wrap: wrap;
    background-color: transparent;
    border-top: none;
    min-height: unset;
    color: initial;
    padding: 0;
    margin-top: unset;
    bottom: 0;
    width: 100%;

}

.avatar{
    background-color: #ca6eea;
    border-radius: 100%;
    padding: 0.3125rem;
    height: 1.5rem;
    width: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: white;
    font-size: 0.815rem;
    margin-right: 0.3125rem;
}

.avatar.paasRA {
    background-color: #004eaa;
    padding-bottom: 4px; 
}

.paas-ai-input-actions {
    right: 0.875rem;
    background-color: #ffffff;
    display: inline-flex;
    position: absolute;
    border-radius: 0.5rem;
    padding-top: 4px;
}

.paas-ai-submitIcon {
    display: flex;
    position: relative;
    align-self: center;
    z-index: 1;
    background-color: #ffc600;
    border-radius: 100%;    
    font-size: 25px;
    padding: 0.005rem;
    // top: 1px;
}

.paas-ai-submitIcon:disabled{
    color: white;
    background-color: #dbdbdb;
    pointer-events: none;
    cursor: initial;
}

.paas-ai-arrow {
    top: 1px;
    font-size: 25px;
    position: relative;
}

.paas-ai-submitIcon:hover:enabled {
    cursor: pointer;
    background-color: #e8b400;
}

.paas-ai-microphoneIcon {
    display: flex;
    position: relative;
    align-self: center;
    border-radius: 0.25rem;
    padding: 0.25rem;
    color: #00358e;
    top: 1px;
}

.paas-ai-microphoneIcon.listening {
    color: #ffffff !important;
    background-color: #73db61 !important;
}


.paas-ai-microphoneIcon:disabled {
    cursor: initial;
    pointer-events: none;
    color: var(--Text-Secondary, #4D4D4D);
}

.paas-ai-microphoneIcon:hover:enabled {
    background-color: #e6ebf4;
    color: #000000;
    cursor: pointer;
}

.paas-ai-separator {
    align-self: center;
    width: 0.0625rem;
    height: 1.5rem;
    background-color: #a3a3a3;
    margin: 0 0.5rem;
}


/* PaasAi Right Panel */
.paas-ai-download-btn {
    margin-right: 105px;
}

.paas-ai-download-container {
    width: 133px;
    height: 30px;
    flex-shrink: 0;
    border-radius: 2px;
    border: 0.5px solid #00358E;
    display: flex;
    margin-top: 24px;
    position: relative;
    left: 84%;
}

.paas-ai-download-text {
    color: #00358E;
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding-left: 23px;
    position: absolute;
    bottom: 5px;
    white-space: nowrap;
}

// START MODAL
.modal {
    display: flex;
    min-width: 400px;
    padding: var(--None, 0px);
    flex-direction: column;
    align-items: center;
    gap: var(--None, 0px);
    border-radius: var(--s, 8px);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
    z-index: 4;
    overflow: hidden;
    max-width: 45vw;
    transition: all 0.25s;
}

.modal.info {
    min-width: 40vw;
    flex-shrink: 0;
    border-radius: 10px;
    background: var(--Background-Primary, #FFF);    
}

.modal-overlay {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 800px;
    width: 95.5%;
    max-width: 119.9rem;   
    flex-shrink: 0;
    border-radius: 7px;
    background: rgba(0, 0, 0, 0.60);
    z-index: 3;
    box-sizing: border-box;
    margin-right: 2.4%;
}

.modal-header {
    display: flex;
    padding: 0.875rem 1.5rem 0.875rem 1.5rem;
    justify-content: center;
    align-items: center;
    gap: 5px;
    align-self: stretch;
    border-bottom: 1px solid var(--Border-Neutral-2, #DBDBDB);
    background: var(--Background-Primary, #FFF);
}

.modal-header.info {
    justify-content: flex-start;
    border-bottom: 1px solid var(--Border-Neutral-2, #DBDBDB);
    gap: 540px;
}

.modal-title {
    color: var(--Text-Primary, #1A1A1A);
    font-size: 1.3rem;

    font-family: Roboto, sans-serif;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;

    display: flex;
    align-items: flex-start;
    flex: 1 0 0;
}

.modal-x-button {
    display: flex;
    width: 44px;
    height: 44px;
    padding: var(--sm, 8px) var(--lg, 16px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--xs, 4px);
}

.modal-x-button.info {
    justify-content: flex-end !important;
}

.modal-body {
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: var(--None, 0px);
    align-self: stretch;
    background: var(--Background-Primary, #FFF);
    scroll-behavior: smooth;
    overflow: auto;
    word-wrap: normal;
    padding: 1.5rem;
    

        &::-webkit-scrollbar {
        width: 7px;
        height: 100px;
        flex-shrink: 0;
    }

    &::-webkit-scrollbar-thumb {
        width: 7px;
        height: 5px;
        flex-shrink: 0;
        background: var(--Background-Action-Disabled, #DBDBDB);
    }
}

.modal-body.info {
    padding: unset;
}

.modal-text {
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    color: var(--Text-Primary, #1A1A1A);
    font-family: Roboto, sans-serif;
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    width: 40vw;
}

.modal-text.info {
    width: 640px;
    width: 40vw;
    padding: 1.5rem;
}

.modal-text.input {
    border: 1px solid #004eaa !important;
}

.modal-footer {
    display: flex;
    padding: 0.8rem 0.8rem 0.8rem 0.8rem;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-top: 1px solid var(--Border-Neutral-2, #DBDBDB);
    background: var(--Background-Primary, #FFF);
    font-size: 16px;

    &.termsOfUse {
        justify-content: space-between;
    }

    >div.termsOfUse {
        font-size: 14px;   
        align-items: center;
        display: flex;

        >input {
            margin-right: 10px;
        }
    }   
}

.modal-cancel-button {
    display: flex;
    height: 44px;
    padding: 1.28rem 2rem;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: var(--xs, 4px);
    border: 1px solid var(--Border-Action-Default, #00358E);
    background: var(--Button-Tertiary-Default, #FFF);
    color: var(--Text-Action-Default, #00358E);
    text-align: center;
    font-family: Roboto, sans-serif;
    font-size: 19px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}

.modal-submit-button {
    display: flex;
    height: 44px;
    padding: 1.28rem 2rem;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: var(--xs, 4px);
    border: 1px solid var(--Border-Action-Default, #004eaa);
    background: var(--Button-Secondary-Default, #004eaa);
    color: var(--Text-Inverse, #ffffff);
    font-family: Roboto, sans-serif;
    font-size: 19px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;

    
    &:disabled {
        pointer-events: none;
        cursor: pointer;
        border: 1px solid var(--Border-Action-Default, #dbdbdb);
        background: #dbdbdb;
        color: #bdbdbd;
    }
}

// END MODAL

.paas-ai-typing-indicator {
    position: relative;
    height: 20px;
    margin-bottom: 20px;
    margin-left: 19px;
    margin-top: 8px;
}

.dot-flashing {
    position: relative;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #00358e;
    color: #00358e;
    animation: dot-flashing 1s infinite linear alternate;
    animation-delay: 0.5s;
}

.dot-flashing::before,
.dot-flashing::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 0;
}

.dot-flashing::before {
    left: -15px;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #00358e;
    color: #00358e;
    animation: dot-flashing 1s infinite alternate;
    animation-delay: 0s;
}

.dot-flashing::after {
    left: 15px;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #00358e;
    color: #00358e;
    animation: dot-flashing 1s infinite alternate;
    animation-delay: 1s;
}

@keyframes dot-flashing {
    0% {
        background-color: #00358e;
    }

    50%,
    100% {
        background-color: rgba(0, 53, 142, 0.3);
    }
}