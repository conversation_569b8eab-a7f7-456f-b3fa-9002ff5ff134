.accordion-block {
    padding-left: 1.875rem;
    padding-right: 1.875rem;
    width: 60%;

    .references {
        padding: 0;

        details {
            summary {
                border: 0;

                .accordion-title {
                    color: $default-link;
                    font-size: 1.875rem;
                    font-weight: 500;
                }
            }

            ::after {
                margin-left: 0.625rem;
                position: relative;
                top: 0.125rem;
                font-size: 1.625rem;
            }

            .reference-listing {
                padding-top: 0.9375rem;

                .accordion-heading {
                    padding: 0.5rem;
                    background-color: $theEs;
                    font-weight: 600;
                    color: $black;
                }

                .accordion-content {
                    padding-left: 0.5rem;
                }
            }
        }
    }
}

.sfh-content {
    margin-top: 2.625rem;
    max-width: 100%;

    p {
        margin-block-start: 0;
        margin-block-end: 0;
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }

    h5 {
        color: #000000;
        font-size: 1.125rem;
        line-height: 1.5;
        margin: 0 0 1rem;
        display: block;
        margin-bottom: 0;
    }

    .accordion.empty {
        .accordionTab {
            h2 {
                color: #999999;
            }

            .unavailable {
                color: #000000;
                border-radius: 0.5rem;
                background-color: #f2f2f2;
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }

            &:hover {
                cursor: auto;
            }
        }
    }

    .title-bg-accent {
        background-color: #f8f8f8;
        padding: 0.75rem 0.625rem;
        width: 100%;
        color: #000000;
        font-size: 1.125rem;
        line-height: 1.5;
        margin: 0 0 1rem;
        display: block;
    }

    .topics-content{
        margin-top: 1.5rem;
        margin-left: 0.625rem;
    }

    .sfh-revision-notice {
        margin: 0 0 1.25rem;
        margin-bottom: 0;
    }

    .sfh-revision-notice-content {
        margin-bottom: 1rem;
    }

    .accordion {
        width: 100%;

        .accordionTab {
            color: #004eaa;
            display: flex;
            align-items: center;
            font-weight: 500;
            border-bottom: 0.063rem solid #e5e5e5;
            padding: 0.25rem 0.5rem;
            min-height: 2.75rem;
            margin-bottom: 0;
            font-size: 1.5rem;
            line-height: 1.25;
            margin: 0 0 0rem;

            h2 {
                font-size: 1.5rem;
                line-height: 1.25;
                margin: 0 0 1rem;
                display: block;

                color: #004eaa;
                margin-bottom: 0;
            }

            span {
                transition: all 0.25s;
                margin-left: 0.5rem;
            }

            &:hover {
                background-color: #f7fbfe;
                cursor: pointer;
            }

            .iconRotate {
                transform: rotate(180deg);
            }
        }

        .accordionContent {
            transition: all 1s ease-out;
            display: none;
            gap: 2rem;
            overflow: hidden;

            &.active {
                opacity: 1;
                display: flex;
                margin-bottom: 1rem;
            }


            aside {
                padding: 1.25rem 0;
                margin: 0;

                section {
                    width: 100%;
                    padding: 1rem;
                    margin: 0;
                }
            }

            .sfh-accordionContent-container {
                padding: 1.3rem 0.625rem;
                display: flex;
                flex-wrap: wrap;
                width: 100%;
                justify-content: space-between;
                align-content: flex-start;

                .sfh-notes{
                    li:not(:last-child) {
                        margin-bottom: 1rem !important;
                      }
                }

                .sfh-iso-filings-table {
                    table {
                        width: 100% !important;
                    }
                }

                .sfh-section {
                    width: 100%;
                }

                .accordion-section {
                    padding: 0 0.625rem;

                    p {
                        margin-block-start: 0;
                        margin-block-end: 0;
                        display: block;
                        width: 100%;
                        margin-bottom: 1rem;
                    }

                    ul {
                        margin-block-start: 0;
                        margin-block-end: 0;
                        margin-bottom: 1rem;
                    }

                    .list-style-none {
                        padding-left: 0;
                        list-style-type: none;

                        .mb-2 {
                            margin-bottom: 0.5rem;
                        }

                        .mb-3 {
                            margin-bottom: 0.75rem;
                        }

                        li {
                            list-style-type: none;
                        }
                    }
                }

                .sfh-referrals-to-company {
                    display: block;
                    width: 100%;
                }

                .content-table {
                    display: flex;
                    flex-wrap: wrap;
                    gap: unset;
                    width: 100%;
                    justify-content: space-between;

                    .leftCol {
                        min-width: calc(50% - 0.5rem);
                    }

                    .rightCol {
                        min-width: calc(50% - 0.5rem);
                    }
                }

                @media (min-width: 67.5rem) {
                    .content-table.mobile {
                        display: none;
                    }
                }


                .sfh-leftCol {
                    width: 100%;

                    @media (min-width: 67.5rem) {
                        width: calc(70% - 1rem);
                    }

                    .revisionDate {
                        font-size: 1.125rem;
                    }

                    .sfh-notes{
                        li:not(:last-child) {
                            margin-bottom: 1rem !important;
                          }
                    }

                    h4 {
                        padding: 0.5rem;
                        background-color: $background-lt-grey;
                        font-weight: 600;
                        color: $black;
                    }

                    .sfh-filing-heading {
                        font-size: 1rem;
                    }

                    .sfh-filing-lob {
                        color: $black;
                        font-weight: 500;
                    }

                    .sfh-topics-lob {
                        margin-left: 0.625rem;
                    }
                }

                .sfh-rightCol {
                    width: 100%;

                    @media (min-width: 67.5rem) {
                        width: calc(30% - 1rem);
                    }

                    aside {
                        padding: 0;
                        margin-right: 0;
                        width: 100%;

                        .background-lt-grey {
                            padding-top: 0.75rem;

                            h2 {
                                font-size: 1rem;
                                padding-bottom: 0.5rem;
                                margin-top: 0;
                                border-bottom: thin solid #bbbbbb;
                            }

                            p {
                                margin-block-start: 0;
                                margin-block-end: 0;
                                display: block;
                                width: 100%;
                                margin-bottom: 1rem;
                            }
                        }
                    }
                }
            }
        }
    }
}