.sfh-asideFilter {
    max-width: unset;

    section {
        padding: 0rem 1rem;

        .site {
            max-width: 100%;
            margin: 0 0;
        }

        .site.flex-wrapper {

            flex-wrap: wrap;
            justify-content: flex-start;
            gap: 2rem;
            padding-top: 0;

            @media (max-width: 67.5rem) {
                flex-direction: column;
            }

            aside.thin {

                margin-left: 0;
                margin-right: 0;
                min-width: 19.125rem;

                @media (min-width: 67.5rem) {
                    width: 19rem;
                }

                @media (max-width: 67.5rem) {
                    width: 100%;
                }

                section {
                    width: 100%;
                    padding: 1rem;
                    margin: 0;
                }

                &.filter form li {
                    padding-left: 1.5rem;
                }

                .background-lt-grey {
                    background-color: #f8f8f8;
                }

                #search-filters {
                    >li {
                        margin-bottom: 1.25rem;

                        &.jurisdictions {
                            margin-bottom: 0.5rem;

                            .sfh-forms-jurisdiction-summary {
                                border-bottom: 0.0625rem solid #e5e5e5;
                            }
                        }
                    }

                    .options {
                        display: flex;
                        flex-direction: column;

                        li,
                        label {
                            padding-bottom: 0.5rem;
                        }

                        .clear-all-btn {
                            margin-left: auto;
                            font-size: .78rem;
                            cursor: pointer;
                            color: #004eaa;
                            padding-right: 0;
                        }
                    }

                    .filter-non-accordion {
                        display: block;
                        padding-bottom: 0.5rem;
                        border-bottom: 0.0625rem solid #e5e5e5;
                        margin-bottom: 1rem;
                    }
                }
            }
        }
    }
}