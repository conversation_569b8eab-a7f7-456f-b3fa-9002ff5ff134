.position-back-to-top {
    display: flex;
    flex-direction: column;
    align-items: center;

    .back-to-top-text {
        font-size: 0.75rem;
        margin-left: -1rem;
        color: $default-link;
        font-weight: 700;
    }
}

.button-icon {
    transition: opacity 0.3s ease-in-out;
    background-image: url(../assets/backtotop.svg);
    background-size: contain;
    background-repeat: no-repeat;
    width: 4rem;
    height: 3rem;
    margin-bottom: -0.5rem;
}

.back-to-top-button {
    position: fixed;
    bottom: 2rem;
    right: 1rem;
    border: none;
    cursor: pointer;
    opacity: 0;
    margin-bottom: -2rem;

    &.visible {
        opacity: 1;
    }
}

.back-to-top-fixed {
    position: absolute;
    bottom: 0rem;
    right: 1rem;
    cursor: pointer;
    opacity: 1;
    margin-bottom: -1rem;
}