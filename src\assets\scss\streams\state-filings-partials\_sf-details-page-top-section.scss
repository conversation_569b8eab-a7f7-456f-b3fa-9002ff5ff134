.tabContent {
    .breadcrumbs {
        margin-bottom: 1.5rem;
        margin-top: 1.5rem;
    }


    .sfh-topSection {
        .sfh-title {
            color: $header-text;
            font-size: 2.25rem;
            line-height: 1.25;
            margin: 0 0 1rem;
        }

        .sfh-details {
            font-size: 0.875rem;

            .sfh-common-media-link {
                color: #004eaa;
                margin-bottom: 0;
                line-height: 1.5;
            }

            .sfh-common-media-link-icon {
                vertical-align: middle;
                color: #004eaa;
            }

            &-item {
                margin-bottom: 0.5rem;

                .label {
                    font-weight: $fw-bold;
                }

                .detail {
                    align-items: flex-start;
                    flex: 1;
                    margin-left: 0.25rem;

                    @media(max-width: 67.5rem) {
                        flex-wrap: wrap;
                    }

                    a {
                        display: inline-flex;
                        align-items: center;
                    }

                }
            }
        }

        .download {
            &.button {
                color: $background-blue;
                border-radius: 0.125rem;
                border: 0.0625rem solid $border-blue;
                display: inline-flex;
                padding: 0.25rem 0.375rem;
                margin-bottom: 1.25rem;
                font-size: 0.875rem;
                font-weight: $fw-medium;

                span {
                    margin-right: 0.375rem;
                }

                &:hover {
                    cursor: pointer;
                    background-color: $background-lt-blue;
                }
            }
        }
    }
}

@media print {
    .position-back-to-top {
        display: none;
    }
}