.facet-child {
  margin-left: 1rem;
}

.jurisdictions {

  label:nth-of-type(n+8) {
    display: none;
  }

  .show-less-jurisdictions,
  .show-more-jurisdictions {
    padding: .5rem 0 1rem;
    font-size: .9rem;
    display: block;
  }

  .show-less-jurisdictions {
    padding-top: 1rem;
  }

  .show-less-jurisdictions,
  .show-more-jurisdictions:target {
    display: none;
  }

  .show-more-jurisdictions:target+.show-less-jurisdictions {
    display: block;
    border: 0px;
  }

  &:has(.show-more-jurisdictions:target) {
    label:nth-of-type(n+8) {
      display: flex;
    }

    .options {
      max-height: 20rem;
      overflow-y: scroll;
    }

  }
}

.commercial-lines-child {
  label:nth-of-type(n + 6) {
    display: none;
  }
}

.personal-lines-child {
  label:nth-of-type(n + 6) {
    display: none;
  }
}

.personal-lines {

  .show-less-apl,
  .show-more-apl {
    padding: 0.5rem 0 1rem;
    font-size: 0.9rem;
    display: block;
  }

  .show-less-apl {
    padding-top: 1rem;
  }

  .show-less-apl,
  .show-more-apl:target {
    display: none;
  }

  .show-more-apl:target+.show-less-apl {
    display: block;
    border: 0rem;
  }

  &:has(.show-more-apl:target) {
    label:nth-of-type(n + 5) {
      display: flex;
    }

    .options {
      max-height: 20rem;
      overflow-y: scroll;
    }
  }
}

.filing-info {
  label:nth-of-type(n + 6) {
    display: none;
  }

  .show-less-fi,
  .show-more-fi {
    padding: 0.5rem 0 1rem;
    font-size: 0.9rem;
    display: block;
  }

  .show-less-fi {
    padding-top: 1rem;
    width: 100%;
    margin-left: -0.01rem;
  }

  .show-less-fi,
  .show-more-fi:target {
    display: none;
  }

  .show-more-fi:target+.show-less-fi {
    display: block;
    border: 0rem;
  }

  &:has(.show-more-fi:target) {
    label:nth-of-type(n + 6) {
      display: flex;
    }

    .options {
      max-height: 20rem;
      overflow-y: scroll;
    }
  }
}

.commercial-lines {

  .show-less-acl,
  .show-more-acl {
    padding: 0.5rem 0 1rem;
    font-size: 0.9rem;
    display: block;
  }

  .show-less-acl {
    padding-top: 1rem;
  }

  .show-less-acl,
  .show-more-acl:target {
    display: none;
  }

  .show-more-acl:target+.show-less-acl {
    display: block;
    border: 0rem;
  }

  &:has(.show-more-acl:target) {
    label:nth-of-type(n + 5) {
      display: flex;
    }

    .options {
      max-height: 20rem;
      overflow-y: scroll;
    }
  }
}

.filing-topics {
  label:nth-of-type(n + 6) {
    display: none;
  }

  .show-less-ft,
  .show-more-ft {
    padding: 0.5rem 0 1rem;
    font-size: 0.9rem;
    display: block;
  }

  .show-less-ft {
    padding-top: 1rem;
  }

  .show-less-ft,
  .show-more-ft:target {
    display: none;
  }

  .show-more-ft:target+.show-less-ft {
    display: block;
    border: 0rem;
  }

  &:has(.show-more-ft:target) {
    label:nth-of-type(n + 6) {
      display: flex;
    }

    .options {
      max-height: 20rem;
      overflow-y: scroll;
    }
  }

}

.sfh-filter {
  padding: 0.875rem;
  flex: 0 0 auto;
  min-width: 15.625rem;
}

input[type=checkbox] {
  margin-top: 0.313rem;
}