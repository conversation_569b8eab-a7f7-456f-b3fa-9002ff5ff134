.sfh-filingRequirement {

    section .site.flex-wrapper .content-wrapper {
        max-width: 100%;
        padding-left: 0;
        padding-right: 0;
        flex-grow: 2;

        @media (min-width: 67.5rem) {
            width: calc(70% - 2rem - 1.875rem);
        }

        .results-meta-sort {
            margin-bottom: 0.5rem;
        }

        .flex-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .table-view {
            padding: 0 0;

            .results-table.scrollable {
                position: relative;
                width: 100%;
                overflow: auto;
                overflow-y: hidden;
                z-index: 1;
                border: thin solid #efefef;

                table {
                    width: 100%;
                    min-width: 80rem;
                    border-collapse: separate;
                    border-spacing: 0;
                    font-size: 0.9rem;

                    thead th {
                        position: sticky;
                        top: 0;
                        text-align: left;
                        font-weight: 500;
                        padding: 0.5rem;
                        background-color: #f8f8f8;
                    }

                    thead th:nth-of-type(2) {
                        left: unset;
                        z-index: 5;
                        box-shadow: 0.3125rem 0 0.3125rem -0.3125rem rgba(0, 0, 0, 0.1);
                    }

                    thead th:first-child {
                        width: 10rem;
                        left: 0;
                        z-index: 999;
                        position: sticky;
                    }

                    tr {
                        vertical-align: text-top;

                        td {
                            padding: 1rem 0.5rem;
                            border-bottom: thin solid #efefef;
                        }

                        td:nth-of-type(1) {
                            width: 10rem;
                            left: 0 !important;
                            z-index: 999;
                            position: sticky;
                            box-shadow: 0.3125rem 0 0.3125rem -0.3125rem rgba(0, 0, 0, 0.1);
                        }
                    }

                    td:nth-of-type(1) {
                        width: 10rem;
                        left: 0;
                        z-index: 999;
                    }

                    th {
                        top: 0;
                        text-align: left;
                        font-weight: 500;
                        padding: 0.5rem;
                        background-color: #f8f8f8;
                    }

                    th:first-child {
                        position: sticky;
                    }

                    th:nth-of-type(2) {
                        left: unset;
                        z-index: 5;
                        left: 5rem;
                        box-shadow: 0.3125rem 0 0.3125rem -0.3125rem rgba(0, 0, 0, 0.1);
                    }

                    tbody td {
                        left: 0 !important;
                    }

                }
            }
        }
    }
}
