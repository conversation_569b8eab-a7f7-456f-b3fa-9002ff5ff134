.sfh-forms {
    section .site.flex-wrapper .content-wrapper {
        padding-left: 0;
        padding-right: 0;
        flex-grow: 2;

        @media (min-width: 67.5rem) {
            width: 0;
            max-width: 100%;
        }

        @media (max-width: 67.5rem) {
            width: 100%;
            max-width: 100%;
        }

        .results-meta-sort {
            margin-bottom: 0.5rem;
        }

        .flex-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .table-view {
            padding: 0 0;

            .results-table.scrollable {
                position: relative;
                width: 100%;
                overflow: auto;
                overflow-y: hidden;
                z-index: 1;
                border: thin solid #efefef;

                table {
                    width: 100%;
                    min-width: 64rem;
                    table-layout: fixed;
                    border-collapse: separate;
                    border-spacing: 0;
                    font-size: 0.9rem;

                    tr {
                        vertical-align: text-top;
                    }

                    thead th:first-child {
                        position: sticky;
                    }

                    thead th {
                        top: 0;
                        text-align: left;
                        font-weight: 500;
                        padding: 0.5rem;
                        background-color: #f8f8f8;
                    }

                    thead th:first-child {
                        width: 9rem;
                        left: 0;
                        z-index: 999;
                        position: sticky;
                    }

                    thead th:nth-of-type(2) {
                        left: unset;
                        z-index: 5;
                        box-shadow: none;
                        width: 22rem;
                    }

                    td:nth-of-type(1) {
                        position: sticky;
                        border-right: 0;
                        width: 9rem;
                        left: 0;
                        z-index: 999;
                        box-shadow: 0.3125rem 0 0.3125rem -0.3125rem rgba(0, 0, 0, 0.1);
                        background-color: #ffffff;
                        padding: 1rem 0.5rem;
                        border-bottom: thin solid #efefef;

                        @media (min-width: 67.5rem) {
                            border-right: 0;
                        }
                    }

                    td:nth-of-type(2) {
                        left: unset;
                        width: 22rem;
                        box-shadow: none;
                    }

                    .format-downloads {
                        a,
                        span {
                            margin-right: 1.25rem;
                        }
                    }
                }
            }
        }
    }
}