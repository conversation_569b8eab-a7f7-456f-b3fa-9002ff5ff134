.hero.nested-in-tab {
    padding: 0;
    margin-bottom: 1rem;

    .container {
        display: flex;
        width: 100%;

        @media (max-width: 67.5rem) {
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
        }
    }

    .container-with-ctas {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        flex-direction: column;
        justify-content: flex-start;
    }

    .messaging {
        padding: 1rem 4rem 0 2rem;
        min-width: 50%;
        max-width: 70%;
        flex: 2;
        display: flex;

        @media (max-width: 67.5rem) {
            max-width: 100%;
            height: auto;
        }

        .container {
            display: flex;
            flex-wrap: wrap;

            span {
                margin-bottom: 1rem;
            }
            .sfh-banner-heading {
                width: 100%;
            }
        }

        +div {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            flex: 1;
            align-items: center;
            justify-content: center;
            padding: 2rem;

            @media (max-width: 67.5rem) {
                width: 100%;
                height: auto;
            }

            h2 {
                align-self: flex-start;
                margin-top: 0;

                @media (max-width: 67.5rem) {
                    margin: 0 auto;
                    padding-bottom: 1rem;
                }
            }

            .call-to-action {
                flex-direction: column;
                display: flex;
                flex-wrap: wrap;
                gap: 1rem;
                padding: 0;
            }

            a {
                margin: 0 auto;
                border: thin solid;

                @media (max-width: 67.5rem) {
                    align-self: center;
                }

                +a {
                    letter-spacing: 0.02rem;
                }
            }
        }
    }

}