.FrSearch {
    display: flex;
    width: 100%;
}

.search-results-loader {
    width: 100%;
}

.results-count-heading {
    margin-top: 4.3rem;
    margin-bottom: 2rem;
}

.sfh-results{
    display: flex;
    gap: 0.5rem;
    align-items: center;
    .items-per-page {
        border: thin solid #004eaa;
        padding: 0.1rem 0.4rem 0.1rem 0.4rem;
    }
}

.results-listing1 {
    width: 100%;

    .results-table {

        .scrollable {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border: 0.0625rem solid #eeeeee;

            thead {
                th {
                    text-align: left;
                    padding: 0.625 rem;
                    // background-color: #f4f4f4;
                    border-bottom: 0.0625rem solid #ddd;


                    a {
                        color: #004eaa;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }

            tbody {
                tr {
                    &:hover {
                        background-color: #f9f9f9;
                    }

                    th {
                        border-bottom: 0.0625rem solid #ddd;

                    }

                    td {
                        padding: 0.625 rem;
                        border-bottom: 0.0625rem solid #ddd;

                        &.searchresultJurisdiction {
                            font-weight: bold; // If the jurisdiction cells are bold
                        }
                    }
                }
            }
        }
    }

    .page-results-wrapper {
        padding-top: 2rem;
        display: flex;
        align-items: center;
        font-size: .9rem;
        flex-wrap: wrap;

        select {
            padding: .25rem 1rem .25rem .5rem;
        }

        nav {
            margin-left: auto;

            ul {
                display: flex;
                align-items: center;

                li {
                    list-style: none;
                    margin: 0 .5rem;

                    &.page-number {
                        a.page-link {
                            padding: 0 .5rem;
                            background-color: $white;
                            border: thin solid $default-link;

                            &:hover {
                                background-color: $default-link;
                                color: $white;
                            }

                            &.active {
                                background-color: $default-link-hover;
                                color: $inverse-link;
                                pointer-events: none;
                            }
                        }

                    }

                    a.disabled {
                        span {
                            color: $lt-body-text;
                            pointer-events: none;
                        }
                    }
                }
            }
        }

        .select-wrapper {
            display: inline-block;
        }

        .select-wrapper:after {
            top: 0.3rem;
            right: 0.3rem;
        }
    }
}

.unfiltered-text {
    width: 40%;
    margin-top: 2.7rem;

    @media (max-width: 67.5rem) {
        width: 100%;
    }
}