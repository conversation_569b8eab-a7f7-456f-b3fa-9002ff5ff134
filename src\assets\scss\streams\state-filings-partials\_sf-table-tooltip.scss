.sfh-tippy-box {
    background-color: black;
    color: white;
    text-align: left;
    border-radius: 0.5rem;
    box-shadow: 0.005rem 0 1rem rgb(0 0 0 / 10%);
}

.has-tooltip {
    cursor: pointer;
    border-bottom: thin dashed #3f3f3f;
    position: relative;
    color: #3f3f3f;

    button {
        font-size: 0.9rem;
        color: #3f3f3f;
    }

    .has-tooltip-text {
        visibility: hidden;
        background-color: #3f3f3f;
        color: white;
        text-align: center;
        border-radius: 0.375rem;
        padding: 0.5rem;
        font-size: 0.8rem;
        transform: translateX(-50%);
        z-index: 999999999;

        /* Add shadow to the tooltip */
        box-shadow: 0rem 0.25rem 0.5rem rgba(0, 0, 0, 0.25);
    }

    /* Position the tooltip */
    .tooltip-jurisdiction {
        width: 10rem;
        position: absolute;
        left: 180%;
        bottom: -0.625rem;
    }

    .tooltip-lobs {
        position: absolute;
        right: -425%;
        bottom: -0.625rem;
    }
}

.has-tooltip:hover .has-tooltip-text {
    visibility: visible;
    cursor: pointer;
}

.tooltip {
    position: relative;
    display: inline-block;
    border-bottom: 0.0625rem dotted #004eaa;
    color: #004eaa;
}

.tooltip .tooltiptext {
    display: flex;
    visibility: hidden;
    width: 12.5rem;
    background-color: black;
    color: white;
    text-align: left;
    border-radius: 0.5rem;
    padding: 0.125rem;
    position: absolute;
    z-index: 1;
    border: 1rem;
    box-shadow: 0.005rem 0 1rem rgb(0 0 0 / 10%);
    font-size: 0.6rem;
    padding-left: 0.5rem;
    transform: translateX(-50%);
}

.position-tooltip-fix {
    right: -10%;
    top: 0%;
}

.position-tooltip {
    right: -10%;
    top: 0%;
}

.tooltip-title {
    display: block; // Display as block-level for margin to take effect
    text-align: left;
    margin-bottom: 0.1rem; // Space below the title
    margin-left: -0.9375rem; // Negative margin to shift to the left
    padding-left: 0; // Reset any default padding
    font-weight: bold; // If you want the title to be bold
}


.tooltip .tooltiptext ul {
    list-style-type: disc; // Removes default list styling
    padding-left: 1rem; // Add padding to align list items to the right
    margin-left: 0.5rem; // Compensate for the negative margin of the title if needed
}

.tooltip .tooltiptext ul li {
    padding-left: 0; // Adjust the padding for individual list items if necessary
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    margin-top: -0.3125rem;
    border-width: 0.3125rem;
    border-style: solid;
    border-color: transparent transparent transparent transparent;

}

.tooltip:hover .tooltiptext {
    visibility: visible;
    z-index: 999999;
}