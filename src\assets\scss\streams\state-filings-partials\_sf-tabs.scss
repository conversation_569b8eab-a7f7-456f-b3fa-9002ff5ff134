.tabs {
  .tabbed {
    nav {
      .tab {
        flex-grow: unset;
      }
    }
  }

  .prev.hidden {
    display: none;
  }

  .prev {
    left: 0;
    position: absolute;
    padding: 0.75rem 1rem;
    border: 1px solid #00358e;
    background-color: #ffffff;
    color: #00358e;
    border-radius: 0.25rem;
    vertical-align: middle;
    bottom: 0.125rem;

    span {
      font-weight: 600;
    }

    font-size: 0.9rem;
  }

  .next.hidden {
    display: none;
  }

  .next {
    right: 0;
    padding: 0.75rem 1rem;
    background-color: #ffffff;
    border: 1px solid #00358e;
    color: #00358e;
    border-radius: 0.25rem;
    vertical-align: middle;
    position: absolute;
    bottom: 0.125rem;

    span {
      font-weight: 600;
    }
  }
}

button.tabNav.next,
button.tabNav.prev {
  &:hover {
    background-color: #edf2fa;
  }
}

.tabNav.tabContent.active {
  display: inline-block;
}

.tabContent {
  width: 100%;

  section {
    padding-top: 0;

  }
}