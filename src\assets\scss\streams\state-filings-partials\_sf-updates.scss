.updates {
    @extend .results-listing;
    padding-top: 0;

    .cards {
        .card {
            time {
                color: $lt-body-text;
            }

            p,div {
                color: $body-text;
                font-size: 0.9rem;
                margin: 0.5rem 0;
            }
        }
    }

    .update-pagination-wrapper {
        @extend .page-results-wrapper;
        justify-content: center;
        width: 100%;

        @media (max-width: 27rem) {
            margin-left: -1.3rem;
        }

        nav {
            margin: 0 auto;
        }

        a.disabled {
            cursor: default;

            span {
                color: $lt-body-text;
                pointer-events: none;
            }
        }
    }
}

.sfh-two-column-layout {
    gap: 0;
    padding-top: 0
}