import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const AccessDeniedBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  return (
    <main className="system-messages-page">
      {route && <Placeholder name="jss-sub-header" rendering={route} />}
      {route && (
        <Placeholder
          name="jss-access-denied"
          fieldsVal={route.fields}
          rendering={route}
        />
      )}
    </main>
  );
};

export default withSitecoreContext()(AccessDeniedBase);
