export const eventDetailsAdobeAnalytics = (response: any) => {
  const {
    LegalEventId,
    EventTitle,
    LmonItemType,
    EffectiveDate,
    CreateDate,
    Status,
    ContentService,
    ProcedureRequirements,
    PolicyReportForms,
    Jurisdiction,
    Topic,
    LOBs,
  } = response;
  if (typeof window !== "undefined") {
    let serviceTypes: any = "";
    if (ContentService?.length !== 0) {
      serviceTypes = ContentService?.map((res: any) => res?.DispalyName).join(
        ","
      );
    }

    let ProcedReq: any = "";
    if (ProcedureRequirements?.length !== 0) {
      ProcedReq = ProcedureRequirements?.map(
        (res: any) => res?.DispalyName
      ).join(",");
    }

    let PSF: any = "";
    if (PolicyReportForms?.length !== 0) {
      PSF = PolicyReportForms?.map((res: any) => res?.DispalyName).join(",");
    }

    const EventDetails: any = {
      eventId: LegalEventId,
      eventType: LmonItemType,
      eventDate: CreateDate,
      effectiveDate: EffectiveDate,
      status: Status,
      serviceType: serviceTypes,
      eventName: EventTitle,
      procedReq: ProcedReq,
      PSF: PSF,
    };
    window.digitalData.product.Product = EventDetails;

    let jurisdiction: any = "";
    if (Jurisdiction?.length !== 0) {
      jurisdiction = Jurisdiction?.map((res: any) => res?.DispalyName).join(
        ","
      );
    }

    let lob: any = "";
    if (LOBs?.length !== 0) {
      lob = LOBs?.map((res: any) => res?.Code).join(",");
    }

    let topic: any = "";
    if (Topic?.length !== 0) {
      topic = Topic?.map((res: any) => res?.DispalyName).join(",");
    }
    window.digitalData.page.pageInfo.page_jurisdiction = jurisdiction;
    window.digitalData.page.pageInfo.page_LOB = lob;
    window.digitalData.page.pageInfo.page_topic = topic;
  }
};

import { facetLabels } from "./LMON Search/LmonSearchUtils";
export const lmonSearchAdobeAnalytics = (
  response: any,
  searchTerm: any,
  requestParams: any,
  UpdatedDateFacet: any
) => {
  const { lmonSearchItems, Facets } = response;
  if (typeof window !== "undefined") {
    window.digitalData.search.info.searchterm = searchTerm;
    if (lmonSearchItems?.length !== 0 || Facets?.length !== 0) {
      if (lmonSearchItems?.length !== 0 && lmonSearchItems !== undefined) {
        window.digitalData.search.info.searchsuccess = "yes";
      } else {
        window.digitalData.search.info.searchsuccess = "no";
        window.digitalData.search.attributes = {};
        window.digitalData.search.refinements = "";
      }
      let refinementsValues = [];
      const filterFacets = Facets?.filter(
        (facet: any) => facet?.Name !== "Date Range Events"
      );
      filterFacets?.forEach((facet: any) => {
        const facetName = facetLabels[facet?.Name];
        const checkedFacet = facet?.Values?.filter(
          (value: any) => value?.Selected
        );
        if (checkedFacet?.length !== 0) {
          const checkedFacets = checkedFacet
            ?.map((value: any) => value?.Key)
            .join(",");
          window.digitalData.search.attributes[facetName] = checkedFacets;
          refinementsValues.push(facetName);
        } else {
          window.digitalData.search.attributes[facetName] = "";
        }
      });
      if (
        requestParams?.effdr?.efds !== "" &&
        requestParams?.effdr?.efde !== ""
      ) {
        window.digitalData.search.attributes[
          "Effective Date Range"
        ] = `${requestParams?.effdr?.efds}_${requestParams?.effdr?.efde}`;
        refinementsValues.push("Effective Date Range");
      }
      if (
        requestParams?.crdr?.crds !== "" &&
        requestParams?.crdr?.crde !== ""
      ) {
        window.digitalData.search.attributes[
          "Created Date Range"
        ] = `${requestParams?.crdr?.crds}_${requestParams?.crdr?.crde}`;
        refinementsValues.push("Created Date Range");
      }
      if (requestParams?.updr?.uds !== "" && requestParams?.updr?.ude !== "") {
        window.digitalData.search.attributes[
          "Updated Date Range"
        ] = `${requestParams?.updr?.timeperiod}`;
        refinementsValues.push("Updated Date Range");
      } else {
        if (requestParams?.updr?.timeperiod !== "") {
          window.digitalData.search.attributes["Updated Date Range"] =
            getKeyByValue(UpdatedDateFacet, requestParams?.updr?.timeperiod);
          refinementsValues.push("Updated Date Range");
        }
        if (refinementsValues.length !== 0) {
          window.digitalData.search.refinements = refinementsValues.join(",");
        } else {
          window.digitalData.search.refinements = "";
        }
      }
    } else {
      window.digitalData.search.info.searchsuccess = "no";
      window.digitalData.search.attributes = {};
      window.digitalData.search.refinements = "";
    }
  }
};

const getKeyByValue: any = (obj: any, value: any) => {
  for (let val in obj) {
    if (obj.hasOwnProperty(val)) {
      if (obj[val] === value) {
        return val;
      }
    }
  }
  return;
};
