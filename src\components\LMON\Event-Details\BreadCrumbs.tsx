import {
  Field,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import React from "react";
import { useRouter } from "next/router";
// import Link from 'next/link';
import ErrorMessage from "components/common/ErrorMessage";
import Loader from "components/common/Loader";

type BreadCrumbsProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const BreadCrumbs = (props: any): JSX.Element => {
  const isEEFlag = props?.isEEFlag;
  const router = useRouter();
  const queryParam = router?.query;

  const handleSearch = () => {
    router.push(
      `/lmon-hub/searchpage?js=${props?.response?.Jurisdiction?.[0]?.Code}`
    );
  };

  if (props?.isError) {
    return (
      <div className="breadcrumbs site flex-wrapper">
        <nav>
          <ErrorMessage message={props?.errorMessage} />
        </nav>
      </div>
    );
  }

  return (
    <div className="breadcrumbs site flex-wrapper">
      {isEEFlag ? (
        <nav>
          <Link field={props?.fields?.HomeLink} />
        </nav>
      ) : (
        <nav>
          {props?.isspinner && <Loader />}
          {!props?.isspinner && (
            <>
              <Link field={props?.fields?.HomeLink}>
                {props.fields.HomeLink.value.text}
              </Link>
              {props?.response?.Jurisdiction?.[0]?.DispalyName.length > 0 &&
                props?.response?.Jurisdiction?.[0]?.DispalyName !== undefined &&
                props?.response?.Jurisdiction?.[0]?.DispalyName !== null && (
                  <a
                    onClick={handleSearch}
                    tabIndex={0}
                    onKeyUp={(e) => e.key === "Enter" && handleSearch()}
                    data-testid="jurisdiction-name"
                  >
                    {props?.response?.Jurisdiction?.[0]?.DispalyName}
                  </a>
                )}
              <a className="current">{props?.response?.EventTitle}</a>
            </>
          )}
          <a className="current" data-testid="keyword-anchortag">
            {queryParam?.keyword || queryParam?.name}{" "}
          </a>
        </nav>
      )}
    </div>
  );
};

export default withDatasourceCheck()<BreadCrumbsProps>(BreadCrumbs);
