import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import ErrorMessage from "components/common/ErrorMessage";

type CommentsProps = ComponentProps & {
  fields: {
    CommentsTitle: Field<string>;
  };
};

const Comments = (props: any): JSX.Element => {
  const paragraph = props?.response?.Comments?.split("\r\n\r\n");
  const isEEFlag = props?.isEEFlag;
  if (props?.isError) {
    return (
      <section>
        <Text field={props?.fields?.CommentsTitle} tag="h2" />
        <ErrorMessage message={props?.errorMessage} />
      </section>
    );
  }
  return (
    <>
      {isEEFlag ? (
        <section>
          <Text field={props?.fields?.CommentsTitle} tag="h2" />
        </section>
      ) : (
        props?.response?.Comments?.length > 0 &&
        props?.response?.Comments !== undefined &&
        props?.response?.Comments !== null && (
          <section data-testid="comments-section">
            <Text field={props?.fields?.CommentsTitle} tag="h2" />
            {paragraph.map((para: any, id: any) => (
              <p key={id} data-testid="comments">
                {para}
              </p>
            ))}
          </section>
        )
      )}
    </>
  );
};

export default withDatasourceCheck()<CommentsProps>(Comments);
