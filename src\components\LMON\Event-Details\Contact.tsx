import {
  Text,
  Field,
  Image,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
// import NextImage from "components/common/NextImage";

type ContactProps = ComponentProps & {
  fields: {
    heading: Field<string>;
    Headline: any;
    "Section Title": any;
    "Verisk Logo": any;
    "Auto Related  Contact": any;
    "Auto Related Text": any;
    "Liability Related Contact": any;
    "Liability Related Text": any;
    "Property Related Contact": any;
    "Property Related Text": any;
  };
  EEFlag: any;
};

const Contact = (props: any): JSX.Element => {
  const Headline = props.fields.Headline;
  const Contact = props.fields["Section Title"];
  const verisklogo = props.fields["Verisk Logo"];
  const autorelatedcontact = props.fields["Auto Related  Contact"];
  const autorelatedtext = props.fields["Auto Related Text"];
  const liabilityrelatedcontact = props.fields["Liability Related Contact"];
  const liabilityrelatedtext = props.fields["Liability Related Text"];
  const propertyrelatedcontact = props.fields["Property Related Contact"];
  const propertyrelatedtext = props.fields["Property Related Text"];

  return (
    <>
      <section className="contact-info-panel">
        <Text field={Contact} tag="h2"></Text>
        <div className="flex-wrapper">
          {/* <NextImage src={verisklogo.src} alt={verisklogo.alt} height={verisklogo.height} width= {verisklogo.width}/> */}
          <Image field={verisklogo} />
          <ul>
            <span>
              <Text field={Headline}></Text>
            </span>
            <li>
              <Text field={autorelatedtext}></Text>{" "}
              <a
                href={`mailto:${props?.fields["Auto Related  Contact"]?.value}`}
              >
                <Text field={autorelatedcontact}></Text>
              </a>
            </li>
            <li>
              <Text field={liabilityrelatedtext}></Text>
              <a
                href={`mailto:${props?.fields["Liability Related Contact"]?.value}`}
              >
                <Text field={liabilityrelatedcontact}></Text>
              </a>
            </li>
            <li>
              <Text field={propertyrelatedtext}></Text>{" "}
              <a
                href={`mailto:${props?.fields["Property Related Contact"]?.value}`}
              >
                <Text field={propertyrelatedcontact}></Text>
              </a>
            </li>
          </ul>
        </div>
      </section>
    </>
  );
};

export default withDatasourceCheck()<ContactProps>(Contact);
