import { Field, withData<PERSON>urce<PERSON>heck } from "@sitecore-jss/sitecore-jss-nextjs";
import React, { useState, useEffect } from "react";
import { ComponentProps } from "lib/component-props";
import { useRouter } from "next/router";

type FilingsProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const Filings = (props: any): JSX.Element => {
  const { query } = useRouter();
  let gid =
    "https://core-contentt.verisk.com/lmon/getevent?eventId=" + query?.eventId;
  const [response, setresponse] = useState<{ Filings: any }>();
  const data = async () => {
    const post = await fetch(gid).then((res) => res.json());
    setresponse(post);
    return post;
  };

  useEffect(() => {
    data();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const Title = props?.fields?.["Title"]?.value;
  const DisplayAllFilingsinTimeline =
    props?.fields?.["DisplayFilingsText"]?.value;
  const DisplayAllCircularsinTimeline =
    props?.fields?.["DisplayCircularsText"]?.value;

  return (
    <>
      <section>
        <h2>{Title}</h2>
        <form>
          <div className="flex-wrapper">
            <label>
              <input type="checkbox" />
              <b>{DisplayAllFilingsinTimeline}</b>
            </label>
            <label>
              <input type="checkbox" />
              <b>{DisplayAllCircularsinTimeline}</b>
            </label>
          </div>
          <div className="flex-wrapper">
            {response?.Filings?.map((val: any, id: any) => {
              return (
                <label key={id}>
                  <input type="checkbox" />
                  <b>{val.LOB}: </b>
                  <a href={val.Link} data-testid="filing-id-link">
                    {val.Id}
                  </a>
                  {(() => {
                    if (val.FilingStatus == "Approved") {
                      return (
                        <span className="material-icons approved">done</span>
                      );
                    } else if (val.FilingStatus == "Disapproved") {
                      return (
                        <span className="material-icons disapproved">
                          close
                        </span>
                      );
                    } else if (val.FilingStatus == "Filed") {
                      return (
                        <span className="material-icons filed">
                          hourglass_empty
                        </span>
                      );
                    } else if (val.FilingStatus == "Under Review") {
                      return (
                        <span className="material-icons under-review">
                          visibility
                        </span>
                      );
                    } else if (val.FilingStatus == "Withdrawn") {
                      return (
                        <span className="material-icons withdrawn">block</span>
                      );
                    } else {
                      return <div></div>;
                    }
                  })()}

                  <span className="badge">
                    {val.ServiceType?.map(
                      (x: { DispalyName: any }) => x.DispalyName
                    )}
                  </span>
                </label>
              );
            })}
          </div>
        </form>
      </section>
    </>
  );
};

export default withDatasourceCheck()<FilingsProps>(Filings);
