import { Text } from "@sitecore-jss/sitecore-jss-nextjs";
const FilingsCheckboxes = ({ ...props }: any) => {
  const { response, handleCheckAllFilingBoxes, Checkedboxes, textFields } =
    props;
  const filings = response?.Filings;
  const countArray = filings?.reduce((acc: any, curr: any) => {
    const ExistingObj: any = acc?.find((obj: any) => obj?.name === curr?.LOB);
    if (ExistingObj) {
      ExistingObj.count++;
    } else {
      acc.push({ name: curr.LOB, count: 1 });
    }
    return acc;
  }, []);
  const filteredarray: any = countArray?.filter((name: any) => {
    return name?.count > 1;
  });

  const ischeckedBox: any = (value: any, count: any) => {
    let checkedcount = 0;
    Checkedboxes?.forEach((element: any) => {
      const obj: any = filings?.find((item: any) => {
        return item?.Id === element;
      });
      if (obj) {
        if (value === obj?.LOB) {
          checkedcount++;
        }
      }
    });
    if (Checkedboxes.indexOf("Filing Event") > -1) {
      return true;
    } else {
      if (checkedcount === count) {
        return true;
      } else {
        return false;
      }
    }
  };

  return (
    <>
      {filteredarray?.map((value: any) => {
        return (
          <label key={value?.name}>
            <input
              type="checkbox"
              value={value?.name}
              onChange={(e: any) => handleCheckAllFilingBoxes(e)}
              defaultChecked={true}
              checked={ischeckedBox(value?.name, value?.count)}
              data-testid={`All${value?.name}inFilings`}
            />
            <b>
              <Text field={textFields?.DisplayLobPreText} /> {value?.name}{" "}
              <Text field={textFields?.DisplayLobPostText} />
            </b>
          </label>
        );
      })}
    </>
  );
};
export default FilingsCheckboxes;
