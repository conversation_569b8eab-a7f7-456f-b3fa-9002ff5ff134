import {
  Field,
  withData<PERSON>ur<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type GlossaryLinkProps = ComponentProps & {
  fields: {
    heading: Field<string>;
    "Glossary Text": any;
    "Glossary Link": any;
  };
  isEEFlag: any;
};

const GlossaryLink = (props: any): JSX.Element => {
  const glossaryLink = props?.fields?.["Glossary Link"];
  const linkText = props?.fields?.["Glossary Link"]?.value?.text;
  const link = props?.fields?.["Glossary Link"]?.value?.href;
  const EEFlag = props?.isEEFlag;

  return (
    <div className="glossary">
      {EEFlag ? (
        <>
          <a href={link}>
            <span className="material-icons">library_books</span>
          </a>
          &nbsp;
          <Link field={glossaryLink}></Link>
        </>
      ) : (
        <Link field={glossaryLink} target="_blank">
          <span className="material-icons">library_books</span>&nbsp;
          {linkText}
        </Link>
      )}
    </div>
  );
};

export default withDatasourceCheck()<GlossaryLinkProps>(GlossaryLink);
