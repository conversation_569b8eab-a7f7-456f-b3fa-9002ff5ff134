import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type LegendProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const Legend = (props: any): JSX.Element => {
  const Disapproved = props.fields?.["Disapproved Text"];
  const Approved = props.fields?.["Approved Text"];
  const Legend = props.fields?.["Legend Text"];
  const Filed = props.fields?.["Filed Text"];
  const Withdrawn = props.fields?.["Withdrawn Text"];
  return (
    <div className="legend flex-wrapper">
      <b>
        <Text field={Legend}></Text>:{" "}
      </b>
      <span>
        <i className="material-icons filed">hourglass_empty</i>{" "}
        <Text field={Filed}></Text>
      </span>
      <span>
        <i className="material-icons approved">done</i>{" "}
        <Text field={Approved}></Text>
      </span>
      <span>
        <i className="material-icons withdrawn">block</i>{" "}
        <Text field={Withdrawn}></Text>
      </span>
      <span>
        <i className="material-icons disapproved">close</i>{" "}
        <Text field={Disapproved}></Text>
      </span>
    </div>
  );
};

export default withDatasourceCheck()<LegendProps>(Legend);
