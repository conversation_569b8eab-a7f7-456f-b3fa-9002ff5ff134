import {
  Field,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import React, { useState } from "react";
import { ComponentProps } from "lib/component-props";
import ErrorMessage from "components/common/ErrorMessage";
import Loader from "components/common/Loader";

type LmonEventDescriptionProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const LmonEventDescription = (props: any): JSX.Element => {
  const [showfulldsc, setshowfulldsc] = useState(false);
  const fullDescription = props?.response?.Description;
  const showonly600 =
    fullDescription?.length > 600
      ? fullDescription?.substring(0, 600) +
        (fullDescription.length > 600 && "...")
      : fullDescription;

  const Description = props.fields?.["Heading"];
  const Showlessdescription = props.fields?.["Less description Text"];
  const Readfulldescription = props.fields?.["Full description Text"];

  const toggleDesc = () => {
    setshowfulldsc(!showfulldsc);
  };

  if (props?.isError) {
    return (
      <section className="padding-bottom">
        <Text field={Description} tag="h2" />
        <div className="description">
          <ErrorMessage message={props?.errorMessage} />
        </div>
      </section>
    );
  }
  return (
    <section className="padding-bottom">
      <Text field={Description} tag="h2" />
      {props?.isspinner && <Loader />}
      {!props?.isspinner && (
        <div className="description">
          {(() => {
            if (showfulldsc == true) {
              return (
                <>
                  {fullDescription
                    ?.split("\r\n\r\n")
                    .map((para: any, id: any) => (
                      <p key={id} data-testid="event-para">
                        {para?.split("\r\n").map((line: any, id: any) => (
                          <React.Fragment key={id}>
                            {line}
                            <br />
                          </React.Fragment>
                        ))}
                      </p>
                    ))}
                </>
              );
            } else {
              return (
                <>
                  {showonly600?.split("\r\n\r\n").map((para: any, id: any) => (
                    <p key={id} data-testid="event-para">
                      {para?.split("\r\n").map((line: any, id: any) => (
                        <React.Fragment key={id}>
                          {line}
                          <br />
                        </React.Fragment>
                      ))}
                    </p>
                  ))}
                </>
              );
            }
          })()}
        </div>
      )}
      {props?.response?.Description?.length > 600 && (
        <>
          <input type="checkbox" id="showMore" name="" value="" />
          <label htmlFor="showMore" className="toggle">
            <span
              onClick={toggleDesc}
              tabIndex={0}
              onKeyUp={(e) => {
                if (e.key === "Enter") toggleDesc();
              }}
              data-testid="show-btn"
            >
              {showfulldsc ? (
                <Text field={Showlessdescription} />
              ) : (
                <Text field={Readfulldescription} />
              )}
            </span>
          </label>
        </>
      )}
    </section>
  );
};

export default withDatasourceCheck()<LmonEventDescriptionProps>(
  LmonEventDescription
);
