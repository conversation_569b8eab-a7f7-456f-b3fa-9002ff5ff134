import {
  Text,
  withSitecoreContext,
  Placeholder,
  Link,
} from "@sitecore-jss/sitecore-jss-nextjs";
import React from "react";
import ErrorMessage from "components/common/ErrorMessage";
import Loader from "components/common/Loader";

const LmonEventOverview = (props: any): JSX.Element => {
  const { route } = props?.sitecoreContext || {};
  const isEEFlag = props?.isEEFlag;
  if (props?.response === "No results found") {
    window.location.href = "/page-not-found?prvURL=LMON";
  }

  const EventDate = props.fields?.["Event Date Text"];
  const Overview = props.fields?.["Heading"];
  const LOBs = props.fields?.["LOBs Text"];
  const ProceduralRequirements = props.fields?.["Procedural Requirements Text"];
  const ServiceTypes = props.fields?.["Service Types Text"];
  const Status = props.fields?.["Status Text"];
  const Topic = props.fields?.["Topic Text"];
  const EffectiveDate = props.fields?.["Effective Date Text"];
  const ItemType = props.fields?.["Item Type Text"];
  const JurisdictionText = props.fields?.["Jurisdiction Text"];
  const PolicySupportForms = props.fields?.["Policy Support Forms Text"];
  const UpdatedDate = props.fields?.["Updated Date Text"];
  const AdditionalInfo = props.fields?.["Additional Info Text"];
  const SourceText = props.fields?.["Source Link Text"]?.value;
  const EEFlag = props?.EEFlag;
  const SourceLink = props?.response?.EventLink;
  const SourceLinkObject = {
    value: {
      href: SourceLink,
      text: SourceText,
      linktype: "external",
      url: SourceLink,
      anchor: "",
    },
  };
  return (
    <>
      <h1>
        {props?.response?.EventTitle}{" "}
        {SourceLink != null ? (
          EEFlag ? (
            <>
              <a
                href={SourceLink}
                className="source-link"
                target="_blank"
                data-testid="source-link"
              >
                <Link field={SourceLinkObject}></Link>{" "}
                <span className="material-icons" aria-hidden="true">
                  open_in_new
                </span>
              </a>
            </>
          ) : (
            <Link
              field={SourceLinkObject}
              className="source-link"
              target="_blank"
            >
              <span>{SourceText} </span>
              <span className="material-icons" aria-hidden="true">
                open_in_new
              </span>
            </Link>
          )
        ) : (
          ""
        )}
      </h1>
      <div className="site flex-wrapper">
        <Placeholder
          name="jss-lmon-glossary-link"
          rendering={route}
          isEEFlag={isEEFlag}
        />
      </div>
      <section>
        <Text field={Overview} tag="h2" />
        {props?.isspinner && !isEEFlag && <Loader />}
        {props?.isError && <ErrorMessage message={props?.errorMessage} />}
        {((!props?.isspinner && !props?.isError) || isEEFlag) && (
          <div className="overview-fields flex-wrapper">
            <div className="group">
              <div className="pseudo-label">
                <b>
                  <Text field={JurisdictionText} />
                  :&nbsp;
                </b>
                <span>{props?.response?.Jurisdiction?.[0]?.DispalyName}</span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={ItemType} />
                  :&nbsp;
                </b>
                <span>{props?.response?.LmonItemType}</span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={EventDate} />
                  :&nbsp;
                </b>
                <span>{props?.response?.CreateDate}</span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={UpdatedDate} />
                  :&nbsp;
                </b>
                <span>{props?.response?.UpdatedDate}</span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={EffectiveDate} />
                  :&nbsp;
                </b>
                <span>{props?.response?.EffectiveDate}</span>
              </div>
            </div>
            <div className="group">
              <div className="pseudo-label">
                <b>
                  <Text field={Topic} />
                  :&nbsp;
                </b>
                <span>
                  {props?.response?.Topic?.map(
                    (x: { DispalyName: any }, index: any) =>
                      (index ? ", " : "") + x.DispalyName
                  )}
                </span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={Status} />
                  :&nbsp;
                </b>
                <span>{props?.response?.Status}</span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={LOBs} />
                  :&nbsp;
                </b>
                <span>
                  {props?.response?.LOBs?.map((x: { Code: any }, index: any) =>
                    x.Code !== "" && x.Code !== null
                      ? (index ? ", " : "") + x.Code
                      : ""
                  )}
                </span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={ServiceTypes} />
                  :&nbsp;
                </b>
                <span>
                  {props?.response?.ContentService?.map(
                    (x: { DispalyName: any }, index: any) =>
                      x.DispalyName !== "" && x.DispalyName !== null
                        ? (index ? ", " : "") + x.DispalyName
                        : ""
                  )}
                </span>
              </div>
            </div>
            <div className="group">
              <div className="pseudo-label">
                <b>
                  <Text field={ProceduralRequirements} />
                  :&nbsp;
                </b>
                <span>
                  {" "}
                  {props?.response?.ProcedureRequirements?.map(
                    (x: { DispalyName: any }, index: any) =>
                      x.DispalyName !== "" && x.DispalyName !== null
                        ? (index ? ", " : "") + x.DispalyName
                        : ""
                  )}
                </span>
              </div>
              <div className="pseudo-label">
                <b>
                  <Text field={PolicySupportForms} />
                  :&nbsp;
                </b>
                <span>
                  {props?.response?.PolicyReportForms?.filter((val: any) => {
                    return (
                      val.DispalyName !== "" &&
                      val.DispalyName !== undefined &&
                      val.DispalyName !== null
                    );
                  }).map((val: any, index: any) => {
                    let values = "";
                    if (index === 0) {
                      values = val.DispalyName;
                    } else {
                      values = "," + val.DispalyName;
                    }
                    return <>{values}</>;
                  })}
                </span>
              </div>
              {props?.response?.AdditionalInfo?.length != 0 ? (
                <div className="pseudo-label">
                  <b>
                    <Text field={AdditionalInfo} />:{" "}
                  </b>
                  <span>
                    {props?.response?.AdditionalInfo?.map(
                      (val: any, index: any) => {
                        return (
                          <>
                            {index ? ", " : ""}
                            <a
                              href={val?.AdditionalInfoUrl}
                              target="_blank"
                              data-testid="additional-link"
                            >
                              {val?.AdditionalInfoText}
                            </a>
                          </>
                        );
                      }
                    )}
                  </span>
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        )}
      </section>
    </>
  );
};

export default withSitecoreContext()(LmonEventOverview);
