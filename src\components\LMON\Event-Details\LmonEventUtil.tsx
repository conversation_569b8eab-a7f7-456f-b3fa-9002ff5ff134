export const EventDetailRequest = (
  props: any,
  accessToken: any,
  query: any
) => {
  const defaultResultCount = parseInt(
    props?.fields?.["Default Result Count"]?.value
  );
  let eventId: any = "";
  eventId = query?.eventId;
  let searchParams = {};
  if (!isNaN(defaultResultCount)) {
    searchParams = {
      eventId: eventId,
      count: defaultResultCount,
    };
  } else {
    searchParams = {
      eventId: eventId,
    };
  }
  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    body: JSON.stringify(searchParams),
  };
  return requestOptions;
};
