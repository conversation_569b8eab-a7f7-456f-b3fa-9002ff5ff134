import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import React, { useState, useEffect, useContext } from "react";
import Link from "next/link";
import Loader from "components/common/Loader";
import ErrorMessage from "components/common/ErrorMessage";
import { AuthContext } from "src/context/authContext";
import { EventDetailRequest } from "./LmonEventUtil";
import { useRouter } from "next/router";

type RecommendedFilingsProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const RecommendedFilings = (props: any): JSX.Element => {
  // const eventcount:any = query?.count
  const [response, setresponse] = useState<{ RecommendedFilings: any }>();
  const [isspinner, setIsSpinner] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isError, setIsError] = useState(false);
  const [isEmpty, setIsEmpty] = useState(false);
  const defaultItemCount = parseInt(
    props?.fields?.["Default Item Count"]?.value
  );
  const apiUrl = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/Lmon/FetchRecommendedFiling`;
  const context = useContext(AuthContext);
  const { accessToken } = context;
  const router = useRouter();
  const queryParam = router?.query;
  const request = EventDetailRequest(props, accessToken, queryParam);

  const data = async () => {
    try {
      setIsSpinner(true);
      setShowMore(false);
      const response = await fetch(apiUrl, request);
      const post = await response.json();
      if (
        post === "No results found" ||
        post?.count === 0 ||
        post?.RecommendedFilings === null
      ) {
        setIsEmpty(true);
        setErrorMessage("No results Found");
      } else {
        setIsEmpty(false);
      }

      setresponse(post);
      setIsSpinner(false);
      return post;
    } catch (error) {
      setIsError(true);
      setErrorMessage(
        "We are experiencing an issue loading Recommended Filings and are working to resolve it. Thank you for your patience."
      );
      setIsSpinner(false);
    }
  };

  useEffect(() => {
    // if eventID is not present, restrict the API call
    if (!!JSON.parse(request?.body)?.eventId) {
      data();
    } else {
      setIsEmpty(true);
      setErrorMessage("No results Found");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.asPath]);

  const [showMore, setShowMore] = useState(false);
  const fullFilings: any = response?.RecommendedFilings;
  const showOnlyDefaultFilings =
    fullFilings?.length > defaultItemCount
      ? fullFilings?.slice(0, defaultItemCount)
      : fullFilings;
  const ShowMoreText = props?.fields?.["Show More Text"];
  const ShowLessText = props?.fields?.["Show Less Text"];

  const toggleEvents = () => {
    setShowMore(!showMore);
  };
  const isShowMoreSelc = showMore ? " recommended-item" : "";
  const listclass = "link-list" + isShowMoreSelc;
  if (isError || isEmpty) {
    return (
      <section data-testid="rec-filings-error">
        <Text field={props?.fields?.Heading} tag="h2" />
        <ErrorMessage message={errorMessage} />
      </section>
    );
  }
  return (
    <>
      <section className="background-lt-grey">
        <h2>
          <Text field={props?.fields?.Heading} />
        </h2>
        {isspinner && <Loader />}
        {!isspinner && response?.RecommendedFilings?.length > 0 && (
          <ul className={listclass}>
            {(() => {
              if (showMore == true) {
                return fullFilings?.map((val: any, id: any) => {
                  return (
                    <li key={id}>
                      {" "}
                      <Link legacyBehavior href={val.Link}>
                        <a
                          data-region={props?.fields?.Heading?.value}
                          data-interaction="click"
                          data-title={val.Id}
                          target="_blank"
                        >
                          {val.Id}
                        </a>
                      </Link>
                    </li>
                  );
                });
              } else {
                return showOnlyDefaultFilings?.map((val: any, id: any) => {
                  return (
                    <li key={id}>
                      {" "}
                      <Link legacyBehavior href={val.Link}>
                        <a
                          data-region={props?.fields?.Heading?.value}
                          data-interaction="click"
                          data-title={val.Id}
                          target="_blank"
                        >
                          {val.Id}
                        </a>
                      </Link>
                    </li>
                  );
                });
              }
            })()}
          </ul>
        )}
        {response?.RecommendedFilings?.length > defaultItemCount &&
        !isspinner ? (
          <a
            onClick={toggleEvents}
            className="recommended-show-btn"
            tabIndex={0}
            onKeyUp={(e) => e.key === "Enter" && toggleEvents()}
            data-testid="show-btn"
          >
            {showMore ? (
              <Text field={ShowLessText} />
            ) : (
              <Text field={ShowMoreText} />
            )}
          </a>
        ) : (
          ""
        )}
      </section>
    </>
  );
};
export default withDatasourceCheck()<RecommendedFilingsProps>(
  RecommendedFilings
);
