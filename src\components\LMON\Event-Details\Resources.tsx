import {
  Text,
  Link,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type ResourcesProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const Resources = (props: any): JSX.Element => {
  return (
    <>
      <section className="background-lt-grey">
        <h2>
          <Text field={props.fields.Heading}></Text>
        </h2>
        <ul className="link-list">
          <li>
            <Link
              field={props.fields.Link1}
              target="_blank"
              data-title={props.fields["Link1"]?.value?.text}
              data-region="Resources"
              data-interaction="click"
            ></Link>
            <span className="material-icons">open_in_new</span>
          </li>
          <li>
            <Link
              field={props.fields.Link2}
              target="_blank"
              data-title={props.fields["Link2"]?.value?.text}
              data-region="Resources"
              data-interaction="click"
            ></Link>
            <span className="material-icons">open_in_new</span>
          </li>
          <li>
            <Link
              field={props.fields.Link3}
              target="_blank"
              data-title={props.fields["Link3"]?.value?.text}
              data-region="Resources"
              data-interaction="click"
            ></Link>
            <span className="material-icons">open_in_new</span>
          </li>
        </ul>
      </section>
    </>
  );
};

export default withDatasourceCheck()<ResourcesProps>(Resources);
