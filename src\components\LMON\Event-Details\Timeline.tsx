import {
  Text,
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import React, { useState, useEffect, useContext } from "react";
import Loader from "components/common/Loader";
import ErrorMessage from "components/common/ErrorMessage";
import { AuthContext } from "src/context/authContext";
import { EventDetailRequest } from "./LmonEventUtil";
import { useRouter } from "next/router";
import FilingsCheckboxes from "./FilingsCheckboxes";

const Timeline = (props: any): JSX.Element => {
  const { route } = props?.sitecoreContext || {};

  const [timeLineresults, settimeLineresults]: any = useState([]);
  const [response, setresponse]: any = useState([]);
  const [Defaulttimeline, setDefaulttimeline]: any = useState([]);
  const [isspinner, setIsSpinner] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isError, setIsError] = useState(false);
  const [Checkedboxes, setcheCkedboxes]: any = useState([]);
  const context = useContext(AuthContext);
  const { accessToken } = context;
  const router = useRouter();
  const queryParam = router?.query;
  const request = EventDetailRequest("", accessToken, queryParam);
  const ignoredLobs = "IL";

  const data = async () => {
    try {
      setIsSpinner(true);
      const result = props?.response;
      const { Timeline } = result;
      const timelinenewevent = Timeline?.filter(
        (res: any) => res?.TimelineItem == "New Event"
      );
      setDefaulttimeline(timelinenewevent);
      setresponse(result);
      const { Filings } = result;
      allIdfromFiling(Filings);
      setIsSpinner(false);
    } catch (error) {
      setIsError(true);
      setErrorMessage(
        "We are experiencing an issue loading TimeLine and are working to resolve it. Thank you for your patience."
      );
      setIsSpinner(false);
    }
  };

  useEffect(() => {
    // if eventID is not present, restrict the API call
    if (!!JSON.parse(request?.body)?.eventId) {
      data();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.asPath]);

  useEffect(() => {
    updateTimeline();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [Checkedboxes]);

  const allIdfromFiling = (Filings: any) => {
    const allId: any = Filings?.map((item: any) => item?.Id);
    if (allId) {
      setcheCkedboxes(["Circular", "Filing Event", ...allId]);
    }
  };

  const checkBoxhandler: any = (e: any) => {
    const isChecked = e.target?.checked;
    const value = e.target?.value;

    if (isChecked) {
      if (Checkedboxes?.indexOf(value) == -1) {
        if (value === "Filing Event") {
          const allId: any =
            response?.Filings?.map((item: any) => item?.Id) || [];

          const allfilingcheckbox: any = Array.from(
            new Set([...Checkedboxes, value, ...allId])
          );

          setcheCkedboxes(allfilingcheckbox);
        } else {
          if (value === "Circular") {
            setcheCkedboxes((oldValues: any) => {
              return [...oldValues, value];
            });
          } else {
            const allId: any =
              response?.Filings?.map((item: any) => item?.Id) || [];
            const allIdfromCheckboxes: any =
              Checkedboxes?.filter(
                (item: any) => item !== "Circular" && item !== "Filing Event"
              ) || [];
            if (allId?.length === allIdfromCheckboxes?.length + 1) {
              setcheCkedboxes((oldvalues: any) => {
                return [...oldvalues, "Filing Event", value];
              });
            } else {
              setcheCkedboxes((oldValues: any) => {
                return [...oldValues, value];
              });
            }
          }
        }
      }
    } else {
      if (value === "Filing Event") {
        const allId: any = response?.Filings?.map((item: any) => item?.Id);
        setcheCkedboxes((oldValues: any) => {
          return oldValues
            .filter((item: any) => item !== value)
            .filter((item: any) => allId?.indexOf(item) == -1);
        });
      } else {
        if (value !== "Circular") {
          setcheCkedboxes((oldValues: any) => {
            return oldValues
              ?.filter((item: any) => item !== value)
              ?.filter((item: any) => item !== "Filing Event");
          });
        } else {
          setcheCkedboxes((oldValues: any) => {
            return oldValues?.filter((item: any) => item !== value);
          });
        }
      }
    }
  };

  const handleCheckAllFilingBoxes: any = (e: any) => {
    const isChecked = e.target?.checked;
    const value = e.target?.value;
    const filingValues = response?.Filings?.filter(
      (val: any) => val?.LOB === value
    )?.map((item: any) => {
      return item?.Id;
    });
    const allId: any = response?.Filings?.map((item: any) => item?.Id) || [];
    const allIdfromCheckboxes: any =
      Checkedboxes?.filter(
        (item: any) => item !== "Circular" && item !== "Filing Event"
      ) || [];
    const allCheckedIds: any = Array.from(
      new Set([...filingValues, ...allIdfromCheckboxes])
    );
    if (isChecked) {
      if (allId?.length === allCheckedIds?.length) {
        setcheCkedboxes((oldvalues: any) => {
          return Array.from(
            new Set([...oldvalues, "Filing Event", ...filingValues])
          );
        });
      } else {
        setcheCkedboxes((oldValues: any) => {
          return Array.from(new Set([...oldValues, ...filingValues]));
        });
      }
    } else {
      setcheCkedboxes((oldValues: any) => {
        return oldValues?.filter((item: any) => {
          if (item === "Filing Event") {
            return false;
          } else {
            return filingValues?.indexOf(item) == -1;
          }
        });
      });
    }
  };

  const updateTimeline = () => {
    const { Timeline } = response;
    const filterdTimeline = Timeline?.filter(
      (item: any) => item.Date !== "1/1/1900"
    );
    if (
      Checkedboxes?.indexOf("Filing Event") > -1 &&
      Checkedboxes?.indexOf("Circular") > -1
    ) {
      const timeLinedata = filterdTimeline?.filter(
        (res: any) => Checkedboxes?.indexOf(res?.TimelineItem) > -1
      );
      settimeLineresults(timeLinedata);
    } else {
      if (
        Checkedboxes?.indexOf("Filing Event") > -1 &&
        Checkedboxes?.indexOf("Circular") == -1
      ) {
        const timeLinedata = filterdTimeline?.filter(
          (res: any) => res?.TimelineItem === "Filing Event"
        );

        settimeLineresults(timeLinedata);
      } else if (
        Checkedboxes?.indexOf("Filing Event") == -1 &&
        Checkedboxes?.indexOf("Circular") > -1
      ) {
        const circularplusid: any = [];
        Checkedboxes?.forEach((item: any) => {
          if (item == "Circular") {
            const timeLinedata = filterdTimeline?.filter(
              (res: any) => res?.TimelineItem === "Circular"
            );
            circularplusid.push(timeLinedata);
          } else {
            const timeLinedata = filterdTimeline?.filter(
              (res: any) => res?.Id === item
            );
            circularplusid.push(timeLinedata);
          }
        });

        const flatarray: any = circularplusid?.flat(1);
        settimeLineresults(flatarray);
      } else {
        const onlyiditems: any = [];
        Checkedboxes?.forEach((item: any) => {
          const timeLinedata = filterdTimeline?.filter(
            (res: any) => res?.Id === item
          );
          onlyiditems.push(timeLinedata);
        });

        const flatarray: any = onlyiditems?.flat(1);

        settimeLineresults(flatarray);
      }
    }
  };

  const Title = props?.fields?.["Title"];
  const DisplayAllFilingsinTimeline = props?.fields?.["DisplayFilingsText"];
  const DisplayAllCircularsinTimeline = props?.fields?.["DisplayCircularsText"];
  const datesort: any = timeLineresults
    ?.filter((item: any) => item?.Date !== "1/1/1900")
    .sort((a: any, b: any) => {
      const date1: any = new Date(a?.Date);
      const date2: any = new Date(b?.Date);

      return date2 - date1;
    });

  const newvar = Array.from(new Set(datesort));
  if (Defaulttimeline) {
    newvar.push(...Defaulttimeline);
  }
  const allId: any = response?.Filings?.map((item: any) => item?.Id) || [];

  return (
    <>
      <section>
        <Text field={Title} tag="h2" />
        <Placeholder name="jss-lmon-filings-legend" rendering={route} />
        {isspinner ? (
          <Loader />
        ) : (
          response?.Filings?.length !== 0 && (
            <form>
              <div className="flex-wrapper filings">
                {response?.Filings?.map((val: any, id: any) => {
                  return (
                    <label key={id}>
                      <input
                        id={val?.Id}
                        type="checkbox"
                        value={val?.Id}
                        checked={Checkedboxes?.indexOf(val.Id) > -1}
                        onChange={checkBoxhandler}
                        data-testid="lob-checkbox"
                      />
                      <b>{val?.LOB}: </b>
                      {val?.Link == "" || val?.Link == null ? (
                        <p className="no-link">{val?.Id?.split("_")[0]}</p>
                      ) : (
                        <a
                          href={val?.Link}
                          target="_blank"
                          data-lob={val?.LOB}
                          data-linkname={val?.Id}
                          data-servicetype={val?.ServiceType?.DispalyName}
                        >
                          {val?.Id?.split("_")[0]}
                        </a>
                      )}
                      {(() => {
                        if (val?.FilingStatus == "Approved") {
                          return (
                            <span className="material-icons approved">
                              done
                            </span>
                          );
                        } else if (val?.FilingStatus == "Disapproved") {
                          return (
                            <span className="material-icons disapproved">
                              close
                            </span>
                          );
                        } else if (val?.FilingStatus == "Filed and Pending") {
                          return (
                            <span className="material-icons filed">
                              hourglass_empty
                            </span>
                          );
                        } else if (val?.FilingStatus == "Withdrawn") {
                          return (
                            <span className="material-icons withdrawn">
                              block
                            </span>
                          );
                        } else {
                          return <div></div>;
                        }
                      })()}
                      <span className="element-wrapper">
                        <span className="badge">
                          {val?.ServiceType?.DispalyName}
                        </span>
                      </span>
                    </label>
                  );
                })}
              </div>
            </form>
          )
        )}
      </section>
      <section>
        <Text field={props?.fields?.TimelineText} tag="h2" />
        {!isspinner && (
          <form>
            {response && (
              <>
                <div className="flex-wrapper timeline-controls">
                  <label>
                    <input
                      type="checkbox"
                      value={"Filing Event"}
                      data-testid="FilingCheckbox"
                      checked={
                        Checkedboxes?.indexOf("Filing Event") > -1
                          ? true
                          : Checkedboxes?.indexOf("Circular") > -1
                          ? Checkedboxes?.length - 1 === allId?.length
                            ? true
                            : false
                          : Checkedboxes?.length === allId?.length
                          ? true
                          : false
                      }
                      onChange={checkBoxhandler}
                    />
                    <Text field={DisplayAllFilingsinTimeline} tag="b" />
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      value={"Circular"}
                      data-testid="CircularCheckbox"
                      defaultChecked={true}
                      onChange={checkBoxhandler}
                    />
                    <Text field={DisplayAllCircularsinTimeline} tag="b" />
                  </label>
                </div>
                <div className="flex-wrapper timeline-controls">
                  <FilingsCheckboxes
                    response={response}
                    handleCheckAllFilingBoxes={handleCheckAllFilingBoxes}
                    Checkedboxes={Checkedboxes}
                    textFields={props?.fields}
                  />
                </div>
              </>
            )}
          </form>
        )}
        {isspinner && <Loader />}
        {isError && <ErrorMessage message={errorMessage} />}
        {!isspinner &&
          !isError &&
          response !== "No results found" &&
          newvar?.length !== 0 && (
            <div className="timeline">
              {newvar?.map((val: any, id: any) => {
                return (
                  <React.Fragment key={id}>
                    <div className="event flex-wrapper">
                      <time>
                        {val?.Date}
                        <span className={id == 0 ? "current" : ""}></span>
                      </time>
                      <div>
                        <h3>{val?.Title}</h3>
                        <p>
                          {val?.Link == "" || val?.Link == null ? (
                            <span
                              className="no-link"
                              data-testid={
                                Checkedboxes?.indexOf(val?.Id) > -1
                                  ? "Filing-Event"
                                  : val?.Id != null
                                  ? "Circular-Event"
                                  : "New-Event"
                              }
                            >
                              {val?.Id?.split("_")[0]}
                            </span>
                          ) : (
                            <a
                              href={val?.Link}
                              target="_blank"
                              data-date={val?.Date}
                              data-title={val?.Title}
                              data-documentnumber={val?.Id}
                              data-testid={
                                Checkedboxes?.indexOf(val?.Id) > -1
                                  ? "Filing-Event"
                                  : val?.Id != null
                                  ? "Circular-Event"
                                  : "New-Event"
                              }
                            >
                              {val?.Id?.split("_")[0]}
                            </a>
                          )}{" "}
                          {val?.SubTitle}
                        </p>
                        {val?.Lob !== null || val?.AdditionalLOBs !== null ? (
                          <p>
                            <Text field={props?.fields?.LobsText} />:{" "}
                            {ignoredLobs?.includes(val?.Lob) ? "" : val?.Lob}
                            {val?.AdditionalLOBs?.map(
                              (item: any, index: any) => {
                                return item?.Code &&
                                  item?.Code !== val?.Lob &&
                                  index < val?.AdditionalLOBs?.length
                                  ? val?.Lob
                                    ? ignoredLobs.includes(val.Lob)
                                      ? index
                                        ? `,${item?.Code}`
                                        : `${item?.Code}`
                                      : `,${item?.Code}`
                                    : ""
                                  : "";
                              }
                            )}
                          </p>
                        ) : undefined}
                      </div>
                    </div>
                  </React.Fragment>
                );
              })}
            </div>
          )}
      </section>
    </>
  );
};
export default withSitecoreContext()(Timeline);
