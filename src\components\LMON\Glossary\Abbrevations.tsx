import {
  Text,
  Field,
  withDatasource<PERSON>heck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import { useEffect, useState } from "react";

type AbbrevationsProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};
const RenderData = (props: any) => {
  const textFields = props?.fields;
  // const mainLabel = isCommercial ? textFields?.LOBTableName : textFields?.LOBTableName
  // const lobarray = isCommercial ? textFields?.LOBAbbrevations : textFields?.LOBAbbrevations
  return (
    <div>
      <Text field={textFields?.TableName} tag="h3" />
      <table>
        <thead>
          <tr>
            <th className="abbreviation">
              <Text field={textFields?.AbbrevationColumnLabel} />
            </th>
            <th>
              <Text field={textFields?.DefinitionColumnnLabel} />
            </th>
          </tr>
        </thead>
        <tbody>
          {textFields?.LOBAbbrevations?.filter(({ fields }: any) => {
            return (
              fields?.Abbrevation?.value !== "" &&
              fields?.Abbrevation?.value !== undefined
            );
          })
            ?.sort(function (a: any, b: any) {
              if (a.fields?.Abbrevation?.value < b.fields?.Abbrevation?.value) {
                return -1;
              }
              if (a.fields?.Abbrevation?.value > b.fields?.Abbrevation?.value) {
                return 1;
              }
              return 0;
            })
            ?.map((value: any) => {
              return (
                <tr key={value?.id}>
                  <td>
                    <Text field={value?.fields?.Abbrevation} />
                  </td>
                  <td>
                    <Text field={value?.fields?.Definition} />
                  </td>
                </tr>
              );
            })}
        </tbody>
      </table>
    </div>
  );
};
const Abbrevations = (props: any): JSX.Element => {
  const isEEFlag = props?.isEEFlag;
  const [clientSide, setClientSide] = useState(false);
  useEffect(() => {
    setClientSide(true);
  }, []);
  return <>{(clientSide || isEEFlag) && RenderData(props)}</>;
};

export default withDatasourceCheck()<AbbrevationsProps>(Abbrevations);
