import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type AbbrevationsTitleProps = ComponentProps & {
  fields: {
    Heading: Field<string>;
  };
};

const AbbrevationsTitle = (props: AbbrevationsTitleProps): JSX.Element => {
  return (
    <>
      <Text field={props?.fields?.Heading} tag="h2" />
    </>
  );
};

export default withDatasourceCheck()<AbbrevationsTitleProps>(AbbrevationsTitle);
