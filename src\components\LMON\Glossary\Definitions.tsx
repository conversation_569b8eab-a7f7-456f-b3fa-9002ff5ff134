import {
  Text,
  Field,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RichText,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import React, { useEffect, useState } from "react";

type DefinitionsProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const Definitions = (props: any): JSX.Element => {
  const isEEFlag = props?.isEEFlag;
  const [clientSide, setClientSide] = useState(false);
  useEffect(() => {
    setClientSide(true);
  }, []);
  return (
    <>
      {(clientSide || isEEFlag) && (
        <section className="field-definitions">
          <Text field={props?.fields?.Heading} tag="h2" />
          <table>
            <tbody>
              <tr>
                <th className="fieldname">
                  <Text field={props?.fields?.TermLabel} />
                </th>
                <th>
                  <Text field={props?.fields?.DefinitionsLabel} />
                </th>
              </tr>
              {props?.fields?.LmonDefinitions?.filter(({ fields }: any) => {
                return (
                  fields?.Term?.value !== "" &&
                  fields?.Term?.value !== undefined
                );
              })
                ?.sort(function (a: any, b: any) {
                  if (a.fields?.Term?.value < b.fields?.Term?.value) {
                    return -1;
                  }
                  if (a.fields?.Term?.value > b.fields?.Term?.value) {
                    return 1;
                  }
                  return 0;
                })
                ?.map((value: any) => {
                  return (
                    <tr key={value?.id}>
                      <td>
                        <Text field={value?.fields?.Term} />
                      </td>
                      <td>
                        <RichText field={value?.fields?.Definition} />
                      </td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </section>
      )}
    </>
  );
};

export default withDatasourceCheck()<DefinitionsProps>(Definitions);
