import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type GlossaryTitleProps = ComponentProps & {
  fields: {
    Heading: Field<string>;
  };
};

const GlossaryTitle = (props: GlossaryTitleProps): JSX.Element => {
  return <Text field={props?.fields?.Heading} tag="h1" className="no-margin" />;
};

export default withDatasourceCheck()<GlossaryTitleProps>(GlossaryTitle);
