import {
  Text,
  Placeholder,
  withSitecoreContext,
} from "@sitecore-jss/sitecore-jss-nextjs";

const LmonHubHeading = (props: any): JSX.Element => {
  const { route } = props?.sitecoreContext || {};
  const isEEFlag = props?.isEEFlag;
  return (
    <>
      <h2 className="no-margin-top">
        <Text field={props.fields.Title} />
      </h2>
      <div className="flex-wrapper glossary-hub">
        <Placeholder
          name="jss-lmon-glossary-link"
          rendering={route}
          isEEFlag={isEEFlag}
        />
      </div>
    </>
  );
};

export default withSitecoreContext()(LmonHubHeading);
