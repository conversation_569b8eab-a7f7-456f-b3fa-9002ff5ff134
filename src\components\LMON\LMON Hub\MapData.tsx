/* eslint-disable @typescript-eslint/ban-ts-comment  */
import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import React, { useState, useEffect, useContext, useRef } from "react";
import { ComponentProps } from "lib/component-props";
import Loader from "components/common/Loader";
import ErrorMessage from "components/common/ErrorMessage";
import { AuthContext } from "src/context/authContext";
import MapView from "./MapView";
import TableView from "./TableView";
import { useRouter } from "next/router";
import { getQueryParams } from "./MapTableViewUtils";
import useMediaQuery from "./useMediaQuery";
import {
  initialSortColumn,
  initialSortDirection,
  lobCodeValues,
  selectedLobCode,
} from "./MapTableViewUtils";
import DatePicker from "../LMON Search/DatePicker";
import { getDayAfter } from "../LMON Search/LmonSearchUtils";

type MapDataProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;

  if (
    typeof a !== "object" ||
    typeof b !== "object" ||
    a === null ||
    b === null
  ) {
    return false;
  }

  const keysA = Object.keys(a);
  const keysB = Object.keys(b);

  if (keysA.length !== keysB.length) return false;

  for (let key of keysA) {
    if (!keysB.includes(key) || !deepEqual(a[key], b[key])) {
      return false;
    }
  }

  return true;
}

const MapData = (props: any): React.JSX.Element => {
  const [controller, setController] = useState(new AbortController());
  const [response, setResponse]: any = useState<any>(null);
  const [toggleState, setToggleState] = useState(1);
  const [showdate, setShowdate] = useState(false);
  const [luValue, setLuValue] = useState("Latest 180 Days");
  const [fdValue, setFdValue] = useState("");
  const [tdValue, setTdValue] = useState("");
  const [isspinner, setIsSpinner] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const [isError, setIsError] = useState(false);
  const [selectedTab, setSelectedTab] = useState("All");
  const [isAllLobChecked, setIsAllLobChecked] = useState(false);
  const [activeToggleTab, setActiveToggleTab] = useState(() => {
    if (typeof window !== "undefined") {
      const isMobile = window?.matchMedia("(max-width: 1080px)").matches; // Set the breakpoint according to needs
      if (isMobile) {
        return "List";
      }
      return "Map";
    }
    return "Map";
  });
  const [sortColumn, setSortColumn] = useState<any>(initialSortColumn);
  const [sortDirection, setSortDirection] = useState<any>(initialSortDirection);

  //Paginations variables
  const [itemsPerPage, setitemsPerPage] = useState(10);
  const [currentPage, setcurrentPage] = useState(1);
  const [maxPageNumberLimit, setmaxPageNumberLimit] = useState(3);
  const [minPageNumberLimit, setminPageNumberLimit] = useState(0);
  const [pageNumberLimit] = useState(3);
  const [pages, setPages] = useState([]);
  const [totalPages, setTotalPages] = useState();
  const [lobValue, setLobValue] = useState<any>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef: any = useRef(null);

  const { fields } = props;
  const context = useContext(AuthContext);
  const { accessToken } = context;
  const router = useRouter();

  const [requestParams, setRequestParams] = useState<any>({
    lastUpdated: luValue,
    fromDate: fdValue,
    toDate: tdValue,
    PageSize: itemsPerPage,
    sortdirection: sortDirection,
    sortcolumn: sortColumn,
    activeToggle: activeToggleTab,
    PageNumber: currentPage,
    lob: lobValue,
  });

  useEffect(() => {
    const fetchDataAsync = async () => {
      setIsError(false);
      setIsSpinner(true);
      try {
        controller.abort();
      } catch (error) {
        console.log("Abort Failed!!!");
      }
    };
    const newController = new AbortController();
    setController(newController);
    fetchData(newController);
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "TablesortOptions",
        JSON.stringify({
          sortColumn: requestParams?.sortcolumn,
          sortDirection: requestParams?.sortdirection,
        })
      );
    }
    fetchDataAsync();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestParams, toggleState]);

  const updateRequestParams = (nextParams: any) => {
    setRequestParams((prev: any) => {
      if (!deepEqual(prev, nextParams)) {
        return nextParams;
      }
      return prev;
    });
  };

  useEffect(() => {
    updateRequestParams({ ...requestParams, lob: lobValue });
  }, [lobValue]);

  const isSmallerScreen = useMediaQuery("(max-width: 1080px)");
  useEffect(() => {
    if (isSmallerScreen) {
      if (activeToggleTab !== "List") {
        updateRequestParams({ ...requestParams, activeToggle: "List" });
      }
    } else {
      if (activeToggleTab !== requestParams?.activeToggle) {
        updateRequestParams({
          ...requestParams,
          activeToggle: activeToggleTab,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSmallerScreen]);

  const getLmonSearchPageLink = (
    Code: any,
    Name: any,
    Data: any,
    Tab: any,
    FDate: any,
    TDate: any,
    selectedTab: any,
    lobQuery: string
  ) => {
    const baseLink = `lmon-hub/searchpage?js=${
      Code !== "All" ? Code : ""
    }&name=${Name !== "All" ? Name : ""}&timeperiod=${Data}`;

    switch (selectedTab) {
      case "All":
        return `${baseLink}&uds=&ude=&st=${lobQuery}`;
      case "All Active":
      case "Active":
        return `${baseLink}&uds=&ude=&st=${filterTabValues(Tab, [
          "INITIALREVIEW",
          "NOFURTHERACTION",
        ])}${lobQuery}`;
      case "Filing Impact":
      case "Under Filing Review":
        return `${baseLink}&uds=&ude=&st=${Tab}${lobQuery}`;
      case "No Filing Impact":
      case "Possible Procedural Impact":
        return `${baseLink}&uds=&ude=&st=${filterTabValues(Tab, [
          "NOFURTHERACTION",
        ])}${lobQuery}`;
      default:
        return `${baseLink}&uds=${FDate}&ude=${TDate}&st=${Tab}${lobQuery}`;
    }
  };

  const filterTabValues = (Tab: any, excludeValues: string[]) =>
    Tab?.filter((tabVal: any) => !excludeValues.includes(tabVal));

  const routeToLmonSearchPage = (
    Code: any,
    Name: any,
    Data: any,
    FDate: any,
    TDate: any,
    Tab: any,
    jurisdictionArrayResults: any,
    selectedTab: any
  ) => {
    const lobQuery = selectedLobCode(jurisdictionArrayResults, Code)
      ? `&lob=${selectedLobCode(jurisdictionArrayResults, Code)}`
      : "";

    const lmonSearchPageLink = getLmonSearchPageLink(
      Code,
      Name,
      Data,
      Tab,
      FDate,
      TDate,
      selectedTab,
      lobQuery
    );

    router.push(lmonSearchPageLink);
  };

  //Results per page handlers
  const handleResultPerPage = (event: any) => {
    scrollToTop();
    updateRequestParams({
      ...requestParams,
      PageSize: event.target?.value,
      PageNumber: 1,
    });
    setitemsPerPage(event.target?.value);
    setmaxPageNumberLimit(3);
    setminPageNumberLimit(0);
  };

  //Pagination handlers
  const handleClick = (event: any) => {
    scrollToTop();
    updateRequestParams({
      ...requestParams,
      PageNumber: Number(event.target.id),
    });
    setcurrentPage(Number(event.target.id));
  };

  const handlePrevbtn = (e: any) => {
    e.preventDefault();
    scrollToTop();
    updateRequestParams({ ...requestParams, PageNumber: currentPage - 1 });

    if ((currentPage - 1) % pageNumberLimit == 0) {
      setmaxPageNumberLimit(currentPage - 1);
      setminPageNumberLimit(currentPage - pageNumberLimit - 1);
    }
  };

  const handleNextbtn = () => {
    scrollToTop();
    updateRequestParams({ ...requestParams, PageNumber: currentPage + 1 });

    if (currentPage + 1 > maxPageNumberLimit) {
      setmaxPageNumberLimit(maxPageNumberLimit + pageNumberLimit);
      setminPageNumberLimit(minPageNumberLimit + pageNumberLimit);
    }
  };

  const handleExtreamNextbtn = () => {
    scrollToTop();
    if (pages.length > 0) {
      const lastPage: any = totalPages;
      updateRequestParams({ ...requestParams, PageNumber: totalPages });

      if (lastPage % pageNumberLimit !== 0) {
        // condition is for no.of pages not multiple of pagelimit(3)
        setmaxPageNumberLimit(lastPage); // last page is fixed that should be hightlighed
        setminPageNumberLimit(
          lastPage - ((lastPage % pageNumberLimit) - 1) - 1
        ); //(lastpage%pagelimit) will give left over pages to show, (lastPage % pageNumberLimit - 1) give what should minus to get the starting value and extra -1 will handle what number should show.
      } else {
        // if no.of pages is multiple of 3
        setmaxPageNumberLimit(lastPage);
        setminPageNumberLimit(lastPage - (pageNumberLimit + 1) + 1); //
      }
    }
  };

  const handleExtreamPrevbtn = () => {
    scrollToTop();
    if (pages.length > 0) {
      const firstPage = pages[0];
      updateRequestParams({ ...requestParams, PageNumber: firstPage });
      setcurrentPage(firstPage);
      setmaxPageNumberLimit(pageNumberLimit); // to show maximum 3 page incase of more than 3 pages
      setminPageNumberLimit(firstPage - 1); // min always will be first page so min should be 0
    }
  };

  function handleLuChange(event: { target: { value: any } }) {
    setLuValue(event.target.value);
    if (event.target.value === "Custom") {
      setShowdate(true);
    } else {
      setShowdate(false);
    }
    updateRequestParams({
      ...requestParams,
      lastUpdated: event?.target?.value,
    });
  }

  function handleFdChange(event: any, tdValue: any) {
    setFdValue(event.target.value);
    if (tdValue === "") {
      console.log("API not called");
    } else {
      updateRequestParams({ ...requestParams, fromDate: event?.target?.value });
    }
  }

  function handleTdChange(event: { target: { value: any } }) {
    setTdValue(event.target.value);
    updateRequestParams({ ...requestParams, toDate: event?.target?.value });
  }

  const handleToggle = (tab: any) => {
    setActiveToggleTab(tab);
    updateRequestParams({ ...requestParams, activeToggle: tab });
  };

  const handleIcon = (column: any) => {
    if (column !== sortColumn) {
      setSortDirection("DESC");
      setSortColumn(column);
      updateRequestParams({
        ...requestParams,
        sortcolumn: column,
        sortdirection: "DESC",
      });
    }
  };

  const handlesortitem = (e: any, column: any) => {
    e.stopPropagation();
    if (sortDirection === "ASC") {
      updateRequestParams({
        ...requestParams,
        sortcolumn: column,
        sortdirection: "DESC",
      });
      setSortDirection("DESC");
    } else {
      updateRequestParams({
        ...requestParams,
        sortcolumn: column,
        sortdirection: "ASC",
      });
      setSortDirection("ASC");
    }
  };

  const ToggleLobDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const openLobDropdown = isDropdownOpen ? "is-open" : "";
  const showDropDown = isDropdownOpen ? "show" : "";

  const handlelobCheck = (event: any) => {
    const lobCheckedValue = event?.target?.value;
    if (event.target.checked) {
      if (lobCheckedValue === "All") {
        const lobCodes = lobCodeValues(props?.fields?.["Lob Tags"]);
        setLobValue((oldValues: any) => {
          return Array.from(new Set([...oldValues, ...lobCodes]));
        });
        setIsAllLobChecked(true);
      } else {
        setLobValue((oldValues: any) => {
          return [...oldValues, lobCheckedValue];
        });
        setIsAllLobChecked(false);
      }
    } else {
      if (lobCheckedValue === "All") {
        const lobCodes = lobCodeValues(props?.fields?.["Lob Tags"]);
        setLobValue((oldValues: any) => {
          return oldValues
            .filter((item: any) => item !== lobCheckedValue)
            .filter((item: any) => lobCodes?.indexOf(item) == -1);
        });
      } else {
        setLobValue((oldValues: any) => {
          return oldValues?.filter((item: any) => item !== lobCheckedValue);
        });
      }
    }
  };

  const handleClearAllLob = () => {
    setLobValue([]);
  };

  useEffect(() => {
    const handleOutsideClick = (event: any) => {
      if (
        dropdownRef?.current &&
        !dropdownRef?.current?.contains(event.target)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    } else {
      document.removeEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isDropdownOpen]);

  const fetchData = async (newController: any) => {
    const { signal } = newController;
    try {
      const { lu, fd, td, st } = getQueryParams(
        luValue,
        fdValue,
        tdValue,
        toggleState
      );
      const data = {
        LastUpdated: lu,
        FromDate: fd,
        ToDate: td,
        Status: st,
        PageNumber: requestParams?.PageNumber,
        PageSize: requestParams?.PageSize,
        SortItem: requestParams?.sortcolumn,
        SortOrder: requestParams?.sortdirection,
        LineOfBusiness: isAllLobChecked ? "" : requestParams?.lob?.join(","),
      };

      let requestDataToApi = {};
      if (requestParams?.activeToggle === "List") {
        const updatedParams = { ...data, ListView: "yes" };
        requestDataToApi = updatedParams;
      } else {
        const updatedParams = { ...data, ListView: "no" };
        requestDataToApi = updatedParams;
      }
      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + accessToken,
        },
        body: JSON.stringify(requestDataToApi),
        signal,
      };
      setIsSpinner(true);
      setIsError(false);
      const url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/FetchLmonMap/results`;
      const postUrl = await fetch(url, requestOptions).then((res) =>
        res.json()
      );
      setIsSpinner(false);
      setResponse(postUrl);
      setcurrentPage(postUrl.PaginationInfo?.CurrentPage);
      setitemsPerPage(postUrl.PaginationInfo?.PageSize);
      setTotalPages(postUrl.PaginationInfo?.TotalPages);
      const pages: any = [];
      for (let i = 1; i <= postUrl.PaginationInfo?.TotalPages; i++) {
        pages.push(i);
      }
      setPages(pages);
    } catch (error: any) {
      console.log("error--", error);
      if (error?.name !== "AbortError") {
        setIsError(true);
        setIsSpinner(false);
        setErrorMessage(
          "We are experiencing an issue loading Map and are working to resolve it. Thank you for your patience."
        );
      }
    }
  };
  const renderPageNumbers = pages.map((number: any, id: any) => {
    if (number < maxPageNumberLimit + 1 && number > minPageNumberLimit) {
      return (
        <li
          className={
            currentPage == number
              ? "page-item page-number active"
              : "page-item page-number"
          }
          key={id}
        >
          <a
            data-testid="mapivew-anchor"
            id={number}
            key={number}
            tabIndex={0}
            onKeyUp={(e) => e.key === "Enter" && handleClick(e)}
            onClick={handleClick}
            className={currentPage == number ? "page-link active" : "page-link"}
          >
            {number}
          </a>
        </li>
      );
    } else {
      return null;
    }
  });

  const today = new Date().toISOString().split("T")[0];

  return (
    <>
      <form>
        <p>
          <Text field={props.fields.FiltersTitle} />
        </p>
        <div className="site flex-wrapper">
          <label>
            <b>
              <Text field={props.fields.LastUpdatedText} />
            </b>
            <div className="select-wrapper">
              <select
                onChange={handleLuChange}
                value={luValue}
                data-testid="lu-select"
              >
                {props.fields?.TimePeriodOptions.map((val: any, id: any) => {
                  return (
                    <>
                      <option key={id}>{val.fields.Key?.value}</option>
                    </>
                  );
                })}
              </select>
            </div>
          </label>
          {showdate ? (
            <div className="pseudo-label">
              <b>
                <Text field={props.fields.DateRangeText} />
              </b>
              <DatePicker
                aria-label="start-date"
                onChange={(e) => handleFdChange(e, tdValue)}
                value={fdValue}
                data-testid="fd-date"
                max={today}
              />
              <span>&ndash;</span>{" "}
              <DatePicker
                aria-label="end-date"
                onChange={handleTdChange}
                value={tdValue}
                disabled={!fdValue}
                min={fdValue ? getDayAfter(fdValue) : today}
              />
            </div>
          ) : null}
          <div className="dropdown multiselect" ref={dropdownRef}>
            <label>
              <b>
                <Text field={props?.fields?.LOBFilterText} />
              </b>
            </label>
            <button
              type="button"
              className={`dropdown-button ${openLobDropdown}`}
              onClick={ToggleLobDropdown}
              data-testid="ToggleLobDropdown"
            >
              <span className="dropdown-text show">
                Select Line(s) of Business
              </span>
              <div id="checkCount" className="count"></div>
              <span className="material-icons">expand_less</span>
            </button>
            <div
              id="myDropdown"
              className={`dropdown-container ${showDropDown}`}
            >
              <div
                className="clear-btn"
                onClick={handleClearAllLob}
                data-testid="ClearAllLob"
              >
                Clear
              </div>
              <div className="dropdown-content">
                <div className="dropdown-option select-all">
                  <label>
                    <input
                      type="checkbox"
                      id="selectAll"
                      onChange={handlelobCheck}
                      value="All"
                      checked={
                        lobValue?.length ===
                        lobCodeValues(props?.fields?.["Lob Tags"])?.length
                      }
                      data-testid={"All lob"}
                    />
                    All Line(s) of Business
                  </label>
                </div>
                {props?.fields?.["Lob Tags"]?.map((lob: any) => {
                  return (
                    <>
                      <div className="dropdown-option-nested">
                        <label>
                          <input
                            type="checkbox"
                            id={lob?.fields?.Code?.value}
                            name={lob?.fields?.["Display Label"]?.value}
                            value={lob?.fields?.Code?.value}
                            onChange={handlelobCheck}
                            checked={
                              lobValue?.indexOf(lob?.fields?.Code?.value) > -1
                            }
                            data-testid={lob?.fields?.["Display Label"]?.value}
                          />
                          <span>{lob?.fields?.["Display Label"]?.value}</span>
                        </label>
                      </div>
                    </>
                  );
                })}
              </div>
            </div>
          </div>
          <div className="tabs no-background flex-wrapper">
            View as
            <div className="tabbed no-dotted-bottom-border">
              <nav>
                {/* <a id="lmonListViewBtn" title="List view" aria-label="List view"> */}
                <a
                  id="lmonTableViewBtn"
                  href="javascript:void(0);"
                  aria-disabled={
                    requestParams?.activeToggle === "List" ? true : false
                  }
                  className={`table-view ${
                    requestParams?.activeToggle === "List" ? "active" : ""
                  }`}
                  onClick={() => handleToggle("List")}
                  title="Table view"
                  aria-label="Table view"
                  data-searchType="table"
                  data-testid="map-data-nav-table-view"
                >
                  <span className="material-symbols-outlined">
                    <span aria-hidden="true">view_column</span>
                  </span>
                </a>
                {/* <a id="lmonMapViewBtn" class="active" title="Map view" aria-label="Map view"> */}
                <a
                  id="lmonMapViewBtn"
                  href="javascript:void(0);"
                  aria-disabled={
                    requestParams?.activeToggle === "Map" ? true : false
                  }
                  className={`map-view ${
                    requestParams?.activeToggle === "Map" ? "active" : ""
                  }`}
                  onClick={() => handleToggle("Map")}
                  title="Map view"
                  aria-label="Map view"
                  data-searchType="map"
                  data-testid="map-data-nav-map-view"
                >
                  <span className="material-symbols-outlined">
                    <span aria-hidden="true">map</span>
                  </span>
                </a>
              </nav>
            </div>
          </div>
        </div>
      </form>
      {isspinner && <Loader />}
      {isError && (
        <ErrorMessage dataTestId="map-data-error" message={errorMessage} />
      )}
      {!isspinner && !isError && response && (
        <MapView
          fields={props.fields}
          response={response}
          toggleState={toggleState}
          selectedTab={selectedTab}
          luValue={luValue}
          fdValue={fdValue}
          tdValue={tdValue}
          showdate={showdate}
          setSelectedTab={setSelectedTab}
          setToggleState={setToggleState}
          routeToLmonSearchPage={routeToLmonSearchPage}
        />
      )}
      {!isspinner && !isError && response && (
        <TableView
          response={response}
          fields={fields}
          routeToLmonSearchPage={routeToLmonSearchPage}
          luValue={luValue}
          fdValue={fdValue}
          tdValue={tdValue}
          handleIcon={handleIcon}
          handlesortitem={handlesortitem}
          sortColumn={sortColumn}
          sortDirection={sortDirection}
          itemsPerPage={itemsPerPage}
          handleResultPerPage={handleResultPerPage}
          //Pagination
          handleNextbtn={handleNextbtn}
          handlePrevbtn={handlePrevbtn}
          handleExtreamNextbtn={handleExtreamNextbtn}
          handleExtreamPrevbtn={handleExtreamPrevbtn}
          currentPage={currentPage}
          pages={pages}
          renderPageNumbers={renderPageNumbers}
          selectedTab={selectedTab}
          showdate={showdate}
        />
      )}
    </>
  );
};

export default withDatasourceCheck()<MapDataProps>(MapData);
