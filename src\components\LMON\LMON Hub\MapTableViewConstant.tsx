let all = [
  "CREATED",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>E<PERSON>",
  "OB<PERSON><PERSON><PERSON><PERSON>",
  "PROCEDURALIMPACT",
  "RECEIVED",
  "SENTTOFIELD",
  "DRAFT",
  "FILINGINPROGRESS",
  "APPROVED",
  "EXPLORATORY",
  "UNDERDEV",
  "PREP",
  "PENDING",
  "CORRECTED",
  "IMPLANNOUNCED",
  "DISAPPROVED",
  "FINAL",
  "DON<PERSON>",
  "NEW",
  "NOFURTHERACTION",
  "NOIMPACT",
  "REVIEW",
  "WITHDRAWN",
];
let filingImpact = ["DONE", "FILINGINPROGRESS"];
let underReview = ["REVIEW"];
let initialReview = ["INITIALREVIEW"];
let noFilingImpact = ["NOFURTHERACTION", "PROCEDURALIMPACT"];
let allActive = [
  "DONE",
  "FILINGINPROGRESS",
  "RE<PERSON><PERSON><PERSON>",
  "INIT<PERSON><PERSON><PERSON><PERSON>E<PERSON>",
  "NOFURTHERACTION",
  "PROCEDURALIMPACT",
];
export const UpdatedStatusFacet: any = {
  All: all,
  "All Active": allActive,
  "Filing Impact": filingImpact,
  "Under Filing Review": underReview,
  "Initial Review": initialReview,
  "No Filing Impact": noFilingImpact,
};
export const UpdatedDateFacet: any = {
  "All Time": "alltime",
  Today: "0",
  "Latest 7 Days": "7",
  "Latest 30 Days": "30",
  "Latest 90 Days": "90",
  "Latest 180 Days": "180",
  "1 Year": "365",
  "5 Years": "1825",
  Custom: "custom",
};

export const SelectedTabValue: any = {
  "1": "AllEvents",
  "2": "Active",
  "3": "FilingImpact",
  "4": "UnderReview",
  "5": "NoFilingImpact",
  "6": "InitialReview",
};

export const TotalJuridictionCount: any = {
  "1": "AllEventsCount",
  "2": "AllActiveEventsCount",
  "3": "FilingImpactStatusResults",
  "4": "UnderReviewStatusResults",
  "5": "NoFilingImpactStatusResults",
  "6": "InitialReviewStatusResults",
};
