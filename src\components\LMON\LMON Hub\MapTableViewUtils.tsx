import { Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { UpdatedDateFacet, UpdatedStatusFacet } from "./MapTableViewConstant";
//Common Utils

export const getQueryParams = (
  luValue: any,
  fdValue: any,
  tdValue: any,
  toggleState: any
) => {
  return {
    lu:
      luValue == "ALL Time"
        ? "alltime"
        : luValue == "Today"
        ? "today"
        : luValue == "Latest 7 Days"
        ? "7"
        : luValue == "Latest 30 Days"
        ? "30"
        : luValue == "Latest 90 Days"
        ? "90"
        : luValue == "Latest 180 Days"
        ? "180"
        : luValue == "1 Year"
        ? "365"
        : luValue == "5 Years"
        ? "1825"
        : luValue == "Custom"
        ? "custom"
        : "alltime",
    fd: fdValue,
    td: tdValue,
    st:
      toggleState === 1
        ? "all"
        : toggleState === 2
        ? "allactive"
        : toggleState === 3
        ? "filingimpact"
        : toggleState === 4
        ? "underreview"
        : toggleState === 5
        ? "nofilingimpact"
        : toggleState === 6
        ? "initialreview"
        : "all",
  };
};

//Map Utils

export const toggleTab = async (
  index: any,
  setSelectedTab: any,
  setToggleState: any,
  event: any
) => {
  event?.preventDefault();
  let stValue = "all";
  switch (index) {
    case 1:
      stValue = "All";
      break;
    case 2:
      stValue = "All Active";
      break;
    case 3:
      stValue = "Filing Impact";
      break;
    case 4:
      stValue = "Under Filing Review";
      break;
    case 5:
      stValue = "No Filing Impact";
      break;
    case 6:
      stValue = "Initial Review";
      break;
    default:
      break;
  }

  setSelectedTab(stValue);
  setToggleState(index);
};

export const fedralCount = (data: any, selectedTab: any) => {
  const fedralJuri: any = data?.JurisdictionResults?.filter(
    (juri: any) => juri?.Name === "Federal" && juri?.Code === "FED"
  );
  if (fedralJuri?.length > 0) {
    const StatusCounts = fedralJuri[0]?.StatusCounts;
    return StatusCounts[selectedTab] || 0;
  }
  return 0;
};

//Table Utils

export const statusColumnHeading = (
  columnText: any,
  isJuri: any,
  handleIcon: any,
  handlesortitem: any,
  sortColumn: any,
  sortDirection: any,
  sortvalue: any
) => {
  if (isJuri) {
    return (
      <th className="active-sort-header descending">
        <a
          data-testid="table-heading-link"
          href="javascript:void(0);"
          onClick={() => handleIcon("js")}
          tabIndex={sortvalue !== sortColumn ? 0 : -1}
          onKeyUp={(e) => e.key === "Enter" && handleIcon("js")}
        >
          <Text field={columnText} />
          {sortColumn === sortvalue && (
            <span
              className="material-icons"
              onClick={(e) => handlesortitem(e, sortvalue)}
              tabIndex={0}
              onKeyUp={(e) => e.key === "Enter" && handlesortitem(e, sortvalue)}
              data-testid="table-sorting-span"
            >
              {sortDirection === "ASC" ? (
                <>arrow_upward</>
              ) : (
                <>arrow_downward</>
              )}
            </span>
          )}
        </a>
      </th>
    );
  }
  return (
    <th>
      <a
        href="javascript:void(0);"
        onClick={() => handleIcon(sortvalue)}
        tabIndex={sortvalue !== sortColumn ? 0 : -1}
        onKeyUp={(e) => e.key === "Enter" && handleIcon(sortvalue)}
        data-testid="sort-table-header-icon"
      >
        <Text field={columnText} />
        {sortColumn === sortvalue && (
          <span
            className="material-icons"
            onClick={(e) => handlesortitem(e, sortvalue)}
            tabIndex={0}
            onKeyUp={(e) => e.key === "Enter" && handlesortitem(e, sortvalue)}
            data-testid="sort-table"
          >
            {sortDirection === "ASC" ? <>arrow_upward</> : <>arrow_downward</>}
          </span>
        )}
      </a>
    </th>
  );
};
export const statusColumnValue = (
  columnvalue: any,
  isJuri: any,
  routeToLmonSearchPage: any,
  Name: any,
  Code: any,
  luValue: any,
  fdValue: any,
  tdValue: any,
  selectedTab: any,
  showdate: any,
  JurisdictionResult: any
) => {
  if (isJuri) {
    return <>{<th className="active-sort">{columnvalue}</th>}</>;
  } else {
    if (columnvalue > 0) {
      return (
        <td>
          <a
            href="#"
            onClick={() => {
              const updatedStatusValue =
                selectedTab?.value === "Possible Procedural Impact"
                  ? UpdatedStatusFacet["No Filing Impact"]
                  : selectedTab?.value === "Active"
                  ? UpdatedStatusFacet["All Active"]
                  : UpdatedStatusFacet[selectedTab?.value];
              routeToLmonSearchPage(
                Code,
                Name,
                UpdatedDateFacet[luValue],
                fdValue,
                tdValue,
                updatedStatusValue,
                JurisdictionResult,
                selectedTab?.value
              );
            }}
            data-tab={selectedTab?.value}
            data-updated={luValue}
            data-state={Name}
            data-date={showdate ? `${fdValue}_${tdValue}` : ""}
            data-searchType="table"
          >
            {columnvalue}
          </a>
        </td>
      );
    } else {
      return (
        <td>
          <span>{columnvalue}</span>
        </td>
      );
    }
  }
};
export const initialSortColumn = () => {
  if (typeof window !== "undefined") {
    const storedSortOptions = localStorage.getItem("TablesortOptions");
    if (storedSortOptions) {
      const parsedStoredSortOptions = JSON.parse(storedSortOptions);
      return parsedStoredSortOptions?.sortColumn;
    }
  }
  return "js";
};

export const initialSortDirection = () => {
  if (typeof window !== "undefined") {
    const storedSortOptions = localStorage.getItem("TablesortOptions");
    if (storedSortOptions) {
      const parsedStoredSortOptions = JSON.parse(storedSortOptions);
      return parsedStoredSortOptions?.sortDirection;
    }
  }
  return "ASC";
};

export const lobCodeValues = (lobTagValues: any) => {
  const TagValues =
    lobTagValues?.map((lob: any) => lob?.fields?.Code?.value) || [];
  return TagValues;
};

export const selectedLobCode = (JurisdictionResults: any, lobCode: any) => {
  if (lobCode === "All") {
    const allLobCodes = JurisdictionResults?.flatMap((jurisdiction: any) =>
      jurisdiction?.Lob?.map((lob: any) => lob?.Code)
    )
      ?.filter(Boolean)
      ?.filter(
        (value: any, index: any, self: any) => self?.indexOf(value) === index
      );
    const codesString = allLobCodes?.join(",");
    return codesString;
  } else {
    const currentJurisdictionObject = JurisdictionResults?.filter(
      (lob: any) => {
        if (lob?.Code === lobCode) {
          return true;
        }
        return false;
      }
    );

    const lobArray = currentJurisdictionObject[0]?.Lob;
    return lobArray
      ?.map((lobs: any) => {
        return lobs?.Code;
      })
      .join(",");
  }
};
