import { Text } from "@sitecore-jss/sitecore-jss-nextjs";
import states from "./MapDataStates";
import {
  UpdatedDateFacet,
  UpdatedStatusFacet,
  SelectedTabValue,
  TotalJuridictionCount,
} from "./MapTableViewConstant";
import { fedralCount, toggleTab } from "./MapTableViewUtils";

const pathHighlighter = (
  routeToLmonSearchPage: any,
  selectedTab: any,
  toggleState: any,
  luValue: any,
  fdValue: any,
  tdValue: any,
  showdate: any,
  data: any,
  pathElement: (arg?: boolean) => any,
  identifier: string,
  tSpanSet: { x: number; y: number; xOffSet: number } = {
    x: 0,
    y: 0,
    xOffSet: 0,
  }
) => {
  const SVGPath = pathElement;
  const presentData = data?.JurisdictionResults?.find(
    (item: any) =>
      item.Name === identifier &&
      item?.StatusCounts[SelectedTabValue[toggleState]] > 0
  );
  const x = tSpanSet?.x ? tSpanSet.x + tSpanSet.xOffSet : 0;
  if (presentData) {
    return (
      <a
        href="javascript:void(0);"
        data-testid="map-view-state"
        onClick={() => {
          routeToLmonSearchPage(
            presentData.Code,
            presentData.Name,
            UpdatedDateFacet[luValue],
            fdValue,
            tdValue,
            UpdatedStatusFacet[selectedTab],
            data?.JurisdictionResults,
            selectedTab
          );
        }}
        xlinkTitle={identifier}
        data-state={presentData.Code}
        data-tab={selectedTab}
        data-updated={luValue}
        data-date={showdate ? `${fdValue}_${tdValue}` : ""}
        data-searchType="map"
      >
        {SVGPath(true)}
        <text
          xmlns="http://www.w3.org/2000/svg"
          xmlSpace="preserve"
          id={`${identifier}-label`}
          xmlnsSodipodi="https://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
          sodipodiLinespacing="100%"
        >
          <tspan
            sodipodiRole="line"
            id={`${identifier}-total`}
            data-id={`${identifier}`}
            data-region="midwest"
            x={x}
            y={tSpanSet?.y || 0}
          >
            {presentData?.StatusCounts[SelectedTabValue[toggleState]]}
          </tspan>
        </text>
      </a>
    );
  } else {
    return (
      <a
        href="javascript:void(0);"
        xlinkTitle={identifier}
        aria-disabled="false"
        aria-role="link"
      >
        {SVGPath()}
        <text
          xmlns="http://www.w3.org/2000/svg"
          xmlSpace="preserve"
          id={`${identifier}-label`}
          xmlnsSodipodi="https://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
          sodipodiLinespacing="100%"
        >
          <tspan
            sodipodiRole="line"
            id={`${identifier}-total`}
            data-id={`${identifier}`}
            data-region="midwest"
            x={x}
            y={tSpanSet?.y || 0}
          ></tspan>
        </text>
      </a>
    );
  }
};

const MapView = ({ ...props }: any) => {
  const {
    fields,
    response,
    toggleState,
    selectedTab,
    luValue,
    fdValue,
    tdValue,
    showdate,
    routeToLmonSearchPage,
    setSelectedTab,
    setToggleState,
  } = props;

  return (
    <div className="lmon-map-view" data-testid="map-view">
      <div className="tabs">
        <div className="tabbed flex-wrapper">
          <div className="nav-wrapper">
            <nav>
              <a
                href="javascript:void(0);"
                className={toggleState === 1 ? "tab active" : "tab"}
                onClick={(e: any) =>
                  toggleTab(1, setSelectedTab, setToggleState, e)
                }
                data-testid="map-tab-toggle-tab1"
              >
                <Text field={fields.AllStatus} />{" "}
                <small>({response?.AllEventsCount})</small>
              </a>
              <a
                href="javascript:void(0);"
                className={toggleState === 2 ? "tab active" : "tab"}
                onClick={(e: any) =>
                  toggleTab(2, setSelectedTab, setToggleState, e)
                }
                data-testid="map-tab-toggle-tab2"
              >
                <Text field={fields.AllActiveStatus} />{" "}
                <small>({response?.AllActiveEventsCount})</small>
              </a>
              <a
                href="javascript:void(0);"
                className={toggleState === 3 ? "tab active" : "tab"}
                onClick={(e: any) =>
                  toggleTab(3, setSelectedTab, setToggleState, e)
                }
                data-testid="map-tab-toggle-tab3"
              >
                <Text field={fields.FilingImpactStatus} />{" "}
                <small>({response?.FilingImpactStatusResults})</small>
              </a>
              <a
                href="javascript:void(0);"
                className={toggleState === 4 ? "tab active" : "tab"}
                onClick={(e: any) =>
                  toggleTab(4, setSelectedTab, setToggleState, e)
                }
                data-testid="map-tab-toggle-tab4"
              >
                <Text field={fields.UnderReviewStatus} />{" "}
                <small>({response?.UnderReviewStatusResults})</small>
              </a>
              <a
                href="javascript:void(0);"
                className={toggleState === 5 ? "tab active" : "tab"}
                onClick={(e: any) =>
                  toggleTab(5, setSelectedTab, setToggleState, e)
                }
                data-testid="map-tab-toggle-tab5"
              >
                <Text field={fields.NoFilingImpactStatus} />{" "}
                <small>({response?.NoFilingImpactStatusResults})</small>
              </a>
            </nav>
          </div>
        </div>
      </div>

      <svg
        xmlnsDc="http://purl.org/dc/elements/1.1/"
        xmlnsCc="https://creativecommons.org/ns#"
        xmlnsRdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
        xmlnsSvg="http://www.w3.org/2000/svg"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsSodipodi="https://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
        xmlnsInkscape="https://www.inkscape.org/namespaces/inkscape"
        enable_background="new 0 0 1000 589"
        height="589px"
        pretty_print="False"
        style={{
          strokeLinejoin: "round",
          stroke: "#fff",
          fill: "#f0f0f0",
        }}
        viewBox="50 0 1000 589"
        width="1000px"
        id="usa-map"
        inkscapeVersion="0.48.4 r9939"
        sodipodiDocname="us.svg"
        {...props}
      >
        <sodipodiNamedview
          pagecolor="#ffffff"
          bordercolor="#666666"
          borderopacity={1}
          objecttolerance={10}
          gridtolerance={10}
          guidetolerance={10}
          inkscapePageopacity={0}
          inkscapePageshadow={2}
          inkscapeWindow-width={1920}
          inkscapeWindow-height={1137}
          id="namedview69"
          showgrid="false"
          inkscapeZoom={0.80893016}
          inkscapeCx={817.66365}
          inkscapeCy={409.3738}
          inkscapeWindow-x={1192}
          inkscapeWindow-y={118}
          inkscapeWindow-maximized={1}
          inkscapeCurrent-layer="svg2"
        />
        <defs id="defs4" />
        <metadata id="metadata8">
          <views id="views10">
            <view h={589.235294118} padding={0} w={1000} id="view12">
              <proj id="laea-usa" lat0={45} lon0={-100} />
              <bbox h={321.97} w={589.33} x={690.09} y={918.69} id="bbox15" />
            </view>
          </views>
          <rdfRDF>
            <ccWork rdfAbout="">
              <dcFormat>{"image/svg+xml"}</dcFormat>
              <dcType rdfResource="http://purl.org/dc/dcmitype/StillImage" />
              <dctitle />
            </ccWork>
          </rdfRDF>
        </metadata>
        {states.map(
          ({
            id,
            name,
            region,
            d,
            annotationLineD,
            annotationLineTransform,
            textD,
            coordinates,
          }: any) =>
            pathHighlighter(
              routeToLmonSearchPage,
              selectedTab,
              toggleState,
              luValue,
              fdValue,
              tdValue,
              showdate,
              response,
              (bool) => (
                <>
                  <path
                    className={bool ? "annotation-state-active" : ""}
                    inkscapeConnector-curvature={0}
                    id={id}
                    data-name={name}
                    data-id={id}
                    data-region={region}
                    d={d}
                  >
                    {" "}
                    {bool ? "" : <title>{name}</title>}
                  </path>
                  {!!annotationLineD && (
                    <path
                      className="annotation-line"
                      d={annotationLineD}
                      transform={annotationLineTransform}
                    >
                      {bool ? " " : <title>{name}</title>}
                    </path>
                  )}
                  {!!textD && (
                    <path d={textD}>{bool ? " " : <title>{name}</title>}</path>
                  )}
                </>
              ),
              name,
              coordinates
            )
        )}
      </svg>

      <div className="flex-wrapper">
        {response && response[TotalJuridictionCount[toggleState]] > 0 ? (
          <a
            tabIndex={0}
            data-testid="map-view-see-all-results"
            onKeyUp={(e) =>
              e.key === "Enter" &&
              routeToLmonSearchPage(
                "All",
                "",
                UpdatedDateFacet[luValue],
                fdValue,
                tdValue,
                UpdatedStatusFacet[selectedTab],
                response?.JurisdictionResults
              )
            }
            onClick={() =>
              routeToLmonSearchPage(
                "All",
                "",
                UpdatedDateFacet[luValue],
                fdValue,
                tdValue,
                UpdatedStatusFacet[selectedTab],
                response?.JurisdictionResults
              )
            }
          >
            <Text field={fields.SeeAllResultsText} /> (
            {response[TotalJuridictionCount[toggleState]]})
          </a>
        ) : (
          <span>
            <Text field={fields.SeeAllResultsText} /> (
            {response[TotalJuridictionCount[toggleState]]})
          </span>
        )}
        {response &&
        fedralCount(response, SelectedTabValue[toggleState]) > 0 ? (
          <a
            tabIndex={0}
            data-testid="map-view-federal-events"
            onKeyUp={(e) =>
              e.key === "Enter" &&
              routeToLmonSearchPage(
                "FED",
                "Federal",
                UpdatedDateFacet[luValue],
                fdValue,
                tdValue,
                UpdatedStatusFacet[selectedTab],
                response?.JurisdictionResults
              )
            }
            onClick={() =>
              routeToLmonSearchPage(
                "FED",
                "Federal",
                UpdatedDateFacet[luValue],
                fdValue,
                tdValue,
                UpdatedStatusFacet[selectedTab],
                response?.JurisdictionResults
              )
            }
          >
            <Text field={fields.FederalEventsText} /> (
            {response && fedralCount(response, SelectedTabValue[toggleState])})
          </a>
        ) : (
          <span>
            <Text field={fields.FederalEventsText} /> (
            {response && fedralCount(response, SelectedTabValue[toggleState])})
          </span>
        )}
      </div>
    </div>
  );
};
export default MapView;
