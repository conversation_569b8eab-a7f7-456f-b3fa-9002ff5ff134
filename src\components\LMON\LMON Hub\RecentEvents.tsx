import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import React, { useState, useEffect, useContext } from "react";
import { ComponentProps } from "lib/component-props";
import Link from "next/link";
import Loader from "components/common/Loader";
import ErrorMessage from "components/common/ErrorMessage";
import { AuthContext } from "src/context/authContext";
import { useRouter } from "next/router";

type RecentEventsProps = ComponentProps & {
  fields: {
    SectionTitle: Field<string>;
    ShowMoreText: Field<string>;
  };
  path: Field<string>;
};
const RecentEvents = (props: any): JSX.Element => {
  const [response, setresponse] = useState<{ RecentEvents: any }>();
  const [isspinner, setIsSpinner] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const [isError, setIsError] = useState(false);
  const context = useContext(AuthContext);
  const { accessToken } = context;
  const dataparams = {
    count: 10,
  };
  const router = useRouter();
  const routeToLmonSearchPage = () => {
    router.push(`lmon-hub/searchpage?js=`);
  };

  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    body: JSON.stringify(dataparams),
  };
  const data = async () => {
    try {
      setIsSpinner(true);
      const url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/FetchLmonRecentEvents/results`;
      const post = await fetch(url, requestOptions).then((res) => res.json());
      setresponse(post);
      setIsSpinner(false);
      return post;
    } catch (error) {
      setErrorMessage(
        "We are experiencing an issue loading Recent Events and are working to resolve it. Thank you for your patience."
      );
      setIsError(true);
      setIsSpinner(false);
    }
  };

  useEffect(() => {
    data();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const eventpage: any = props?.path + "/lmon%20event";

  if (isError) {
    return (
      <section className="background-lt-grey">
        <Text field={props?.fields?.SectionTitle} tag="h2" />
        <ErrorMessage message={errorMessage} />
      </section>
    );
  }
  return (
    <section className="background-lt-grey">
      <Text field={props?.fields?.SectionTitle} tag="h2" />
      <ul className="link-list">
        <>
          {isspinner ? (
            <Loader />
          ) : (
            <>
              {response?.RecentEvents?.length > 0 ? (
                <>
                  {response?.RecentEvents?.map((val: any, id: any) => {
                    return (
                      <li key={id}>
                        <Link
                          legacyBehavior
                          href={eventpage + "?eventId=" + val.LmonEventItemId}
                        >
                          <a
                            data-region={props?.fields?.SectionTitle?.value}
                            data-interaction="click"
                            data-title={val.EventName}
                            data-testid="recent-events"
                          >
                            {val.EventName}
                          </a>
                        </Link>
                      </li>
                    );
                  })}
                  <a
                    onClick={() => routeToLmonSearchPage()}
                    data-testid="showmore-test"
                  >
                    <small>
                      <Text field={props?.fields?.ShowMoreText} />
                    </small>
                  </a>
                </>
              ) : (
                <>
                  <p>No Result Found</p>
                </>
              )}
            </>
          )}
        </>
      </ul>
    </section>
  );
};
export default withDatasourceCheck()<RecentEventsProps>(RecentEvents);
