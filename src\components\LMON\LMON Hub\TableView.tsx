import { statusColumnHeading, statusColumnValue } from "./MapTableViewUtils";
import ErrorMessage from "components/common/ErrorMessage";

const TableView = ({
  response,
  fields,
  routeToLmonSearchPage,
  luValue,
  fdValue,
  tdValue,
  handleIcon,
  handlesortitem,
  sortColumn,
  sortDirection,
  itemsPerPage,
  handleResultPerPage,
  handleNextbtn,
  handlePrevbtn,
  handleExtreamNextbtn,
  handleExtreamPrevbtn,
  currentPage,
  pages,
  renderPageNumbers,
  showdate,
}: any) => {
  const { JurisdictionResults } = response;

  if (response === "No results found") {
    return (
      <div className="lmon-table-view">
        <ErrorMessage message="No results found for this date range" />
      </div>
    );
  }

  return (
    <div className="lmon-table-view" data-testid="table-view">
      <section className="results-listing">
        <div className="results-table scrollable">
          <table>
            <thead>
              <tr>
                {statusColumnHeading(
                  { value: "Juris" },
                  true,
                  handleIcon,
                  handlesortitem,
                  sortColumn,
                  sortDirection,
                  "js"
                )}
                {statusColumnHeading(
                  fields.AllStatus,
                  false,
                  handleIcon,
                  handlesortitem,
                  sortColumn,
                  sortDirection,
                  "all"
                )}
                {statusColumnHeading(
                  fields.AllActiveStatus,
                  false,
                  handleIcon,
                  handlesortitem,
                  sortColumn,
                  sortDirection,
                  "active"
                )}
                {statusColumnHeading(
                  fields.FilingImpactStatus,
                  false,
                  handleIcon,
                  handlesortitem,
                  sortColumn,
                  sortDirection,
                  "filing"
                )}
                {statusColumnHeading(
                  fields.UnderReviewStatus,
                  false,
                  handleIcon,
                  handlesortitem,
                  sortColumn,
                  sortDirection,
                  "under"
                )}
                {statusColumnHeading(
                  fields.NoFilingImpactStatus,
                  false,
                  handleIcon,
                  handlesortitem,
                  sortColumn,
                  sortDirection,
                  "nofiling"
                )}
              </tr>
            </thead>
            <tbody>
              {JurisdictionResults?.map((juri: any) => {
                return (
                  <tr key={juri?.Code}>
                    {statusColumnValue(
                      juri?.Name,
                      true,
                      "",
                      "",
                      "",
                      "",
                      "",
                      "",
                      "",
                      showdate,
                      ""
                    )}
                    {statusColumnValue(
                      juri?.StatusCounts?.AllEvents,
                      false,
                      routeToLmonSearchPage,
                      juri?.Name,
                      juri?.Code,
                      luValue,
                      fdValue,
                      tdValue,
                      fields.AllStatus,
                      showdate,
                      JurisdictionResults
                    )}
                    {statusColumnValue(
                      juri?.StatusCounts?.Active,
                      false,
                      routeToLmonSearchPage,
                      juri?.Name,
                      juri?.Code,
                      luValue,
                      fdValue,
                      tdValue,
                      fields.AllActiveStatus,
                      showdate,
                      JurisdictionResults
                    )}
                    {statusColumnValue(
                      juri?.StatusCounts?.FilingImpact,
                      false,
                      routeToLmonSearchPage,
                      juri?.Name,
                      juri?.Code,
                      luValue,
                      fdValue,
                      tdValue,
                      fields.FilingImpactStatus,
                      showdate,
                      JurisdictionResults
                    )}
                    {statusColumnValue(
                      juri?.StatusCounts?.UnderReview,
                      false,
                      routeToLmonSearchPage,
                      juri?.Name,
                      juri?.Code,
                      luValue,
                      fdValue,
                      tdValue,
                      fields.UnderReviewStatus,
                      showdate,
                      JurisdictionResults
                    )}
                    {statusColumnValue(
                      juri?.StatusCounts?.NoFilingImpact,
                      false,
                      routeToLmonSearchPage,
                      juri?.Name,
                      juri?.Code,
                      luValue,
                      fdValue,
                      tdValue,
                      fields.NoFilingImpactStatus,
                      showdate,
                      JurisdictionResults
                    )}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        <div className="page-results-wrapper">
          <div className="pageresults">
            <span> Results per page: </span>
            <div className="select-wrapper">
              <select
                name="count"
                id="results-options"
                onChange={(e) => handleResultPerPage(e)}
                value={itemsPerPage}
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="100">100</option>
                <option value="250">250</option>
              </select>
            </div>
          </div>
          <nav aria-label="Page navigation example">
            <ul className="pagination">
              <li className="page-item">
                <a
                  data-testid="table-extreme-prev-btn"
                  className="page-link first-page disabled"
                  onClick={handleExtreamPrevbtn}
                  tabIndex={currentPage == pages[0] ? -1 : 0}
                  onKeyUp={(e) => e.key === "Enter" && handleExtreamPrevbtn(e)}
                  aria-disabled={currentPage == pages[0] ? true : false}
                >
                  <span className="material-icons">first_page</span>
                </a>
              </li>
              <li className="page-item">
                <a
                  data-testid="table-prev-btn"
                  className="page-link previous-page disabled"
                  onClick={handlePrevbtn}
                  tabIndex={currentPage == pages[0] ? -1 : 0}
                  onKeyUp={(e) => e.key === "Enter" && handlePrevbtn(e)}
                  aria-disabled={currentPage == pages[0] ? true : false}
                >
                  <span>Previous</span>
                </a>
              </li>
              {renderPageNumbers}
              <li className="page-item next">
                <a
                  data-testid="table-next-btn"
                  className="page-link next-page"
                  onClick={handleNextbtn}
                  tabIndex={currentPage == pages[pages.length - 1] ? -1 : 0}
                  onKeyUp={(e) => e.key === "Enter" && handleNextbtn(e)}
                  aria-disabled={
                    currentPage == pages[pages.length - 1] ? true : false
                  }
                >
                  <span>Next</span>
                </a>
              </li>
              <li className="page-item">
                <a
                  data-testid="table-extreme-next-btn"
                  className="page-link last-page"
                  onClick={handleExtreamNextbtn}
                  tabIndex={currentPage == pages[pages.length - 1] ? -1 : 0}
                  onKeyUp={(e) => e.key === "Enter" && handleExtreamNextbtn(e)}
                  aria-disabled={
                    currentPage == pages[pages.length - 1] ? true : false
                  }
                >
                  <span className="material-icons">last_page</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </section>
    </div>
  );
};
export default TableView;
