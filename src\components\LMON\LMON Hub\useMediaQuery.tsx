import { useEffect, useState } from "react";

function useMediaQuery(query: any) {
  let matchMedia: any = { matches: false };
  if (typeof window !== "undefined") {
    matchMedia = window.matchMedia(query);
  }
  const [match, setMatch] = useState(matchMedia.matches);
  useEffect(() => {
    const handleChange = () => {
      setMatch(matchMedia.matches);
    };
    matchMedia.addEventListener("change", handleChange);

    return () => {
      matchMedia.removeEventListener("change", handleChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);
  return match;
}
export default useMediaQuery;
