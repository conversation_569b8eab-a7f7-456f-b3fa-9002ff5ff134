interface DateInputProps {
  id?: string;
  name?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  ariaLabel?: string;
  min?: string;
  max?: string;
  disabled?: boolean;
  dataTestId?: string;
  refinementSection?: string;
  refinementTitle?: string;
  interaction?: string;
  refinementStatus?: string;
}

const DatePicker: React.FC<DateInputProps> = ({
  id,
  name,
  value,
  onChange,
  ariaLabel,
  min,
  max,
  disabled,
  dataTestId,
  refinementSection,
  refinementTitle,
  interaction,
  refinementStatus,
}) => {
  return (
    <input
      type="date"
      id={id}
      name={name}
      value={value}
      onChange={onChange}
      aria-label={ariaLabel}
      min={min}
      max={max}
      disabled={disabled}
      data-testid={dataTestId}
      data-refinement-section={refinementSection}
      data-refinement-title={refinementTitle}
      data-interaction={interaction}
      data-refinement-status={refinementStatus}
    />
  );
};

export default DatePicker;
