import { UpdatedDateCapsuleFacet, changeDateFormat } from "./LmonSearchUtils";

const FacetCapLayout = ({ ...props }: any) => {
  const { handleCapsule, handleDateCapsule, requestParams, facetObj }: any =
    props;
  let isCrdr: any = true;
  let isEffdr: any = true;
  let isUpdr: any = true;

  if (requestParams?.crdr?.crds === "" && requestParams?.crdr.crde === "") {
    isCrdr = false;
  }

  if (requestParams?.effdr?.efds === "" && requestParams?.effdr?.efde === "") {
    isEffdr = false;
  }

  if (requestParams.updr.timeperiod === "") {
    isUpdr = false;
  } else {
    if (requestParams.updr.timeperiod === "custom") {
      if (requestParams?.updr?.uds === "" && requestParams?.updr?.ude === "") {
        isUpdr = false;
      }
    }
  }

  const facetTypesCheckbox: any = [
    "js",
    "lob",
    "crdr",
    "it",
    "tp",
    "st",
    "updr",
    "effdr",
    "sty",
    "pr",
    "pf",
  ];

  const facetsValues: any = [];

  facetTypesCheckbox?.forEach((element: any) => {
    const Values: any = facetObj[element]?.Values || [];
    const CheckedValues: any =
      Values?.filter((item: any) => item.Selected) || [];
    const CheckedFacetObj: any = { Key: element, Values: CheckedValues };
    facetsValues.push(CheckedFacetObj);
  });

  return (
    <>
      {isCrdr && (
        <strong data-testid="crdr-capsule">
          {`Created ${changeDateFormat(
            requestParams?.crdr?.crds
          )}-${changeDateFormat(requestParams?.crdr?.crde)} `}
          <a
            onClick={() => handleDateCapsule("crdr")}
            tabIndex={0}
            onKeyUp={(e) => e.key === "Enter" && handleDateCapsule("crdr")}
            data-testid="crdr-close"
          >
            <span className="material-icons">close</span>
          </a>
        </strong>
      )}
      {isEffdr && (
        <strong data-testid="effdr-capsule">
          {`Effective ${changeDateFormat(
            requestParams?.effdr?.efds
          )}-${changeDateFormat(requestParams?.effdr?.efde)} `}
          <a
            onClick={() => handleDateCapsule("effdr")}
            tabIndex={0}
            onKeyUp={(e) => e.key === "Enter" && handleDateCapsule("effdr")}
            data-testid="effdr-close"
          >
            <span className="material-icons">close</span>
          </a>
        </strong>
      )}

      {isUpdr &&
        (requestParams.updr.timeperiod !== "custom" ? (
          <strong data-testid="updr-capsule">
            {`Updated ${
              UpdatedDateCapsuleFacet[requestParams?.updr.timeperiod]
            }`}
            <a
              onClick={() => handleDateCapsule("updr")}
              tabIndex={0}
              onKeyUp={(e) => e.key === "Enter" && handleDateCapsule("updr")}
              data-testid="updr-close-not-custom"
            >
              <span className="material-icons">close</span>
            </a>
          </strong>
        ) : (
          <strong data-testid="updr-capsule">
            {`Updated ${changeDateFormat(
              requestParams?.updr?.uds
            )}-${changeDateFormat(requestParams?.updr?.ude)} `}
            <a
              onClick={() => handleDateCapsule("updr")}
              tabIndex={0}
              onKeyUp={(e) => e.key === "Enter" && handleDateCapsule("updr")}
              data-testid="updr-close"
            >
              <span className="material-icons">close</span>
            </a>
          </strong>
        ))}

      {facetsValues?.map(({ Values, Key }: any) => {
        return Values?.map((item: any) => {
          return (
            <strong key={item?.Key} data-testid={`facet-${Key}-${item?.Key}`}>
              {item?.Name}
              <a
                onClick={() => handleCapsule(Key, item?.Key)}
                tabIndex={0}
                onKeyUp={(e) =>
                  e.key === "Enter" && handleCapsule(Key, item?.Key)
                }
                data-testid={`facet-${Key}-${item?.Key}-close`}
              >
                <span className="material-icons">close</span>
              </a>
            </strong>
          );
        });
      })}
    </>
  );
};

export default FacetCapLayout;
