import { RequestOptions } from "./LmonSearchUtils";
import { apiUrl } from "./LmonSearchUtils";

export const InitialLmonApi = async (
  requestParams: any,
  props: any,
  queryParam: any,
  accessToken: any
) => {
  const Rankingrule = props.fields.RankingRules.map(
    (arr: any) => arr.fields?.["Phrase"].value
  ).join(",");
  const requestOptions = RequestOptions(
    requestParams,
    queryParam,
    Rankingrule,
    "",
    accessToken
  );
  try {
    const response = await fetch(apiUrl, requestOptions);
    const data = await response.json();
    return data;
  } catch (error) {
    console.log("error", error);
  }
};

export const UpdateFacetsApi = async (
  requestParams: any,
  queryParam: any,
  accessToken: any
) => {
  const requestOptions = RequestOptions(
    requestParams,
    queryParam,
    "",
    "",
    accessToken
  );
  try {
    const response = await fetch(apiUrl, requestOptions);
    const data = await response.json();
    return data;
  } catch (error) {
    console.log("error", error);
  }
};

export const ExcelApiData = async (
  requestParams: any,
  queryParam: any,
  accessToken: any
) => {
  const requestOptions = RequestOptions(
    requestParams,
    queryParam,
    "",
    "yes",
    accessToken
  );
  try {
    const response = await fetch(apiUrl, requestOptions);
    const data = await response.json();
    return data;
  } catch (error) {
    console.log("error", error);
  }
};
