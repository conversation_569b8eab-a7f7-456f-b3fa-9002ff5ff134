import React, { useState, useEffect } from "react";
import { Text } from "@sitecore-jss/sitecore-jss-nextjs";

import {
  getLabels,
  // sortMethods,
  AdobeDataRefinementSection,
  changeDateFormat,
  getDayAfter,
} from "./LmonSearchUtils";
import CommonJurisdictionFilter from "components/Platform/common/CommonJurisdictionFilter";
import { commonJurisdictionHelper } from "src/helpers/common/filter";
import DatePicker from "./DatePicker";

const EEshowMoreText = (labels: any) => {
  return (
    <>
      <a className="show-more-lob">
        <Text field={labels["showmore"]} />
      </a>
      <a className="show-less-lob">
        <Text field={labels["showless"]} />
      </a>
    </>
  );
};

const EEFacetsFilter = (facetTitle: any, labels: any) => {
  return (
    <>
      <li className="jurisdictions">
        <details>
          <summary>
            <Text field={facetTitle} />
          </summary>
          {EEshowMoreText(labels)}
        </details>
      </li>
    </>
  );
};

const EEDateFilter = (
  labels: any,
  isUpdatedDate: any,
  isCreatedDate: any,
  isEffectiveDate: any
) => {
  return (
    <>
      <li>
        <details>
          <summary>
            {isUpdatedDate && <Text field={labels["updatedDate"]} />}
            {isCreatedDate && <Text field={labels["createddate"]} />}
            {isEffectiveDate && <Text field={labels["effectiveDate"]} />}
          </summary>
          <form className="options">
            {isUpdatedDate &&
              labels?.["UpdatedDateList"]?.map((obj: any) => {
                return (
                  <label key={obj?.fields.Key}>
                    <input type="checkbox" id="1" name="updated" value="a" />
                    <span>
                      <Text field={obj?.fields.Key} />
                    </span>
                  </label>
                );
              })}
            <label className="date-range">
              <span>
                <Text field={labels["startDate"]} />{" "}
              </span>
              <input type="date" id="start" name="date-range" />
            </label>
            <label className="date-range">
              <span>
                <Text field={labels["endDate"]} />{" "}
              </span>
              <input type="date" id="end" name="date-range" />
            </label>
          </form>
        </details>
      </li>
    </>
  );
};

const showMoreText = (
  facettype: any,
  classname: any,
  Value: any,
  { ...props }
) => {
  const textFields = props;
  return (
    <>
      <a
        id={`show_more_${facettype}`}
        href={`#show_more_${facettype}`}
        className={`show-more-${classname}`}
      >
        <Text field={textFields?.textFields?.ShowMoreText} />
        &nbsp;
        <Text field={Value} />
        {/* {`${textFields?.textFields?.ShowMoreText?.value} ${Value}`} */}
      </a>
      <a
        id={`show_less_${facettype}`}
        href={`#show_less_${facettype}`}
        className={`show-less-${classname}`}
      >
        {/* {`${textFields?.textFields?.ShowLessText?.value} ${Value}`} */}
        <Text field={textFields?.textFields?.ShowLessText} />
        &nbsp;
        <Text field={Value} />
      </a>
    </>
  );
};

const FacetLayout = (
  keyValue: any,
  jurisdictionsearch: any,
  setJurisdictionSearch: any,
  topicsearch: any,
  setTopicSearch: any,
  labels: any,

  { ...props }
) => {
  const {
    handleDateStart,
    handleDateEnd,
    hanldeUpdatedDate,
    handleFacets,
    requestParams,
    facetObj,
    textFields,
    showdate,
    UpdatedDateFacet,
    UpdatedDateCount,
    selectedUpdatedDate,
    errorVal,
  } = props;
  // const { ascending } = sortMethods;

  if (keyValue === "js") {
    const { sortedData, highlightText } = commonJurisdictionHelper(
      facetObj?.js?.Values,
      jurisdictionsearch
    );
    return (
      <>
        {facetObj && facetObj?.js?.Values.length > 0 && (
          <li className="jurisdictions">
            {(() => {
              return (
                <>
                  <details open>
                    <summary>
                      <div className="lines-item">
                        <Text field={labels["jurisdiction"]} />
                      </div>
                      {requestParams?.[keyValue]?.length > 0 && (
                        <span className="item-count">
                          <small className={"options-selected"}>
                            {requestParams?.[keyValue]?.length > 0
                              ? requestParams?.[keyValue]?.length
                              : undefined}
                          </small>
                        </span>
                      )}
                    </summary>
                    <form className="options">
                      <label>
                        <span className="sr-only">Jurisdiction Typeahead</span>
                        <input
                          data-testid="juridiction_typeahead"
                          type="text"
                          placeholder="begin typing to filter"
                          onChange={(e) =>
                            setJurisdictionSearch(e.target.value)
                          }
                        />
                      </label>
                      <>
                        {sortedData.length === 0 ? (
                          <p>No matches found.</p>
                        ) : (
                          <>
                            {sortedData.map((option: any, id: any) => (
                              <label key={id}>
                                <input
                                  type="checkbox"
                                  id={option.Key}
                                  name="jurisdiction"
                                  value={option.Key}
                                  onChange={handleFacets}
                                  data-key={facetObj?.js?.Key}
                                  checked={
                                    option.Selected ||
                                    requestParams?.js?.indexOf(option.Key) > -1
                                  }
                                  Data-refinement-section={
                                    labels["jurisdiction"]?.value
                                  }
                                  Data-refinement-title={option.Name}
                                  Data-interaction="refinement"
                                  Data-refinement-status={
                                    option.Selected ||
                                    requestParams?.js?.indexOf(option.Key) > -1
                                      ? "selected"
                                      : "unselected"
                                  }
                                />

                                <CommonJurisdictionFilter
                                  highlightText={highlightText}
                                  option={option}
                                  jurisdictionsearchTerm={jurisdictionsearch}
                                />
                                <span>{option.Count}</span>
                              </label>
                            ))}
                          </>
                        )}
                      </>
                    </form>
                    {keyValue === "js" &&
                      sortedData?.length >= 6 &&
                      sortedData.length > 0 &&
                      showMoreText(
                        "jurisdictions",
                        "jurisdictions",
                        labels["jurisdiction"],
                        { ...props }
                      )}
                  </details>
                </>
              );
            })()}
          </li>
        )}
      </>
    );
  }
  if (keyValue === "tp") {
    return (
      <>
        {facetObj?.tp?.Values.length > 0 ? (
          <li className="topics">
            <details>
              <summary>
                <div className="lines-item">
                  <Text field={labels["topicValue"]} />
                </div>
                {requestParams?.[keyValue]?.length > 0 && (
                  <span className="item-count">
                    <small className={"options-selected"}>
                      {requestParams?.[keyValue]?.length > 0
                        ? requestParams?.[keyValue]?.length
                        : undefined}
                    </small>
                  </span>
                )}
              </summary>
              <form className="options topics">
                <label>
                  <input
                    data-testid="topics_typeahead"
                    type="text"
                    placeholder="begin typing to filter"
                    onChange={(e) => setTopicSearch(e.target.value)}
                  />
                </label>

                {facetObj?.tp?.Values?.filter((item: any) =>
                  item?.Name?.toLocaleLowerCase()?.startsWith(
                    topicsearch?.toLocaleLowerCase()
                  )
                )?.map((facetsValues: any) => {
                  return (
                    <label key={facetsValues.Key}>
                      <input
                        type="checkbox"
                        id={facetsValues.Key}
                        name={labels["topicValue"]?.value}
                        value={facetsValues.Key}
                        onChange={handleFacets}
                        data-key={facetObj?.tp?.Key}
                        Data-refinement-section={labels["topicValue"]?.value}
                        Data-refinement-title={facetsValues.Name}
                        Data-interaction="refinement"
                        Data-refinement-status={
                          facetsValues.Selected ||
                          requestParams?.tp?.indexOf(facetsValues.Key) > -1
                            ? "selected"
                            : "unselected"
                        }
                        // checked={facetsValues.Selected}
                      />
                      <span>{facetsValues.Name} </span>
                      <span>{facetsValues.Count}</span>
                    </label>
                  );
                })}
              </form>
              {keyValue === "tp" &&
                facetObj?.tp?.Values?.length > 6 &&
                showMoreText("topics", "topics", labels["topicValue"], {
                  ...props,
                })}
            </details>
          </li>
        ) : (
          " "
        )}
      </>
    );
  }

  if (
    keyValue === "it" ||
    keyValue === "st" ||
    keyValue === "lob" ||
    keyValue === "sty" ||
    keyValue === "pr" ||
    keyValue === "pf"
  ) {
    let variable: any = {};
    if (keyValue == "it" && facetObj?.it?.Values.length > 0) {
      variable = facetObj?.it;
    } else if (keyValue === "lob" && facetObj?.lob?.Values.length > 0) {
      variable = facetObj?.lob;
    } else if (keyValue === "st" && facetObj?.st?.Values.length > 0) {
      variable = facetObj?.st;
    } else if (keyValue === "sty" && facetObj?.sty?.Values.length > 0) {
      variable = facetObj?.sty;
    } else if (keyValue === "pr" && facetObj?.pr?.Values.length > 0) {
      variable = facetObj?.pr;
    } else if (keyValue === "pf" && facetObj?.pf?.Values.length > 0) {
      variable = facetObj?.pf;
    }
    return (
      <>
        {variable?.Values?.length > 0 && (
          <li className="lines">
            <details open={keyValue === "st" || keyValue === "lob"}>
              <summary>
                {keyValue === "it" && facetObj?.it?.Values.length > 0 ? (
                  <div className="lines-item">
                    <Text field={labels["ItemType"]} />
                  </div>
                ) : keyValue === "lob" && facetObj?.lob?.Values.length > 0 ? (
                  <div className="lines-item">
                    <Text field={labels["lob"]} />
                  </div>
                ) : keyValue === "st" && facetObj?.st?.Values.length > 0 ? (
                  <div className="lines-item">
                    <Text field={labels["Status"]} />
                  </div>
                ) : keyValue === "sty" && facetObj?.sty?.Values.length > 0 ? (
                  <div className="lines-item">
                    <Text field={labels["serviceType"]} />
                  </div>
                ) : keyValue === "pr" && facetObj?.pr?.Values.length > 0 ? (
                  <div className="lines-item">
                    <Text field={labels["procedural"]} />
                  </div>
                ) : keyValue === "pf" && facetObj?.pf?.Values.length > 0 ? (
                  <div className="lines-item">
                    <Text field={labels["policyform"]} tag="span" />
                  </div>
                ) : (
                  ""
                )}
                {keyValue === "st" ? (
                  facetObj?.st.Values?.filter(
                    (val: any) => val.Selected === true
                  ).length > 0 ? (
                    <span className="item-count">
                      <small className="options-selected">
                        {
                          facetObj?.st.Values?.filter(
                            (val: any) => val.Selected === true
                          ).length
                        }
                      </small>
                    </span>
                  ) : (
                    ""
                  )
                ) : (
                  requestParams?.[keyValue]?.length > 0 && (
                    <span className="item-count">
                      <small className="options-selected">
                        {requestParams?.[keyValue]?.length > 0 &&
                          requestParams?.[keyValue]?.length}
                      </small>
                    </span>
                  )
                )}
              </summary>
              <form className="options lines">
                {variable?.Values?.map((facetValues: any) => {
                  return (
                    <label key={facetValues.Key}>
                      <input
                        type="checkbox"
                        id={facetValues.Key}
                        name={AdobeDataRefinementSection(keyValue, labels)}
                        value={facetValues.Key}
                        onChange={handleFacets}
                        data-key={variable.Key}
                        checked={
                          facetValues.Selected &&
                          requestParams[keyValue].indexOf(facetValues.Key) > -1
                        }
                        Data-refinement-section={AdobeDataRefinementSection(
                          keyValue,
                          labels
                        )}
                        Data-refinement-title={facetValues?.Name}
                        Data-interaction="refinement"
                        Data-refinement-status={
                          facetValues.Selected &&
                          requestParams[keyValue].indexOf(facetValues.Key) > -1
                            ? "selected"
                            : "unselected"
                        }
                      />
                      <span>{facetValues.Name} </span>
                      <span>{facetValues.Count}</span>
                    </label>
                  );
                })}
              </form>
              {keyValue === "it" &&
                variable?.Values?.length > 6 &&
                showMoreText("types", "lines", labels["ItemType"], {
                  ...props,
                })}
              {keyValue === "lob" &&
                variable?.Values?.length > 6 &&
                showMoreText("lines", "lines", labels["lob"], { ...props })}
              {keyValue === "st" &&
                variable?.Values?.length > 6 &&
                showMoreText("status", "lines", labels["Status"], { ...props })}
              {keyValue === "sty" &&
                variable?.Values?.length > 6 &&
                showMoreText("services", "lines", labels["serviceType"], {
                  ...props,
                })}
              {keyValue === "pr" &&
                variable?.Values?.length > 6 &&
                showMoreText("requirements", "lines", labels["procedural"], {
                  ...props,
                })}
              {keyValue === "pf" &&
                variable?.Values?.length > 6 &&
                showMoreText("forms", "lines", labels["policyform"], {
                  ...props,
                })}
            </details>
          </li>
        )}
      </>
    );
  }
  if (keyValue === "crdr" || keyValue === "effdr") {
    const today = new Date().toISOString().split("T")[0];

    let startingValue = "";
    let endingValue = "";
    let content: any = "";

    if (keyValue === "crdr") {
      startingValue =
        errorVal?.crdr?.errorMessage && errorVal?.crdr?.startDate
          ? errorVal?.crdr?.startDate
          : requestParams.crdr.crds;
      endingValue = requestParams?.crdr.crde;
      content = (
        <div className="lines-item">
          <Text field={labels?.["createddate"]} />
        </div>
      );
    }

    if (keyValue === "effdr") {
      startingValue =
        errorVal?.effdr?.errorMessage && errorVal?.effdr?.startDate
          ? errorVal?.effdr.startDate
          : requestParams.effdr.efds;
      endingValue = requestParams?.effdr.efde;
      content = (
        <div className="lines-item">
          <Text field={labels?.["effectiveDate"]} />
        </div>
      );
    }

    return (
      <li>
        <details>
          <summary>{content}</summary>
          <form className="options">
            <label className="date-range">
              <span>
                <Text field={labels["startDate"]} />
              </span>
              <DatePicker
                id="start"
                name="date-range"
                value={startingValue}
                onChange={(e) => handleDateStart(e, keyValue)}
                max={keyValue === "effdr" ? "" : today}
                dataTestId={AdobeDataRefinementSection(keyValue, labels)}
                refinementSection={AdobeDataRefinementSection(keyValue, labels)}
                refinementTitle={`{Start:${changeDateFormat(
                  startingValue
                )} - End:${changeDateFormat(endingValue)}}`}
                interaction="refinement"
                refinementStatus={
                  startingValue !== "" || endingValue !== ""
                    ? "selected"
                    : "unselected"
                }
              />
            </label>
            <label className="date-range">
              <span>
                <Text field={labels["endDate"]} />
              </span>

              <DatePicker
                id="end"
                name="date-range"
                value={endingValue}
                onChange={(e) => handleDateEnd(e, keyValue)}
                disabled={!startingValue}
                min={startingValue ? getDayAfter(startingValue) : today}
                dataTestId={`end-date-${keyValue}`}
                refinementSection={AdobeDataRefinementSection(keyValue, labels)}
                refinementTitle={`{Start:${changeDateFormat(
                  startingValue
                )} - End:${changeDateFormat(endingValue)}}`}
                interaction="refinement"
                refinementStatus={
                  startingValue !== "" || endingValue !== ""
                    ? "selected"
                    : "unselected"
                }
              />
            </label>
          </form>
        </details>
      </li>
    );
  }
  const CountValue: any = facetObj?.DateRange?.DateRangeEvents || [];

  if (keyValue === "updr") {
    const today = new Date().toISOString().split("T")[0];

    const startDate =
      errorVal?.updr?.errorMessage && errorVal?.updr?.startDate
        ? errorVal?.updr.startDate
        : requestParams.updr.uds;
    const endDate = requestParams.updr.ude;

    return (
      <li>
        <details open>
          <summary>
            <div className="lines-item">
              <Text field={labels["updatedDate"]} />
            </div>
          </summary>
          <form className="options">
            {labels?.["UpdatedDateList"]?.map((val: any) => {
              return (
                <>
                  <label>
                    <input
                      type="checkbox"
                      id="1"
                      name="updated"
                      value={UpdatedDateFacet[val?.fields?.Key?.value]}
                      onChange={hanldeUpdatedDate}
                      checked={
                        selectedUpdatedDate ===
                          UpdatedDateFacet[val?.fields?.Key?.value] ||
                        requestParams.updr.timeperiod ===
                          UpdatedDateFacet[val?.fields?.Key?.value]
                      }
                      Data-refinement-section={labels["updatedDate"].value}
                      Data-refinement-title={val?.fields?.Key?.value}
                      Data-interaction="refinement"
                      Data-refinement-status={
                        selectedUpdatedDate ===
                          UpdatedDateFacet[val?.fields?.Key?.value] ||
                        requestParams.updr.timeperiod ===
                          UpdatedDateFacet[val?.fields?.Key?.value]
                          ? "selected"
                          : "unselected"
                      }
                    />
                    <span>{val.fields.Key.value}</span>
                    <span>
                      {CountValue[UpdatedDateCount[val?.fields?.Key?.value]]}
                    </span>
                  </label>
                </>
              );
            })}
            {showdate && (
              <>
                <label className="date-range">
                  <span>
                    <Text field={textFields?.StartDateText} />
                  </span>
                  <DatePicker
                    id="start"
                    name="date-range"
                    value={startDate}
                    onChange={(e) => handleDateStart(e, keyValue)}
                    max={today}
                    refinementSection={labels["updatedDate"].value}
                    refinementTitle={`{Start:${changeDateFormat(
                      startDate
                    )} - End:${changeDateFormat(endDate)}}`}
                    interaction="refinement"
                    refinementStatus={
                      startDate !== "" || endDate !== ""
                        ? "selected"
                        : "unselected"
                    }
                  />
                </label>
                <label className="date-range">
                  <span>
                    <Text field={textFields?.EndDateText} />
                  </span>
                  <DatePicker
                    id="end"
                    name="date-range"
                    value={endDate}
                    onChange={(e) => handleDateEnd(e, keyValue)}
                    disabled={startDate === ""}
                    min={startDate ? getDayAfter(startDate) : today}
                    refinementSection={labels["updatedDate"].value}
                    refinementTitle={`{Start:${changeDateFormat(
                      startDate
                    )} - End:${changeDateFormat(endDate)}}`}
                    interaction="refinement"
                    refinementStatus={
                      startDate !== "" || endDate !== ""
                        ? "selected"
                        : "unselected"
                    }
                  />
                </label>
              </>
            )}
          </form>
          {/* {keyValue === "updr" && labels['updatedDate']?.length > 6 &&           
            showMoreText("updated", "lines", labels['updatedDate'], { ...props })} */}
        </details>
      </li>
    );
  }
  return <p>""</p>;
};

const LmonFacetLayout = ({ ...props }): JSX.Element => {
  const { showfacets, textFields, EEFlag, isError, lmonFacets } = props;
  const [jurisdictionsearch, setJurisdictionSearch] = useState<any>("");
  const [topicsearch, setTopicSearch] = useState<any>("");
  const [isFilterToggle, setIsFilterToggle] = useState(false);
  const filterToggle = () => {
    setIsFilterToggle(!isFilterToggle);
  };

  useEffect(() => {
    const handleResize = () => {
      const isMobile = window?.matchMedia("(max-width: 1080px)").matches; // Set the breakpoint according to needs
      if (isMobile) {
        setIsFilterToggle(false);
      } else {
        setIsFilterToggle(true);
      }
    };

    // Initial check on component mount
    handleResize();

    // Event listener for window resize
    window?.addEventListener("resize", handleResize);

    // Cleanup the event listener on component unmount
    return () => {
      window?.removeEventListener("resize", handleResize);
    };
  }, []);

  const labels = getLabels(textFields);

  if ((EEFlag && isError) || (EEFlag && lmonFacets?.length === 0) || EEFlag) {
    return (
      <>
        <aside className="filter thin">
          <section className="background-lt-grey  with-show-more-filters">
            <>
              <Text field={textFields.FiltersTitle} tag="h2" />
              <ul className="link-list" id="search-filters">
                {EEFacetsFilter(labels["jurisdiction"], labels)}
                {EEFacetsFilter(labels["lob"], labels)}
                {EEDateFilter(labels, false, true, false)}
                {EEFacetsFilter(labels["ItemType"], labels)}
                {EEFacetsFilter(labels["topicValue"], labels)}
                {EEFacetsFilter(labels["Status"], labels)}
                {EEDateFilter(labels, true, false, false)}
                {EEDateFilter(labels, false, false, true)}
                {EEFacetsFilter(labels["serviceType"], labels)}
                {EEFacetsFilter(labels["procedural"], labels)}
                {EEFacetsFilter(labels["policyform"], labels)}
              </ul>
            </>
          </section>
        </aside>
      </>
    );
  }

  return (
    <>
      {showfacets && (
        <aside className="filter thin">
          <section className="background-lt-grey  with-show-more-filters">
            <>
              <h2
                data-testid="toggle_filter"
                className="filter-toggle-mobile"
                onClick={() => filterToggle()}
              >
                <Text field={textFields.FiltersTitle} />
                <a aria-label="Filter icon button">
                  <span className="material-icons">filter_list</span>
                </a>
              </h2>
              {isFilterToggle && (
                <ul className="link-list" id="search-filters">
                  {[
                    "js",
                    "lob",
                    "crdr",
                    "it",
                    "tp",
                    "st",
                    "updr",
                    "effdr",
                    "sty",
                    "pr",
                    "pf",
                  ].map((FacetsKey: any) =>
                    FacetLayout(
                      FacetsKey,
                      jurisdictionsearch,
                      setJurisdictionSearch,
                      topicsearch,
                      setTopicSearch,
                      labels,
                      { ...props }
                    )
                  )}
                </ul>
              )}
            </>
          </section>
        </aside>
      )}
    </>
  );
};

// export default withDatasourceCheck()<LmonFacetLayoutProps>(LmonFacetLayout);
export default LmonFacetLayout;
