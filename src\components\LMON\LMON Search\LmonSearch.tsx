import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from "next/router";
import LmonSearchResultLayout from "./LmonSearchResultLayout";
import LmonFacetLayout from "./LmonFacetLayout";
import {
  initialSortColumn,
  initialSortDirection,
} from "../LMON Search/LmonSearchUtils";
import {
  InitialLmonApi,
  UpdateFacetsApi,
  ExcelApiData,
} from "./LmonApiServices";
import { lmonSearchAdobeAnalytics } from "../AdobeAnalyticsUtils";
import { AuthContext } from "src/context/authContext";

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

const LmonSearch = (props: any): JSX.Element => {
  const EEFlag = props?.EEFlag;
  const context = useContext(AuthContext);
  const { accessToken } = context;

  const router = useRouter();
  const queryParam = router?.query;
  const [lmondata, setLmonData] = useState<any>([]);
  const [currentPage, setcurrentPage] = useState(1);
  const [itemsPerPage, setitemsPerPage] = useState(10);
  const [maxPageNumberLimit, setmaxPageNumberLimit] = useState(3);
  const [minPageNumberLimit, setminPageNumberLimit] = useState(0);
  const [pageNumberLimit] = useState(3);
  const [pagination, setPagination] = useState<any>();
  const [showdate, setShowdate] = useState<any>(() => {
    if ((queryParam?.timeperiod as string) === "custom") return true;
    else return false;
  });
  const [selectedUpdatedDate, setSelectedUpdatedDate] = useState<any>("");
  const [facetObj, setFacetObj] = useState<any>({});
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState();
  const [pages, setPages] = useState([]);
  const [spinner, setIsSpiner] = useState(true);
  const [showfacets, setShowFacets] = useState<any>(false);
  const [sortColumn, setSortColumn] = useState<any>(() =>
    initialSortColumn(queryParam)
  );
  const [sortDirection, setSortDirection] = useState<any>(() =>
    initialSortDirection(queryParam)
  );
  const [isError, setIsError] = useState(false);
  const [lmonFacets, setLmonFacets] = useState<any>([]);
  const [isExcelSpinner, setIsExcelSpinner] = useState(true);
  const [jsCheckFirstTime, setJsCheckFirstTime] = useState(true);
  const [checkedValue, setCheckedValue] = useState("");
  const [hideResultsPerPage, setHideResultsPerPage] = useState(false);
  const [resultsPerPage, setResultsPerPage] = useState(() => {
    return [...props?.fields?.ValueList];
  });
  const [errorVal, setErrorVal] = useState<any>({});

  const UpdatedDateFacet: any = {
    "All Time": "alltime",
    Today: "0",
    "Latest 7 Days": "7",
    "Latest 30 Days": "30",
    "Latest 90 Days": "90",
    "Latest 180 Days": "180",
    "1 Year": "365",
    "5 Years": "1825",
    Custom: "custom",
  };

  const UpdatedDateCount: any = {
    Today: "Today",
    "Latest 7 Days": "SevenDays",
    "Latest 30 Days": "ThirtyDays",
    "Latest 90 Days": "NintyDays",
    "Latest 180 Days": "OneEightyDays",
    "1 Year": "OneYear",
    "5 Years": "FiveYears",
    "All Time": "AllTime",
    Custom: "Custom",
  };

  let stValue: any = [];
  let jsValue: any = [];
  let lobvalue: any = [];
  let updatedDateRange = { timeperiod: "", uds: "", ude: "" };
  if (queryParam.hasOwnProperty("js")) {
    if ((queryParam.js as string) != "") {
      jsValue.push(queryParam.js);
    }
    if ((queryParam?.lob as string) != "" && queryParam?.lob !== undefined) {
      let lobCodeValues = queryParam?.lob as string;
      lobCodeValues?.split(",").forEach((obj: any) => {
        lobvalue.push(obj);
      });
    }
    if (queryParam.st !== undefined && queryParam.timeperiod !== undefined) {
      var statusValues = queryParam.st as string;
      statusValues.split(",").forEach((obj: any) => {
        stValue.push(obj);
      });
      updatedDateRange = {
        ...updatedDateRange,
        timeperiod: queryParam.timeperiod as string,
        uds: queryParam.uds as string,
        ude: queryParam.ude as string,
      };
    }
  }

  // sort from even-details page
  // let sortfromEvent: any = "";
  // let sortItemfromEvent: any = "";
  // if (queryParam.hasOwnProperty("js")) {
  //   sortfromEvent = "ASC";
  //   sortItemfromEvent = "js";
  // }

  const [requestParams, setRequestParams] = useState<any>({
    queryParameter: queryParam,
    pageNumb: 1,
    dropDownCount: 10,
    sort: sortDirection,
    sortitem: sortColumn,
    js: jsValue,
    it: [],
    tp: [],
    st: stValue,
    lob: lobvalue,
    sty: [],
    pr: [],
    pf: [],
    effdr: { efds: "", efde: "" },
    crdr: { crds: "", crde: "" },
    updr: { ...updatedDateRange },
  });

  const freshparams = (
    requestParams: any,
    setRequestParams: any,
    setIsSpiner: any,
    queryParam: any
  ) => {
    if (queryParam.hasOwnProperty("js")) {
      // setInitialRender(true);
      let freshRequestParams: any = {
        ...requestParams,
        pageNumb: 1,
        dropDownCount: 10,
        js: [],
        it: [],
        tp: [],
        st: [],
        lob: [],
        sty: [],
        pr: [],
        pf: [],
        updr: { timeperiod: "", uds: "", ude: "" },
        effdr: { ...requestParams.effdr, efds: "", efde: "" },
        crdr: { ...requestParams.crdr, crds: "", crde: "" },
      };
      freshRequestParams.queryParameter = queryParam;
      setRequestParams(freshRequestParams);
      // UpdateFacets(freshRequestParams)
      setFacetObj([]);
      setSelectedUpdatedDate("");
    } else {
      setIsSpiner(false);
    }
  };

  const fetchDataOnQueryChange = (
    queryParam: any,
    requestParams: any,
    setRequestParams: any,
    setIsSpiner: any,
    LmondataAPI: any,
    setInitialRender: any
  ) => {
    if (queryParam.hasOwnProperty("keyword")) {
      setInitialRender(true);
      setShowFacets(false);
      if (typeof window !== "undefined") {
        const storedSortOptions = localStorage.getItem("sortOptions");
        if (storedSortOptions) {
          localStorage.removeItem("sortOptions");
          setSortDirection("");
          setSortColumn("");
        }
      }
      const freshRequestParams: any = {
        ...requestParams,
        pageNumb: 1,
        dropDownCount: 10,
        sort: "",
        sortitem: "",
        js: [],
        it: [],
        tp: [],
        st: [],
        lob: [],
        sty: [],
        pr: [],
        pf: [],
        updr: { timeperiod: "", uds: "", ude: "" },
        effdr: { ...requestParams.effdr, efds: "", efde: "" },
        crdr: { ...requestParams.crdr, crds: "", crde: "" },
      };
      freshRequestParams.queryParameter = queryParam;
      setRequestParams(freshRequestParams);
      LmondataAPI(freshRequestParams);
    } else {
      setIsSpiner(false);
    }
  };

  const resultsPerPageCount = (count: any, initialArray: any) => {
    if (count <= 10) {
      setHideResultsPerPage(true);
    } else if (count <= 25) {
      setHideResultsPerPage(false);
      setResultsPerPage([...initialArray.slice(0, 2)]);
    } else if (count <= 100) {
      setHideResultsPerPage(false);
      setResultsPerPage([...initialArray.slice(0, 3)]);
    } else if (count <= 250 || count > 250) {
      setHideResultsPerPage(false);
      setResultsPerPage([...initialArray]);
    }
  };

  const LmondataAPI = async (requestParams: any) => {
    try {
      setIsError(false);
      setIsSpiner(true);
      const post = await InitialLmonApi(
        requestParams,
        props,
        queryParam,
        accessToken
      );
      if (queryParam?.hasOwnProperty("keyword")) {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics(
            post,
            queryParam?.keyword,
            requestParams,
            UpdatedDateFacet
          );
        }
      } else {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics(post, "", requestParams, UpdatedDateFacet);
        }
      }
      setLmonData(post?.lmonSearchItems || []);
      setLmonFacets(post?.Facets || []);
      setPagination(post.PaginationInfo?.RecordCount);
      setTotalResults(post?.lmonSearchItems?.length);
      setIsSpiner(false);
      setcurrentPage(post?.PaginationInfo?.CurrentPage);
      resultsPerPageCount(
        post?.PaginationInfo?.RecordCount,
        props?.fields?.ValueList
      );
      setitemsPerPage(post.PaginationInfo?.PageSize);
      setTotalPages(post.PaginationInfo?.TotalPages);
      const pages: any = [];
      for (let i = 1; i <= post.PaginationInfo?.TotalPages; i++) {
        pages.push(i);
      }
      setPages(pages);
      setmaxPageNumberLimit(3);
      setminPageNumberLimit(0);
      setInitialRender(false);

      post?.Facets?.forEach((obj: any) => {
        setFacetObj((prevState: any) => ({ ...prevState, [obj.Key]: obj }));
      });
      if (
        (post?.Facets !== null || post?.Facets?.length !== 0) &&
        post?.lmonSearchItems?.length !== 0 &&
        post !== ""
      ) {
        setShowFacets(true);
      }
      setIsExcelSpinner(false);
    } catch (error) {
      if (queryParam?.hasOwnProperty("keyword")) {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics(
            [],
            queryParam?.keyword,
            requestParams,
            UpdatedDateFacet
          );
        }
      } else {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics([], "", requestParams, UpdatedDateFacet);
        }
      }
      setIsError(true);
      setIsSpiner(false);
      setIsExcelSpinner(false);
    }
  };

  const UpdateFacets = async (requestParams: any) => {
    try {
      setIsError(false);
      setIsSpiner(true);
      if (
        queryParam.hasOwnProperty("js") &&
        jsCheckFirstTime &&
        checkedValue === queryParam.js
      ) {
        setShowFacets(false);
      }
      const post = await UpdateFacetsApi(
        requestParams,
        queryParam,
        accessToken
      );
      if (queryParam?.hasOwnProperty("keyword")) {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics(
            post,
            queryParam?.keyword,
            requestParams,
            UpdatedDateFacet
          );
        }
      } else {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics(post, "", requestParams, UpdatedDateFacet);
        }
      }
      setLmonData(post?.lmonSearchItems || []);
      setLmonFacets(post?.Facets || []);
      setPagination(post.PaginationInfo?.RecordCount);
      setTotalResults(post?.lmonSearchItems?.length);
      setcurrentPage(post.PaginationInfo?.CurrentPage);
      resultsPerPageCount(
        post?.PaginationInfo?.RecordCount,
        props?.fields?.ValueList
      );
      setitemsPerPage(post.PaginationInfo?.PageSize);
      setTotalPages(post.PaginationInfo?.TotalPages);
      const pages: any = [];
      for (let i = 1; i <= post.PaginationInfo?.TotalPages; i++) {
        pages.push(i);
      }
      if (
        queryParam.hasOwnProperty("js") &&
        jsCheckFirstTime &&
        checkedValue === queryParam.js
      ) {
        post?.Facets?.forEach((obj: any) => {
          setFacetObj((prevState: any) => ({ ...prevState, [obj.Key]: obj }));
          if (jsCheckFirstTime) {
            setJsCheckFirstTime(false);
          }
        });
      }
      if (
        (post?.Facets !== null || post?.Facets?.length !== 0) &&
        post?.lmonSearchItems?.length !== 0 &&
        post !== ""
      ) {
        setShowFacets(true);
      }
      setPages(pages);
      setIsSpiner(false);
      setIsExcelSpinner(false);
    } catch (error) {
      if (queryParam?.hasOwnProperty("keyword")) {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics(
            [],
            queryParam?.keyword,
            requestParams,
            UpdatedDateFacet
          );
        }
      } else {
        if (typeof window !== "undefined" && !EEFlag) {
          lmonSearchAdobeAnalytics([], "", requestParams, UpdatedDateFacet);
        }
      }
      setIsError(true);
      setIsSpiner(false);
      setIsExcelSpinner(false);
    }
  };

  const [initialRender, setInitialRender] = useState(true);

  useEffect(() => {
    if (initialRender) {
      if (
        queryParam?.hasOwnProperty("keyword") ||
        queryParam?.hasOwnProperty("js")
      ) {
        requestParams.queryParameter = queryParam;
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "sortOptions",
            JSON.stringify({
              sortColumn: requestParams?.sortitem,
              sortDirection: requestParams?.sort,
            })
          );
        }
        LmondataAPI(requestParams);
        setInitialRender(false);
      } else {
        setIsSpiner(false);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParam]);

  useEffect(() => {
    if (!initialRender) {
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "sortOptions",
          JSON.stringify({
            sortColumn: requestParams?.sortitem,
            sortDirection: requestParams?.sort,
          })
        );
        UpdateFacets(requestParams);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestParams]);

  useEffect(() => {
    if (!initialRender) {
      fetchDataOnQueryChange(
        queryParam,
        requestParams,
        setRequestParams,
        setIsSpiner,
        LmondataAPI,
        setInitialRender
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParam]);

  const hanldeUpdatedDate = (event: any) => {
    if (event.target.checked) {
      setSelectedUpdatedDate(event.target.value);
      setRequestParams({
        ...requestParams,
        updr: { ...requestParams.updr, timeperiod: event.target.value },
      });

      if (event.target.value === "custom") {
        setShowdate(true);
      } else {
        setShowdate(false);
      }
    } else {
      setRequestParams({
        ...requestParams,
        updr: { ...requestParams.updr, timeperiod: "" },
      });
      setSelectedUpdatedDate("");
      setShowdate(false);
    }
  };

  const handleDateStart = (event: any, keyValue: any) => {
    const startDate = event.target.value;
    let endDate = "";

    if (keyValue === "crdr") {
      endDate = requestParams.crdr.crde;
    } else if (keyValue === "effdr") {
      endDate = requestParams.effdr.efde;
    } else {
      endDate = requestParams.updr.ude;
    }

    if (!endDate) {
      setErrorVal((prevState: any) => ({
        ...prevState,
        [keyValue]: {
          ...prevState[keyValue],
          startDate: startDate,
          endDate: prevState[keyValue]?.endDate || "",
          errorMessage: "End date is missing",
          type: keyValue,
        },
      }));
      console.log(
        `Skipping requestParams update for ${keyValue} because end date is missing.`
      );
      return;
    }

    setErrorVal((prevState: any) => ({
      ...prevState,
      [keyValue]: {
        ...prevState[keyValue],
        startDate: startDate,
        endDate: endDate,
        errorMessage: "",
        type: keyValue,
      },
    }));

    if (keyValue === "crdr") {
      setRequestParams({
        ...requestParams,
        crdr: { ...requestParams.crdr, crds: startDate },
      });
    } else if (keyValue === "effdr") {
      setRequestParams({
        ...requestParams,
        effdr: { ...requestParams.effdr, efds: startDate },
      });
    } else {
      setRequestParams({
        ...requestParams,
        updr: { ...requestParams.updr, uds: startDate },
      });
    }
  };

  const handleDateEnd = (event: any, keyValue: any) => {
    const endDate = event.target.value;

    let startDate = "";
    if (keyValue === "crdr") {
      startDate = requestParams.crdr.crds || errorVal.crdr?.startDate;
    } else if (keyValue === "effdr") {
      startDate = requestParams.effdr.efds || errorVal.effdr?.startDate;
    } else {
      startDate = requestParams.updr.uds || errorVal.updr?.startDate;
    }

    if (startDate && endDate) {
      setErrorVal((prevState: any) => ({
        ...prevState,
        [keyValue]: {
          ...prevState[keyValue],
          startDate: startDate,
          endDate: endDate,
          errorMessage: "",
          type: keyValue,
        },
      }));

      if (keyValue === "crdr") {
        setRequestParams({
          ...requestParams,
          crdr: { crds: startDate, crde: endDate },
        });
      } else if (keyValue === "effdr") {
        setRequestParams({
          ...requestParams,
          effdr: { efds: startDate, efde: endDate },
        });
      } else {
        setRequestParams({
          ...requestParams,
          updr: { ...requestParams.updr, uds: startDate, ude: endDate },
        });
      }
    } else {
      setErrorVal((prevState: any) => ({
        ...prevState,
        [keyValue]: {
          ...prevState[keyValue],
          startDate: startDate,
          endDate: endDate,
          errorMessage: "Start date is missing",
          type: keyValue,
        },
      }));
    }
  };

  const handleFacets = (event: any) => {
    setmaxPageNumberLimit(3);
    setminPageNumberLimit(0);
    const facetKey = event.target.dataset.key;
    const value = event.target.value;

    if (event.target.checked) {
      setCheckedValue(value);
      const checkedFacet = facetObj[facetKey]?.Values?.map((item: any) => {
        if (item.Key === value) {
          item.Selected = true;
          return item;
        }
        return item;
      });
      const copyFacetdata = { ...facetObj };
      copyFacetdata[facetKey] = {
        ...copyFacetdata[facetKey],
        Values: [...checkedFacet],
      };
      setFacetObj(copyFacetdata);

      const requData = requestParams[facetKey];
      requData?.push(value);
      requestParams[facetKey] = requData;
      setRequestParams({
        ...requestParams,
        pageNumb: 1,
      });
    } else {
      setCheckedValue(value);
      const checkedFacet = facetObj[facetKey]?.Values?.map((item: any) => {
        if (item.Key === value) {
          item.Selected = false;
          return item;
        }
        return item;
      });
      const copyFacetdata = { ...facetObj };
      copyFacetdata[facetKey] = {
        ...copyFacetdata[facetKey],
        Values: [...checkedFacet],
      };
      setFacetObj(copyFacetdata);

      const requData = requestParams[facetKey];
      const position = requData.indexOf(value);
      if (~position) requData.splice(position, 1);
      requestParams[facetKey] = requData;
      setRequestParams({
        ...requestParams,
        pageNumb: 1,
      });
      if (facetKey === "js") {
        if (!initialRender && jsCheckFirstTime && value === queryParam.js) {
          freshparams(requestParams, setRequestParams, setIsSpiner, queryParam);
          setIsSpiner(true);
          // EventDetailsFacets()
        }
      }
    }
  };

  const handleDateCapsule: any = (dateType: any) => {
    setErrorVal((prevState: any) => ({
      ...prevState,
      [dateType]: {
        startDate: "",
        endDate: "",
        errorMessage: "",
        type: dateType,
      },
    }));

    if (dateType === "crdr") {
      setRequestParams({
        ...requestParams,
        crdr: { ...requestParams.crdr, crds: "", crde: "" },
      });
    }
    if (dateType === "effdr") {
      setRequestParams({
        ...requestParams,
        effdr: { ...requestParams.effdr, efds: "", efde: "" },
      });
    }
    if (dateType === "updr") {
      setRequestParams({
        ...requestParams,
        updr: { ...requestParams.updr, timeperiod: "", uds: "", ude: "" },
      });
      setSelectedUpdatedDate("");
      setShowdate(false);
    }
  };

  const handleCapsule: any = (facetKey: any, value: any) => {
    setmaxPageNumberLimit(3);
    setminPageNumberLimit(0);
    setCheckedValue(value);
    const checkedFacet = facetObj[facetKey]?.Values?.map((item: any) => {
      if (item.Key === value) {
        item.Selected = false;
        return item;
      }
      return item;
    });
    const copyFacetdata = { ...facetObj };
    copyFacetdata[facetKey] = {
      ...copyFacetdata[facetKey],
      Values: [...checkedFacet],
    };
    setFacetObj(copyFacetdata);

    const requData = requestParams[facetKey];
    const position = requData.indexOf(value);
    if (~position) requData.splice(position, 1);
    requestParams[facetKey] = requData;
    setRequestParams({
      ...requestParams,
      pageNumb: 1,
    });
  };

  const handleClick = (event: any) => {
    scrollToTop();
    setRequestParams({ ...requestParams, pageNumb: Number(event.target.id) });
  };

  const handleSelect = (event: any) => {
    scrollToTop();
    setRequestParams({
      ...requestParams,
      dropDownCount: event.target?.value,
      pageNumb: 1,
    });
    // setResultsPerPage(event.target?.value)
    setmaxPageNumberLimit(3);
    setminPageNumberLimit(0);
  };

  const handleNextbtn = () => {
    scrollToTop();
    setRequestParams({ ...requestParams, pageNumb: currentPage + 1 });

    if (currentPage + 1 > maxPageNumberLimit) {
      setmaxPageNumberLimit(maxPageNumberLimit + pageNumberLimit);
      setminPageNumberLimit(minPageNumberLimit + pageNumberLimit);
    }
  };

  const handleExtreamNextbtn = () => {
    scrollToTop();
    if (pages.length > 0) {
      const lastPage: any = totalPages;
      setRequestParams({ ...requestParams, pageNumb: totalPages });

      if (lastPage % pageNumberLimit !== 0) {
        // condition is for no.of pages not multiple of pagelimit(3)
        setmaxPageNumberLimit(lastPage); // last page is fixed that should be hightlighed
        setminPageNumberLimit(
          lastPage - ((lastPage % pageNumberLimit) - 1) - 1
        ); //(lastpage%pagelimit) will give left over pages to show, (lastPage % pageNumberLimit - 1) give what should minus to get the starting value and extra -1 will handle what number should show.
      } else {
        // if no.of pages is multiple of 3
        setmaxPageNumberLimit(lastPage);
        setminPageNumberLimit(lastPage - (pageNumberLimit + 1) + 1); //
      }
    }
  };

  const handleExtreamPrevbtn = () => {
    scrollToTop();
    if (pages.length > 0) {
      const firstPage = pages[0];
      setRequestParams({ ...requestParams, pageNumb: firstPage });
      setcurrentPage(firstPage);
      setmaxPageNumberLimit(pageNumberLimit); // to show maximum 3 page incase of more than 3 pages
      setminPageNumberLimit(firstPage - 1); // min always will be first page so min should be 0
    }
  };

  const handlePrevbtn = (e: any) => {
    e.preventDefault();
    if (e.keyCode === 13) {
    }
    scrollToTop();
    setRequestParams({ ...requestParams, pageNumb: currentPage - 1 });

    if ((currentPage - 1) % pageNumberLimit == 0) {
      setmaxPageNumberLimit(currentPage - 1);
      setminPageNumberLimit(currentPage - pageNumberLimit - 1);
    }
  };

  const handleIcon = (column: any) => {
    if (column !== sortColumn) {
      if (
        column === "effective" ||
        column === "updated" ||
        column === "created"
      ) {
        setSortDirection("DESC");
        setRequestParams({ ...requestParams, sortitem: column, sort: "DESC" });
      } else {
        setSortDirection("ASC");
        setRequestParams({ ...requestParams, sortitem: column, sort: "ASC" });
      }
      setSortColumn(column);
    }
  };

  const excelDownload = () => {
    alert("hello");
  };

  const handlesortitem = (e: any, column: any) => {
    e.stopPropagation();
    if (sortDirection === "ASC") {
      setRequestParams({ ...requestParams, sortitem: column, sort: "DESC" });
      setSortDirection("DESC");
    } else {
      setRequestParams({ ...requestParams, sortitem: column, sort: "ASC" });
      setSortDirection("ASC");
    }
  };

  const ExcelApi = async () => {
    setIsExcelSpinner(true);
    try {
      const post = await ExcelApiData(requestParams, queryParam, accessToken);
      const excelData = post?.lmonSearchItems;
      setIsExcelSpinner(false);
      return excelData;
    } catch (err) {
      setIsError(false);
      setIsExcelSpinner(false);
    }
  };

  const handleExportExcel = async () => {
    const lmonData = await ExcelApi();
    // const lmonData =ExcelData?.lmonSearchItems;
    // const {lmonSearchItems} = jsondata
    const getValueByPath = (obj: any, path: any) => {
      const parts = Array.isArray(path) ? path : path.split(".");
      let value = obj;
      for (const part of parts) {
        value = value[part];
        if (value === undefined) return undefined;
      }
      return value;
    };

    // defining columns to include in the excel file
    const columns = [
      { label: "Jurisdiction", value: ["Jurisdiction", 0, "Code"], width: 20 },
      { label: "EventTitle", value: "EventTitle", width: 60 },
      { label: "ItemType", value: ["ItemType", 0, "DispalyName"], width: 15 },
      { label: "EffectiveDate", value: "EffectiveDate", width: 20 },
      {
        label: "LOBS",
        value: (item: any) => item.LOBs.map((lob: any) => lob.Code).join(", "),
        width: 25,
      },
      { label: "Status", value: ["Status", 0, "DispalyName"], width: 10 },
      { label: "UpdatedDate", value: "UpdatedDate", width: 20 },
      { label: "CreatedDate", value: "CreatedDate", width: 20 },
      { label: "Description", value: "Description", width: 45 },
      { label: "Comments", value: "Comments", width: 45 },
      { label: "Filing ID's", value: "FilingIDs", width: 45 },
      { label: "Circular ID's", value: "CircularIDs", width: 45 },
    ];
    //prepare the data for the excel file
    const data = lmonData?.map((item: any) => {
      const rowData: any = {};
      columns.forEach((column: any) => {
        const { value } = column;
        rowData[column.label] =
          typeof value === "function"
            ? value(item)
            : getValueByPath(item, value);
      });
      return rowData;
    });
    // create a new workbook and add the worksheet

    const XLSX: any = await import("sheetjs-style");
    const worksheet = XLSX.utils.json_to_sheet(data, {
      header: columns.map((column) => column.label),
      origin: "A2",
    });

    const datacellStyle = {
      alignment: { horizontal: "left", vertical: "center", wrapText: true },
    };
    columns.forEach((column, columnIndex) => {
      const ref = XLSX.utils.encode_col(columnIndex);
      worksheet["!cols"] = worksheet["!cols"] || [];
      worksheet["!cols"].push({ wch: column.width, ref });
    });

    const range = XLSX.utils.decode_range(worksheet["!ref"]);
    for (let R = range.s.r + 1; R <= range.e.r; ++R) {
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cellAdress = XLSX.utils.encode_cell({ r: R, c: C });
        const cell = worksheet[cellAdress];
        cell.s = datacellStyle;
      }
    }
    // add link to event title

    // adding styles
    const addLinkToCell = (cell: any, url: any) => {
      const hyperlinkStyle = {
        alignment: { horizontal: "left", vertical: "center" },
        font: { underline: true, color: { rgb: "004eaa" } },
      };
      cell.l = { Target: url, Tooltip: url };
      cell.s = hyperlinkStyle;
    };

    const addHyperLinkToEventTitle = (rowData: any, rowIndex: any) => {
      const EventTitle = rowData.EventTitle;
      if (EventTitle) {
        const eventid = lmonData[rowIndex - 1].LMONItemID;
        const publicurl = process.env.PUBLIC_URL;
        // http://localhost:3000/Lmon-Hub/lmon%20event?eventId=12471
        const url = `${publicurl}/lmon%20hub/lmon%20event?eventId=${eventid}`;
        const cellAddress = XLSX.utils.encode_cell({ r: rowIndex + 1, c: 1 });
        const cell = worksheet[cellAddress];
        addLinkToCell(cell, url);
      }
    };
    data.forEach((rowData: any, rowIndex: any) => {
      addHyperLinkToEventTitle(rowData, rowIndex + 1);
    });

    // ADD Disclaimer in the first row and merge cells

    const disclamerText = props?.fields?.ExcelDisclaimer?.value;
    const disclaimerStyle = {
      alignment: { horizontal: "center", vertical: "center", wrapText: true },
    };
    worksheet["B1"] = { t: "s", v: disclamerText, s: disclaimerStyle };
    worksheet["!merges"] = [
      { s: { r: 0, c: 1 }, e: { r: 0, c: columns.length - 5 } },
    ];
    worksheet["!merges"].s = disclaimerStyle;
    const characterPerLine = 165;
    const rowHeight = Math.ceil(disclamerText.length / characterPerLine) * 15;
    worksheet["!rows"] = [{ hpt: rowHeight, hpx: rowHeight }];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    // Generate the Excel file

    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    const excelBlob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    //create download link and trigger the download

    const excelURL = URL.createObjectURL(excelBlob);
    const link = document.createElement("a");
    link.href = excelURL;
    link.download = "Search Excel";
    link.click();

    URL.revokeObjectURL(excelURL);
  };
  const startIndex: any = (currentPage - 1) * itemsPerPage;
  const endIndex: any = startIndex + itemsPerPage;
  const fromResult: any = startIndex + 1;
  const toResult: any = Math.min(endIndex, pagination);

  const renderPageNumbers = pages.map((number: any, id: any) => {
    if (number < maxPageNumberLimit + 1 && number > minPageNumberLimit) {
      return (
        <li
          className={
            currentPage == number
              ? "page-item page-number active"
              : "page-item page-number"
          }
          key={id}
        >
          <a
            id={number}
            key={number}
            tabIndex={0}
            onKeyUp={(e) => e.key === "Enter" && handleClick(e)}
            onClick={handleClick}
            className={currentPage == number ? "page-link active" : "page-link"}
          >
            {number}
          </a>
        </li>
      );
    } else {
      return null;
    }
  });

  return (
    <>
      <LmonFacetLayout
        EEFlag={EEFlag}
        lmondata={lmondata}
        facetObj={facetObj}
        requestParams={requestParams}
        textFields={props?.fields}
        handleDateStart={handleDateStart}
        handleDateEnd={handleDateEnd}
        hanldeUpdatedDate={hanldeUpdatedDate}
        handleFacets={handleFacets}
        UpdatedDateFacet={UpdatedDateFacet}
        UpdatedDateCount={UpdatedDateCount}
        selectedUpdatedDate={selectedUpdatedDate}
        showfacets={showfacets}
        showdate={showdate}
        queryParam={queryParam}
        isError={isError}
        lmonFacets={lmonFacets}
        errorVal={errorVal}
      />

      <LmonSearchResultLayout
        //Common Property
        EEFlag={EEFlag}
        textFields={props?.fields}
        spinner={spinner}
        requestParams={requestParams}
        lmondata={lmondata}
        facetObj={facetObj}
        handleCapsule={handleCapsule}
        handleDateCapsule={handleDateCapsule}
        showfacets={showfacets}
        showdate={showdate}
        selectedUpdatedDate={selectedUpdatedDate}
        excelDownload={excelDownload}
        sortColumn={sortColumn}
        sortDirection={sortDirection}
        setSortDirection={setSortDirection}
        isError={isError}
        lmonFacets={lmonFacets}
        handleExportExcel={handleExportExcel}
        isExcelSpinner={isExcelSpinner}
        //Info about result
        totalResults={totalResults}
        fromResult={fromResult}
        toResult={toResult}
        //Pagination realted
        pagination={pagination}
        itemsPerPage={itemsPerPage}
        renderPageNumbers={renderPageNumbers}
        currentPage={currentPage}
        pages={pages}
        //All handlers
        handleIcon={handleIcon}
        handlesortitem={handlesortitem}
        handleSelect={handleSelect}
        handleExtreamPrevbtn={handleExtreamPrevbtn}
        handlePrevbtn={handlePrevbtn}
        handleNextbtn={handleNextbtn}
        handleExtreamNextbtn={handleExtreamNextbtn}
        hideResultsPerPage={hideResultsPerPage}
        resultsPerPage={resultsPerPage}
      />
    </>
  );
};

export default withSitecoreContext()(LmonSearch);
