import {
  Text,
  Placeholder,
  withSitecoreContext,
} from "@sitecore-jss/sitecore-jss-nextjs";
import React from "react";
import Loader from "components/common/Loader";
import ErrorMessage from "components/common/ErrorMessage";
import FacetCapLayout from "./FacetCapLayout";

const lmonResultsDisplay = (lmondata: any): JSX.Element => {
  const eventpage: any = "/lmon-hub/lmon%20event";

  return (
    <>
      {lmondata?.length > 0 && (
        <>
          {lmondata?.map((tableresults: any, id: any) => {
            const lobvalues = tableresults?.LOBs?.map((values: any) => {
              if (values?.Code !== "" && values?.Code !== undefined) {
                return values?.Code;
              }
            });
            const lobfilter = lobvalues?.filter((value: any) => {
              if (value !== undefined) {
                return value;
              }
            });
            return (
              <React.Fragment key={id}>
                <tr>
                  <th className="active-sort">
                    {tableresults?.Jurisdiction?.[0]?.Code}
                  </th>
                  <td>
                    <a
                      href={eventpage + "?eventId=" + tableresults?.LMONItemID}
                      target="_blank"
                      data-state={tableresults?.Jurisdiction?.[0]?.Code}
                      data-name={tableresults?.EventTitle}
                      data-type={tableresults?.ItemType?.[0]?.DispalyName}
                      data-eventdate={tableresults?.CreatedDate}
                      data-effectivedate={tableresults?.EffectiveDate}
                      data-status={tableresults?.Status?.[0]?.DispalyName}
                      data-lob={lobfilter?.join(",")}
                      data-updated={tableresults?.UpdatedDate}
                      data-eventId={tableresults?.LMONItemID}
                    >
                      {tableresults?.EventTitle}
                    </a>
                  </td>
                  <td>{tableresults?.ItemType?.[0]?.DispalyName}</td>
                  <td>{tableresults?.EffectiveDate}</td>
                  <td>{lobfilter?.join(", ")}</td>
                  <td>{tableresults?.Status?.[0]?.DispalyName}</td>
                  <td>{tableresults?.CreatedDate}</td>
                  <td>{tableresults?.UpdatedDate}</td>
                </tr>
              </React.Fragment>
            );
          })}
        </>
      )}
      {/* {lmondata?.length === 0 && 'No Results Found'} */}
    </>
  );
};
const LmonSearchResultLayout = ({ ...props }: any): JSX.Element => {
  const { route } = props?.sitecoreContext || {};

  //Common Property
  const {
    spinner,
    lmondata,
    textFields,
    showfacets,
    facetObj,
    sortColumn,
    sortDirection,
    isExcelSpinner,
    requestParams,
  } = props;
  //Info about result
  const { totalResults, fromResult, toResult, EEFlag, isError } = props;
  //Pagination realted
  const {
    pagination,
    itemsPerPage,
    renderPageNumbers,
    currentPage,
    pages,
    hideResultsPerPage,
    resultsPerPage,
  } = props;
  //All handlers
  const {
    handleSelect,
    handleExtreamPrevbtn,
    handlePrevbtn,
    handleNextbtn,
    handleExtreamNextbtn,
    handlesortitem,
    handleIcon,
    handleExportExcel,
    handleCapsule,
    handleDateCapsule,
  } = props;

  if ((EEFlag && isError) || (EEFlag && lmondata?.length === 0) || EEFlag) {
    return (
      <div className="content-wrapper">
        <Placeholder
          name="jss-search-breadcrumbs"
          rendering={route}
          isEEFlag={EEFlag}
        />
        <Text field={textFields.LmonSearchHeading} tag="h1" />
        <div className="flex-wrapper glossary-search">
          <Placeholder
            name="jss-lmon-glossary-link"
            rendering={route}
            isEEFlag={EEFlag}
          />
        </div>
        <div className="results-meta-sort flex-wrapper">
          <span>
            <Text field={textFields.ShowingText} />
            &nbsp;
            <Text field={textFields.ofText} />
            &nbsp;
            <Text field={textFields.ResultsText} />
          </span>
          <div className="download-results">
            <a>
              <span className="material-icons" aria-hidden="true">
                download
              </span>
              <span>
                <Text field={textFields?.ExcelDownloadText} />
              </span>
            </a>
          </div>
        </div>
        <section className="results-listing">
          <div className="results-table scrollable">
            <table>
              <thead>
                <tr>
                  <th className="active-sort-header descending">
                    <a>
                      <Text field={textFields.Jurisdiction} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.EventName} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.ItemType} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.EffectiveDate} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.LOB} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.Status} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.CreateDate} />
                    </a>
                  </th>
                  <th>
                    <a>
                      <Text field={textFields.UpdatedDate} />
                    </a>
                  </th>
                </tr>
              </thead>
            </table>
          </div>
          <div className="page-results-wrapper">
            <div className="pageresults">
              <span>
                <Text field={textFields?.PaginationTitle} />
                &nbsp;
              </span>
              <div className="select-wrapper">
                <select
                  name="count"
                  id="results-options"
                  defaultValue={itemsPerPage}
                  onChange={handleSelect}
                >
                  {resultsPerPage?.map((num: any, id: any) => {
                    return (
                      <option value={num?.displayName} key={id}>
                        {num?.fields?.Value?.value}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
            <nav aria-label="Page navigation example">
              <ul className="pagination">
                <li className="page-item">
                  <a className="page-link previous-page disabled">
                    <Text field={textFields?.PreviousText} />
                  </a>
                </li>
                <li className="page-item next">
                  <a className="page-link next-page">
                    <Text field={textFields?.NextText} />
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </section>
        <Placeholder name="jss-search-disclaimer" rendering={route} />
      </div>
    );
  }
  if (isError) {
    return (
      <>
        <div className="site flex-wrapper">
          <ErrorMessage message="We are experiencing an issue loading Search results and are working to resolve it. Thank you for your patience." />
        </div>
      </>
    );
  }

  return (
    <div className="content-wrapper">
      {!spinner ? (
        <>
          {totalResults > 0 && (
            <Placeholder name="jss-search-breadcrumbs" rendering={route} />
          )}
          {totalResults > 0 && (
            <Text field={textFields.LmonSearchHeading} tag="h1" />
          )}
          <div className="flex-wrapper glossary-search">
            <Placeholder
              name="jss-lmon-glossary-link"
              rendering={route}
              isEEFlag={EEFlag}
            />
          </div>
          <div className="pills removable">
            {
              <FacetCapLayout
                requestParams={requestParams}
                handleCapsule={handleCapsule}
                handleDateCapsule={handleDateCapsule}
                facetObj={facetObj}
                textFields={textFields}
              />
            }
          </div>

          <div className="results-meta-sort flex-wrapper">
            {totalResults > 0 && (
              <>
                <span>
                  <Text field={textFields.ShowingText} />
                  &nbsp;
                  {fromResult}-{toResult}&nbsp;
                  <Text field={textFields.ofText} /> {pagination}&nbsp;
                  <Text field={textFields.ResultsText} />
                </span>
                {isExcelSpinner && <Loader />}
                {!isExcelSpinner && (
                  <div
                    className="download-results"
                    data-testid="download"
                    onClick={() => handleExportExcel()}
                  >
                    <a
                      tabIndex={0}
                      onKeyUp={(e) => e.key === "Enter" && handleExportExcel()}
                      data-testid="download-excel"
                    >
                      <span className="material-icons" aria-hidden="true">
                        download
                      </span>
                      &nbsp;
                      <span>
                        <Text field={textFields?.ExcelDownloadText} />
                      </span>
                    </a>
                  </div>
                )}
              </>
            )}
          </div>
          {lmondata?.length > 0 && (
            <section className="results-listing">
              <div className="results-table scrollable">
                <table>
                  <>
                    <thead>
                      <tr>
                        <th className="active-sort-header descending">
                          <a
                            onClick={() => handleIcon("js")}
                            className="sortarrow"
                            tabIndex={"js" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("js")
                            }
                            data-testid="js"
                          >
                            {textFields.Jurisdiction.value}
                            {sortColumn === "js" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "js")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" && handlesortitem(e, "js")
                                }
                                data-testid="sort-js"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("Name")}
                            className="sortarrow"
                            tabIndex={"Name" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("Name")
                            }
                            data-testid="name"
                          >
                            {textFields.EventName.value}
                            {sortColumn === "Name" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "Name")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" && handlesortitem(e, "Name")
                                }
                                data-testid="sort-name"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("type")}
                            className="sortarrow"
                            tabIndex={"type" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("type")
                            }
                            data-testid="type"
                          >
                            {textFields.ItemType.value}
                            {sortColumn === "type" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "type")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" && handlesortitem(e, "type")
                                }
                                data-testid="sort-type"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("effective")}
                            className="sortarrow"
                            tabIndex={"effective" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("effective")
                            }
                            data-testid="effective"
                          >
                            {textFields.EffectiveDate.value}
                            {sortColumn === "effective" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "effective")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" &&
                                  handlesortitem(e, "effective")
                                }
                                data-testid="sort-effective"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("lob")}
                            className="sortarrow"
                            tabIndex={"lob" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("lob")
                            }
                            data-testid="lob"
                          >
                            {textFields.LOB.value}
                            {sortColumn === "lob" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "lob")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" && handlesortitem(e, "lob")
                                }
                                data-testid="sort-lob"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("status")}
                            className="sortarrow"
                            tabIndex={"status" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("status")
                            }
                            data-testid="status"
                          >
                            {textFields.Status.value}
                            {sortColumn === "status" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "status")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" &&
                                  handlesortitem(e, "status")
                                }
                                data-testid="sort-status"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("created")}
                            className="sortarrow"
                            tabIndex={"created" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("created")
                            }
                            data-testid="created"
                          >
                            {textFields.CreateDate.value}
                            {sortColumn === "created" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "created")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" &&
                                  handlesortitem(e, "created")
                                }
                                data-testid="sort-created"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                        <th>
                          <a
                            onClick={() => handleIcon("updated")}
                            className="sortarrow"
                            tabIndex={"updated" !== sortColumn ? 0 : -1}
                            onKeyUp={(e) =>
                              e.key === "Enter" && handleIcon("updated")
                            }
                            data-testid="updated"
                          >
                            {textFields.UpdatedDate.value}
                            {sortColumn === "updated" && (
                              <span
                                className="material-icons"
                                onClick={(e) => handlesortitem(e, "updated")}
                                tabIndex={0}
                                onKeyUp={(e) =>
                                  e.key === "Enter" &&
                                  handlesortitem(e, "updated")
                                }
                                data-testid="sort-updated"
                              >
                                {sortDirection === "ASC" ? (
                                  <>arrow_upward</>
                                ) : (
                                  <>arrow_downward</>
                                )}
                              </span>
                            )}
                          </a>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {spinner ? <Loader /> : lmonResultsDisplay(lmondata)}
                    </tbody>
                  </>
                  {/* )} */}
                </table>
              </div>
              <div className="page-results-wrapper">
                {!hideResultsPerPage && (
                  <div className="pageresults">
                    <span>
                      <Text field={textFields?.PaginationTitle} />
                      &nbsp;
                    </span>
                    <div className="select-wrapper">
                      <select
                        name="count"
                        id="results-options"
                        defaultValue={itemsPerPage}
                        onChange={handleSelect}
                      >
                        {resultsPerPage?.map((num: any, id: any) => {
                          return (
                            <option value={num?.displayName} key={id}>
                              {num?.fields?.Value?.value}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  </div>
                )}
                <nav aria-label="Page navigation example">
                  <ul className="pagination">
                    <li className="page-item">
                      <a
                        className="page-link first-page"
                        onClick={handleExtreamPrevbtn}
                        tabIndex={currentPage == pages[0] ? -1 : 0}
                        onKeyUp={(e) =>
                          e.key === "Enter" && handleExtreamPrevbtn(e)
                        }
                        aria-disabled={currentPage == pages[0] ? true : false}
                        data-testid="extreme-prev"
                      >
                        <span className="material-icons">first_page</span>
                      </a>
                    </li>
                    <li className="page-item">
                      <a
                        className="page-link previous-page disabled"
                        onClick={handlePrevbtn}
                        tabIndex={currentPage == pages[0] ? -1 : 0}
                        onKeyUp={(e) => e.key === "Enter" && handlePrevbtn(e)}
                        aria-disabled={currentPage == pages[0] ? true : false}
                        data-testid="prev"
                      >
                        <Text field={textFields?.PreviousText} />
                      </a>
                    </li>
                    {renderPageNumbers}
                    <li className="page-item next">
                      <a
                        className="page-link next-page"
                        onClick={handleNextbtn}
                        tabIndex={
                          currentPage == pages[pages.length - 1] ? -1 : 0
                        }
                        onKeyUp={(e) => e.key === "Enter" && handleNextbtn(e)}
                        aria-disabled={
                          currentPage == pages[pages.length - 1] ? true : false
                        }
                        data-testid="next"
                      >
                        <Text field={textFields?.NextText} />
                      </a>
                    </li>
                    <li className="page-item">
                      <a
                        className="page-link last-page"
                        onClick={handleExtreamNextbtn}
                        tabIndex={
                          currentPage == pages[pages.length - 1] ? -1 : 0
                        }
                        onKeyUp={(e) =>
                          e.key === "Enter" && handleExtreamNextbtn(e)
                        }
                        aria-disabled={
                          currentPage == pages[pages.length - 1] ? true : false
                        }
                        data-testid="extreme-next"
                      >
                        <span className="material-icons">last_page</span>
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            </section>
          )}

          {!showfacets && lmondata?.length === 0 ? "No Results Found" : ""}
          {showfacets && lmondata?.length === 0
            ? "No Results Found for This Search"
            : ""}
          <Placeholder name="jss-search-disclaimer" rendering={route} />
        </>
      ) : (
        <Loader />
      )}
    </div>
  );
};

// export default LmonSearchResultLayout;
export default withSitecoreContext()(LmonSearchResultLayout);
