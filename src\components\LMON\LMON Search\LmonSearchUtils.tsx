export const RequestOptions = (
  requestParams: any,
  queryParam: any,
  Rankingrule: any,
  Excel: any,
  accessToken: any
) => {
  let timePeriodCheck: any = "";
  if (requestParams.updr.timeperiod !== "") {
    timePeriodCheck = requestParams.updr.timeperiod;
  }
  let eventDateStart: any = "";
  if (requestParams.crdr.crds !== "") {
    eventDateStart = requestParams.crdr.crds;
  }
  let eventDateEnd: any = "";
  if (requestParams.crdr.crde !== "") {
    eventDateEnd = requestParams.crdr.crde;
  }
  let effectiveDateStart: any = "";
  if (requestParams.effdr.efds !== "") {
    effectiveDateStart = requestParams.effdr.efds;
  }
  let effectiveDateEnd: any = "";
  if (requestParams.effdr.efde !== "") {
    effectiveDateEnd = requestParams.effdr.efde;
  }
  let updatedDateStart: any = "";
  if (requestParams.updr.uds !== "") {
    updatedDateStart = requestParams.updr.uds;
  }
  let updatedDateEnd: any = "";
  if (requestParams.updr.ude !== "") {
    updatedDateEnd = requestParams.updr.ude;
  }
  let sort: any = "";
  if (requestParams.sort !== "") {
    sort = requestParams.sort;
  }
  let sortitem: any = "";
  if (requestParams.sortitem !== "") {
    sortitem = requestParams.sortitem;
  }
  let keyword: any = "";
  if (queryParam.hasOwnProperty("keyword")) {
    keyword = requestParams.queryParameter.keyword;
  }

  let Jurisdiction = "";
  if (requestParams?.js.length !== 0) {
    Jurisdiction = requestParams?.js.join(",");
  }

  let LineOfBusiness = "";
  if (requestParams?.lob?.length !== 0) {
    LineOfBusiness = requestParams?.lob?.join(",");
  }

  let ItemType = "";
  if (requestParams?.it?.length !== 0) {
    ItemType = requestParams?.it?.join(",");
  }

  let TopicItem = "";
  if (requestParams?.tp?.length !== 0) {
    TopicItem = requestParams?.tp?.join(",");
  }

  let Status = "";
  if (requestParams?.st?.length !== 0) {
    Status = requestParams?.st?.join(",");
  }

  let ServiceType = "";
  if (requestParams?.sty?.length !== 0) {
    ServiceType = requestParams?.sty?.join(",");
  }

  let ProceduralRequirements = "";
  if (requestParams?.pr?.length !== 0) {
    ProceduralRequirements = requestParams?.pr?.join(",");
  }

  let PolicySupportForms = "";
  if (requestParams?.pf?.length !== 0) {
    PolicySupportForms = requestParams?.pf?.join(",");
  }

  const searchParams = {
    Keyword: encodeURIComponent(keyword),
    Lang: "en",
    LineOfBusiness: LineOfBusiness,
    ItemType: ItemType,
    TopicItem: TopicItem,
    Jurisdiction: Jurisdiction,
    Status: Status,
    PageNumber: requestParams?.pageNumb,
    PageSize: requestParams?.dropDownCount,
    SortItem: sortitem,
    SortOption: sort,
    CreatedDateStart: eventDateStart,
    CreatedDateEnd: eventDateEnd,
    UpdatedDateStart: updatedDateStart,
    UpdatedDateEnd: updatedDateEnd,
    EffectiveDateStart: effectiveDateStart,
    EffectiveDateEnd: effectiveDateEnd,
    Timeperiod: timePeriodCheck,
    ServiceType: ServiceType,
    ProceduralRequirements: ProceduralRequirements,
    PolicySupportForms: PolicySupportForms,
    SearchRules: Rankingrule,
    Excel: Excel,
  };

  let searchQuery: any = {};
  if (queryParam?.hasOwnProperty("keyword")) {
    searchQuery = searchParams;
  } else if (queryParam?.hasOwnProperty("js")) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { Keyword, SearchRules, ...searchjsQuery }: any = searchParams; // removed keys to use rest
    searchQuery = searchjsQuery;
  }

  const requestOptions = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
    body: JSON.stringify(searchQuery),
  };
  return requestOptions;
};

export const getLabels = (textFields: any) => {
  let labels: any = {};
  const jurisdiction = textFields?.JurisdictionFilter;
  labels["jurisdiction"] = jurisdiction;
  const ItemType = textFields?.ItemTypeFilter;
  labels["ItemType"] = ItemType;
  const lob = textFields?.LobFilter;
  labels["lob"] = lob;
  const Status = textFields?.StatusFilter;
  labels["Status"] = Status;
  const serviceType = textFields?.ServiceTypeFilter;
  labels["serviceType"] = serviceType;
  const procedural = textFields?.ProceduralRequirementsFilter;
  labels["procedural"] = procedural;
  const policyform = textFields?.PolicyFormsFilter;
  labels["policyform"] = policyform;
  const topicValue = textFields?.TopicFilter;
  labels["topicValue"] = topicValue;
  const createddate = textFields?.CreateDateRangeFilter;
  labels["createddate"] = createddate;
  const startDate = textFields?.StartDateText;
  labels["startDate"] = startDate;
  const endDate = textFields?.EndDateText;
  labels["endDate"] = endDate;
  const UpdatedDateList = textFields?.UpdatedDateList;
  labels["UpdatedDateList"] = UpdatedDateList;
  const updatedDate = textFields?.UpdatedDateFilter;
  labels["updatedDate"] = updatedDate;
  const effectiveDate = textFields?.EffectiveDateFilter;
  labels["effectiveDate"] = effectiveDate;
  const showmore = textFields?.ShowMoreText;
  labels["showmore"] = showmore;
  const showless = textFields?.ShowLessText;
  labels["showless"] = showless;
  return labels;
};

export const sortMethods = {
  ascending: (a: any, b: any) => (a.Name > b.Name ? 1 : -1),
  descending: (a: any, b: any) => (a.Name > b.Name ? -1 : 1),
};

export const facetLabels: any = {
  "Lines of business": "Line of Business",
  ittitle: "Type",
  Topic: "Topic",
  Jurisdiction: "Jurisdictions",
  sttitle: "Status",
  stytitle: "Service Type",
  pftitle: "Policy Writing Support Forms",
  prtitle: "Procedural Requirements",
};

export const apiUrl = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/LMON/SearchResult`;

//Initial Sort Functions

export const initialSortColumn = (queryParam: any) => {
  if (typeof window !== "undefined") {
    const storedSortOptions = localStorage.getItem("sortOptions");
    if (queryParam.hasOwnProperty("keyword")) {
      if (storedSortOptions) {
        localStorage.removeItem("sortOptions");
      }

      return "";
    } else {
      if (storedSortOptions) {
        const parsedStoredSortOptions = JSON.parse(storedSortOptions);
        if (parsedStoredSortOptions?.sortColumn === "") {
          return "updated";
        } else {
          return parsedStoredSortOptions?.sortColumn;
        }
      } else {
        return "updated";
      }
    }
  }
};

export const initialSortDirection = (queryParam: any) => {
  if (typeof window !== "undefined") {
    const storedSortOptions = localStorage.getItem("sortOptions");
    if (queryParam.hasOwnProperty("keyword")) {
      if (storedSortOptions) {
        localStorage.removeItem("sortOptions");
      }
      return "";
    } else {
      if (storedSortOptions) {
        const parsedStoredSortOptions = JSON.parse(storedSortOptions);
        if (parsedStoredSortOptions?.sortDirection === "") {
          return "DESC";
        } else {
          return parsedStoredSortOptions?.sortDirection;
        }
      } else {
        return "DESC";
      }
    }
  }
};

export const UpdatedDateCapsuleFacet: any = {
  alltime: "All Time",
  "0": "Today",
  "7": "Latest 7 Days",
  "30": "Latest 30 Days",
  "90": "Latest 90 Days",
  "180": "Latest 180 Days",
  "365": "1 Year",
  "1825": "5 Years",
  custom: "Custom",
};

export const changeDateFormat = (inputDate: any) => {
  // expects Y-m-d
  if (inputDate !== "") {
    const splitDate = inputDate.split("-");
    if (splitDate.count == 0) {
      return null;
    }

    const year = splitDate[0];
    const month = splitDate[1];
    const day = splitDate[2];

    return month + "/" + day + "/" + year;
  }
  return "";
};

export const AdobeDataRefinementSection = (keyValue: any, labels: any) => {
  const refinementSection =
    keyValue === "it"
      ? labels["ItemType"]?.value
      : keyValue === "lob"
      ? labels["lob"]?.value
      : keyValue === "st"
      ? labels["Status"]?.value?.trim()
      : keyValue === "sty"
      ? labels["serviceType"]?.value
      : keyValue === "pr"
      ? labels["procedural"]?.value
      : keyValue === "pf"
      ? labels["policyform"]?.value
      : keyValue === "crdr"
      ? labels?.["createddate"]?.value
      : keyValue === "effdr"
      ? labels?.["effectiveDate"]?.value
      : "";
  return refinementSection;
};

export const getDayAfter = (date: any) => {
  const nextDay = new Date(date);
  nextDay.setDate(nextDay.getDate());

  return nextDay.toISOString().split("T")[0];
};
