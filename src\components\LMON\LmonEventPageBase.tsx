import React, { useContext, useEffect, useState } from "react";
import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { useRouter } from "next/router";
import { EventDetailRequest } from "./Event-Details/LmonEventUtil";
import { AuthContext } from "src/context/authContext";
import Loader from "components/common/Loader";

const LmonEventpageBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const isEEFlag = rendering?.isEEFlag;

  const apiUrl = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/Lmon/FetchEvent`;
  const [response, setresponse]: any = useState();
  const [errorMessage, setErrorMessage] = useState("");
  const [isError, setIsError] = useState(false);
  const [isspinner, setIsSpinner] = useState(false);

  const router = useRouter();
  const queryParam = router?.query;

  const eventcode: any = queryParam?.eventid || queryParam?.eventId;

  const context = useContext(AuthContext);
  const { accessToken } = context;

  const request = EventDetailRequest("", accessToken, queryParam);

  const [lastEventId, setLastEventId] = useState<string | null>(null);

  const data = async () => {
    try {
      setIsSpinner(true);
      const response = await fetch(apiUrl, request);
      const post = await response.json();
      setresponse(post);
      setIsSpinner(false);
      setLastEventId(eventcode);
      return post;
    } catch (error) {
      setIsError(true);
      setErrorMessage(
        "We are experiencing an issue loading the Content and are working to resolve it. Thank you for your patience."
      );
      setIsSpinner(false);
    }
  };

  useEffect(() => {
    // if eventID is not present, restrict the API call
    if (!!JSON.parse(request?.body)?.eventId) {
      data();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.asPath, eventcode]);

  return (
    <main className="legislative-monitoring detail">
      {route && <Placeholder name="jss-sub-header" rendering={route} />}
      {isspinner && <Loader />}
      <div className="site flex-wrapper">
        {!isspinner && route && (
          <div className="content-wrapper">
            <Placeholder
              name="jss-lmon-event"
              rendering={route}
              isEEFlag={isEEFlag}
              response={response}
              isError={isError}
              errorMessage={errorMessage}
              isspinner={isspinner}
            />
          </div>
        )}
        <aside className="thin">
          {response && !isspinner && route && lastEventId === eventcode && (
            <Placeholder
              name="right-section"
              rendering={route}
              path={rendering?.path}
            />
          )}
        </aside>
      </div>
    </main>
  );
};

export default withSitecoreContext()(LmonEventpageBase);
