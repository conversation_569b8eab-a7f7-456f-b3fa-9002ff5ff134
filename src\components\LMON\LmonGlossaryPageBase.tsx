import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const LmonGlossaryPageBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const { isEEFlag } = rendering;
  return (
    <>
      <main className="legislative-monitoring glossary">
        {route && <Placeholder name="jss-sub-header" rendering={route} />}
        <div className="site flex-wrapper">
          <div className="content-wrapper">
            {route && (
              <Placeholder
                name="jss-glossary-mainsection"
                rendering={route}
                isEEFlag={isEEFlag}
              />
            )}
            {route && (
              <Placeholder
                name="jss-disclaimer"
                rendering={route}
                isEEFlag={isEEFlag}
              />
            )}
          </div>
        </div>
      </main>
    </>
  );
};

export default withSitecoreContext()(LmonGlossaryPageBase);
