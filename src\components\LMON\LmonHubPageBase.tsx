import React from "react";
import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const LmonHubPageBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const isEEFlag = rendering?.isEEFlag;
  console.log("route lmon", route);

  return (
    <>
      <main className="legislative-monitoring hub">
        {route && <Placeholder name="jss-sub-header" rendering={route} />}
        <div className="site flex-wrapper">
          <section className="description content-wrapper">
            {route && (
              <Placeholder
                name="jss-lmon-hub"
                rendering={route}
                isEEFlag={isEEFlag}
              />
            )}
          </section>
          <aside className="thin">
            {route && (
              <Placeholder
                name="right-section"
                rendering={route}
                path={rendering?.path}
              />
            )}
          </aside>
        </div>
        <Placeholder
          name="jss-main-relatedinsight"
          rendering={route}
          EEFlag={isEEFlag}
        />
      </main>
    </>
  );
};

export default withSitecoreContext()(LmonHubPageBase);
