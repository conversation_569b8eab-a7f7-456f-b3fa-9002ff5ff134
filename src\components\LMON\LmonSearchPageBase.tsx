import React from "react";
import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const LmonSearchPageBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const isEEFlag = rendering?.isEEFlag;

  return (
    <>
      <main className="legislative-monitoring search">
        {route && <Placeholder name="jss-sub-header" rendering={route} />}
        <div className="site flex-wrapper">
          {route && (
            <Placeholder
              name="jss-lmon-search"
              rendering={route}
              EEFlag={isEEFlag}
            />
          )}
        </div>
      </main>
    </>
  );
};

export default withSitecoreContext()(LmonSearchPageBase);
