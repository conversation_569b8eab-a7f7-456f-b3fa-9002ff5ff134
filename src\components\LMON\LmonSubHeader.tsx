import {
  Field,
  withData<PERSON>urceCheck,
  RichText,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type LmonSubHeaderProps = ComponentProps & {
  fields: {
    Title: Field<string>;
  };
};

const LmonSubHeader = (props: LmonSubHeaderProps): JSX.Element => {
  return (
    <div className="sub-header">
      <RichText field={props?.fields?.Title} />
    </div>
  );
};

export default withDatasourceCheck()<LmonSubHeaderProps>(LmonSubHeader);
