import React, { useEffect, useState } from "react";
import {
  Text,
  RichText,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";

function RedirectBanner(props: any): JSX.Element {
  const [showBanner, setShowBanner] = useState(true);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const domElement = document.getElementsByClassName(
        "info-banner-content"
      )[0];
      domElement.querySelectorAll("*").forEach((node: any) => {
        node.removeAttribute("style");
      });
    }
  }, []);

  useEffect(() => {
    const isBannerClosed = localStorage.getItem("isBannerClosed");
    if (isBannerClosed === "true") {
      setShowBanner(false);
    }
  }, []);

  const hideRedirectBanner = () => {
    localStorage.setItem("isBannerClosed", "true");
    setShowBanner(false);
  };
  if (!showBanner) return <></>;
  return (
    <div className="info-banner flex-wrapper">
      <div className="info-banner-content">
        <Text
          className="banner-heading"
          field={props?.fields?.Title}
          tag="strong"
          role="heading"
        />
        <RichText
          // className="banner-body"
          field={props.fields?.Description}
          tag="span"
        />
      </div>
      <a className="close-icon" onClick={hideRedirectBanner}>
        <span className="material-icons">close</span>
      </a>
    </div>
  );
}

export default withDatasourceCheck()(RedirectBanner);
