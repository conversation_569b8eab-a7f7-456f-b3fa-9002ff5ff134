import {
  RichText,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type DisclaimerProps = ComponentProps & {
  fields: {
    DisclaimerDescription: any;
  };
};

const Disclaimer = (props: any): JSX.Element => {
  return (
    <>
      <small className="disclaimer">
        <RichText field={props?.fields?.DisclaimerDescription} />
      </small>
    </>
  );
};

export default withDatasourceCheck()<DisclaimerProps>(Disclaimer);
