import {
  Text,
  Field,
  RichText,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type AccessDeniedProps = ComponentProps & {
  fields: {
    AccessDeniedTitle: Field<string>;
    AccessDeniedDescription: any;
    AccessDeniedSupport: any;
  };
};

const AccessDenied = (props: any): JSX.Element => {
  return (
    <>
      <section>
        <div className="site">
          <h2 className="no-margin-top">
            <Text field={props?.fields?.AccessDeniedTitle} />
          </h2>
          <RichText field={props?.fields?.AccessDeniedDescription} />
          <div className="flex-wrapper">
            <RichText field={props?.fields?.AccessDeniedSupport} />
          </div>
        </div>
      </section>
    </>
  );
};

export default withDatasourceCheck()<AccessDeniedProps>(AccessDenied);
