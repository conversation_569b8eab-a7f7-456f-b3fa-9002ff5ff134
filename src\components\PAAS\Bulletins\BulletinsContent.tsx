import React from "react";
import { decodeHTMLEntities } from "components/PAAS/PaasUtilities/DecodeHtmlEntities";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";

const Content = (props: { contenData: string; toggle: string }) => {
  const { contenData, toggle } = props;

  const content = removeInlineCss(contenData?.replace(/(\&nbsp;)+/g, "&nbsp;"));

  return (
    <div
      className={
        toggle === "430a7d47-bfe2-4475-9184-836936a406aa"
          ? "tabNav tabContent active"
          : "tabNav tabContent"
      }
    >
      <p
        dangerouslySetInnerHTML={{
          __html: decodeHTMLEntities(content),
        }}
      ></p>
    </div>
  );
};

export default Content;
