export const bulletinsDataApi = async (payload: any, accessToken: string) => {
  const url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetBulletins`;
  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  }).then((res) => res.json());
  return response;
};
