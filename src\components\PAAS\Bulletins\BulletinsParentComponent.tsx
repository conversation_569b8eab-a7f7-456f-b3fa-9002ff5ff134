import { useRouter } from "next/router";
import EducationalBulletins from "./EducationalBulletins";
import BoardAndBureauBulletins from "./BoardAndBureauBulletins";
import LegislativeBulletins from "./LegislativeBulletins";
import { paasContentType } from "../PaasUtilities/CustomTypes";

const BulletinParentComponent = (props: paasContentType): JSX.Element => {
  const router = useRouter();
  const queryParameter = router.query;
  const { searchTerm, setPaasSearchClicked, setCurrentTableItem, restProps } =
    props;

  const renderBulletins = () => {
    if (queryParameter?.contentType?.includes("Educational Bulletin")) {
      return (
        <EducationalBulletins
          searchTerm={searchTerm}
          setPaasSearchClicked={setPaasSearchClicked}
          setCurrentTableItem={setCurrentTableItem}
          EducationalBulletinTabs={restProps?.EducationalBulletinTabs}
        />
      );
    } else if (
      queryParameter?.contentType?.includes("Board and Bureau Bulletin")
    ) {
      return (
        <BoardAndBureauBulletins
          searchTerm={searchTerm}
          setPaasSearchClicked={setPaasSearchClicked}
          setCurrentTableItem={setCurrentTableItem}
          BnBBulletinTabs={restProps?.BnBBulletinTabs}
        />
      );
    } else if (queryParameter?.contentType?.includes("Legislative Bulletin")) {
      return (
        <LegislativeBulletins
          searchTerm={searchTerm}
          setPaasSearchClicked={setPaasSearchClicked}
          setCurrentTableItem={setCurrentTableItem}
          LegislativeBulletinTabs={restProps?.LegislativeBulletinTabs}
        />
      );
    } else {
      return null;
    }
  };

  return <>{renderBulletins()}</>;
};

export default BulletinParentComponent;
