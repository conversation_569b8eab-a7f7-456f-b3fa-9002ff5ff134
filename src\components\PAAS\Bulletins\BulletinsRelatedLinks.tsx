import React from "react";
import { documentType } from "../PaasUtilities/CustomTypes";
import { convertToCodeArray } from "../PaasUtilities/ConvertToArray";
import ClassCodeProcessor from "../common/ClassCodeProcessor";
//test
const BulletinsRelatedLinks = (props: any) => {
  const { relatedLinks, toggle } = props;

  const onRelatedLinkClick = (relatedLink: documentType) => {
    let path = `/PAAS/search/?contentType=${relatedLink?.ContentType.slice(
      0,
      relatedLink?.ContentType?.length - 1
    )}&id=${relatedLink?.ItemID.replace(/[{()}]/g, "")}`;
    window.open(window.location.origin + path, "_blank");
  };

  return (
    <>
      {relatedLinks?.ContentType === "Educational Bulletin" ? (
        <>
          <div
            className={
              toggle === "50e12e02-58c0-4f81-8eb5-f886d9c5c7de"
                ? "tabNav tabContent active"
                : "tabNav tabContent"
            }
          >
            <div className="sub-tab-content">
              {relatedLinks?.WCClassificationLinks?.length > 0 && (
                <>
                  <h3>WC Classification Links</h3>
                  <ul className="link-list">
                    {relatedLinks?.WCClassificationLinks?.map(
                      (item: documentType, index: number) => {
                        return (
                          <li
                            key={index}
                            data-interaction="PaasTabRelatedLinks"
                            data-code={item?.Code}
                            data-title={item?.Title}
                            data-contentType={item?.ContentType.slice(
                              3,
                              item?.ContentType.length
                            )}
                            data-LOB={convertToCodeArray(item?.Lobs, ",")}
                            data-state={convertToCodeArray(
                              item?.Jurisdiction,
                              ","
                            )}
                          >
                            <a
                              data-testid="relatedlink-click"
                              onClick={() => onRelatedLinkClick(item)}
                            >
                              {item?.Jurisdiction?.length === 1
                                ? item?.Jurisdiction.map(
                                    (js: { Code: string; Name: string }) =>
                                      js.Code.includes("CC") ? "" : js.Code
                                  )
                                : ""}{" "}
                              {ClassCodeProcessor(
                                item?.Code,
                                item?.Jurisdiction?.length === 1
                                  ? item?.Jurisdiction.map(
                                      (js: { Code: string; Name: string }) =>
                                        js.Code
                                    )
                                  : "",
                                convertToCodeArray(item?.Lobs, ",")
                              )}{" "}
                              {item?.Title}
                            </a>
                          </li>
                        );
                      }
                    )}
                  </ul>
                </>
              )}
              {relatedLinks?.GLClassificationLinks?.length > 0 && (
                <>
                  <h3>GL Classification Links</h3>
                  <ul className="link-list">
                    {relatedLinks?.GLClassificationLinks?.map(
                      (item: documentType, index: number) => {
                        return (
                          <li
                            key={index}
                            data-interaction="PaasTabRelatedLinks"
                            data-code={item?.Code}
                            data-title={item?.Title}
                            data-contentType={item?.ContentType.slice(
                              3,
                              item?.ContentType.length
                            )}
                            data-LOB={convertToCodeArray(item?.Lobs, ",")}
                            data-state={convertToCodeArray(
                              item?.Jurisdiction,
                              ","
                            )}
                          >
                            <a
                              data-testid="related-link-click"
                              onClick={() => onRelatedLinkClick(item)}
                            >
                              {item?.Jurisdiction?.length === 1
                                ? item?.Jurisdiction.map(
                                    (js: { Code: string; Name: string }) =>
                                      js.Code.includes("CC") ? "" : js.Code
                                  )
                                : ""}{" "}
                              {ClassCodeProcessor(
                                item?.Code,
                                item?.Jurisdiction?.length === 1
                                  ? item?.Jurisdiction.map(
                                      (js: { Code: string; Name: string }) =>
                                        js.Code
                                    )
                                  : "",
                                convertToCodeArray(item?.Lobs, ",")
                              )}{" "}
                              {item?.Title}
                            </a>
                          </li>
                        );
                      }
                    )}
                  </ul>
                </>
              )}
            </div>
          </div>
        </>
      ) : (
        <div> Related Links </div>
      )}
    </>
  );
};

export default BulletinsRelatedLinks;
