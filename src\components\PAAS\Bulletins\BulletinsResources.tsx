import React from "react";
import { EDUCATIONAL_BULLETINS } from "components/PAAS/PaasLandingPageSearch/SearchConstants";
import { resourceType } from "../PaasUtilities/CustomTypes";
import { convertToCodeArray } from "../PaasUtilities/ConvertToArray";

const BulletinsResources = (props: any) => {
  const { resources, toggle } = props;

  const onResourceClick = (resource: resourceType) => {
    let path = `/PAAS/search/?contentType=${EDUCATIONAL_BULLETINS?.slice(
      0,
      EDUCATIONAL_BULLETINS?.length - 1
    )}&id=${resource?.ItemID}`;
    window.open(window.location.origin + path, "_blank");
  };

  return (
    <div
      className={
        toggle === "9e17d759-fcc9-4d99-bd72-ec5a98a5e083"
          ? "tabNav tabContent active"
          : "tabNav tabContent"
      }
    >
      {resources?.ReferenceBulletins?.length > 0 && (
        <>
          <p className="content-label">
            <strong>Educational Bulletins</strong>
          </p>
          <ul className="linkLists">
            {resources?.ReferenceBulletins?.map(
              (item: resourceType, index: number) => {
                return (
                  <li
                    key={index}
                    data-interaction="PaasTabResource"
                    data-code={item?.Code || ""}
                    data-title={item?.Name || ""}
                    data-contentType={item?.ContentType || ""}
                    data-LOB={convertToCodeArray(item?.Lobs, ",") || ""}
                    data-state={
                      convertToCodeArray(item?.Jurisdiction, ",") || ""
                    }
                  >
                    <a
                      data-testid="Resource-click"
                      onClick={() => onResourceClick(item)}
                    >
                      {item.Name}
                    </a>
                  </li>
                );
              }
            )}
          </ul>
        </>
      )}
    </div>
  );
};

export default BulletinsResources;
