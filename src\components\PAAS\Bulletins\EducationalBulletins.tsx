import React, { useContext, useEffect, useState } from "react";
import { useRouter } from "next/router";
import { bulletinsDataApi } from "./BulletinsDataService";
import Content from "./BulletinsContent";
import BulletinsRelatedLinks from "./BulletinsRelatedLinks";
import BulletinsResources from "./BulletinsResources";
import { renderBulletinsDetails } from "./BulletinsUtilities";
import { paasContentType, paasTabsType } from "../PaasUtilities/CustomTypes";
import { AuthContext } from "src/context/authContext";
import { convertToNameArray } from "../PaasUtilities/ConvertToArray";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
import useRedirectToBasePage from "./../../../hooks/paas/useRedirectToBasePage";
const EducationalBulletins = (props: paasContentType) => {
  const [educationalData, setEducationalData] = useState<any>([]);
  const router = useRouter();
  const queryParameter: any = router.query;
  const {
    searchTerm,
    setPaasSearchClicked,
    setCurrentTableItem,
    EducationalBulletinTabs,
  } = props;
  const Route = `/PAAS/search?keyword=${searchTerm}`;
  const [toggle, setToggle] = useState("430a7d47-bfe2-4475-9184-836936a406aa");
  const [isWC, setIsWC] = useState<any>(false);
  const { accessToken } = useContext(AuthContext);

  const returnToResults = () => {
    setPaasSearchClicked(true);
    setCurrentTableItem([]);
    router.push(Route);
  };

  const contentAnalytics = (response: any, pageCategory: any) => {
    if (typeof window !== "undefined" && window?.digitalData) {
      window.digitalData.page.pageInfo.page_category = pageCategory;
      const lobArray: string[] = [];
      response.Lobs?.map((lob: { Code: string; Name: string }) => {
        lobArray.push(lob.Code);
      });
      window.digitalData.page.pageInfo.page_LOB = lobArray.join(",");
      const jurisdictionArray: string[] = [];
      response.Jurisdiction?.map(
        (jurisdiction: { Code: string; Name: string }) => {
          jurisdictionArray.push(jurisdiction?.Code);
        }
      );
      window.digitalData.page.pageInfo.page_jurisdiction =
        jurisdictionArray.join(",");
      window.digitalData.product.PAAS["Content_type"] =
        response.ContentType || "";
      window.digitalData.product.PAAS["Content_number"] =
        response.ClassCode || "";
      window.digitalData.product.PAAS["Content_title"] = response.Title || "";
      const categoryArray: string[] = [];
      response.Category?.map((category: { Code: string; Name: string }) => {
        categoryArray.push(category?.Name);
      });
      window.digitalData.product.PAAS["Content_category"] =
        categoryArray.join(", ") || "";
    }
  };

  useEffect(() => {
    getEducationalBulletinsAPI();
    setToggle("430a7d47-bfe2-4475-9184-836936a406aa");
  }, [queryParameter]);

  const getEducationalBulletinsAPI = async () => {
    const payload = {
      ItemId: queryParameter?.id,
      ContentType: `${queryParameter?.contentType?.trimStart()}s`,
    };
    const post = await bulletinsDataApi(payload, accessToken);
    if (typeof window !== "undefined") contentAnalytics(post, "content");
    setEducationalData(post);
    if (post) {
      setIsWC(
        post?.Lobs?.filter((lob: any) => lob.Code === "WC")[0]?.Code === "WC"
      );
    }
  };

  useRedirectToBasePage(isWC, useGetSelectedCustomerNumber());

  const renderPage = () => {
    return (
      <section className="paas-board-bureau">
        {searchTerm ? (
          <div className="breadcrumbs">
            <nav>
              <a data-testid="return-click" onClick={returnToResults}>
                Search Results
              </a>
              <a href="#" className="current">
                Educational Bulletin
              </a>
            </nav>
          </div>
        ) : null}
        <div className="paas-board-bureau-topSection">
          <h2 className="paas-board-bureau-title">{educationalData.Title}</h2>
          <div className="paas-board-bureau-details">
            {renderBulletinsDetails(
              "Line Of Business: ",
              convertToNameArray(educationalData.Lobs, " and ")
            )}
            {renderBulletinsDetails(
              "Released on: ",
              `${educationalData.ReleaseMonth} ${educationalData.ReleaseYear}`
            )}
            {renderBulletinsDetails(
              "Bulletin Number: ",
              educationalData.BulletinNumber
            )}
          </div>
        </div>
        <div className="paas-board-bureau-content">
          <div className="paas-board-bureau-content-leftCol">
            <div className="tabs underline no-background tabNav">
              <div className="tabbed">
                <nav>
                  {EducationalBulletinTabs?.map(
                    (val: paasTabsType, id: number) => {
                      if (
                        (val.id === "430a7d47-bfe2-4475-9184-836936a406aa" &&
                          !educationalData.Notes?.length) ||
                        (val.id === "9e17d759-fcc9-4d99-bd72-ec5a98a5e083" &&
                          !educationalData?.ReferenceBulletins?.length) ||
                        (val.id === "50e12e02-58c0-4f81-8eb5-f886d9c5c7de" &&
                          !educationalData?.WCClassificationLinks?.length &&
                          !educationalData?.GLClassificationLinks?.length) ||
                        val.id === "d67c5009-a084-4ea6-93d9-e523bd664051"
                      ) {
                        return;
                      }
                      return (
                        <a
                          data-testid="eb-tab-label"
                          key={id}
                          className={toggle === val.id ? "tab active " : "tab"}
                          onClick={() => setToggle(val.id)}
                          data-interaction="PaasTab"
                          data-refinement-title={val?.fields?.Phrase?.value}
                          data-region="PAAS Content Tabs"
                        >
                          {val?.fields?.Phrase?.value}
                        </a>
                      );
                    }
                  )}
                </nav>
              </div>
            </div>
            <Content contenData={educationalData.Notes} toggle={toggle} />
            <BulletinsRelatedLinks
              relatedLinks={educationalData}
              toggle={toggle}
            />
            <BulletinsResources resources={educationalData} toggle={toggle} />
          </div>
        </div>
      </section>
    );
  };
  return <>{renderPage()}</>;
};

export default EducationalBulletins;
