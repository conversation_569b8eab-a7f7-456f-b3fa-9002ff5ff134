import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { bulletinsDataApi } from "./BulletinsDataService";
import Content from "./BulletinsContent";
import { renderBulletinsDetails } from "./BulletinsUtilities";
import { paasContentType, paasTabsType } from "../PaasUtilities/CustomTypes";
import { AuthContext } from "src/context/authContext";
import { convertToNameArray } from "../PaasUtilities/ConvertToArray";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
import useRedirectToBasePage from "./../../../hooks/paas/useRedirectToBasePage";
const LegislativeBulletins = (props: paasContentType): JSX.Element => {
  const [legislativeData, setLegislativeData] = useState<any>([]);
  const router = useRouter();
  const queryParameter: any = router.query;
  const {
    searchTerm,
    setPaasSearchClicked,
    setCurrentTableItem,
    LegislativeBulletinTabs,
  } = props;
  const Route = `/PAAS/search?keyword=${searchTerm}`;
  const [toggle, setToggle] = useState("430a7d47-bfe2-4475-9184-836936a406aa");
  const [isWC, setIsWC] = useState<any>(false);
  const { accessToken } = useContext(AuthContext);

  const returnToResults = () => {
    setPaasSearchClicked(true);
    setCurrentTableItem([]);
    router.push(Route);
  };

  const contentAnalytics = (response: any, pageCategory: any) => {
    if (typeof window !== "undefined" && window?.digitalData) {
      window.digitalData.page.pageInfo.page_category = pageCategory;
      const lobArray: string[] = [];
      response.Lobs?.map((lob: { Code: string; Name: string }) => {
        lobArray.push(lob.Code);
      });
      window.digitalData.page.pageInfo.page_LOB = lobArray.join(",");
      const jurisdictionArray: string[] = [];
      response.Jurisdiction?.map(
        (jurisdiction: { Code: string; Name: string }) => {
          jurisdictionArray.push(jurisdiction?.Code);
        }
      );
      window.digitalData.page.pageInfo.page_jurisdiction =
        jurisdictionArray.join(",");
      window.digitalData.product.PAAS["Content_type"] =
        response.ContentType || "";
      window.digitalData.product.PAAS["Content_number"] =
        response.ClassCode || "";
      window.digitalData.product.PAAS["Content_title"] = response.Title || "";
      const categoryArray: string[] = [];
      response.Category?.map((category: { Code: string; Name: string }) => {
        categoryArray.push(category?.Name);
      });
      window.digitalData.product.PAAS["Content_category"] =
        categoryArray.join(", ") || "";
    }
  };

  useEffect(() => {
    getLegislativeBulletinsAPI();
  }, [queryParameter]);

  const getLegislativeBulletinsAPI = async () => {
    const payload = {
      ItemId: queryParameter?.id,
      ContentType: `${queryParameter?.contentType?.trimStart()}s`,
    };
    const post = await bulletinsDataApi(payload, accessToken);
    if (typeof window !== "undefined") contentAnalytics(post, "content");
    setLegislativeData(post);
    if (post) {
      setIsWC(
        post?.Lobs?.filter((lob: any) => lob.Code === "WC")[0]?.Code === "WC"
      );
    }
  };

  useRedirectToBasePage(isWC, useGetSelectedCustomerNumber());

  return (
    <section className="paas-board-bureau">
      {searchTerm ? (
        <div className="breadcrumbs">
          <nav>
            <a data-testid="return-click" onClick={returnToResults}>
              Search Results
            </a>
            <a href="#" className="current">
              Legislative Bulletin
            </a>
          </nav>
        </div>
      ) : null}
      <div className="paas-board-bureau-topSection">
        <h2 className="paas-board-bureau-title">{legislativeData.Title}</h2>
        <div className="paas-board-bureau-details">
          {renderBulletinsDetails(
            "Line Of Business: ",
            convertToNameArray(legislativeData.Lobs, " and ")
          )}
          {renderBulletinsDetails("Bill Number: ", legislativeData.BillNumber)}
          {renderBulletinsDetails("Enacted: ", legislativeData.Enacted)}
          {renderBulletinsDetails(
            "Released on: ",
            `${legislativeData.ReleaseMonth} ${legislativeData.ReleaseYear}`
          )}
          {renderBulletinsDetails(
            "Bulletin Number: ",
            legislativeData.BulletinNumber
          )}
        </div>
      </div>
      <div className="paas-board-bureau-content">
        <div className="paas-board-bureau-content-leftCol">
          <div className="tabs underline no-background tabNav">
            <div className="tabbed">
              <nav>
                {LegislativeBulletinTabs?.map(
                  (val: paasTabsType, id: number) => {
                    if (
                      val.name === "Resources" ||
                      val.name === "Related Links" ||
                      val.id === "d67c5009-a084-4ea6-93d9-e523bd664051"
                    ) {
                      return;
                    }
                    return (
                      <a
                        data-testid="lb-tab-label"
                        key={id}
                        className={toggle === val.id ? "tab active " : "tab"}
                        onClick={() => setToggle(val.id)}
                        data-interaction="PaasTab"
                        data-refinement-title={val?.fields?.Phrase?.value}
                        data-region="PAAS Content Tabs"
                      >
                        {val?.fields?.Phrase?.value}
                      </a>
                    );
                  }
                )}
              </nav>
            </div>
          </div>
          <Content contenData={legislativeData.Notes} toggle={toggle} />
        </div>
      </div>
    </section>
  );
};

export default LegislativeBulletins;
