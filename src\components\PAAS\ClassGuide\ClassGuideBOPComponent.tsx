import History from "./History";
import React from "react";
import {
  classGuideDataType,
  documentType,
  paasTabsType,
} from "../PaasUtilities/CustomTypes";
import ClassCodeProcessor from "../common/ClassCodeProcessor";
import {
  convertToCodeArray,
  convertToNameArray,
} from "../PaasUtilities/ConvertToArray";
import ClassInformation from "./ClassInformation";
import Resources from "./ClassGuideResources";
import RelatedLinks from "./ClassGuideRelatedLinks";
import { SEARCH_REGEX } from "../PaasLandingPageSearch/SearchConstants";

type ClassGuidePropsType = {
  classGuideTabs?: object[];
  classGuideData: classGuideDataType;
  wcCheck: boolean;
  setToggle: (value: number) => void;
  toggle: number;
  restProps?: Record<string, any>;
  paasEntitlement: any;
  selectedCustomerNumber: string | number;
  onCrossReferenceClick: (code: documentType) => void;
};

function ClassGuideBOPComponent(props: ClassGuidePropsType) {
  const {
    classGuideTabs,
    classGuideData,
    wcCheck,
    setToggle,
    toggle,
    restProps,
    paasEntitlement,
    selectedCustomerNumber,
    onCrossReferenceClick,
  } = props;
  return (
    <>
      <div className="paas-class-guides-topSection">
        <h2 className="paas-class-guides-title">
          {ClassCodeProcessor(
            classGuideData?.ClassCode,
            convertToCodeArray(classGuideData?.Jurisdiction, ", "),
            convertToCodeArray(classGuideData?.Lobs, ", ")
          )}{" "}
          {classGuideData?.Phraseology?.replace(SEARCH_REGEX, "")}{" "}
        </h2>
        <div className="paas-class-guides-details">
          <div className="paas-class-guides-details-item">
            <span className="label">Line of business:</span>{" "}
            <span className="detail">
              {classGuideData?.Lobs?.map(
                (lob: { Code: string; Name: string }) => lob.Name
              )}
            </span>
          </div>
          <div className="paas-class-guides-details-item flex">
            <span className="label">Applicable in:</span>
            <span className="detail">
              {convertToCodeArray(classGuideData?.Jurisdiction, ", ")}
            </span>
          </div>
          {classGuideData?.Lobs?.map(
            (lob: { Code: string; Name: string }) => lob.Code === "BP"
          ) && (
            <div className="paas-class-guides-details-item">
              {classGuideData?.LiabilityExposureBase && (
                <>
                  <span className="label">Premium Basis:</span>{" "}
                  <span className="detail">
                    {classGuideData?.LiabilityExposureBase}
                  </span>
                </>
              )}
            </div>
          )}
          <div className="paas-class-guides-details-item">
            {classGuideData?.Category?.length > 0 && (
              <>
                <span className="label">Category:</span>{" "}
                <span className="detail">
                  {convertToNameArray(classGuideData.Category, " and ")}
                </span>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="paas-class-guides-content">
        <div className="paas-class-guides-content-leftCol">
          <div className="tabs underline no-background tabNav">
            <div className="tabbed">
              <nav>
                {classGuideTabs?.map((val: paasTabsType, id: number) => {
                  if (
                    (val.id === "3a501e30-cd93-4a1b-bfa5-9112d5bf06ee" &&
                      !classGuideData?.History?.replace(
                        SEARCH_REGEX,
                        ""
                      )?.replace(/(\&nbsp;)+/g, "")?.length) ||
                    (val.id === "5e89dcc6-97bf-487e-babe-03d5041c95b6" &&
                      !(classGuideData?.PaasDocuments?.length && !wcCheck)) ||
                    (val.id === "f282538d-644d-46a5-bd0f-1f17cbcffd20" &&
                      !classGuideData?.Notes?.length &&
                      !classGuideData?.ContemplatedOperations?.length &&
                      !classGuideData?.OperationsNotContemplated?.length &&
                      classGuideData?.Analogies?.length) ||
                    (val.id === "ce4b5f3a-e1f8-4d52-a825-95b06057b979" &&
                      !classGuideData?.RelatedGLCodes?.length &&
                      !classGuideData?.AdditionalPhraseologies?.length &&
                      !classGuideData?.GLStateException?.length &&
                      !classGuideData?.GlToSICMapping?.length &&
                      !classGuideData?.ExternalLinks?.length &&
                      !classGuideData?.RelatedWcCodes?.length &&
                      !classGuideData?.WCStateException?.length &&
                      !classGuideData?.WCToSICMapping?.length &&
                      !classGuideData?.PropertyRateNumber &&
                      !classGuideData?.LiabilityClassGroup &&
                      !classGuideData?.EarthquakeGrade &&
                      !classGuideData?.EslSusceptibilityGrade &&
                      !classGuideData?.LiabilityExposureBase)
                  ) {
                    return;
                  } else {
                    return (
                      <a
                        key={id}
                        tabIndex={id}
                        data-testid="cg-tab-label"
                        className={toggle === id ? "tab active " : "tab"}
                        onClick={() => setToggle(id)}
                        data-interaction="PaasTab"
                        data-refinement-title={val?.fields?.Phrase?.value}
                        data-region="PAAS Content Tabs"
                      >
                        {val?.fields?.Phrase?.value}
                      </a>
                    );
                  }
                })}
              </nav>
            </div>
          </div>
          <ClassInformation classInformation={classGuideData} toggle={toggle} />
          <Resources resources={classGuideData} toggle={toggle} />
          <RelatedLinks
            relatedLinks={classGuideData}
            toggle={toggle}
            NaicsHyperlink={restProps?.NaicsHyperlink}
            SicHyperlink={restProps?.SicHyperlink}
          />
          <History history={classGuideData} toggle={toggle} />
        </div>
        <div className="paas-class-guides-content-rightCol">
          <aside className="thin">
            {classGuideData?.WcCrossReference?.length > 0 &&
              ((classGuideData?.Lobs?.map(
                (lob: { Code: string; Name: string }) => lob?.Code
              )[0] === "GL" &&
                paasEntitlement
                  ?.find(
                    (customer: any) =>
                      customer?.customerNumber === selectedCustomerNumber
                  )
                  ?.customerPAASParticipation?.includes("LOB_WC")) ||
                classGuideData?.Lobs?.map(
                  (lob: { Code: string; Name: string }) => lob?.Code
                )[0] === "WC" ||
                classGuideData?.Lobs?.map(
                  (lob: { Code: string; Name: string }) => lob?.Code
                )[0] === "BP") && (
                <>
                  <section className="background-lt-grey">
                    <h2>Workers Compensation Cross Reference</h2>
                    <ul className="link-list">
                      {classGuideData?.WcCrossReference?.map(
                        (code: documentType, index: number) => {
                          return (
                            <li
                              key={index}
                              data-interaction="PaasRightRailComponent"
                              data-code={code?.ClassCode}
                              data-title={code?.Title}
                              data-contentType={code?.ClassGuideType.slice(
                                3,
                                code?.ClassGuideType.length
                              )}
                              data-LOB={convertToCodeArray(code?.Lob, ",")}
                              data-state={convertToCodeArray(
                                code?.Jurisdiction,
                                ","
                              )}
                            >
                              <a
                                data-testid="crossrefernce-click"
                                onClick={() => onCrossReferenceClick(code)}
                              >
                                {code?.Jurisdiction?.length === 1
                                  ? code?.Jurisdiction.map(
                                      (js: { Code: string; Name: string }) =>
                                        js.Code
                                    )
                                  : ""}{" "}
                                {ClassCodeProcessor(
                                  code?.ClassCode,
                                  convertToCodeArray(code?.Jurisdiction, ","),
                                  convertToCodeArray(code?.Lob, ",")
                                )}{" "}
                                {code?.Title}
                              </a>
                            </li>
                          );
                        }
                      )}
                    </ul>
                  </section>
                </>
              )}
            {classGuideData?.GlCrossReference?.length > 0 &&
              ((classGuideData?.Lobs?.map(
                (lob: { Code: string; Name: string }) => lob?.Code
              )[0] === "GL" &&
                paasEntitlement
                  ?.find(
                    (customer: any) =>
                      customer?.customerNumber === selectedCustomerNumber
                  )
                  ?.customerPAASParticipation?.includes("LOB_WC")) ||
                classGuideData?.Lobs?.map(
                  (lob: { Code: string; Name: string }) => lob?.Code
                )[0] === "WC" ||
                classGuideData?.Lobs?.map(
                  (lob: { Code: string; Name: string }) => lob?.Code
                )[0] === "BP") && (
                <>
                  <section className="background-lt-grey">
                    <h2>General Liability Cross Reference</h2>
                    <ul className="link-list">
                      {classGuideData?.GlCrossReference?.map(
                        (code: documentType, index: number) => {
                          return (
                            <li
                              key={index}
                              data-interaction="PaasRightRailComponent"
                              data-code={code?.ClassCode}
                              data-title={code?.Title}
                              data-contentType={code?.ClassGuideType.slice(
                                3,
                                code?.ClassGuideType.length
                              )}
                              data-LOB={convertToCodeArray(code?.Lob, ",")}
                              data-state={convertToCodeArray(
                                code?.Jurisdiction,
                                ","
                              )}
                            >
                              <a
                                data-testid="crossrefernce-click"
                                onClick={() => onCrossReferenceClick(code)}
                              >
                                {code?.Jurisdiction?.length === 1
                                  ? code?.Jurisdiction.map(
                                      (js: { Code: string; Name: string }) =>
                                        js.Code
                                    )
                                  : ""}{" "}
                                {ClassCodeProcessor(
                                  code?.ClassCode,
                                  convertToCodeArray(code?.Jurisdiction, ","),
                                  convertToCodeArray(code?.Lob, ",")
                                )}{" "}
                                {code?.Title}
                              </a>
                            </li>
                          );
                        }
                      )}
                    </ul>
                  </section>
                </>
              )}
          </aside>
        </div>
      </div>
    </>
  );
}

export default ClassGuideBOPComponent;
