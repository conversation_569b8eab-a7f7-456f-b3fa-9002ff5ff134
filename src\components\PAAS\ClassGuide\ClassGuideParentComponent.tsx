import { useEffect, useState, useContext } from "react";
import { useRouter } from "next/router";
import FAQ from "components/PAAS/Faqs/FaqParentComponent";
import Bulletin from "components/PAAS/Bulletins/BulletinsParentComponent";
import ClassInformation from "./ClassInformation";
import RelatedLinks from "./ClassGuideRelatedLinks";
import Resources from "./ClassGuideResources";
import History from "./History";
import JurisdictionalInformation from "components/PAAS/JurisdictionInformation/JurisdictionalInformation";
import { classGuideDataApi } from "./ClassGuideDataService";
import { AuthContext } from "src/context/authContext";
import {
  classGuideDataType,
  paasTabsType,
  documentType,
  paasContentType,
  resourceType,
} from "../PaasUtilities/CustomTypes";
import {
  convertToCodeArray,
  convertToNameArray,
} from "../PaasUtilities/ConvertToArray";
import ClassCodeProcessor from "components/PAAS/common/ClassCodeProcessor";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
import useRedirectToBasePage from "./../../../hooks/paas/useRedirectToBasePage";
import { SEARCH_REGEX } from "../PaasLandingPageSearch/SearchConstants";
import ClassGuideBOPComponent from "./ClassGuideBOPComponent";

const ClassGuideParentComponent = (props: paasContentType) => {
  const {
    searchTerm,
    currentTableItem,
    setCurrentTableItem,
    setPaasSearchClicked,
    classGuideTabs,
    faqTabs,
    restProps,
  } = props;
  const router = useRouter();
  const queryParameter = router.query;
  const [toggle, setToggle] = useState(0);
  const [classGuideData, setClassGuideData] = useState<classGuideDataType>(
    {} as classGuideDataType
  );
  const { accessToken } = useContext(AuthContext);
  const { paasEntitlement } = useContext(AuthContext);

  let selectedCustomerNumber = useGetSelectedCustomerNumber();
  const [isWCORBOP, setIsWCORBOP] = useState<any>(false);

  const wcCheck =
    classGuideData?.PaasDocuments?.filter((resource: resourceType) => {
      return (
        resource?.Lobs?.filter((lob) => lob.Code === "GL").length > 0 &&
        resource?.Lobs?.filter((lob) => lob.Code === "WC").length === 0
      );
    }).length === 0 &&
    !paasEntitlement
      ?.find(
        (customer: any) => customer?.customerNumber === selectedCustomerNumber
      )
      ?.customerPAASParticipation?.includes("LOB_WC");
  const breadcrumbs = [
    {
      Name: "Search Results",
      Route: `/PAAS/search?keyword=${
        searchTerm || sessionStorage.getItem("searchTerm")
      }`,
      Current: false,
    },
    {
      Name: "Class guide",
      Route: "#",
      Current: true,
    },
  ];

  useEffect(() => {
    getClassGuideAPI();
    setToggle(0);
  }, [queryParameter]);

  useRedirectToBasePage(isWCORBOP, selectedCustomerNumber);

  const contentAnalytics = (response: any, pageCategory: any) => {
    if (typeof window !== "undefined" && window?.digitalData) {
      window.digitalData.page.pageInfo.page_category = pageCategory;
      const lobArray: string[] = [];
      response.Lobs?.map((lob: { Code: string; Name: string }) => {
        lobArray.push(lob.Code);
      });
      window.digitalData.page.pageInfo.page_LOB = lobArray.join(",");
      const jurisdictionArray: string[] = [];
      response.Jurisdiction?.map(
        (jurisdiction: { Code: string; Name: string }) => {
          jurisdictionArray.push(jurisdiction?.Code);
        }
      );
      window.digitalData.page.pageInfo.page_jurisdiction =
        jurisdictionArray.join(",");
      window.digitalData.product.PAAS["Content_type"] =
        response.ContentType || "";
      window.digitalData.product.PAAS["Content_number"] =
        response.ClassCode || "";
      window.digitalData.product.PAAS["Content_title"] =
        response.Phraseology || "";
      const categoryArray: string[] = [];
      response.Category?.map((category: { Code: string; Name: string }) => {
        categoryArray.push(category?.Name);
      });
      window.digitalData.product.PAAS["Content_category"] =
        categoryArray.join(", ") || "";
    }
  };

  const getClassGuideAPI = async () => {
    let isMounted = true;
    const payload = {
      ItemId: queryParameter?.id,
      ContentSubType: `${queryParameter?.contentType}s`,
    };
    try {
      let url: string = "";
      if (queryParameter?.contentType?.includes("Class Guide")) {
        url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetClassGuide`;
      }
      const post = await classGuideDataApi(url, payload, accessToken);
      if (typeof window !== "undefined") contentAnalytics(post, "content");
      if (isMounted) {
        setClassGuideData(post);
        if (post) {
          setIsWCORBOP(
            post?.Lobs?.map(
              (lob: { Code: string; Name: string }) => lob.Code
            )[0] === "WC" ||
              post?.Lobs?.map(
                (lob: { Code: string; Name: string }) => lob.Code
              )[0] === "BP"
          );
        }
      }
    } catch (error) {
      console.error("Error in Class guide API :", error);
    } finally {
      isMounted = false;
    }
  };

  const breadcrumbsItemClick = (route: string) => {
    setPaasSearchClicked(true);
    setCurrentTableItem([]);
    router.push(route);
  };

  const onCrossReferenceClick = (crossReference: documentType) => {
    let path = `/PAAS/search/?contentType=${crossReference?.ClassGuideType.slice(
      0,
      crossReference?.ClassGuideType?.length - 1
    )}&id=${crossReference?.ItemId.replace(/[{()}]/g, "")}`;
    window.open(window.location.origin + path, "_blank");
  };

  useRedirectToBasePage(isWCORBOP, selectedCustomerNumber);

  const renderBreadcrumbs = () => (
    <div className="breadcrumbs">
      <nav>
        {breadcrumbs.map((nav: any, index: number) => (
          <a
            data-testid="breadcrumbs-clicks"
            className={nav.Current ? "current" : ""}
            onClick={() => breadcrumbsItemClick(nav.Route)}
            key={index}
          >
            {nav.Name}
          </a>
        ))}
      </nav>
    </div>
  );

  const renderClassGuideDetails = () => (
    <div className="paas-class-guides-details">
      <div className="paas-class-guides-details-item">
        <span className="label">Line of business:</span>{" "}
        <span className="detail">
          {classGuideData?.Lobs?.map(
            (lob: { Code: string; Name: string }) => lob.Name
          )}
        </span>
      </div>
      <div className="paas-class-guides-details-item flex">
        <span className="label">Applicable in:</span>
        <span className="detail">
          {convertToCodeArray(classGuideData?.Jurisdiction, ", ")}
        </span>
      </div>
      {classGuideData?.PremiumBasis?.length > 0 && (
        <div className="paas-class-guides-details-item">
          <span className="label">Premium Basis:</span>{" "}
          <span className="detail">{classGuideData?.PremiumBasis}</span>
        </div>
      )}
      {classGuideData?.Category?.length > 0 && (
        <div className="paas-class-guides-details-item">
          <span className="label">Category:</span>{" "}
          <span className="detail">
            {convertToNameArray(classGuideData.Category, " and ")}
          </span>
        </div>
      )}
    </div>
  );

  const renderTabs = () => (
    <div className="tabs underline no-background tabNav">
      <div className="tabbed">
        <nav>
          {classGuideTabs?.map((val: paasTabsType, id: number) => {
            if (
              (val.id === "3a501e30-cd93-4a1b-bfa5-9112d5bf06ee" &&
                !classGuideData?.History?.replace(SEARCH_REGEX, "")?.replace(
                  /(\&nbsp;)+/g,
                  ""
                )?.length) ||
              (val.id === "5e89dcc6-97bf-487e-babe-03d5041c95b6" &&
                !(classGuideData?.PaasDocuments?.length && !wcCheck)) ||
              (val.id === "f282538d-644d-46a5-bd0f-1f17cbcffd20" &&
                !classGuideData?.Notes?.length &&
                !classGuideData?.ContemplatedOperations?.length &&
                !classGuideData?.OperationsNotContemplated?.length &&
                classGuideData?.Analogies?.length) ||
              (val.id === "ce4b5f3a-e1f8-4d52-a825-95b06057b979" &&
                !classGuideData?.RelatedGLCodes?.length &&
                !classGuideData?.AdditionalPhraseologies?.length &&
                !classGuideData?.GLStateException?.length &&
                !classGuideData?.GlToSICMapping?.length &&
                !classGuideData?.ExternalLinks?.length &&
                !classGuideData?.RelatedWcCodes?.length &&
                !classGuideData?.WCStateException?.length &&
                !classGuideData?.WCToSICMapping?.length)
            ) {
              return null;
            } else {
              return (
                <a
                  key={id}
                  tabIndex={id}
                  data-testid="cg-tab-label"
                  className={toggle === id ? "tab active " : "tab"}
                  onClick={() => setToggle(id)}
                  data-interaction="PaasTab"
                  data-refinement-title={val?.fields?.Phrase?.value}
                  data-region="PAAS Content Tabs"
                >
                  {val?.fields?.Phrase?.value}
                </a>
              );
            }
          })}
        </nav>
      </div>
    </div>
  );

  const renderCrossReference = () => (
    <aside className="thin">
      {classGuideData?.WcCrossReference?.length > 0 &&
        ((classGuideData?.Lobs?.map(
          (lob: { Code: string; Name: string }) => lob?.Code
        )[0] === "GL" &&
          paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_WC")) ||
          classGuideData?.Lobs?.map(
            (lob: { Code: string; Name: string }) => lob?.Code
          )[0] === "WC") && (
          <section className="background-lt-grey">
            <h2>Workers Compensation Cross Reference</h2>
            <ul className="link-list">
              {classGuideData?.WcCrossReference?.map(
                (code: documentType, index: number) => (
                  <li
                    key={index}
                    data-interaction="PaasRightRailComponent"
                    data-code={code?.ClassCode}
                    data-title={code?.Title}
                    data-contentType={code?.ClassGuideType.slice(
                      3,
                      code?.ClassGuideType.length
                    )}
                    data-LOB={convertToCodeArray(code?.Lob, ",")}
                    data-state={convertToCodeArray(code?.Jurisdiction, ",")}
                  >
                    <a
                      data-testid="crossrefernce-click"
                      onClick={() => onCrossReferenceClick(code)}
                    >
                      {code?.Jurisdiction?.length === 1
                        ? code?.Jurisdiction.map(
                            (js: { Code: string; Name: string }) => js.Code
                          )
                        : ""}{" "}
                      {ClassCodeProcessor(
                        code?.ClassCode,
                        convertToCodeArray(code?.Jurisdiction, ","),
                        convertToCodeArray(code?.Lob, ",")
                      )}{" "}
                      {code?.Title}
                    </a>
                  </li>
                )
              )}
            </ul>
          </section>
        )}
      {classGuideData?.GlCrossReference?.length > 0 &&
        ((classGuideData?.Lobs?.map(
          (lob: { Code: string; Name: string }) => lob?.Code
        )[0] === "GL" &&
          paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_WC")) ||
          classGuideData?.Lobs?.map(
            (lob: { Code: string; Name: string }) => lob?.Code
          )[0] === "WC") && (
          <section className="background-lt-grey">
            <h2>General Liability Cross Reference</h2>
            <ul className="link-list">
              {classGuideData?.GlCrossReference?.map(
                (code: documentType, index: number) => (
                  <li
                    key={index}
                    data-interaction="PaasRightRailComponent"
                    data-code={code?.ClassCode}
                    data-title={code?.Title}
                    data-contentType={code?.ClassGuideType.slice(
                      3,
                      code?.ClassGuideType.length
                    )}
                    data-LOB={convertToCodeArray(code?.Lob, ",")}
                    data-state={convertToCodeArray(code?.Jurisdiction, ",")}
                  >
                    <a
                      data-testid="crossrefernce-click"
                      onClick={() => onCrossReferenceClick(code)}
                    >
                      {code?.Jurisdiction?.length === 1
                        ? code?.Jurisdiction.map(
                            (js: { Code: string; Name: string }) => js.Code
                          )
                        : ""}{" "}
                      {ClassCodeProcessor(
                        code?.ClassCode,
                        convertToCodeArray(code?.Jurisdiction, ","),
                        convertToCodeArray(code?.Lob, ",")
                      )}{" "}
                      {code?.Title}
                    </a>
                  </li>
                )
              )}
            </ul>
          </section>
        )}
      {classGuideData?.BpCrossReference?.length > 0 &&
        ((classGuideData?.Lobs?.map(
          (lob: { Code: string; Name: string }) => lob?.Code
        )[0] === "GL" &&
          paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_WC")) ||
          classGuideData?.Lobs?.map(
            (lob: { Code: string; Name: string }) => lob?.Code
          )[0] === "WC") && (
          <section className="background-lt-grey">
            <h2>Businessowners Cross Reference</h2>
            <ul className="link-list">
              {classGuideData?.BpCrossReference?.map(
                (code: documentType, index: number) => (
                  <li
                    key={index}
                    data-interaction="PaasRightRailComponent"
                    data-code={code?.ClassCode}
                    data-title={code?.Title}
                    data-contentType={code?.ClassGuideType.slice(
                      3,
                      code?.ClassGuideType.length
                    )}
                    data-LOB={convertToCodeArray(code?.Lob, ",")}
                    data-state={convertToCodeArray(code?.Jurisdiction, ",")}
                  >
                    <a
                      data-testid="crossrefernce-click"
                      onClick={() => onCrossReferenceClick(code)}
                    >
                      {code?.Jurisdiction?.length === 1
                        ? code?.Jurisdiction.map(
                            (js: { Code: string; Name: string }) => js.Code
                          )
                        : ""}{" "}
                      {ClassCodeProcessor(
                        code?.ClassCode,
                        convertToCodeArray(code?.Jurisdiction, ","),
                        convertToCodeArray(code?.Lob, ",")
                      )}{" "}
                      {code?.Title}
                    </a>
                  </li>
                )
              )}
            </ul>
          </section>
        )}{" "}
    </aside>
  );
  const renderPage = () => {
    {
      (searchTerm || sessionStorage.getItem("searchTerm")) &&
        !queryParameter.contentType?.includes("Class Guide") &&
        renderBreadcrumbs();
    }

    if (queryParameter.contentType?.includes("Class Guide")) {
      return (
        <div className="paas-class-guides">
          {(searchTerm || sessionStorage.getItem("searchTerm")) &&
            renderBreadcrumbs()}

          {queryParameter.contentType?.includes("BP") ? (
            <ClassGuideBOPComponent
              classGuideTabs={classGuideTabs}
              classGuideData={classGuideData}
              wcCheck={wcCheck}
              setToggle={setToggle}
              toggle={toggle}
              restProps={restProps}
              selectedCustomerNumber={selectedCustomerNumber}
              paasEntitlement={paasEntitlement}
              onCrossReferenceClick={onCrossReferenceClick}
            />
          ) : (
            <>
              <div className="paas-class-guides-topSection">
                <h2 className="paas-class-guides-title">
                  {ClassCodeProcessor(
                    classGuideData?.ClassCode,
                    convertToCodeArray(classGuideData?.Jurisdiction, ", "),
                    convertToCodeArray(classGuideData?.Lobs, ", ")
                  )}{" "}
                  {classGuideData?.Phraseology?.replace(SEARCH_REGEX, "")}{" "}
                </h2>
                {renderClassGuideDetails()}
              </div>
              <div className="paas-class-guides-content">
                <div className="paas-class-guides-content-leftCol">
                  {renderTabs()}
                  <ClassInformation
                    classInformation={classGuideData}
                    toggle={toggle}
                  />
                  <Resources resources={classGuideData} toggle={toggle} />
                  <RelatedLinks
                    relatedLinks={classGuideData}
                    toggle={toggle}
                    NaicsHyperlink={restProps?.NaicsHyperlink}
                    SicHyperlink={restProps?.SicHyperlink}
                  />
                  <History history={classGuideData} toggle={toggle} />
                </div>
                <div className="paas-class-guides-content-rightCol">
                  {renderCrossReference()}
                </div>
              </div>
            </>
          )}
        </div>
      );
    } else if (queryParameter.contentType?.includes("FAQ")) {
      return (
        <FAQ
          searchTerm={
            searchTerm || (sessionStorage.getItem("searchTerm") as string)
          }
          setPaasSearchClicked={setPaasSearchClicked}
          setCurrentTableItem={setCurrentTableItem}
          faqTabs={faqTabs}
        />
      );
    } else if (queryParameter.contentType?.includes("Bulletin")) {
      return (
        <Bulletin
          searchTerm={
            searchTerm || (sessionStorage.getItem("searchTerm") as string)
          }
          setPaasSearchClicked={setPaasSearchClicked}
          setCurrentTableItem={setCurrentTableItem}
          restProps={restProps}
        />
      );
    } else if (
      queryParameter.contentType?.includes("Jurisdiction Information")
    ) {
      return (
        <JurisdictionalInformation
          currentTableItem={currentTableItem}
          setCurrentTableItem={setCurrentTableItem}
          searchVal={
            searchTerm || (sessionStorage.getItem("searchTerm") as string)
          }
          setPaasSearchClicked={setPaasSearchClicked}
        />
      );
    }
    return null;
  };

  return <>{renderPage()}</>;
};

export default ClassGuideParentComponent;
