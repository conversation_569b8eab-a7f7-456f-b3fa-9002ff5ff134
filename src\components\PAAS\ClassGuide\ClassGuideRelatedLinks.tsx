import React from "react";
import { Field } from "@sitecore-jss/sitecore-jss-nextjs";
import { classGuideDataType, documentType } from "../PaasUtilities/CustomTypes";
import { convertToCodeArray } from "../PaasUtilities/ConvertToArray";
import ClassCodeProcessor from "../common/ClassCodeProcessor";

type relatedLinksType = {
  NaicsHyperlink?: Field<string>;
  SicHyperlink?: Field<string>;
  relatedLinks: classGuideDataType;
  toggle: number;
};

const RelatedLinks = (props: relatedLinksType) => {
  const { relatedLinks, toggle, NaicsHyperlink, SicHyperlink } = props;

  const onRelatedLinkClick = (relatedLink: documentType) => {
    let path = `/PAAS/search/?contentType=${relatedLink?.ClassGuideType?.slice(
      0,
      relatedLink?.ClassGuideType?.length - 1
    )}&id=${relatedLink?.ItemId.replace(/[{()}]/g, "")}`;
    window.open(window.location.origin + path, "_blank");
  };

  const renderGlLinks = () => {
    return (
      <>
        <div
          className={
            toggle === 2 ? "tabNav tabContent active" : "tabNav tabContent"
          }
        >
          {relatedLinks?.RelatedGLCodes?.length > 0 && (
            <>
              <p className="content-label">
                <strong>Related Class Code(s)</strong>
              </p>
              <ul className="linkLists">
                {relatedLinks?.RelatedGLCodes?.map(
                  (item: documentType, index: number) => {
                    return (
                      <li
                        key={index}
                        data-interaction="PaasTabRelatedLinks"
                        data-code={item?.ClassCode}
                        data-title={item?.Title}
                        data-contentType={item?.ClassGuideType?.slice(
                          3,
                          item?.ClassGuideType?.length
                        )}
                        data-LOB={convertToCodeArray(item?.Lob, ",")}
                        data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                      >
                        <a onClick={() => onRelatedLinkClick(item)}>
                          {item?.Jurisdiction?.length === 1
                            ? item?.Jurisdiction.map(
                                (js: { Code: string; Name: string }) => js.Code
                              )
                            : ""}{" "}
                          {ClassCodeProcessor(
                            item?.ClassCode,
                            item?.Jurisdiction?.length === 1
                              ? item?.Jurisdiction.map(
                                  (js: { Code: string; Name: string }) =>
                                    js.Code
                                )
                              : "",
                            convertToCodeArray(item?.Lob, ",")
                          )}{" "}
                          {item?.Title}
                        </a>
                      </li>
                    );
                  }
                )}
              </ul>
            </>
          )}
          {relatedLinks?.AdditionalPhraseologies?.length > 0 && (
            <>
              <p className="content-label">
                <strong>Additional Phraseologies</strong>
              </p>
              <ul className="linkLists">
                {relatedLinks?.AdditionalPhraseologies?.map(
                  (item: documentType, index: number) => {
                    return (
                      <li
                        key={index}
                        data-interaction="PaasTabRelatedLinks"
                        data-code={item?.ClassCode}
                        data-title={item?.Title}
                        data-contentType={item?.ClassGuideType?.slice(
                          3,
                          item?.ClassGuideType?.length
                        )}
                        data-LOB={convertToCodeArray(item?.Lob, ",")}
                        data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                      >
                        <a href="#">{item.Title}</a>
                      </li>
                    );
                  }
                )}
              </ul>
            </>
          )}
          {relatedLinks?.GLStateException?.length > 0 && (
            <>
              <p className="content-label">
                <strong>State Exceptions</strong>
              </p>
              <ul className="linkLists">
                {relatedLinks?.GLStateException?.map(
                  (item: documentType, index: number) => {
                    return (
                      <li
                        key={index}
                        data-interaction="PaasTabRelatedLinks"
                        data-code={item?.ClassCode}
                        data-title={item?.Title}
                        data-contentType={item?.ClassGuideType?.slice(
                          3,
                          item?.ClassGuideType?.length
                        )}
                        data-LOB={convertToCodeArray(item?.Lob, ",")}
                        data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                      >
                        <a onClick={() => onRelatedLinkClick(item)}>
                          {item?.Jurisdiction?.length === 1
                            ? item?.Jurisdiction.map(
                                (js: { Code: string; Name: string }) => js.Code
                              )
                            : ""}{" "}
                          {ClassCodeProcessor(
                            item?.ClassCode,
                            item?.Jurisdiction?.length === 1
                              ? item?.Jurisdiction.map(
                                  (js: { Code: string; Name: string }) =>
                                    js.Code
                                )
                              : "",
                            convertToCodeArray(item?.Lob, ",")
                          )}{" "}
                          {item.Title}
                        </a>
                      </li>
                    );
                  }
                )}
              </ul>
            </>
          )}
          {relatedLinks?.GlToSICMapping?.length > 0 && (
            <>
              <p className="content-label">
                <strong>Industry Mappings</strong>
              </p>
              <ul className="linkLists">
                <div className="industry-mapping">
                  <p className="no-margin-top no-margin-bottom">
                    <span>SIC Dictionary:</span>
                    <div>
                      {relatedLinks?.GlToSICMapping?.map(
                        (item: any, index: number) => {
                          return (
                            <div key={index}>
                              <a href={SicHyperlink?.value} target="_blank">
                                {item?.SicCode} {item?.SicDescription}
                                <sup className="material-symbols-outlined">
                                  open_in_new
                                </sup>
                              </a>
                            </div>
                          );
                        }
                      )}
                    </div>
                  </p>
                  <p className="no-margin-top no-margin-bottom">
                    <span>NAICS Dictionary:</span>
                    <div>
                      {relatedLinks?.GlToSICMapping?.map(
                        (item: any, index: number) => {
                          return (
                            <div key={index}>
                              <a href={NaicsHyperlink?.value} target="_blank">
                                {item?.NaicsCode} {item?.NaicsDescription}
                                <sup className="material-symbols-outlined">
                                  open_in_new
                                </sup>
                              </a>
                            </div>
                          );
                        }
                      )}
                    </div>
                  </p>
                </div>
                <div className="industry-mapping">
                  {relatedLinks?.RiskSchedule?.length > 0 && (
                    <p className="no-margin-top no-margin-bottom">
                      <span>Risk Schedule:</span>
                      <span>{relatedLinks?.RiskSchedule}</span>
                    </p>
                  )}
                  {relatedLinks?.IndustrialGroup?.length > 0 && (
                    <p className="no-margin-top no-margin-bottom">
                      <span>Industrial Group:</span>
                      <span>{relatedLinks?.IndustrialGroup}</span>
                    </p>
                  )}
                </div>
              </ul>
            </>
          )}
          {relatedLinks?.ExternalLinks?.length > 0 && (
            <>
              <p className="content-label">
                <strong>External Links</strong>
              </p>
              <ul className="linkLists">
                {relatedLinks?.ExternalLinks?.map(
                  (item: documentType, index: number) => {
                    return (
                      <li key={index}>
                        <a href="#">{item.Title}</a>
                      </li>
                    );
                  }
                )}
              </ul>
            </>
          )}
        </div>
      </>
    );
  };

  const renderWcLinks = () => {
    return (
      <div
        className={
          toggle === 2 ? "tabNav tabContent active" : "tabNav tabContent"
        }
      >
        {relatedLinks?.RelatedWcCodes?.length > 0 && (
          <>
            <p className="content-label">
              <strong>Related Class Code(s)</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.RelatedWcCodes?.map(
                (item: documentType, index: number) => {
                  return (
                    <li
                      key={index}
                      data-interaction="PaasTabRelatedLinks"
                      data-code={item?.ClassCode}
                      data-title={item?.Title}
                      data-contentType={item?.ClassGuideType?.slice(
                        3,
                        item?.ClassGuideType?.length
                      )}
                      data-LOB={convertToCodeArray(item?.Lob, ",")}
                      data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                    >
                      <a onClick={() => onRelatedLinkClick(item)}>
                        {item?.Jurisdiction?.length === 1
                          ? item?.Jurisdiction.map(
                              (js: { Code: string; Name: string }) => js.Code
                            )
                          : ""}{" "}
                        {ClassCodeProcessor(
                          item?.ClassCode,
                          item?.Jurisdiction?.length === 1
                            ? item?.Jurisdiction.map(
                                (js: { Code: string; Name: string }) => js.Code
                              )
                            : "",
                          convertToCodeArray(item?.Lob, ",")
                        )}{" "}
                        {item.Title}
                      </a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
        {relatedLinks?.AdditionalPhraseologies?.length > 0 && (
          <>
            <p className="content-label">
              <strong>Additional Phraseologies</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.AdditionalPhraseologies?.map(
                (item: documentType, index: number) => {
                  return (
                    <li
                      key={index}
                      data-interaction="PaasTabRelatedLinks"
                      data-code={item?.ClassCode}
                      data-title={item?.Title}
                      data-contentType={item?.ClassGuideType?.slice(
                        3,
                        item?.ClassGuideType?.length
                      )}
                      data-LOB={convertToCodeArray(item?.Lob, ",")}
                      data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                    >
                      <a href="#">{item.Title}</a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
        {relatedLinks?.WCStateException?.length > 0 && (
          <>
            <p className="content-label">
              <strong>State Exceptions</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.WCStateException?.map(
                (item: documentType, index: number) => {
                  return (
                    <li
                      key={index}
                      data-interaction="PaasTabRelatedLinks"
                      data-code={item?.ClassCode}
                      data-title={item?.Title}
                      data-contentType={item?.ClassGuideType?.slice(
                        3,
                        item?.ClassGuideType?.length
                      )}
                      data-LOB={convertToCodeArray(item?.Lob, ",")}
                      data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                    >
                      <a onClick={() => onRelatedLinkClick(item)}>
                        {item?.Jurisdiction?.length === 1
                          ? item?.Jurisdiction.map(
                              (js: { Code: string; Name: string }) => js.Code
                            )
                          : ""}{" "}
                        {ClassCodeProcessor(
                          item?.ClassCode,
                          item?.Jurisdiction?.length === 1
                            ? item?.Jurisdiction.map(
                                (js: { Code: string; Name: string }) => js.Code
                              )
                            : "",
                          convertToCodeArray(item?.Lob, ",")
                        )}{" "}
                        {item.Title}
                      </a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
        {relatedLinks?.WCToSICMapping?.length > 0 && (
          <>
            <p className="content-label">
              <strong>Industry Mappings</strong>
            </p>
            <ul className="linkLists">
              <div className="industry-mapping">
                <p className="no-margin-top no-margin-bottom">
                  <span>SIC Dictionary:</span>
                  <div>
                    {relatedLinks?.WCToSICMapping?.map(
                      (item: any, index: number) => {
                        return (
                          <div key={index}>
                            <a href={SicHyperlink?.value} target="_blank">
                              {item?.SicCode} {item?.SicDescription}
                              <sup className="material-symbols-outlined">
                                open_in_new
                              </sup>
                            </a>
                          </div>
                        );
                      }
                    )}
                  </div>
                </p>
                <p className="no-margin-top no-margin-bottom">
                  <span>NAICS Dictionary:</span>
                  <div>
                    {relatedLinks?.WCToSICMapping?.map(
                      (item: any, index: number) => {
                        return (
                          <div key={index}>
                            <a href={NaicsHyperlink?.value} target="_blank">
                              {item?.NaicsCode} {item?.NaicsDescription}
                              <sup className="material-symbols-outlined">
                                open_in_new
                              </sup>
                            </a>
                          </div>
                        );
                      }
                    )}
                  </div>
                </p>
              </div>
              <div className="industry-mapping">
                {relatedLinks?.RiskSchedule?.length > 0 && (
                  <p className="no-margin-top no-margin-bottom">
                    <span>Risk Schedule:</span>
                    <span>{relatedLinks?.RiskSchedule}</span>
                  </p>
                )}
                {relatedLinks?.IndustrialGroup?.length > 0 && (
                  <p className="no-margin-top no-margin-bottom">
                    <span>Industrial Group:</span>
                    <span>{relatedLinks?.IndustrialGroup}</span>
                  </p>
                )}
              </div>
            </ul>
          </>
        )}
        {relatedLinks?.ExternalLinks?.length > 0 && (
          <>
            <p className="content-label">
              <strong>External Links</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.ExternalLinks?.map(
                (item: documentType, index: number) => {
                  return (
                    <li key={index}>
                      <a href="#">{item.Title}</a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
      </div>
    );
  };

  const renderBPLinks = () => {
    return (
      <div
        className={
          toggle === 2 ? "tabNav tabContent active" : "tabNav tabContent"
        }
      >
        {relatedLinks?.BPRelated?.length > 0 && (
          <>
            <p className="content-label">
              <strong>Related Class Code(s)</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.BPRelated?.map(
                (item: documentType, index: number) => {
                  return (
                    <li
                      key={index}
                      data-interaction="PaasTabRelatedLinks"
                      data-code={item?.ClassCode}
                      data-title={item?.Title}
                      data-contentType={item?.ClassGuideType?.slice(
                        3,
                        item?.ClassGuideType?.length
                      )}
                      data-LOB={convertToCodeArray(item?.Lob, ",")}
                      data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                    >
                      <a onClick={() => onRelatedLinkClick(item)}>
                        {item?.Jurisdiction?.length === 1
                          ? item?.Jurisdiction.map(
                              (js: { Code: string; Name: string }) => js.Code
                            )
                          : ""}{" "}
                        {ClassCodeProcessor(
                          item?.ClassCode,
                          item?.Jurisdiction?.length === 1
                            ? item?.Jurisdiction.map(
                                (js: { Code: string; Name: string }) => js.Code
                              )
                            : "",
                          convertToCodeArray(item?.Lob, ",")
                        )}{" "}
                        {item.Title}
                      </a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}

        <>
          <p className="content-label">
            <strong>Rating Information</strong>
          </p>
          <ul className="linkLists">
            <div className="industry-mapping BOPRating">
              {relatedLinks?.PropertyRateNumber && (
                <p className="no-margin-top no-margin-bottom">
                  <span>Property Rate Number:</span>
                  <div>
                    <span>{relatedLinks?.PropertyRateNumber}</span>
                  </div>
                </p>
              )}
              {relatedLinks?.LiabilityClassGroup && (
                <p className="no-margin-top no-margin-bottom">
                  <span>Liability Class Group:</span>
                  <div>
                    <span>{relatedLinks?.LiabilityClassGroup}</span>
                  </div>
                </p>
              )}
              {relatedLinks?.EarthquakeGrade && (
                <p className="no-margin-top no-margin-bottom">
                  <span>Earthquake Rate Grade (EQ):</span>
                  <div>
                    <span>{relatedLinks?.EarthquakeGrade}</span>
                  </div>
                </p>
              )}
              {relatedLinks?.EslSusceptibilityGrade && (
                <p className="no-margin-top no-margin-bottom">
                  <span>
                    Earthquake Sprinkler Leakage Susceptibility (EQSL):
                  </span>
                  <div>
                    <span>{relatedLinks?.EslSusceptibilityGrade}</span>
                  </div>
                </p>
              )}
            </div>
          </ul>
        </>
        {relatedLinks?.AdditionalPhraseologies?.length > 0 && (
          <>
            <p className="content-label">
              <strong>Additional Phraseologies</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.AdditionalPhraseologies?.map(
                (item: documentType, index: number) => {
                  return (
                    <li
                      key={index}
                      data-interaction="PaasTabRelatedLinks"
                      data-code={item?.ClassCode}
                      data-title={item?.Title}
                      data-contentType={item?.ClassGuideType?.slice(
                        3,
                        item?.ClassGuideType?.length
                      )}
                      data-LOB={convertToCodeArray(item?.Lob, ",")}
                      data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                    >
                      <a href="#">{item.Title}</a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
        {relatedLinks?.StateException?.length > 0 && (
          <>
            <p className="content-label">
              <strong>State Exceptions</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.StateException?.map(
                (item: documentType, index: number) => {
                  return (
                    <li
                      key={index}
                      data-interaction="PaasTabRelatedLinks"
                      data-code={item?.ClassCode}
                      data-title={item?.Title}
                      data-contentType={item?.ClassGuideType?.slice(
                        3,
                        item?.ClassGuideType?.length
                      )}
                      data-LOB={convertToCodeArray(item?.Lob, ",")}
                      data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                    >
                      <a onClick={() => onRelatedLinkClick(item)}>
                        {item?.Jurisdiction?.length === 1
                          ? item?.Jurisdiction.map(
                              (js: { Code: string; Name: string }) => js.Code
                            )
                          : ""}{" "}
                        {ClassCodeProcessor(
                          item?.ClassCode,
                          item?.Jurisdiction?.length === 1
                            ? item?.Jurisdiction.map(
                                (js: { Code: string; Name: string }) => js.Code
                              )
                            : "",
                          convertToCodeArray(item?.Lob, ",")
                        )}{" "}
                        {item.Title}
                      </a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
        {relatedLinks?.BPToSICMapping?.length > 0 && (
          <>
            <p className="content-label">
              <strong>Industry Mappings</strong>
            </p>
            <ul className="linkLists">
              <div className="industry-mapping">
                <p className="no-margin-top no-margin-bottom">
                  <span>SIC Dictionary:</span>
                  <div>
                    {relatedLinks?.BPToSICMapping?.map(
                      (item: any, index: number) => {
                        return (
                          <div key={index}>
                            <a href={SicHyperlink?.value} target="_blank">
                              {item?.SicCode} {item?.SicDescription}
                              <sup className="material-symbols-outlined">
                                open_in_new
                              </sup>
                            </a>
                          </div>
                        );
                      }
                    )}
                  </div>
                </p>
                <p className="no-margin-top no-margin-bottom">
                  <span>NAICS Dictionary:</span>
                  <div>
                    {relatedLinks?.BPToSICMapping?.map(
                      (item: any, index: number) => {
                        return (
                          <div key={index}>
                            <a href={NaicsHyperlink?.value} target="_blank">
                              {item?.NaicsCode} {item?.NaicsDescription}
                              <sup className="material-symbols-outlined">
                                open_in_new
                              </sup>
                            </a>
                          </div>
                        );
                      }
                    )}
                  </div>
                </p>
              </div>
              <div className="industry-mapping">
                {relatedLinks?.RiskSchedule?.length > 0 && (
                  <p className="no-margin-top no-margin-bottom">
                    <span>Risk Schedule:</span>
                    <span>{relatedLinks?.RiskSchedule}</span>
                  </p>
                )}
                {relatedLinks?.IndustrialGroup?.length > 0 && (
                  <p className="no-margin-top no-margin-bottom">
                    <span>Industrial Group:</span>
                    <span>{relatedLinks?.IndustrialGroup}</span>
                  </p>
                )}
              </div>
            </ul>
          </>
        )}
        {relatedLinks?.ExternalLinks?.length > 0 && (
          <>
            <p className="content-label">
              <strong>External Links</strong>
            </p>
            <ul className="linkLists">
              {relatedLinks?.ExternalLinks?.map(
                (item: documentType, index: number) => {
                  return (
                    <li key={index}>
                      <a href="#">{item.Title}</a>
                    </li>
                  );
                }
              )}
            </ul>
          </>
        )}
      </div>
    );
  };

  const getRenderedLinks = () => {
    const contentSubType = relatedLinks?.ContentSubType;
    switch (contentSubType?.slice(0, 2)) {
      case "BP":
        return renderBPLinks();
      case "GL":
        return renderGlLinks();
      default:
        return renderWcLinks();
    }
  };

  return <>{getRenderedLinks()}</>;
};

export default RelatedLinks;
