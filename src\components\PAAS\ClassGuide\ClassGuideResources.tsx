import {
  EDUCATIONAL_BULLETINS,
  FAQS,
  INDUSTRY_GUIDES,
  TRAINING_MANUALS,
} from "components/PAAS/PaasLandingPageSearch/SearchConstants";
import { useContext } from "react";
import { AuthContext } from "src/context/authContext";
import { classGuideDataType, resourceType } from "../PaasUtilities/CustomTypes";
import { convertToCodeArray } from "../PaasUtilities/ConvertToArray";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
const Resources = (props: {
  resources: classGuideDataType;
  toggle: number;
}) => {
  const { resources, toggle } = props;
  const FAQs: resourceType[] = [];
  const EducationalBulletins: resourceType[] = [];
  const TrainingManuals: resourceType[] = [];
  const IndustryGuides: resourceType[] = [];
  let selectedCustomerNumber = useGetSelectedCustomerNumber();
  const { paasEntitlement } = useContext(AuthContext);
  resources?.PaasDocuments?.map((resource: resourceType) => {
    if (
      !(
        resource?.Lobs?.filter((lob) => lob.Code === "WC").length > 0 &&
        !paasEntitlement
          ?.find(
            (customer: any) =>
              customer?.customerNumber === selectedCustomerNumber
          )
          ?.customerPAASParticipation?.includes("LOB_WC")
      )
    ) {
      if (resource?.DocumentType === FAQS) {
        FAQs.push(resource);
      } else if (resource?.DocumentType === EDUCATIONAL_BULLETINS) {
        EducationalBulletins.push(resource);
      } else if (resource?.DocumentType === TRAINING_MANUALS) {
        TrainingManuals.push(resource);
      } else if (resource?.DocumentType === INDUSTRY_GUIDES) {
        IndustryGuides.push(resource);
      }
    }
  });

  const onResourceClick = (resource: resourceType) => {
    let path = `/PAAS/search/?contentType=${resource?.DocumentType.slice(
      0,
      resource?.DocumentType?.length - 1
    )}&id=${resource?.ItemId}`;
    window.open(window.location.origin + path, "_blank");
  };

  return (
    <div
      className={
        toggle === 1 ? "tabNav tabContent active" : "tabNav tabContent"
      }
    >
      {FAQs?.length > 0 && (
        <>
          <p className="content-label">
            <strong>FAQs</strong>
          </p>
          <ul className="linkLists">
            {FAQs?.map((item: resourceType, index: number) => {
              return (
                <li
                  key={index}
                  data-interaction="PaasTabResource"
                  data-code={item?.Code || ""}
                  data-title={item?.Title || ""}
                  data-contentType={item?.DocumentType || ""}
                  data-LOB={convertToCodeArray(item?.Lobs, ",") || ""}
                  data-state={""}
                >
                  <a onClick={() => onResourceClick(item)}>{item.Title}</a>
                </li>
              );
            })}
          </ul>
        </>
      )}
      {EducationalBulletins?.length > 0 && (
        <>
          <p className="content-label">
            <strong>Educational Bulletins</strong>
          </p>
          <ul className="linkLists">
            {EducationalBulletins.map((item: resourceType, index: number) => {
              return (
                <li
                  key={index}
                  data-interaction="PaasTabResource"
                  data-code={item?.Code || ""}
                  data-title={item?.Title || ""}
                  data-contentType={item?.DocumentType || ""}
                  data-LOB={convertToCodeArray(item?.Lobs, ",") || ""}
                  data-state={""}
                >
                  <a onClick={() => onResourceClick(item)}>{item.Title}</a>
                </li>
              );
            })}
          </ul>
        </>
      )}
      {TrainingManuals?.length > 0 && (
        <>
          <p className="content-label">
            <strong>Trainings</strong>
          </p>
          <ul className="linkLists">
            {TrainingManuals?.map((item: resourceType, index: number) => {
              return (
                <li
                  key={index}
                  data-interaction="PaasTabResource"
                  data-code={item?.Code || ""}
                  data-title={item?.Title || ""}
                  data-contentType={item?.DocumentType || ""}
                  data-LOB={convertToCodeArray(item?.Lobs, ",") || ""}
                  data-state={""}
                >
                  <a href="#">{item.Title}</a>
                </li>
              );
            })}
          </ul>
        </>
      )}
      {IndustryGuides?.length > 0 && (
        <>
          <p className="content-label">
            <strong>Industry Guides</strong>
          </p>
          <ul className="linkLists">
            {IndustryGuides?.map((item: resourceType, index: number) => {
              return (
                <li
                  key={index}
                  data-interaction="PaasTabResource"
                  data-code={item?.Code || ""}
                  data-title={item?.Title || ""}
                  data-contentType={item?.DocumentType || ""}
                  data-LOB={convertToCodeArray(item?.Lobs, ",") || ""}
                  data-state={""}
                >
                  <a href="#">{item.Title}</a>
                </li>
              );
            })}
          </ul>
        </>
      )}
    </div>
  );
};

export default Resources;
