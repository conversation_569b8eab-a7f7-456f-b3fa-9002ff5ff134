import { decodeHTMLEntities } from "components/PAAS/PaasUtilities/DecodeHtmlEntities";
import { classGuideDataType } from "../PaasUtilities/CustomTypes";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";

const ClassInformation = (props: {
  classInformation: classGuideDataType;
  toggle: number;
}) => {
  const { classInformation, toggle } = props;

  const Notes = removeInlineCss(
    classInformation?.Notes?.replace(/(\&nbsp;)+/g, "&nbsp;").replace(
      /\\n/g,
      ""
    )
  );
  const contemplatedOperations = removeInlineCss(
    classInformation?.ContemplatedOperations?.replace(/(\&nbsp;)+/g, "&nbsp;")
  );
  const operationsNotContemplated =
    classInformation?.OperationsNotContemplated?.replace(
      /(\&nbsp;)+/g,
      "&nbsp;"
    );
  const analogies = removeInlineCss(
    classInformation?.Analogies?.replace(/(\&nbsp;)+/g, "&nbsp;")
  );

  return (
    <>
      {classInformation?.ContentSubType?.includes("BP") ? (
        <div
          className={
            toggle === 0 ? "tabNav tabContent active" : "tabNav tabContent"
          }
        >
          {classInformation?.Notes?.length ? (
            <>
              <p className="content-label">
                <strong>Note</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{ __html: decodeHTMLEntities(Notes) }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.GroupNote.length > 0 ? (
            <>
              <p className="content-label">
                <strong>
                  {classInformation?.GroupNote[0]?.GroupNoteTitle}
                </strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(
                    classInformation?.GroupNote[0]?.GroupNoteContent
                  ),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.ContemplatedOperations?.length ? (
            <>
              <p className="content-label">
                <strong>Contemplated Operations</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(contemplatedOperations),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.OperationsNotContemplated?.length ? (
            <>
              <p className="content-label">
                <strong>Operations Not Contemplated</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(operationsNotContemplated),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.Analogies?.length ? (
            <>
              <p className="content-label">
                <strong>Analogies</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(analogies),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
        </div>
      ) : (
        <div
          className={
            toggle === 0 ? "tabNav tabContent active" : "tabNav tabContent"
          }
        >
          {classInformation?.Notes?.length ? (
            <>
              <p className="content-label">
                <strong>Note</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{ __html: decodeHTMLEntities(Notes) }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.ContemplatedOperations?.length ? (
            <>
              <p className="content-label">
                <strong>Contemplated Operations</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(contemplatedOperations),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.OperationsNotContemplated?.length ? (
            <>
              <p className="content-label">
                <strong>Operations Not Contemplated</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(operationsNotContemplated),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
          {classInformation?.Analogies?.length ? (
            <>
              <p className="content-label">
                <strong>Analogies</strong>
              </p>
              <p
                dangerouslySetInnerHTML={{
                  __html: decodeHTMLEntities(analogies),
                }}
              ></p>
            </>
          ) : (
            <>{""}</>
          )}
        </div>
      )}
    </>
  );
};

export default ClassInformation;
