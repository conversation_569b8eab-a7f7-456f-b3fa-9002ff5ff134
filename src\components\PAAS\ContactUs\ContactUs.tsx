import {
  withDatasourceCheck,
  Text,
  RichText,
  Field,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

//test - just dummy change to trigger a new deployment
type contactUsProps = ComponentProps & {
  fields: {
    LeftHeader: Field<string>;
    LeftEditor: Field<string>;
    RightHeader: Field<string>;
    RightEditor: Field<string>;
  };
};

const ContactUs = (props: contactUsProps): JSX.Element => {
  return (
    <div className="contact-us-wrapper">
      <div className="left-content">
        <Text field={props?.fields?.LeftHeader} tag="h2" />
        <RichText field={props.fields?.LeftEditor} />
      </div>
      <div className="right-content">
        <Text field={props?.fields?.RightHeader} tag="h2" />
        <RichText field={props.fields?.RightEditor} />
      </div>
    </div>
  );
};

export default withDatasourceCheck()<contactUsProps>(ContactUs);
