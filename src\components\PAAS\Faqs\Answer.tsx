import { decodeHTMLEntities } from "components/PAAS/PaasUtilities/DecodeHtmlEntities";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";

const Answer = (props: { answer: string; toggle: number }) => {
  const { answer, toggle } = props;

  const faqAnswer = removeInlineCss(answer?.replace(/(\&nbsp;)+/g, "&nbsp;"));

  return (
    <div
      className={
        toggle === 0 ? "tabNav tabContent active" : "tabNav tabContent"
      }
    >
      {answer?.length > 0 && (
        <>
          <p
            dangerouslySetInnerHTML={{
              __html: decodeHTMLEntities(faqAnswer),
            }}
          ></p>
        </>
      )}
    </div>
  );
};

export default Answer;
