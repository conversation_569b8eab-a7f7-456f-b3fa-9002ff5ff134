import { useContext, useEffect, useState } from "react";
import Answer from "./Answer";
import RelatedLinks from "./FaqRelatedLinks";
import { useRouter } from "next/router";
import { faqDataApi } from "./FaqDataService";
import { decodeHTMLEntities } from "components/PAAS/PaasUtilities/DecodeHtmlEntities";
import FaqResources from "./FaqResources";
import { paasContentType, paasTabsType } from "../PaasUtilities/CustomTypes";
import { AuthContext } from "src/context/authContext";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";
import { convertToNameArray } from "../PaasUtilities/ConvertToArray";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
import useRedirectToBasePage from "./../../../hooks/paas/useRedirectToBasePage";
const FaqParentComponent = (props: paasContentType): JSX.Element => {
  const [toggle, setToggle] = useState(0);
  const { searchTerm, setPaasSearchClicked, setCurrentTableItem, faqTabs } =
    props;
  const [faqData, setFaqData] = useState<any>([]);
  const router = useRouter();
  const queryParameter = router.query;
  const Route = `/PAAS/search?keyword=${searchTerm}`;
  const { accessToken } = useContext(AuthContext);
  const [isWC, setIsWC] = useState<any>(false);

  const returnToResults = () => {
    setPaasSearchClicked(true);
    setCurrentTableItem([]);
    router.push(Route);
  };

  useEffect(() => {
    getFaqAPI();
  }, [queryParameter]);

  const contentAnalytics = (response: any, pageCategory: any) => {
    if (typeof window !== "undefined" && window?.digitalData) {
      window.digitalData.page.pageInfo.page_category = pageCategory;
      const lobArray: string[] = [];
      response.Lobs?.map((lob: { Code: string; Name: string }) => {
        lobArray.push(lob.Code);
      });
      window.digitalData.page.pageInfo.page_LOB = lobArray.join(",");
      const jurisdictionArray: string[] = [];
      response.Jurisdiction?.map(
        (jurisdiction: { Code: string; Name: string }) => {
          jurisdictionArray.push(jurisdiction?.Code);
        }
      );
      window.digitalData.page.pageInfo.page_jurisdiction =
        jurisdictionArray.join(",");
      window.digitalData.product.PAAS["Content_type"] =
        response.ContentType || "";
      window.digitalData.product.PAAS["Content_number"] =
        response.ClassCode || "";
      window.digitalData.product.PAAS["Content_title"] = response.Title || "";
      const categoryArray: string[] = [];
      response.Category?.map((category: { Code: string; Name: string }) => {
        categoryArray.push(category?.Name);
      });
      window.digitalData.product.PAAS["Content_category"] =
        categoryArray.join(", ") || "";
    }
  };

  const getFaqAPI = async () => {
    let isMounted = true;
    const payload = {
      ItemId: queryParameter?.id,
      ContentType: `${queryParameter?.contentType}s`,
    };
    try {
      let url: string = "";
      if (queryParameter?.contentType?.includes("FAQ")) {
        url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetFAQs`;
      }
      const post = await faqDataApi(url, payload, accessToken);
      if (typeof window !== "undefined") contentAnalytics(post, "content");
      if (isMounted) {
        setFaqData(post);
        if (post) {
          setIsWC(
            post?.Lobs?.filter((lob: any) => lob.Code === "WC")[0]?.Code ===
              "WC"
          );
        }
      }
    } catch (error) {
      console.error("faq api not working:", error);
    } finally {
      isMounted = false;
    }
  };

  const renderFAQDetails = (label: string, detail: string) => {
    return (
      <div className="paas-class-guides-details-item">
        <span className="label">{label}</span>
        <span className="detail">{detail}</span>
      </div>
    );
  };
  useRedirectToBasePage(isWC, useGetSelectedCustomerNumber());

  return (
    <section className="paas-class-guides">
      {searchTerm ? (
        <div className="breadcrumbs">
          <nav>
            <a data-testid="results-button" onClick={returnToResults}>
              Search Results
            </a>
            <a href="#" className="current">
              FAQ
            </a>
          </nav>
        </div>
      ) : null}
      <div className="paas-class-guides-topSection">
        <h2 className="paas-class-guides-title">{faqData.Title}</h2>
        <div className="paas-class-guides-details">
          {renderFAQDetails(
            "Line of business:",
            convertToNameArray(faqData?.Lobs, " and ")
          )}
         {renderFAQDetails(
            "Released on:",
            `${faqData?.ReleaseMonth ? faqData?.ReleaseMonth : ""} ${
              faqData?.ReleaseYear ? faqData?.ReleaseYear : ""
            }`
          )}
        </div>
      </div>
      <div className="paas-class-guides-content paas-faq-wrapper">
        <div className="paas-class-guides-content-leftCol">
          <div>
            <b>Question</b>
            <h3
              className="question"
              dangerouslySetInnerHTML={{
                __html: decodeHTMLEntities(
                  removeInlineCss(faqData?.Question ? faqData?.Question : "")
                ),
              }}
            ></h3>
          </div>
          <div className="tabs underline no-background tabNav responsive-tabs subNav">
            <div className="tabbed flex-wrapper">
              <div className="nav-wrapper">
                <nav>
                  {faqTabs?.map((val: paasTabsType, id: any) => {
                    if (
                      (val.id === "eb74cced-4060-441f-a101-93c654a924f8" &&
                        !faqData?.Answer?.length) ||
                      (val.id === "6bc2f7fe-2fe6-45d2-873f-1f7f9a68c516" &&
                        !faqData?.WCClassificationLink?.length &&
                        !faqData?.GLClassificationLink?.length) ||
                      (val.id === "65a7c196-8c5f-4f4c-a70e-664ffd9098a6" &&
                        !faqData?.Resources?.length)
                    ) {
                      return;
                    } else {
                      return (
                        <a
                          key={id}
                          tabIndex={id}
                          data-testid="tab-label"
                          className={toggle === id ? "tab active " : "tab"}
                          onClick={() => setToggle(id)}
                          data-interaction="PaasTab"
                          data-refinement-title={val?.fields?.Phrase?.value}
                          data-region="PAAS Content Tabs"
                        >
                          {val?.fields?.Phrase?.value}
                        </a>
                      );
                    }
                  })}
                </nav>
              </div>
            </div>
          </div>
          <Answer answer={faqData?.Answer} toggle={toggle} />
          <RelatedLinks
            wcClassificationLinks={faqData?.WCClassificationLink}
            glClassificationLinks={faqData?.GLClassificationLink}
            toggle={toggle}
          />
          <FaqResources resources={faqData?.Resources} toggle={toggle} />
        </div>
      </div>
    </section>
  );
};

export default FaqParentComponent;
