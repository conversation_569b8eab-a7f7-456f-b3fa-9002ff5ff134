import React from "react";
import { documentType } from "../PaasUtilities/CustomTypes";
import { convertToCodeArray } from "../PaasUtilities/ConvertToArray";
import ClassCodeProcessor from "../common/ClassCodeProcessor";

const RelatedLinks = (props: any) => {
  const { wcClassificationLinks, glClassificationLinks, toggle } = props;

  const onRelatedLinkClick = (relatedLink: documentType) => {
    let path = `/PAAS/search/?contentType=${relatedLink?.ContentType.slice(
      0,
      relatedLink?.ContentType?.length - 1
    )}&id=${relatedLink?.ItemID.replace(/[{()}]/g, "")}`;
    window.open(window.location.origin + path, "_blank");
  };

  return (
    <div
      className={
        toggle === 1 ? "tabNav tabContent active" : "tabNav tabContent"
      }
    >
      {wcClassificationLinks?.length > 0 && (
        <>
          <h3>WC Classification Links</h3>
          <ul className="link-list">
            {wcClassificationLinks?.map((item: documentType, index: number) => {
              return (
                <li
                  key={index}
                  data-interaction="PaasTabRelatedLinks"
                  data-code={item?.Code}
                  data-title={item?.Title}
                  data-contentType={item?.ContentType.slice(
                    3,
                    item?.ContentType.length
                  )}
                  data-LOB={convertToCodeArray(item?.Lobs, ",")}
                  data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                >
                  <a
                    data-testid="wc-link"
                    onClick={() => onRelatedLinkClick(item)}
                  >
                    {item?.Jurisdiction?.length === 1
                      ? item?.Jurisdiction.map(
                          (js: { Code: string; Name: string }) =>
                            js.Code.includes("CC") ? "" : js.Code
                        )
                      : ""}{" "}
                    {ClassCodeProcessor(
                      item?.Code,
                      item?.Jurisdiction?.length === 1
                        ? item?.Jurisdiction.map(
                            (js: { Code: string; Name: string }) =>
                              js.Code.includes("CC") ? "" : js.Code
                          )
                        : "",
                      convertToCodeArray(item?.Lobs, ",")
                    )}{" "}
                    {item?.Title}
                  </a>
                </li>
              );
            })}
          </ul>
        </>
      )}
      {glClassificationLinks?.length > 0 && (
        <>
          <h3>GL Classification Links</h3>
          <ul className="link-list">
            {glClassificationLinks?.map((item: documentType, index: number) => {
              return (
                <li
                  key={index}
                  data-interaction="PaasTabRelatedLinks"
                  data-code={item?.Code}
                  data-title={item?.Title}
                  data-contentType={item?.ContentType.slice(
                    3,
                    item?.ContentType.length
                  )}
                  data-LOB={convertToCodeArray(item?.Lobs, ",")}
                  data-state={convertToCodeArray(item?.Jurisdiction, ",")}
                >
                  <a
                    data-testid="gl-link"
                    onClick={() => onRelatedLinkClick(item)}
                  >
                    {item?.Jurisdiction?.length === 1
                      ? item?.Jurisdiction.map(
                          (js: { Code: string; Name: string }) =>
                            js.Code.includes("CC") ? "" : js.Code
                        )
                      : ""}{" "}
                    {ClassCodeProcessor(
                      item?.Code,
                      item?.Jurisdiction?.length === 1
                        ? item?.Jurisdiction.map(
                            (js: { Code: string; Name: string }) =>
                              js.Code.includes("CC") ? "" : js.Code
                          )
                        : "",
                      convertToCodeArray(item?.Lobs, ",")
                    )}{" "}
                    {item?.Title}
                  </a>
                </li>
              );
            })}
          </ul>
        </>
      )}
    </div>
  );
};

export default RelatedLinks;
