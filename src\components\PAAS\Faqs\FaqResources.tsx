import { decodeHTMLEntities } from "components/PAAS/PaasUtilities/DecodeHtmlEntities";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";

const FaqResources = (props: any) => {
  const { resources, toggle } = props;

  const faqResources = removeInlineCss(
    resources?.replace(/(\&nbsp;)+/g, "&nbsp;")
  );

  return (
    <div
      className={
        toggle === 2 ? "tabNav tabContent active" : "tabNav tabContent"
      }
    >
      {resources?.length > 0 && (
        <>
          <p
            dangerouslySetInnerHTML={{
              __html: decodeHTMLEntities(faqResources),
            }}
          ></p>
        </>
      )}
    </div>
  );
};

export default FaqResources;

//test
