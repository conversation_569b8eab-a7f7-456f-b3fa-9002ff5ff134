import { useContext, useEffect, useRef, useState } from "react";
import { useOktaAuth } from "src/context/oktaAuth";
import {
  checkAgreementVersion,
  getUserConversations,
  updateAgreementVersion,
} from "./PaasAiService";
import {
  Conversation,
  ConversationList,
  ModalType,
} from "./interfaces/interfaces";
import PaasAiTabs from "./PaasAiTabs";
import PaasAiMessages from "./PaasAiMessages";
import { AuthContext } from "src/context/authContext";
import { isEqual } from "lodash";

const PaasAi = (props: any): JSX.Element => {
  const [conversationList, setConversationList] = useState<ConversationList>(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [currentConversation, setCurrentConversation] = useState<Conversation>(
    {} as Conversation
  );
  const { nextSessionUserEmail, nextSessionUserName } = useOktaAuth();
  const userId = useRef(nextSessionUserEmail?.split("@")[0]);
  const username = useRef(nextSessionUserName);
  const [isTermsOfUseAccepted, setIsTermsOfUseAccepted] = useState(false);
  const [isUserDataLoading, setIsUserDataLoading] = useState(true);
  const { accessToken } = useContext(AuthContext);
  useEffect(() => {
    if (!isTermsOfUseAccepted) verifyTermsOfAgreement();
  }, [isTermsOfUseAccepted]);

  // If currentConversation changes => update conversationList
  useEffect(() => {
    if (!isTermsOfUseAccepted || isUserDataLoading) return;
    updateConversationList(currentConversation);
  }, [currentConversation]);

  const updateConversationList = (currentConversation: Conversation) => {
    const isNewConversation = !conversationList.some(
      (conversation) =>
        conversation.conversationId === currentConversation.conversationId
    );

    if (isNewConversation) {
      setConversationList((list) =>
        list.filter((c) => c.conversationId !== "")
      );
      setConversationList((prevList) => [
        {
          ...currentConversation,
          isCurrent: true,
        },
        ...prevList.map((conversation) => ({
          ...conversation,
          isCurrent: false,
        })),
      ]);
    } else {
      const filteredList = conversationList.filter(
        (c) => c.conversationId !== currentConversation.conversationId
      );

      setConversationList(() => [
        {
          ...currentConversation,
          isCurrent: true,
        },
        ...filteredList.map((conversation) => ({
          ...conversation,
          isCurrent: false,
        })),
      ]);
    }
  };

  const verifyTermsOfAgreement = () => {
    setIsUserDataLoading(true);
    setIsLoading(true);
    checkAgreementVersion(
      userId?.current,
      String(props?.rendering?.fields?.TermsOfUseText?.value),
      accessToken
    ).then(async (response: Response) => {
      const isLatest = await response.json();
      setIsTermsOfUseAccepted(isLatest);
      setIsUserDataLoading(false);
      if (isLatest) initializeUserData();
    });
  };

  const onTermsOfAgreementAccept = () => {
    updateAgreementVersion(userId?.current, accessToken);
    setIsTermsOfUseAccepted(true);
    initializeUserData();
  };

  const initializeUserData = () => {
    const formatDateTime = (timestamp: string): Date => {
      const [year, month, day, hour, minute, second] = timestamp
        .split("-")
        .map(Number);

      const utcDate = new Date(
        Date.UTC(year, month - 1, day, hour, minute, second)
      );

      return utcDate;
    };

    setIsLoading(true);
    getUserConversations(userId?.current, accessToken)
      .then(async (conversationList: ConversationList) => {
        if (conversationList && conversationList.length > 0) {
          conversationList = conversationList.filter(
            (conversation) =>
              !(
                conversation.isCurrent === false &&
                conversation.conversationId === ""
              )
          );
          conversationList = conversationList.map((data: any) => ({
            ...data,
            userId: userId?.current,
            isCurrent: false,
            datetime: formatDateTime(data.time),
            isDisabled: data.disabled,
          }));
          setConversationList(conversationList);
        }
        createNewConversation();
        setIsLoading(false);
      })
      .catch((error) => {
        console.error("Error loading conversation history:", error);
      });
  };

  // Create a new conversation: greeting message, start a new tab and set to active
  const createNewConversation = () => {
    setConversationList((list) => list.filter((c) => c.conversationId !== ""));
    const newConversation = {
      isCurrent: true,
      userId: userId?.current,
      conversationId: "",
      isDisabled: false,
      name: "New Conversation",
      datetime: new Date(),
      content: [],
    };
    setIsLoading(false);
    setIsLoading(false);
    setCurrentConversation(newConversation);
  };

  // Handle changes made from tab
  const onConversationChange = (type: ModalType, data: string) => {
    // Set new conversation as current conversation
    const onChangeTab = (conversationId: string) => {
      if (conversationId === currentConversation.conversationId) return;
      if (conversationId === undefined) createNewConversation();
      setConversationList((list) => filterConversations(list));

      const activeConversation = conversationList.find(
        (c) => c.conversationId === conversationId
      );
      if (activeConversation) setCurrentConversation(activeConversation);
    };

    const filterConversations = (list: Conversation[]) => {
      return list.filter((c) => c.conversationId !== "");
    };

    const onRenameConversation = (conversationName: string) => {
      setCurrentConversation((prevConversation) => ({
        ...prevConversation,
        name: conversationName,
      }));
    };

    const onDeleteConversation = (conversationId: string) => {
      const filterConversation = (conversation: Conversation) => {
        return conversation.conversationId !== conversationId;
      };

      if (conversationId === "") {
        setConversationList([]);
      } else {
        setConversationList((prevList) => prevList.filter(filterConversation));
      }

      createNewConversation();
    };

    switch (type) {
      case ModalType.new:
        createNewConversation();
        break;
      case ModalType.tab:
        onChangeTab(data);
        break;
      case ModalType.rename:
        onRenameConversation(data);
        break;
      case ModalType.delete:
        onDeleteConversation(data);
        break;
      case ModalType.deleteAll:
        onDeleteConversation(data);
        break;
      default:
        break;
    }
  };

  const checkConversationChange = (conversation: Conversation) => {
    if (!isEqual(conversation, currentConversation)) {
      setCurrentConversation(conversation);
    }
  };

  return (
    <div className="paas-ai-main">
      <PaasAiTabs
        props={props}
        accessToken={accessToken}
        isTyping={isTyping}
        isLoading={isLoading}
        isUserDataLoading={isUserDataLoading}
        isTermsOfUseAccepted={isTermsOfUseAccepted}
        currentConversation={currentConversation}
        conversationList={conversationList}
        emitConversationChange={(type: ModalType, data: string) =>
          onConversationChange(type, data)
        }
        emitTermsOfUseChange={() => onTermsOfAgreementAccept()}
      />
      {!isUserDataLoading && (
        <PaasAiMessages
          props={props}
          accessToken={accessToken}
          isLoading={isLoading}
          isUserDataLoading={isUserDataLoading}
          currentConversation={currentConversation}
          username={username?.current}
          emitConversationChange={(conversation: Conversation) =>
            checkConversationChange(conversation)
          }
          emitIsTyping={(isTyping: boolean) => setIsTyping(isTyping)}
        />
      )}
    </div>
  );
};

export default PaasAi;
