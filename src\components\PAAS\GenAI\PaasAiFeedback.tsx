import React, { useEffect, useState } from "react";

const PaasAiFeedback = ({
  elementRef,
  emitFeedbackChange,
}: {
  elementRef: any;
  emitFeedbackChange: (
    isSubmitted: boolean,
    feedbackInput: string | null
  ) => void;
}) => {
  const [selectedRadio, setSelectedRadio] = useState("");
  const [feedbackInput, setFeedbackInput] = useState("");
  const toolTipText = "Why did you choose this rating? (optional)";
  const feedbackOptions = ["Not relevant enough", "Issue with this content"];
  const otherPlaceholder = "Tell us more";

  // Listener for off screen click
  useEffect(() => {
    document.addEventListener("mousedown", handleOffScreenClick);
    return () => {
      document.removeEventListener("mousedown", handleOffScreenClick);
    };
  }, []);

  // Submit and emit to parent
  const submitFeedback = (isSubmitted: boolean) => {
    if (selectedRadio === "Other") {
      emitFeedbackChange(isSubmitted, feedbackInput);
    } else {
      emitFeedbackChange(isSubmitted, selectedRadio);
    }
    clearFeedback();
  };

  // Clear feedback input
  const clearFeedback = () => {
    setFeedbackInput("");
  };

  // Click off screen to cancel and close modal
  const handleOffScreenClick = (e: any) => {
    if (!e.target.className.includes("feedback")) {
      submitFeedback(false);
    }
  };

  // Keep track of user input
  const handleFeedbackInput = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setFeedbackInput(e.target.value);
  };

  // Submit feedback on Enter key press
  const handleFeedbackKeyPress = (e: any) => {
    if (e.key === "Enter") {
      submitFeedback(true);
    }
  };

  // Return if options menu displays above or below tab
  const getFeedbackPosition = () => {
    const panelHeight = document
      .getElementById("paas-ai-middle-panel")
      ?.getBoundingClientRect().bottom;
    const elementPosition = elementRef?.getBoundingClientRect().top;
    const difference = panelHeight ? panelHeight - elementPosition : 0;
    return difference < 350 ? "show-above" : "show-below";
  };

  // Return radio options
  const getRadioOptions = (text: string) => {
    return (
      <div className="paas-ai-feedback-radio">
        <input
          value={text}
          name="feedback"
          className="feedback"
          type="radio"
          onChange={(e) => {
            setSelectedRadio(e.target.value);
            if (text !== "Other") {
              clearFeedback();
            }
          }}
        />
        {text}
      </div>
    );
  };

  return (
    <div
      className={`tooltip-feedback ${getFeedbackPosition()}`}
      data-testid="tooltip-feedback"
    >
      <div className="paas-ai-feedback-header">
        {toolTipText}
        <button
          data-testid="paas-ai-feedback-x-button"
          className="paas-ai-feedback-x-button"
          onClick={() => submitFeedback(false)}
        >
          <span className="material-symbols-outlined feedback">Close</span>
        </button>
      </div>

      {getRadioOptions(feedbackOptions[0])}
      {getRadioOptions(feedbackOptions[1])}
      {getRadioOptions("Other")}

      <input
        disabled={selectedRadio !== "Other"}
        id="feedback"
        className="paas-ai-feedback-input feedback"
        placeholder={otherPlaceholder}
        onKeyDown={handleFeedbackKeyPress}
        onChange={handleFeedbackInput}
        value={feedbackInput}
      />

      <button
        onClick={() => submitFeedback(true)}
        className="paas-ai-feedback-submit"
        data-testid="paas-ai-feedback-submit"
      >
        Submit
      </button>
    </div>
  );
};

export default PaasAiFeedback;
