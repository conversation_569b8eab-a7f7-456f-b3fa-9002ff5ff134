/* eslint-disable prettier/prettier */
import React, { useEffect, useState } from "react";
import {
  ChatResponse,
  Conversation,
  Message,
  Source,
} from "./interfaces/interfaces";
import { ClipLoader } from "react-spinners";
import { postChatStream } from "./PaasAiService";
import PaasAiToolTips from "./PaasAiTooltips";
import IconBotAvatar from "./assets/avatar-bot-avatar";
import IconUserAvatar from "./assets/icon-user-avatar";
import TypingIndicator from "./assets/typing-indicator";
import IconInputSubmit from "./assets/icon-input-submit";
import IconMicrophone from "./assets/icon-microphone";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import { isEqual } from "lodash";
import { GenAI_STREAM } from "../PaasLandingPageSearch/SearchConstants";

const PaasAiMessages = ({
  props,
  accessToken,
  isLoading,
  isUserDataLoading,
  currentConversation,
  username,
  emitConversationChange,
  emitIsTyping,
}: {
  props: any;
  accessToken: string;
  isLoading: boolean;
  isUserDataLoading: boolean;
  currentConversation: Conversation;
  username: string;
  emitConversationChange: (currentConversation: Conversation) => void;
  emitIsTyping: (isTyping: boolean) => void;
}) => {
  const [botName] = useState(props.rendering.fields.BotNameText.value);
  const [inputTextPlaceholder] = useState(
    props.rendering.fields.InputPlaceholderText.value
  );
  const [conversationStarters] = useState(
    props.rendering.fields.ConversationStartersText.value
  );
  const [greetingMessage] = useState(
    props.rendering.fields.GreetingMessageText.value
  );
  const [isTyping, setIsTyping] = useState(false);
  const [userInput, setUserInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeConversation, setActiveConversation] = useState<Conversation>(
    {} as Conversation
  );
  const [automaticDeleteWarningText] = useState(
    "*Conversations older than 180 days are automatically deleted for you"
  );
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();
  const [isMicDisabled, setIsMicDisabled] = useState(false);
  const [skipScroll, setSkipScroll] = useState(false);

  useEffect(() => {
    if (!browserSupportsSpeechRecognition) {
      setIsMicDisabled(true);
    }
  }, [browserSupportsSpeechRecognition]);

  useEffect(() => {
    if (isUserDataLoading) return;
    if (isEqual(activeConversation, currentConversation)) return;

    setUserInput("");
    SpeechRecognition.stopListening();
  }, [activeConversation, isUserDataLoading]);

  // New Conversation or Tab Change
  useEffect(() => {
    if (isUserDataLoading) return;
    if (isEqual(activeConversation, currentConversation)) return;

    const formatConversationList = () => {
      let formattedMessages: Message[] = [];
      formattedMessages.push({
        userMessage: "",
        botMessage: formatBotMessage([], greetingMessage),
        rating: 0,
      });

      currentConversation.content.forEach(async (messages) => {
        formattedMessages.push({
          userMessage: messages.userMsg,
          botMessage: formatBotMessage(messages.source || [], messages.botMsg),
          rating: messages.rating,
        });
      });
      setMessages(formattedMessages);
    };

    if (currentConversation.conversationId === undefined) return;
    if (currentConversation === activeConversation) return;

    setMessages([]);
    setActiveConversation(currentConversation);
    formatConversationList();
  }, [currentConversation, isUserDataLoading]);

  // Emit any conversation changes to the parent
  useEffect(() => {
    if (!isEqual(activeConversation, currentConversation))
      emitConversationChange(activeConversation);
  }, [activeConversation]);

  // Scroll to most recent message
  useEffect(() => {
    const scrollMessages = () => {
      const element = document.getElementById(
        "paas-ai-middle-panel-conversation"
      );
      setTimeout(function () {
        if (element) {
          element.scrollTop = element.scrollHeight;
        }
      }, 0);
    };

    if (skipScroll) {
      setSkipScroll(false);
    } else {
      setUserInput("");
      scrollMessages();
    }
  }, [messages]);

  // Voice to Text input
  useEffect(() => {
    if (transcript.length > 0 && !listening) {
      setUserInput(
        userInput.length === 0
          ? transcript[0].toUpperCase() + transcript.slice(1)
          : userInput + " " + transcript
      );
    }
  }, [listening]);

  // Take user input, send to API, and return response from GenAI
  const addNewMessageToConversation = (newMessage: Message) => {
    setMessages((prevMessages) => {
      if (newMessage.botMessage === "") {
        return [...prevMessages, newMessage];
      } else {
        const messages = [...prevMessages];
        messages[messages.length - 1] = newMessage;
        return messages;
      }
    });
  };

  const updateConversation = (
    userMessage: string,
    botMessage: string,
    conversationId: string,
    metadata?: any,
    isDisabled?: boolean
  ) => {
    setActiveConversation((conversation: Conversation) => ({
      ...conversation,
      conversationId:
        activeConversation?.conversationId === ""
          ? conversationId
          : activeConversation.conversationId,
      name:
        activeConversation?.conversationId === ""
          ? userMessage
          : conversation.name,
      datetime: new Date(),
      isDisabled: isDisabled || false,
      content: [
        ...conversation.content,
        {
          userMsg: userMessage,
          botMsg: botMessage,
          time: new Date(),
          rating: 0,
          metadata,
        },
      ],
    }));
  };

  const safeSingleToDoubleQuotes = (str: string) => {
    const tempStr = str.replace(/(\w)'(\w)/g, "$1_APOSTROPHE_$2");

    if (tempStr.trim().startsWith("[") && tempStr.trim().endsWith("]")) {
      let inString = false;
      let result = "";
      let currentQuote = null;

      for (let i = 0; i < tempStr.length; i++) {
        const char = tempStr[i];
        const prevChar = i > 0 ? tempStr[i - 1] : null;

        if ((char === "'" || char === '"') && prevChar !== "\\") {
          if (!inString) {
            result += '"';
            currentQuote = char;
            inString = true;
          } else if (char === currentQuote) {
            result += '"';
            currentQuote = null;
            inString = false;
          } else {
            result += "\\" + char;
          }
        } else {
          result += char;
        }
      }

      return result.replace(/_APOSTROPHE_/g, "'");
    }

    return str;
  };

  // DO NOT CHANGE THE EXISITNG QUOTES
  const extractSuggestionsManually = (str: string): string[] => {
    const suggestions: string[] = [];
    const content = str.trim().replace(/^\[|\]$/g, '');
    
    let current = '';
    let inQuotes = false;
    let quoteChar = null;
    
    for (let i = 0; i < content.length; i++) {
      const char = content[i];
      const prevChar = i > 0 ? content[i - 1] : null;
      
      if ((char === '"' || char === "'") && prevChar !== '\\') {
        if (!inQuotes) {
          inQuotes = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuotes = false;
          quoteChar = null;
        } else {
          current += char;
        }
      } else if (char === ',' && !inQuotes) {
        if (current.trim()) {
          suggestions.push(current.trim());
        }
        current = '';
      } else if (char !== ' ' || inQuotes || current.trim()) {
        current += char;
      }
    }

    if (current.trim()) {
      suggestions.push(current.trim());
    }

    return suggestions;
  };

  const parseArrayWithMixedQuotes = (str: string): string[] => {
    try {
      return JSON.parse(str);
    } catch (error) {
      try {
        const normalized = safeSingleToDoubleQuotes(str);
        return JSON.parse(normalized);
      } catch (error2) {
        return extractSuggestionsManually(str);
      }
    }
  };

  const tryParseJSON = (data: string) => {
    try {
      return JSON.parse(data);
    } catch (error) {
      return null;
    }
  };

  const processEventLine = (line: string, state: StreamProcessingState) => {
    line = line.trimStart();
    if (!line) return;

    if (line.startsWith("data:")) {
      line = line.substring(5).trimStart();
    }

    const eventData = line;

    if (handleEventStart(eventData, state)) return;

    processContentLine(eventData, state);
  };

  // Helper function to handle event start and end markers
  const handleEventStart = (
    eventData: string,
    state: StreamProcessingState
  ): boolean => {
    const eventHandlers: { [key: string]: () => void } = {
      "<RESPONSE_START>": () => (state.isCapturingMainResponse = true),
      "<RESPONSE_END>": () => (state.isCapturingMainResponse = false),
      "<FINAL_RESPONSE_START>": () => (state.isCapturingFinalResponse = true),
      "<FINAL_RESPONSE_END>": () => (state.isCapturingFinalResponse = false),
      "<SUGGESTION_START>": () => {
        state.isCapturingSuggestions = true;
        state.suggestionsBuffer = "";
        state.rawSuggestionData = [];
      },
      "<SUGGESTION_END>": () => processSuggestionEnd(state),
    };

    const handler = eventHandlers[eventData];
    if (handler) {
      handler();
      return true;
    }

    return false;
  };

  const processSuggestionEnd = (state: StreamProcessingState) => {
    state.isCapturingSuggestions = false;

    if (state.suggestionsBuffer.trim()) {
      try {
        state.suggestions = parseBufferDirectly(
          state.suggestionsBuffer.trim(),
          safeSingleToDoubleQuotes
        );
      } catch (error) {
        console.error("Buffer parsing failed:", error);
        state.suggestions = extractSuggestionsFromRawData(
          state.rawSuggestionData,
          safeSingleToDoubleQuotes
        );
      }
    }

    state.setShowSuggestions(
      Array.isArray(state.suggestions) && state.suggestions.length > 0
    );

    state.newMessage.suggestions = state.suggestions;
    addNewMessageToConversation({ ...state.newMessage });
  };

  const parseBufferDirectly = (
    buffer: string,
    quotesConverter: (str: string) => string
  ) => {
    try {
      return parseArrayWithMixedQuotes(buffer);
    } catch (error) {
      console.log("Mixed quotes parser failed, trying original approach");

      // Fallback to original logic
      if (buffer.startsWith("[") && buffer.endsWith("]")) {
        const cleanData = quotesConverter(buffer);
        const result = JSON.parse(cleanData);
        console.log("Successfully parsed buffer directly:", result);
        return result;
      } else {
        const formattedData = quotesConverter(buffer);
        let jsonToEvaluate = formattedData;
        if (!formattedData.startsWith("[")) {
          jsonToEvaluate = "[" + formattedData;
        }
        if (!formattedData.endsWith("]")) {
          jsonToEvaluate = jsonToEvaluate + "]";
        }
        const result = JSON.parse(jsonToEvaluate);
        console.log("Successfully parsed with added brackets:", result);
        return result;
      }
    }
  };

  const extractSuggestionsFromRawData = (
    rawData: string[],
    quotesConverter: (str: string) => string
  ): string[] => {
    if (!rawData.length) return [];

    const combinedData = rawData.join("");
    try {
      const suggestions = parseArrayWithMixedQuotes(combinedData);
      if (suggestions.length) {
        console.log("Mixed quotes parser succeeded on raw data:", suggestions);
        return suggestions;
      }
    } catch (error) {
      console.log("Mixed quotes parser failed on raw data");
    }

    // Try extracting suggestions directly from raw data
    const directSuggestions = extractDirectSuggestions(
      rawData,
      quotesConverter
    );
    if (directSuggestions.length) return directSuggestions;

    // Fallback to regex-based extraction
    const regexSuggestions = extractSuggestionsUsingRegex(rawData);
    if (regexSuggestions.length) return regexSuggestions;

    return [];
  };

  // Helper function to extract suggestions directly
  const extractDirectSuggestions = (
    rawData: string[],
    quotesConverter: (str: string) => string
  ): string[] => {
    for (const rawItem of rawData) {
      if (rawItem.includes("[") && rawItem.includes("]")) {
        const start = rawItem.indexOf("[");
        const end = rawItem.lastIndexOf("]") + 1;
        const arrayStr = rawItem.substring(start, end);
        try {
          const suggestions = parseArrayWithMixedQuotes(arrayStr);
          console.log("Extracted array from raw data:", suggestions);
          return suggestions;
        } catch (error) {
          console.log("New parser failed, trying quotesConverter fallback");
          try {
            const convertedStr = quotesConverter(arrayStr);
            const suggestions = JSON.parse(convertedStr);
            console.log("Extracted array with quotesConverter:", suggestions);
            return suggestions;
          } catch (error2) {
            console.log("Raw item extraction failed:", error2);
          }
        }
      }
    }
    return [];
  };

  // Helper function to extract suggestions using regex
  const extractSuggestionsUsingRegex = (rawData: string[]): string[] => {
    try {
      const regex = /'([^']+)'/g;
      const allMatches: string[] = [];

      for (const rawItem of rawData) {
        const matches = rawItem.match(regex);
        if (matches) {
          allMatches.push(...matches.map((m) => m.slice(1, -1)));
        }
      }

      if (allMatches.length) {
        console.log("Regex extracted suggestions:", allMatches);
      }

      return allMatches;
    } catch (error) {
      console.error("Regex extraction failed:", error);
      return [];
    }
  };

  const processContentLine = (
    eventData: string,
    state: StreamProcessingState
  ) => {
    if (state.isCapturingMainResponse) {
      processMainResponse(eventData, state);
    } else if (state.isCapturingFinalResponse) {
      processFinalResponse(eventData, state);
    } else if (state.isCapturingSuggestions) {
      processSuggestion(eventData, state);
    } else if (eventData.startsWith("{")) {
      processMetadata(eventData, state);
    }

    updateBotMessage(state);
  };

  const processMainResponse = (
    eventData: string,
    state: StreamProcessingState
  ) => {
    try {
      const parsed = tryParseJSON(eventData);
      if (parsed && typeof parsed === "object" && parsed.answer) {
        state.responseBuffer += parsed.answer + " ";
        state.metadata = parsed.metadata || [];
        state.conversationId = parsed.conversationId;
      } else {
        state.responseBuffer += eventData + " ";
      }
    } catch {
      state.responseBuffer += eventData + " ";
    }
  };

  const processFinalResponse = (
    eventData: string,
    state: StreamProcessingState
  ) => {
    try {
      const parsed = tryParseJSON(eventData);
      if (parsed && typeof parsed === "object" && parsed.answer) {
        state.finalResponseBuffer += parsed.answer + " ";
      } else {
        state.finalResponseBuffer += eventData + " ";
      }
    } catch {
      state.finalResponseBuffer += eventData + " ";
    }
  };

  const processSuggestion = (
    eventData: string,
    state: StreamProcessingState
  ) => {
    state.rawSuggestionData.push(eventData);
    state.suggestionsBuffer += eventData;

    try {
      if (eventData.includes("[") && eventData.includes("]")) {
        const start = eventData.indexOf("[");
        const end = eventData.lastIndexOf("]") + 1;
        const arrayStr = eventData.substring(start, end);

        state.suggestions = parseArrayWithMixedQuotes(arrayStr);
        console.log("Direct chunk parse success:", state.suggestions);
      }

      if (
        state.suggestionsBuffer.includes("[") &&
        state.suggestionsBuffer.includes("]")
      ) {
        const start = state.suggestionsBuffer.indexOf("[");
        const end = state.suggestionsBuffer.lastIndexOf("]") + 1;
        const bufferArray = state.suggestionsBuffer.substring(start, end);

        state.suggestions = parseArrayWithMixedQuotes(bufferArray);
        console.log("Buffer parse success:", state.suggestions);
      }
    } catch {
      console.log("Still accumulating suggestion data");
    }
  };

  const processMetadata = (eventData: string, state: StreamProcessingState) => {
    try {
      const json = tryParseJSON(eventData);
      if (json) {
        state.metadata = json.metadata || [];
        state.conversationId = json.conversationId;
        state.isValidConversation = json.valid;
      }
    } catch (error) {
      console.error("Error parsing metadata JSON:", error);
    }
  };

  const updateBotMessage = (state: StreamProcessingState) => {
    state.newMessage.botMessage = formatBotMessage(
      state.metadata,
      state.responseBuffer.trim(),
      state.isCapturingMainResponse || state.isCapturingFinalResponse
    );

    addNewMessageToConversation({ ...state.newMessage });

    if (state.finalResponseBuffer.trim()) {
      state.newMessage.botMessage = formatBotMessage(
        state.metadata,
        state.finalResponseBuffer.trim(),
        state.isCapturingMainResponse || state.isCapturingFinalResponse
      );

      addNewMessageToConversation({ ...state.newMessage });
    }
  };

  const processStreamResponse = async (
    response: Response,
    state: StreamProcessingState
  ) => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    if (!response.body) {
      throw new Error("Response body is empty or null.");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    let partialLine = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      partialLine += chunk;

      const lines = partialLine.split(/\r?\n/);
      partialLine = lines.pop()!;

      for (let line of lines) {
        processEventLine(line, state);
      }
    }

    if (partialLine.trim()) {
      let line = partialLine.trim();
      processPartialLine(line, state);
    }

    finalizeConversation(state);
  };

  const processPartialLine = (line: string, state: StreamProcessingState) => {
    if (line.startsWith("data:")) {
      line = line.substring(5).trim();
    }

    if (state.isCapturingMainResponse) {
      state.responseBuffer += line + " ";
    } else if (state.isCapturingFinalResponse) {
      state.finalResponseBuffer += line + " ";
    } else if (state.isCapturingSuggestions) {
      state.rawSuggestionData.push(line);
      state.suggestionsBuffer += line;
    }
  };

  const finalizeConversation = (state: StreamProcessingState) => {
    const finalMessage = state.finalResponseBuffer.trim()
      ? state.finalResponseBuffer.trim()
      : state.responseBuffer.trim();

    if (state.conversationId) {
      updateConversation(
        state.newMessage.userMessage,
        finalMessage,
        state.conversationId,
        state.metadata || [],
        !state.isValidConversation
      );
    }
  };

  interface StreamProcessingState {
    responseBuffer: string;
    finalResponseBuffer: string;
    metadata: any;
    conversationId: string | undefined;
    isValidConversation: boolean;
    suggestions: string[];
    suggestionsBuffer: string;
    rawSuggestionData: string[];
    isCapturingMainResponse: boolean;
    isCapturingFinalResponse: boolean;
    isCapturingSuggestions: boolean;
    newMessage: Message;
    setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
    setShowSuggestions: React.Dispatch<React.SetStateAction<boolean>>;
    activeConversation: Conversation;
    setActiveConversation: React.Dispatch<React.SetStateAction<Conversation>>;
  }

  const initializeStreamState = (
    newMessage: Message,
    setMessages: React.Dispatch<React.SetStateAction<Message[]>>,
    setShowSuggestions: React.Dispatch<React.SetStateAction<boolean>>,
    activeConversation: Conversation,
    setActiveConversation: React.Dispatch<React.SetStateAction<Conversation>>
  ): StreamProcessingState => {
    return {
      responseBuffer: "",
      finalResponseBuffer: "",
      metadata: undefined,
      conversationId: undefined,
      isValidConversation: true,
      suggestions: [],
      suggestionsBuffer: "",
      rawSuggestionData: [],
      isCapturingMainResponse: false,
      isCapturingFinalResponse: false,
      isCapturingSuggestions: false,
      newMessage,
      setMessages,
      setShowSuggestions,
      activeConversation,
      setActiveConversation,
    };
  };

  const processInputQuestion = async (suggestion?: string) => {
    if (!suggestion && !userInput.trim()) return;

    updateIsTyping(true);
    let newMessage: Message = {
      userMessage: suggestion || userInput.trim(),
      botMessage: "",
      rating: 0,
      suggestions: [],
    };

    if (activeConversation?.conversationId === "") {
      setActiveConversation((conversation: Conversation) => ({
        ...conversation,
        name: newMessage.userMessage.substring(0, 30),
      }));
    }

    addNewMessageToConversation(newMessage);

    try {
      const response = await postChatStream(
        newMessage.userMessage,
        activeConversation.userId,
        activeConversation.conversationId,
        accessToken
      );

      const state = initializeStreamState(
        newMessage,
        setMessages,
        setShowSuggestions,
        activeConversation,
        setActiveConversation
      );

      await processStreamResponse(response, state);
    } catch (error) {
      console.error("Error processing message:", error);
      newMessage.botMessage =
        "There was a problem processing this request. Please try again later.";
      addNewMessageToConversation(newMessage);
    } finally {
      updateIsTyping(false);
    }
  };

  const updateIsTyping = (isTyping: boolean) => {
    setIsTyping(isTyping);
    emitIsTyping(isTyping);
  };

  const handleUserInput = (e: {
    target: { value: React.SetStateAction<string> };
  }) => {
    setUserInput(e.target.value);
  };

  const handleKeyPress = (e: any) => {
    if (e.key === "Enter" && !isTyping && !isLoading) {
      processInputQuestion();
    }
  };

  const onRatingsChange = (messageIndex: number, newRating: number) => {
    setSkipScroll(true);
    setMessages((prevMessages) =>
      prevMessages.map((message, index) =>
        index == messageIndex ? { ...message, rating: newRating } : message
      )
    );
    setActiveConversation({
      ...activeConversation,
      content: activeConversation.content.map((content, index) =>
        index === messageIndex - 1 ? { ...content, rating: newRating } : content
      ),
    });
  };

  // Format bot response, including spacing, bulletin points, and linked sources
  const formatBotMessage = (
    metadata: ChatResponse["metadata"] | Source[],
    response: ChatResponse["answer"],
    isCapturingResponse?: boolean
  ) => {
    let formattedSources = "";
    let greetingStyling = "";
    if (response == greetingMessage) greetingStyling = "greeting";

    if (metadata?.length > 0) {
      formattedSources = metadata
        .map((metadata: any) => {
          const docTypeLower = metadata.docType.toLowerCase();
          const isTrainingManual = docTypeLower.includes("training manual");
          const isIndustryGuide = docTypeLower.includes("industry guides");
          const keywords = [
            "audit information summary",
            "general state links",
            "test audit information",
            "wc state statutory exceptions",
            "rules and state exception",
          ];

          const isUnderJurisdiction = keywords.some((keyword) =>
            docTypeLower.includes(keyword.toLowerCase())
          );
          let url = "";

          if (isTrainingManual) {
            if (metadata?.parentItemId) {
              url = `/PAAS/training-manual?id=${metadata.parentItemId.replace(
                /[{()}]/g,
                ""
              )}`;
              if (metadata?.itemId) {
                url += `&chapterid=${metadata.itemId.replace(/[{()}]/g, "")}`;
              }
            } else if (metadata?.itemId) {
              url = `/PAAS/training-manual?id=${metadata.itemId.replace(
                /[{()}]/g,
                ""
              )}`;
            }
          } else if (isIndustryGuide) {
            if (metadata?.parentItemId) {
              url = `/PAAS/industryguide?id=${metadata.parentItemId.replace(
                /[{()}]/g,
                ""
              )}`;
              if (metadata?.itemId) {
                url += `&chapterid=${metadata.itemId.replace(/[{()}]/g, "")}`;
              }
            } else if (metadata?.itemId) {
              url = `/PAAS/industryguide?id=${metadata.itemId.replace(
                /[{()}]/g,
                ""
              )}`;
            }
          } else if (isUnderJurisdiction) {
            let urlParams = `jurisdiction=${
              metadata?.jurisdiction || ""
            }&docType=${encodeURIComponent(metadata?.docType || "")}`;

            if (metadata?.topic) {
              urlParams += `&topic=${encodeURIComponent(metadata.topic)}`;
            }

            if (metadata?.rule) {
              urlParams += `&rule=${encodeURIComponent(metadata.rule)}`;
            }

            url = `/PAAS/jurisdiction?${urlParams}`;
          } else {
            url = `/paas/search/?contentType=${metadata?.docType?.slice(
              0,
              -1
            )}&id=${metadata?.itemId?.replace(/[{()}]/g, "")}`;
          }

          return `<a href="${url}" target="_blank">${metadata.title}</a>`;
        })
        .join("");
    }

    const typingClass = isCapturingResponse ? "typing" : "";

    const formattedChatResponse = response
      .split("\n\n")
      .map((message: any) => {
        message = message.trim();
        if (message.includes("\n")) {
          message = message
            .split("\n")
            .map((subMessage: any) => {
              subMessage = subMessage.trim();
              if (subMessage.startsWith("-")) {
                return `
                <li class="sub-bulletin ${typingClass}">
                  ${subMessage.replace(/^-/, "")}
                </li>
              `;
              } else {
                return `
                <li style="list-style-type:none" class="${typingClass}">
                  ${subMessage}
                </li>
              `;
              }
            })
            .join("");
        }

        if (message.startsWith("-")) {
          return `
          <li class="${typingClass}">
            ${message.trim().replace(/^-/, "")}
          </li>
        `;
        } else if (GenAI_STREAM.test(message)) {
          //NOSONAR
          return `
          <div class="${typingClass}">
            ${message.trim().replace(/^-/, "")}
          </div>
        `;
        } else {
          return `
          <div class="${typingClass}">
            ${message}
          </div>
        `;
        }
      })
      .join(`<br>`);

    return `
      <div class="paas-ai-message-content ${greetingStyling}">
        ${formattedChatResponse}
      </div>
      ${
        formattedSources.length > 0
          ? `
          <div class="paas-ai-message-sources">
            <div class="paas-ai-message-sender sources">
              PAAS Source:
            </div>
            <div class="paas-ai-message-content sources">
                ${formattedSources}
            </div>
          </div>
            `
          : ""
      }
    `;
  };

  const getDeletionWarning = () => {
    const date = new Date(currentConversation.datetime);
    date.setDate(date.getDate() + 180);
    const month = date.toLocaleString("en-US", { month: "long" });
    const day = date.toLocaleString("en-US", { day: "2-digit" });
    const year = date.toLocaleDateString("en-US", { year: "numeric" });
    return `This conversation is scheduled for automatic deletion on ${month} ${day}, ${year}.`;
  };

  const onClickMicrophone = () => {
    if (listening) {
      SpeechRecognition.stopListening();
    } else {
      resetTranscript();
      SpeechRecognition.startListening();
    }
  };

  const getMessage = (message: Message, index: number) => {
    const getBotMessage = () => {
      if (message.botMessage === "") {
        return (
          <div className="paas-ai-message-content">
            <TypingIndicator />
          </div>
        );
      } else {
        return (
          <div>
            <div
              dangerouslySetInnerHTML={{
                __html: message.botMessage,
              }}
            ></div>
            <PaasAiToolTips
              accessToken={accessToken}
              index={index}
              message={message}
              isTyping={isTyping}
              isLoading={isLoading}
              activeConversation={activeConversation}
              emitRatingsChange={(messageIndex: number, rating: number) =>
                onRatingsChange(messageIndex, rating)
              }
            />
          </div>
        );
      }
    };

    if (messages.length == 1 && message.userMessage === "") {
      return (
        <div key={index} className="paas-ai-message-container">
          <div className="paas-ai-message">
            <div className="paas-ai-message-header">
              <IconBotAvatar />
              <div className="paas-ai-message-sender">{botName}</div>
            </div>
            <div dangerouslySetInnerHTML={{ __html: message.botMessage }}></div>
          </div>
          <div
            className="paas-ai-conversation-starters"
            dangerouslySetInnerHTML={{
              __html: conversationStarters,
            }}
          ></div>
        </div>
      );
    } else if (index !== 0) {
      return (
        <div key={index} className="paas-ai-message-container">
          <div className="paas-ai-message">
            <div className="paas-ai-message-header">
              <IconUserAvatar username={username} />
              <div className="paas-ai-message-sender">You</div>
            </div>
            <div className="paas-ai-message-content">{message.userMessage}</div>
          </div>

          <div className="paas-ai-message bot">
            <div className="paas-ai-message-header">
              <IconBotAvatar />
              <div className="paas-ai-message-sender">{botName}</div>
            </div>
            {getBotMessage()}
          </div>
        </div>
      );
    } else {
      return "";
    }
  };

  return (
    <div id="paas-ai-middle-panel" className="paas-ai-middle-panel">
      {/* PDF Button  */}
      {/* <div className="paas-ai-download-container">
        <button disabled={isTyping || isLoading}>
          <img src={IconDownloadPdf.src} alt="download PDF"/>
          <div className="paas-ai-download-text">Download PDF</div>
        </button>
      </div> */}

      {/* Loading symbol  */}
      {isLoading || isUserDataLoading ? (
        <div className="paas-ai-middle-panel-conversation loading">
          <div className="paas-ai-spinner">
            <ClipLoader />
          </div>
        </div>
      ) : (
        <>
          <div className="paas-ai-conversation-title">
            {messages.length > 1 && messages[0].botMessage !== "" ? (
              <>
                <div className="paas-ai-title">{activeConversation.name}</div>
                <div className="paas-ai-warning">{getDeletionWarning()}</div>
              </>
            ) : (
              ""
            )}
          </div>

          <div
            className="paas-ai-middle-panel-conversation"
            id="paas-ai-middle-panel-conversation"
          >
            {messages.map((message: Message, index: number) =>
              getMessage(message, index)
            )}
          </div>
          {showSuggestions &&
          messages.length !== 1 &&
          messages[messages.length - 1]?.suggestions?.length ? (
            <div
              className="paas-ai-middle-panel-suggestion"
              id="paas-ai-middle-panel-suggestion"
            >
              <div className="paas-ai-middle-panel-suggestion-title-container">
                <p>Quick Suggestions</p>
                <a>
                  <span
                    id="close-suggestions"
                    className="material-icons"
                    onClick={() => {
                      setShowSuggestions(false);
                    }}
                  >
                    close
                  </span>
                </a>
              </div>
              <div className="chip-container">
                {messages[messages.length - 1]?.suggestions?.map(
                  (suggestion) => (
                    <button
                      key={suggestion}
                      className="chip-button"
                      onClick={() => {
                        processInputQuestion(suggestion);
                      }}
                    >
                      {suggestion}
                    </button>
                  )
                )}
              </div>
            </div>
          ) : (
            <></>
          )}
          <div className="paas-ai-middle-panel-input-container-main">
            <div className="paas-ai-middle-panel-input-container">
              <input
                data-testid="paas-ai-message-input"
                id="messageInput"
                disabled={currentConversation.isDisabled}
                className="paas-ai-middle-panel-input-text"
                placeholder={
                  currentConversation.isDisabled
                    ? "This conversation has been disabled"
                    : inputTextPlaceholder
                }
                onKeyDown={handleKeyPress}
                onChange={handleUserInput}
                value={userInput}
              />
              <div className="paas-ai-input-actions">
                <button
                  disabled={
                    isTyping ||
                    isLoading ||
                    userInput.length == 0 ||
                    currentConversation.isDisabled
                  }
                  data-testid="paas-ai-submit-message-btn"
                  className="paas-ai-submitIcon"
                  onClick={() => {
                    processInputQuestion();
                  }}
                >
                  <IconInputSubmit />
                </button>

                <span className="paas-ai-separator"></span>

                <button
                  className={`paas-ai-microphoneIcon ${
                    listening ? "listening" : ""
                  }`}
                  data-testid="paas-ai-microphone-btn"
                  onClick={onClickMicrophone}
                  disabled={
                    isTyping ||
                    isLoading ||
                    isMicDisabled ||
                    currentConversation.isDisabled
                  }
                >
                  <IconMicrophone />
                </button>
              </div>
            </div>
            <div className="paas-ai-warning-text">
              {automaticDeleteWarningText}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PaasAiMessages;
