import React, { useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { ActiveModal, ModalType } from "./interfaces/interfaces";

const Modal = ({
  props,
  activeModal,
  emitCloseModal,
}: {
  props: any;
  activeModal: ActiveModal;
  emitCloseModal: (
    isModalConfirmed: any,
    modalType: ModalType,
    modalData: string
  ) => void;
}) => {
  let modalWrapperRef: any;
  const [disclaimerTitle] = useState("Terms of Use");

  const [howToUseTitle] = useState("How to Use");
  const [howToUseText] = useState(
    props?.rendering?.fields?.HowToUseText?.value
  );

  const [renameTitle] = useState("Rename Conversation");
  const [renameButton] = useState("Rename");
  const [renameText, setRenameText] = useState(activeModal.modalData);

  const [deleteTitle] = useState("Delete Conversation");
  const [deleteButton] = useState("Delete");
  const [deleteText] = useState(
    'This will delete "' + activeModal.modalData + '". Do you want to continue?'
  );

  const [deleteAllTitle] = useState("Delete All Conversations");
  const [deleteAllButton] = useState("Delete All");
  const [deleteAllText] = useState(
    "This will permanently delete all of your conversations."
  );

  const [termsOfUseTitle] = useState("Terms of Use");
  const [termsOfUseButton] = useState("Continue");
  const [termsOfUseText] = useState(
    props?.rendering?.fields?.TermsOfUseText?.value
  );
  const [termsOfUseConfirmationText] = useState("I agree to the Terms of Use");
  const [termsOfAgreementChecked, setTermsOfAgreementChecked] = useState(false);

  useEffect(() => {
    document.addEventListener("mousedown", handleOffScreenClick);
    return () => {
      document.removeEventListener("mousedown", handleOffScreenClick);
    };
  }, []);

  // Click off screen to cancel and close modal
  const handleOffScreenClick = (e: any) => {
    if (
      e.target.className.includes("overlay") &&
      activeModal.modalType !== ModalType.termsOfUse
    ) {
      closeModal(false);
    }
  };

  // Submit or cancel modal and return to chatbox
  const closeModal = (isConfirmed: boolean) => {
    emitCloseModal(isConfirmed, activeModal.modalType, renameText);
  };

  // SET modal header title
  const getModalTitle = () => {
    switch (activeModal.modalType) {
      case ModalType.delete:
        return deleteTitle;
      case ModalType.deleteAll:
        return deleteAllTitle;
      case ModalType.rename:
        return renameTitle;
      case ModalType.disclaimer:
        return disclaimerTitle;
      case ModalType.use:
        return howToUseTitle;
      default:
        return termsOfUseTitle;
    }
  };

  // SET modal body text
  const getModalText = () => {
    switch (activeModal.modalType) {
      case ModalType.delete:
        return deleteText;
      case ModalType.deleteAll:
        return deleteAllText;
      case ModalType.rename:
        return renameText;
      case ModalType.disclaimer:
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: termsOfUseText || "",
            }}
          />
        );
      case ModalType.use:
        return howToUseText;
      default:
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: termsOfUseText || "",
            }}
          />
        );
    }
  };

  // SET submit button text
  const GetSubmitButtonText = () => {
    switch (activeModal.modalType) {
      case ModalType.delete:
        return deleteButton;
      case ModalType.deleteAll:
        return deleteAllButton;
      case ModalType.rename:
        return renameButton;
      default:
        return termsOfUseButton;
    }
  };

  // Allows user to hit enter to submit message
  const handleKeyPress = (e: any) => {
    if (e.key === "Enter") {
      closeModal(true);
    }
  };

  const modalContent = (
    <div
      ref={modalWrapperRef}
      className={`modal-overlay ${activeModal.needConfirm ? "" : "info"}`}
    >
      <div className={`modal ${activeModal.needConfirm ? "" : "info"}`}>
        <div className={`modal-header`}>
          <div className={`modal-title`}>{getModalTitle()}</div>
          {activeModal.modalType === ModalType.termsOfUse ? (
            ""
          ) : (
            <button
              data-testid="modal-x-button"
              className={`modal-x-button ${
                activeModal?.needConfirm ? "" : "info"
              }`}
              onClick={() => closeModal(false)}
            >
              <span className="material-symbols-outlined">close</span>
            </button>
          )}
        </div>

        {activeModal.modalType === ModalType.rename ? (
          <div className="modal-body">
            <input
              autoFocus
              data-testid="rename-input"
              className="modal-text input"
              type="text"
              maxLength={1000}
              value={renameText}
              placeholder={getModalText()}
              onChange={(e) => setRenameText(e.target.value)}
              onKeyDown={handleKeyPress}
            />
          </div>
        ) : (
          <div
            className={`modal-body ${activeModal.needConfirm ? "" : "info"}`}
          >
            <div
              className={`modal-text ${activeModal.needConfirm ? "" : "info"}`}
            >
              {getModalText()}
            </div>
          </div>
        )}

        {activeModal?.needConfirm ? (
          <div
            className={`modal-footer${
              activeModal.modalType === ModalType.termsOfUse
                ? " termsOfUse"
                : ""
            }`}
          >
            {activeModal.modalType === ModalType.termsOfUse ? (
              <>
                <div className="termsOfUse">
                  <input
                    data-testid="termsOfUseCheckbox"
                    id="check-terms"
                    type="checkbox"
                    checked={termsOfAgreementChecked}
                    onChange={() =>
                      setTermsOfAgreementChecked(!termsOfAgreementChecked)
                    }
                  />
                  <label htmlFor="check-terms">
                    {termsOfUseConfirmationText}
                  </label>
                </div>
                <button
                  disabled={!termsOfAgreementChecked}
                  onClick={() => closeModal(true)}
                  className="modal-submit-button"
                  data-testid="modal-submit-button"
                >
                  {GetSubmitButtonText()}
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => closeModal(false)}
                  className="modal-cancel-button"
                  data-testid="modal-cancel-button"
                >
                  Cancel
                </button>
                <button
                  onClick={() => closeModal(true)}
                  className="modal-submit-button"
                  data-testid="modal-submit-button"
                >
                  {GetSubmitButtonText()}
                </button>
              </>
            )}
          </div>
        ) : (
          ""
        )}
      </div>
    </div>
  );

  const modalRoot =
    document.getElementById("modal-root") || document.createElement("div");
  return ReactDOM.createPortal(modalContent, modalRoot);
};

export default Modal;
