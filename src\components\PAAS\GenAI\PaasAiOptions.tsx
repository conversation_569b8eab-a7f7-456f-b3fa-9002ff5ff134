import React, { useEffect } from "react";
import { ModalType } from "./interfaces/interfaces";

const PaasAiOptions = ({
  isTyping,
  isLoading,
  elementRef,
  onClose,
}: {
  isTyping: boolean;
  isLoading: boolean;
  elementRef: any;
  onClose: (modalType: ModalType | null) => void;
}) => {
  // Listener for off screen click
  useEffect(() => {
    document.addEventListener("mousedown", handleOffScreenClick);
    return () => {
      document.removeEventListener("mousedown", handleOffScreenClick);
    };
  }, []);

  // Click off screen to cancel and close modal
  const handleOffScreenClick = (e: any) => {
    if (!e.target.className.includes("options")) {
      onClose(null);
    }
  };

  // Return if options menu displays above or below tab
  const getOptionsPosition = () => {
    const panelHeight = document
      .getElementById("paas-ai-left-panel-body")
      ?.getBoundingClientRect().bottom;
    const elementPosition = elementRef?.getBoundingClientRect().top;
    const difference = panelHeight ? panelHeight - elementPosition : 0;
    return difference > 150 ? "show-below" : "show-above";
  };

  return (
    <div className={`options-container ${getOptionsPosition()}`}>
      {/* <div>
          <button
              data-testid="paas-ai-pdf-button"
              onClick={() => onClose(ModalType.pdf)}
              disabled={isTyping || isLoading}>
              <span className="material-symbols-outlined">file_download</span>
          </button>
          Download PDF
          </div> */}

      <div className="options">
        <button
          data-testid="paas-ai-rename-button"
          onClick={() => onClose(ModalType.rename)}
          disabled={isTyping || isLoading}
        >
          <span className="material-symbols-outlined options">edit</span>
        </button>
        Rename
      </div>

      <div className="options">
        <button
          className="deleteButton"
          data-testid="paas-ai-delete-button"
          onClick={() => onClose(ModalType.delete)}
          disabled={isTyping || isLoading}
        >
          <span className="material-symbols-outlined options">delete</span>
        </button>
        Delete
      </div>
    </div>
  );
};

export default PaasAiOptions;
