import React, { useEffect, useRef, useState } from "react";
import {
  ActiveModal,
  ChatResponse,
  Conversation,
  ConversationList,
  ConversationTab,
  DateCategory,
  ModalType,
} from "./interfaces/interfaces";
import { deleteConversation, renameConversation } from "./PaasAiService";
import Modal from "./PaasAiModal";
import PaasAiOptions from "./PaasAiOptions";

const PaasAiTabs = ({
  props,
  accessToken,
  isTyping,
  isLoading,
  isUserDataLoading,
  isTermsOfUseAccepted,
  currentConversation,
  conversationList,
  emitConversationChange,
  emitTermsOfUseChange,
}: {
  props: any;
  accessToken: string;
  isTyping: boolean;
  isLoading: boolean;
  isUserDataLoading: boolean;
  isTermsOfUseAccepted: boolean;
  currentConversation: Conversation;
  conversationList: ConversationList;
  emitConversationChange: (type: ModalType, data: string) => void;
  emitTermsOfUseChange: () => void;
}) => {
  const elementRef = useRef(null);
  const [isOptionsOpen, setIsOptionsOpen] = useState(false);
  const [activeModal, setActiveModal] = useState<ActiveModal>(
    {} as ActiveModal
  );
  const [conversationTabs, setConversationTabs] = useState<ConversationTab>([]);
  const canDeleteAll = conversationTabs.some(
    (tab) => tab.conversationId !== ""
  );

  // Text placeholders
  const [newConversationText] = useState(
    props.rendering.fields.NewConversationText.value
  );
  const [disclaimerButtonText] = useState("Disclaimers");
  const [howToUseButtonText] = useState("How to Use");

  // Track Terms of Agreement acceptance
  useEffect(() => {
    if (isTermsOfUseAccepted) return;
    if (!isTermsOfUseAccepted && !isUserDataLoading)
      openModal(ModalType.termsOfUse);
  }, [isTermsOfUseAccepted, isUserDataLoading]);

  // Update tabs whenever conversationList changes
  useEffect(() => {
    if (
      conversationList &&
      conversationList.length > 0 &&
      conversationList[0].userId
    ) {
      setTabs();
    }
  }, [conversationList]);

  // Date categorization logic
  const getDateCategory = (date: Date): DateCategory => {
    const today = new Date();
    const oneDay = 1000 * 60 * 60 * 24;

    const localDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const itemDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );

    const diffDays = Math.floor(
      (localDate.getTime() - itemDate.getTime()) / oneDay
    );

    if (diffDays === 0) return DateCategory.today;
    if (diffDays <= 7) return DateCategory.lastSevenDays;
    if (diffDays <= 30) return DateCategory.lastThirtyDays;
    return DateCategory.older;
  };
  const setTabs = () => {
    const tabList = conversationList
      .filter(
        (c) =>
          !(
            c.isCurrent === false &&
            (!c.conversationId || c.conversationId === "")
          )
      )
      .map((c) => {
        return {
          name: c.name?.trim(),
          conversationId: c.conversationId,
          datetime: c.datetime,
          dateCategory: getDateCategory(c.datetime),
          isActive: c.isCurrent,
        };
      })
      .sort((a, b) => b.datetime.getTime() - a.datetime.getTime());

    // Move active tab to top
    tabList.sort((a, b) => {
      if (a.isActive && !b.isActive) return -1;
      if (!a.isActive && b.isActive) return 1;
      return 0;
    });

    setConversationTabs(tabList);
  };

  // Open Modal pop up for rename, delete, disclaimer, how to use
  const openModal = (modalType: ModalType) => {
    setActiveModal({
      isOpen: true,
      modalData: currentConversation?.name,
      modalType: modalType,
      needConfirm:
        modalType === ModalType.delete ||
        modalType === ModalType.rename ||
        modalType === ModalType.deleteAll ||
        modalType === ModalType.termsOfUse,
    });
  };

  // Triggered when modal closes
  const processModalRequest = (
    isModalConfirmed: any,
    modalType: ModalType,
    modalData: any
  ) => {
    // Delete current conversation
    const deleteCurrentConversation = async () => {
      try {
        if (currentConversation) {
          const res = await deleteConversation(
            currentConversation.userId,
            currentConversation.conversationId,
            accessToken
          );
          if (res) {
            emitConversationChange(
              ModalType.delete,
              currentConversation.conversationId
            );
          }
        }
      } catch (error) {
        console.error("Error processing message:", error);
      }
    };

    // Delete all conversations
    const deleteAllConversations = async () => {
      try {
        if (currentConversation) {
          const res = await deleteConversation(
            currentConversation.userId,
            "",
            accessToken
          );
          if (res) emitConversationChange(ModalType.deleteAll, "");
        }
      } catch (error) {
        console.error("Error processing message:", error);
      }
    };

    // Rename current conversation
    const renameCurrentConversation = async (newName: string) => {
      try {
        if (currentConversation) {
          const updateTabName = (tab: any, newName: string) => {
            return tab.conversationId === currentConversation.conversationId
              ? { ...tab, name: newName }
              : tab;
          };
          setConversationTabs(
            conversationTabs.map((tab) => updateTabName(tab, newName))
          );

          const res: ChatResponse = await renameConversation(
            currentConversation.userId,
            currentConversation.conversationId,
            newName,
            accessToken
          );
          if (res) {
            emitConversationChange(ModalType.rename, newName);
          }
        }
      } catch (error) {
        console.error("Error processing message:", error);
      }
    };

    const updateTermsOfUse = async () => {
      emitTermsOfUseChange();
    };

    setActiveModal((prevModal) => ({ ...prevModal, isOpen: false }));
    if (!isModalConfirmed) return;
    switch (modalType) {
      case ModalType.delete:
        deleteCurrentConversation();
        break;
      case ModalType.deleteAll:
        deleteAllConversations();
        break;
      case ModalType.rename:
        renameCurrentConversation(modalData);
        break;
      case ModalType.termsOfUse:
        updateTermsOfUse();
        break;
      default:
        break;
    }
  };

  // Display tabs separated by Date Category
  const getTabsByDateCategory = (category: DateCategory) => {
    const downloadPDF = () => {
      // Implement the PDF download logic here
      console.log("Downloading PDF...");
    };

    const processOptionsRequest = (modalType: ModalType) => {
      setIsOptionsOpen(false);
      switch (modalType) {
        case ModalType.delete:
          openModal(ModalType.delete);
          break;
        case ModalType.rename:
          openModal(ModalType.rename);
          break;
        case ModalType.pdf:
          downloadPDF();
          break;
        default:
          break;
      }
    };

    const getTabs = (category: DateCategory) => {
      if (
        conversationTabs.filter((tab) => tab.dateCategory === category)
          .length === 0
      ) {
        return;
      }

      const currentTabs = conversationTabs.filter(
        (tab) => tab.dateCategory === category
      );
      return (
        <div className="paas-ai-tab-container">
          <div className="category">{category}</div>
          {currentTabs.map((tab) => (
            <div
              key={tab.conversationId + tab.isActive}
              className={`tab ${tab.isActive ? "active" : ""}`}
            >
              <button
                data-testid={tab.conversationId + tab.isActive}
                onClick={() =>
                  emitConversationChange(ModalType.tab, tab.conversationId)
                }
                disabled={isTyping || isLoading}
              >
                <div>{tab.name}</div>
              </button>

              {tab.isActive && tab.name !== "New Conversation" ? (
                <div ref={elementRef} className="options">
                  <button
                    data-testid="paas-ai-options-button"
                    disabled={isTyping || isLoading}
                    onClick={() => setIsOptionsOpen(!isOptionsOpen)}
                  >
                    <span className="material-symbols-outlined options">
                      more_horiz
                    </span>
                  </button>
                  {isOptionsOpen && (
                    <PaasAiOptions
                      elementRef={elementRef.current}
                      isTyping={isTyping}
                      isLoading={isLoading}
                      onClose={(modalType: ModalType) =>
                        processOptionsRequest(modalType)
                      }
                    />
                  )}
                </div>
              ) : null}
            </div>
          ))}
        </div>
      );
    };

    return <div>{getTabs(category)}</div>;
  };

  return (
    <>
      <div id="modal-root"></div>
      <div>
        {activeModal.isOpen && (
          <Modal
            props={props}
            activeModal={activeModal}
            emitCloseModal={(
              isModalConfirmed: any,
              modalType: ModalType,
              modalData: string
            ) => processModalRequest(isModalConfirmed, modalType, modalData)}
          />
        )}
      </div>
      <div className="paas-ai-left-panel">
        <button
          className="paas-ai-new-conversation-button"
          data-testid="paas-ai-new-conversation-button"
          onClick={() => emitConversationChange(ModalType.new, "")}
          disabled={isTyping || isLoading}
        >
          <div className="paas-ai-conversation-button-plus">+</div>
          <div className="paas-ai-conversation-button-text">
            {newConversationText}
          </div>
        </button>
        <div id="paas-ai-left-panel-body" className="paas-ai-left-panel-body">
          {conversationTabs.length === 0 ? (
            ""
          ) : (
            <div className="paas-ai-tabs-header">
              <div>Your Conversations</div>
              {canDeleteAll ? (
                <button
                  data-testid="paas-ai-delete-all-button"
                  disabled={isTyping || isLoading}
                  onClick={() => openModal(ModalType.deleteAll)}
                >
                  Delete All
                </button>
              ) : (
                ""
              )}
            </div>
          )}
          <div className="paas-ai-tabs-body">
            {getTabsByDateCategory(DateCategory.today)}
            {getTabsByDateCategory(DateCategory.lastSevenDays)}
            {getTabsByDateCategory(DateCategory.lastThirtyDays)}
            {getTabsByDateCategory(DateCategory.older)}
          </div>
        </div>
        <div className="paas-ai-left-panel-footer">
          <button
            data-testid="paas-ai-how-to-use-button"
            disabled={isTyping || isLoading}
            className="paas-ai-left-panel-footer-btn"
            onClick={() => openModal(ModalType.use)}
          >
            {howToUseButtonText}
          </button>
          <span className="paas-ai-separator"></span>
          <button
            data-testid="paas-ai-disclaimer-button"
            disabled={isTyping || isLoading}
            className="paas-ai-left-panel-footer-btn"
            onClick={() => openModal(ModalType.disclaimer)}
          >
            {disclaimerButtonText}
          </button>
        </div>
      </div>
    </>
  );
};

export default PaasAiTabs;
