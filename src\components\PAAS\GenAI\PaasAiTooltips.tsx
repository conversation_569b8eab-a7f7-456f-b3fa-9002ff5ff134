import React, { useRef, useState } from "react";
import {
  ButtonTypes,
  Conversation,
  ActiveButton,
  Message,
} from "./interfaces/interfaces";
import { rateMsg } from "./PaasAiService";
import PaasAiFeedback from "./PaasAiFeedback";

const PaasAiToolTips = ({
  accessToken,
  index,
  message,
  isTyping,
  isLoading,
  activeConversation,
  emitRatingsChange,
}: {
  accessToken: string;
  index: number;
  message: Message;
  isTyping: boolean;
  isLoading: boolean;
  activeConversation: Conversation;
  emitRatingsChange: (messageIndex: number, rating: number) => void;
}) => {
  const buttonRef = useRef<HTMLInputElement>(null);
  const elementRef = useRef(null);
  const [activeButton, setActiveButton] = useState<ActiveButton>(
    {} as ActiveButton
  );

  const getToolTipText = (button: ButtonTypes, index: number) => {
    const active =
      activeButton?.isClicked &&
      activeButton?.button === button &&
      activeButton?.index === index;

    switch (button) {
      case "copy":
        return active ? "Copied" : "Copy";
      case "upvote":
        return active ? "Thank you for your feedback!" : "Good Response";
      case "downvote":
        if (active && activeButton.isSubmitted) {
          return "Thank you for your feedback!";
        } else if (active) {
          return "Why did you choose this rating? (optional)";
        } else {
          return "Bad Response";
        }
      default:
        return "";
    }
  };

  // Rate message Good or Bad, and pass optional feedback for Bad rating
  const rateMessage = async (
    msgIndex: number,
    rating: number,
    comment: string
  ) => {
    try {
      emitRatingsChange(msgIndex, rating);
      await rateMsg(
        activeConversation.userId,
        activeConversation.conversationId,
        msgIndex - 1,
        rating,
        comment,
        accessToken
      );
    } catch (error) {
      console.error("Error rating message:", error);
      emitRatingsChange(msgIndex, 0);
    }
  };

  const isActiveTooltip = (button: ButtonTypes, index: number) => {
    return (
      activeButton?.isHovered &&
      activeButton?.button === button &&
      activeButton?.index === index
    );
  };

  const isDownvoteFeedback = (button: ButtonTypes, index: number) => {
    return (
      activeButton.isClicked &&
      !activeButton?.isSubmitted &&
      activeButton?.button === button &&
      activeButton?.index === index
    );
  };

  const isFeedbackOpen = () => {
    return (
      activeButton.button === ButtonTypes.downvote &&
      activeButton.isClicked &&
      !activeButton.isSubmitted
    );
  };

  const isButtonClicked = (button: ButtonTypes, index: number) => {
    return (
      activeButton?.isClicked &&
      activeButton?.button === button &&
      activeButton?.index === index
    );
  };

  // onclick - Copy the bot response to clipboard
  const copyMessage = (msgIndex: number) => {
    const message = activeConversation.content.find(
      (_content, index) => index === msgIndex - 1
    )!;
    const sourceLinks = message?.metadata || message?.source;
    let formattedSources = "";

    if (sourceLinks && sourceLinks.length > 0) {
      formattedSources += "PAAS Source:\n\n";
      formattedSources += sourceLinks
        .map((source) => source.title + "\n")
        .join("");
    }

    navigator.clipboard.writeText(
      (message?.userMsg ?? "") +
        "\n\n" +
        (message?.botMsg ?? "") +
        "\n\n" +
        formattedSources
    );
  };

  const onFeedbackChange = (isSubmitted: boolean, feedbackInput: string) => {
    if (isSubmitted) {
      setActiveButton({
        button: ButtonTypes.downvote,
        index: activeButton.index,
        isHovered: true,
        isClicked: true,
        isSubmitted: true,
      });
      rateMessage(activeButton.index, -1, feedbackInput);
      setTooltipTimeout();
    } else {
      setActiveButton({
        button: activeButton.button,
        index: 0,
        isHovered: false,
        isClicked: false,
        isSubmitted: false,
      });
    }
  };

  // Fade feedback after 1 second
  const setTooltipTimeout = () => {
    const resetActiveButton = () => {
      setActiveButton({
        button: activeButton.button,
        index: 0,
        isHovered: false,
        isClicked: false,
        isSubmitted: false,
      });
    };

    const tooltipTimeout = setTimeout(function () {
      resetActiveButton();
    }, 1000);
    return () => clearTimeout(tooltipTimeout);
  };

  const getTooltips = () => {
    const getActiveTooltip = () => {
      if (activeButton.index !== index) return;
      switch (activeButton?.button) {
        case ButtonTypes.downvote:
          return getDownvoteTooltip();

        case ButtonTypes.upvote:
          return getUpvoteTooltip();

        case ButtonTypes.copy:
          return getCopyTooltip();
        default:
          return;
      }
    };
    return (
      <div className="paas-ai-tooltips-container">{getActiveTooltip()}</div>
    );
  };

  const getDownvoteTooltip = () => {
    if (isDownvoteFeedback(ButtonTypes.downvote, index)) {
      return (
        <PaasAiFeedback
          elementRef={elementRef.current}
          emitFeedbackChange={(isSubmitted: boolean, feedbackInput: string) =>
            onFeedbackChange(isSubmitted, feedbackInput)
          }
        />
      );
    } else {
      if (!isActiveTooltip(ButtonTypes.downvote, index)) return;
      return (
        <div
          ref={buttonRef}
          className={`downvote-tooltip ${getFeedbackPosition()} 
                                          ${
                                            isButtonClicked(
                                              ButtonTypes.downvote,
                                              index
                                            )
                                              ? `downvote-tooltip-active`
                                              : ""
                                          }`}
        >
          {getToolTipText(ButtonTypes.downvote, index)}
        </div>
      );
    }
  };

  const getUpvoteTooltip = () => {
    if (!isActiveTooltip(ButtonTypes.upvote, index)) return;
    return (
      <div
        ref={buttonRef}
        className={`upvote-tooltip ${getFeedbackPosition()} ${
          isButtonClicked(ButtonTypes.upvote, index)
            ? `upvote-tooltip-active`
            : ""
        }`}
      >
        {getToolTipText(ButtonTypes.upvote, index)}
      </div>
    );
  };

  const getCopyTooltip = () => {
    if (!isActiveTooltip(ButtonTypes.copy, index)) return;
    return (
      <div
        ref={buttonRef}
        className={`copy-tooltip ${getFeedbackPosition()} ${
          isButtonClicked(ButtonTypes.copy, index) ? `copy-tooltip-active` : ""
        }`}
      >
        {getToolTipText(ButtonTypes.copy, index)}
      </div>
    );
  };

  // Return if options menu displays above or below tab
  const getFeedbackPosition = () => {
    const panelHeight = document
      .getElementById("paas-ai-middle-panel")
      ?.getBoundingClientRect().bottom;
    const elementPosition = buttonRef?.current?.getBoundingClientRect().top;
    const difference =
      panelHeight && elementPosition ? panelHeight - elementPosition : 200;
    return difference < 200 ? "show-above" : "show-below";
  };

  const getRatingButtons = (
    buttonType: ButtonTypes,
    index: number,
    rating: number
  ) => {
    const getRatingIcon = () => {
      switch (buttonType) {
        case ButtonTypes.copy:
          return isButtonClicked(buttonType, index) ? (
            <span className="material-symbols-outlined">check</span>
          ) : (
            <span className="material-symbols-outlined">content_copy</span>
          );
        case ButtonTypes.downvote:
          return <span className="material-symbols-outlined">thumb_down</span>;
        default:
          return <span className="material-symbols-outlined">thumb_up</span>;
      }
    };

    const getClassName = () => {
      let className = "rating-button";
      switch (buttonType) {
        case ButtonTypes.downvote:
          className += " downvote";
          className += rating < 0 ? " downvote-selected" : "";
          break;
        case ButtonTypes.upvote:
          className += " upvote";
          className += rating > 0 ? " upvote-selected" : "";
          break;
      }
      return className;
    };

    const isDisabled = () => {
      return (
        (isTyping ||
          isLoading ||
          isFeedbackOpen() ||
          (buttonType == ButtonTypes.downvote && message.rating !== 0) ||
          (buttonType == ButtonTypes.upvote && message.rating !== 0)) &&
        buttonType !== ButtonTypes.copy
      );
    };

    // After button has been clicked, fade feedback after 3 seconds
    const onButtonClick = (buttonType: ButtonTypes, index: number) => {
      if (buttonType === ButtonTypes.upvote) rateMessage(index, 1, "");
      if (buttonType === ButtonTypes.copy) copyMessage(index);

      setActiveButton({
        button: buttonType,
        index: index,
        isHovered: true,
        isClicked: true,
        isSubmitted: false,
      });
      if (buttonType !== ButtonTypes.downvote) setTooltipTimeout();
    };

    // On hover, display tooltip hint; on leave, remove hint
    const onMouseHover = (isHovered: boolean) => {
      if (activeButton.isClicked) {
        if (
          !(
            activeButton.button == ButtonTypes.downvote &&
            !activeButton.isSubmitted
          )
        ) {
          setTooltipTimeout();
        }
        return;
      }

      setActiveButton({
        button: buttonType,
        index,
        isHovered: isHovered,
        isClicked: false,
        isSubmitted: false,
      });
    };

    return (
      <button
        disabled={isDisabled()}
        className={getClassName()}
        data-testid={getClassName()}
        onMouseEnter={() => {
          onMouseHover(true);
        }}
        onMouseLeave={() => {
          onMouseHover(false);
        }}
        onClick={() => onButtonClick(buttonType, index)}
      >
        {getRatingIcon()}
      </button>
    );
  };

  return (
    <div ref={elementRef} className="paas-ai-ratings-container">
      {getRatingButtons(ButtonTypes.copy, index, message.rating)}
      {getRatingButtons(ButtonTypes.upvote, index, message.rating)}
      {getRatingButtons(ButtonTypes.downvote, index, message.rating)}
      {getTooltips()}
    </div>
  );
};

export default PaasAiToolTips;
