export type Conversation = {
  isCurrent: boolean;
  userId: string;
  conversationId: string;
  isDisabled: boolean;
  name: string;
  datetime: Date;
  content: {
    userMsg: string;
    botMsg: string;
    time: Date;
    rating: number;
    metadata?: Source[]
    source?:Source[]
  }[];
};

export type Source = {
  section: string;
  docType: string;
  itemId: string;
  jurisdiction: string;
  lob: string;
  source: string;
  title: string;
  updateDate: string;
}

export type ConversationList = {
  isCurrent: boolean;
  userId: string;
  conversationId: string;
  isDisabled: boolean;
  name: string;
  datetime: Date;
  content: {
    userMsg: string;
    botMsg: string;
    time: Date;
    rating: number;
  }[];
}[];

export type ChatResponse = {
  answer: string;
  conversationId: string;
  isDisabled: boolean;
  metadata: {
    source:Source[]
    title: string;
    docType: string;
    itemId: string;
    updateDate: Date;
  }[];
};

export type ConversationTab = {
  name: string;
  conversationId: string;
  datetime: Date;
  dateCategory: DateCategory;
  isActive: boolean;
}[];

export enum DateCategory {
  today = "Today",
  lastSevenDays = "Last 7 days",
  lastThirtyDays = "Last 30 days",
  older = "Older",
}

export type ActiveModal = {
  isOpen: boolean;
  needConfirm: boolean;
  modalType: ModalType;
  modalData: string;
};

export enum ModalType {
  delete = "delete",
  deleteAll = "delete all",
  rename = "rename",
  disclaimer = "disclaimer",
  use = "use",
  termsOfUse = "termsOfUse",
  pdf = "pdf",
  new = "new",
  tab = "tab",
}

export type Message = {
  userMessage: string;
  botMessage: string;
  rating: number;
  suggestions?: string[];
};

export type ActiveButton = {
  button: ButtonTypes;
  index: number;
  isHovered: boolean;
  isClicked: boolean;
  isSubmitted: boolean;
};

export enum ButtonTypes {
  copy = "copy",
  upvote = "upvote",
  downvote = "downvote",
  submit = "submit",
  voiceToText = "voiceToText",
}
