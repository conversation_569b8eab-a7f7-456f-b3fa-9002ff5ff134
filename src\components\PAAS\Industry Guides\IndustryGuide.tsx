import {
  RichText,
  Text,
  Image,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/authContext";
import { IndustryContent } from "./IndustryGuidesContent";
import React, { useContext, useMemo } from "react";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
const IndustryGuide = (props: any): JSX.Element => {
  const router = useRouter();
  const queryParameter = router.query;
  let selectedCustomerNumber = useGetSelectedCustomerNumber();
  const { paasEntitlement } = useContext(AuthContext);
  const onIndustryGuideClick = (industryGuide: any) => {
    router.push(
      `/PAAS/industryguide/?id=${industryGuide?.fields?.Heading[0]?.id
        ?.toUpperCase()
        .replace(/[{()}]/g, "")}`
    );
  };

  const filteredIndustryGuides = useMemo(() => {
    return props?.fields?.IndustryGuideCategory.filter((industryGuide: any) => {
      return industryGuide?.fields?.Lob.some((lob: any) =>
        paasEntitlement
          ?.find(
            (customer: any) =>
              customer?.customerNumber === selectedCustomerNumber
          )
          ?.customerPAASParticipation?.includes("LOB_" + lob?.name)
      );
    });
  }, [
    props?.fields?.IndustryGuideCategory,
    paasEntitlement,
    selectedCustomerNumber,
  ]);

  return (
    <React.Fragment>
      {!queryParameter.hasOwnProperty("id") ? (
        <section className="hover-lift-cards industry-guides">
          <div className="site">
            <Text
              field={props?.fields?.Heading}
              tag="h2"
              className="no-margin-bottom"
            />
            <RichText field={props?.fields?.Description} tag="p" />
            <section className="results-listing">
              <div className="cards flex-wrapper">
                {filteredIndustryGuides.map((industryGuide: any) => (
                  <article
                    className="card"
                    key={industryGuide?.fields?.Heading[0]?.id}
                  >
                    <a
                      onClick={() => onIndustryGuideClick(industryGuide)}
                      data-testid="industry-guide"
                    >
                      <Image field={industryGuide?.fields?.Image} />
                      <div>
                        <Text
                          field={
                            industryGuide?.fields?.Heading[0]?.fields?.Title
                          }
                          tag="h3"
                          className="no-margin-bottom"
                        />
                        <RichText
                          field={industryGuide?.fields?.Description}
                          tag="p"
                        />
                      </div>
                    </a>
                  </article>
                ))}
              </div>
            </section>
          </div>
        </section>
      ) : (
        <IndustryContent
          industryGuideCategory={props?.fields?.IndustryGuideCategory}
        />
      )}
    </React.Fragment>
  );
};

export default withDatasourceCheck()(IndustryGuide);
