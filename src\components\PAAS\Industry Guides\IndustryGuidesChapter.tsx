import { useRouter } from "next/router";
import { useEffect, useRef, useState } from "react";

export const IndustryGuideChapter = (props: any) => {
  const [openDetails, setOpenDetails] = useState(false);
  const { industry, parent, setParent, index, chapterShortcutSelected } = props;
  const [itemIndex, setItemIndex] = useState(index);
  const scrollContainer = useRef<HTMLDivElement>(null);
  const chapterScrollContainer = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const queryParameter = router.query;

  useEffect(() => {
    if (parent === industry?.ItemID) {
      setOpenDetails(true);
    }
  }, [parent, industry?.ItemID]);

  useEffect(() => {
    if (
      !queryParameter.chapterid
        ? itemIndex === 0
        : queryParameter.chapterid === industry?.ItemID.replace(/[{()}]/g, "")
    ) {
      setOpenDetails(true);
    }
  }, [queryParameter, industry?.ItemID, itemIndex]);

  useEffect(() => {
    if (!scrollContainer.current) return;

    scrollContainer.current.scrollIntoView({
      block: "center",
    });
    //mounting to let accordion load
    const timer = setTimeout(() => {
      if (scrollContainer.current) {
        scrollContainer.current.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
      }
    }, 1);

    // unmount
    return () => clearTimeout(timer);
  }, [chapterShortcutSelected, scrollContainer]);

  useEffect(() => {
    if (!chapterScrollContainer.current) return;

    chapterScrollContainer.current.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  }, []);

  return (
    <>
      <div className="accordion">
        <div
          className={openDetails ? "accordionTab active" : "accordionTab"}
          role="button"
          aria-expanded="false"
          aria-controls="accordion-content-0"
          onClick={() => {
            setOpenDetails(!openDetails);
            setParent("");
            setItemIndex(null);
          }}
          ref={
            queryParameter.chapterid === industry?.ItemID.replace(/[{()}]/g, "")
              ? scrollContainer
              : null
          }
        >
          <h2>{industry?.Title}</h2>
          <span className="material-symbols-outlined icon">expand_more</span>
        </div>
        <div
          className={
            openDetails ? "accordionContent active" : "accordionContent"
          }
        >
          <div className="flex-wrap">
            {industry?.SectionList?.map((section: any, index: any) => {
              return (
                <section key={index}>
                  <h3
                    className="bg-gray"
                    id={`${section?.Heading.trim()} ${industry?.ItemID.replace(
                      /[{()}]/g,
                      ""
                    )}`}
                    ref={
                      chapterShortcutSelected ===
                      `${section?.Heading.trim()} ${industry?.ItemID.replace(
                        /[{()}]/g,
                        ""
                      )}`
                        ? scrollContainer
                        : null
                    }
                  >
                    {section?.Heading}
                  </h3>
                  <p
                    dangerouslySetInnerHTML={{
                      __html: section?.SectionText,
                    }}
                  ></p>
                </section>
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};
