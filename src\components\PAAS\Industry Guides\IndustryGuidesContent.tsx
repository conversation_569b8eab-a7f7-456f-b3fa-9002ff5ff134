import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/authContext";
import { getIndustryDataApi } from "./IndustryGuideDataService";
import { IndustryGuideChapter } from "./IndustryGuidesChapter";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
export const IndustryContent = (props: any) => {
  const { industryGuideCategory } = props;
  const router = useRouter();
  const queryParameter = router.query;
  const { accessToken } = useContext(AuthContext);
  const [industryData, setIndustryData] = useState<any>([]);
  const [chapterShortcutSelected, setChapterShortcutSelected] = useState("");
  const [chapterHeadingSelected, setChapterHeadingSelected] = useState("");
  const [parent, setParent] = useState("");
  let selectedCustomerNumber = useGetSelectedCustomerNumber();
  const { paasEntitlement } = useContext(AuthContext);

  useEffect(() => {
    getIndustryAPI();
  }, [queryParameter]);

  const getIndustryAPI = async () => {
    let isMounted = true;
    const payload = {
      IndustryGuideID: queryParameter?.id,
      ContentType: "Industry Guide Chapter",
    };
    try {
      let url: string = "";
      url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetIndustryGuides`;
      const post = await getIndustryDataApi(url, payload, accessToken);
      if (isMounted) {
        setIndustryData(post);
      }
    } catch (error) {
      console.error("industry api not working:", error);
    } finally {
      isMounted = false;
    }
  };

  const onBreadcrumbsClick = () => {
    router.push("/PAAS/industryguide");
  };

  const onIndustryGuideClick = (industryGuide: any) => {
    router.push(
      `/PAAS/industryguide/?id=${industryGuide?.fields?.Heading[0]?.id
        ?.toUpperCase()
        .replace(/[{()}]/g, "")}`
    );
  };

  const renderIndustryChapters = () => {
    return industryData?.IndustryGuideChapter?.map((industry: any) => {
      const { Title, ItemID, ItemName, SectionList } = industry;

      const isOpen = !queryParameter.chapterid
        ? industryData.IndustryGuideChapter[0] === industry
        : queryParameter.chapterid === ItemID.replace(/[{()}]/g, "");

      const handleSectionClick = (sectionHeading: string) => {
        setChapterShortcutSelected(
          `${sectionHeading.trim()} ${ItemID.replace(/[{()}]/g, "")}`
        );
        setChapterHeadingSelected(`${sectionHeading.trim()}`);
        setParent(ItemID);
      };
      return (
        <li className={Title} key={ItemID}>
          <details open={isOpen}>
            <summary>
              <div className="lines-item">{ItemName}</div>
            </summary>
            {SectionList?.map((section: any, index: any) => {
              const { Heading } = section;
              const isSelected = chapterHeadingSelected === Heading;

              return (
                <div
                  className={`btn-wrapper ${isSelected ? "selected" : ""}`}
                  onClick={() => handleSectionClick(Heading)}
                  key={index}
                  data-testid="shortcut-click"
                >
                  <a>{Heading}</a>
                </div>
              );
            })}
          </details>
        </li>
      );
    });
  };

  const renderIndustryGuides = () => {
    return industryGuideCategory?.map((industryGuide: any, index: any) => {
      const { fields } = industryGuide;
      const heading = fields?.Heading[0]?.fields;
      const industryId = fields?.Heading[0].id;
      const lobList = fields?.Lob;

      const hasAccess = lobList?.some((lob: any) =>
        paasEntitlement?.some(
          (customer: any) =>
            customer.customerNumber === selectedCustomerNumber &&
            customer.customerPAASParticipation?.includes("LOB_" + lob.name)
        )
      );

      if (!hasAccess) return null;

      return (
        <li key={index}>
          <label htmlFor={heading?.Title?.value}>
            <input
              name="IndustryGuides"
              type="radio"
              id={heading?.Title?.value}
              value=""
              checked={queryParameter.id === industryId?.toUpperCase()}
              onClick={() => onIndustryGuideClick(industryGuide)}
              data-testid="industry-guide-radio-button"
            />
            <span>{heading?.Title?.value}</span>
          </label>
        </li>
      );
    });
  };

  return (
    <div className="industry-guides">
      <div className="breadcrumbs">
        <nav>
          <a
            onClick={() => onBreadcrumbsClick()}
            data-testid="industry-guide-breadcrumbs"
          >
            Industry Guides
          </a>
          <strong>{industryData?.IndustryGuideName}</strong>
        </nav>
      </div>
      <div className="site flex-wrapper">
        <aside className="filter thin">
          <section className="with-show-more-filters background-lt-grey">
            <h2 className="filter-toggle-mobile" aria-controls="search-filters">
              Filters
              <a aria-label="Filter icon button" role="button">
                <span className="material-icons">filter_list</span>
              </a>
            </h2>
            <ul className="link-list link-list-closed">
              {renderIndustryChapters()}
              <li className="industry-guides">
                <details open={industryGuideCategory?.length > 0}>
                  <summary>
                    <div className="lines-item">Industry Guides</div>
                  </summary>
                  <form
                    className="radio-group"
                    aria-label="Content type filter options"
                  >
                    <fieldset>
                      <ul>{renderIndustryGuides()}</ul>
                    </fieldset>
                  </form>
                </details>
              </li>
            </ul>
          </section>
        </aside>
        <div className="content-wrapper">
          <div className="jurisdictional-info-content-header">
            <h2>{industryData?.IndustryGuideName}</h2>
          </div>
          <div className="flex-wrap">
            {industryData?.IndustryGuideChapter?.map((industry: any) => {
              return (
                <>
                  <IndustryGuideChapter
                    industry={industry}
                    parent={parent}
                    setParent={setParent}
                    index={industryData?.IndustryGuideChapter?.indexOf(
                      industry
                    )}
                    chapterShortcutSelected={chapterShortcutSelected}
                  />
                </>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
