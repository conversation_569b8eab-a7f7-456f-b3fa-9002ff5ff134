import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { useEffect, useState } from "react";
import StateShortcut from "./StateShortcut";
import AuditInformationSummaryInfo from "./AuditInformationSummaryInfo";

export const AuditInformationSummary = (props: any) => {
  const {
    AISHeader,
    AISInfo,
    selectedJsText,
    selectedJurisdictions,
    jurisdictionalInfoData,
    restProps,
    noExceptionsText,
    payrollLimitationsText,
    payrollLimitationsList,
    setLegalEntityType,
    auditSummaryText,
  } = props;

  const [clicked, setClicked] = useState("");
  const [pinned, setPinned] = useState(false);
  const [expandAll, setExpandAll] = useState(false);

  let jurisdictions: any = [];
  selectedJurisdictions.map((js: any) => {
    jurisdictions.push(js?.code);
  });

  useEffect(() => {
    const changePinnedState = () => {
      if (window.scrollY > 750 || window.scrollY < 550) {
        setPinned(true);
      } else {
        setPinned(false);
      }
    };
    window?.addEventListener("scroll", changePinnedState);
    return () => {
      window?.removeEventListener("scroll", changePinnedState);
    };
  }, []);

  const handlePayrollChange = (e: any) => {
    setLegalEntityType(e.target.value);
  };

  return (
    <>
      <div
        className={
          pinned
            ? "jurisdictional-info-content-header is-pinned"
            : "jurisdictional-info-content-header"
        }
      >
        <div className="flex-wrapper">
          <Text field={AISHeader} tag="h2" />
        </div>
        <RichText field={AISInfo} tag="p" />
        <div className="jurisdictional-info-skeleton jump-links">
          <div className="jurisdictional-info-skeleton-items-wrapper">
            <div className="jurisdictional-info-skeleton-items show">
              <p>
                <Text field={selectedJsText} tag="strong" />
              </p>
              <div className="selected-jurisdictions">
                {selectedJurisdictions
                  .sort((value1: any, value2: any) => {
                    const nameA = value1?.label?.toUpperCase();
                    const nameB = value2?.label?.toUpperCase();
                    if (nameA < nameB) {
                      return -1;
                    }
                    if (nameA > nameB) {
                      return 1;
                    }
                    return 0;
                  })
                  .map((state: any) => {
                    if (state.code === "CC") {
                      return;
                    } else
                      return (
                        <>
                          <StateShortcut
                            state={state}
                            setClicked={setClicked}
                            clicked={clicked}
                          />
                        </>
                      );
                  })}
              </div>
            </div>
            <div className="payroll-limitations">
              <label>
                <Text field={payrollLimitationsText} tag="strong" />
              </label>
              <div className="select-wrapper">
                <select onChange={(e: any) => handlePayrollChange(e)}>
                  <option value="All Types">All Types</option>
                  {payrollLimitationsList.map((payroll: any, index: any) => {
                    return (
                      <option value={payroll?.fields?.Code?.value} key={index}>
                        {payroll?.fields?.Name?.value}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
          </div>
        </div>
        {selectedJurisdictions.length===1? <></>: <div className="flex-wrapper">
          <a
            onClick={() => setExpandAll(!expandAll)}
            className={expandAll ? "expand-all open" : "expand-all closed"}
            data-testid="expand-all"
          >
            <span className="material-symbols-outlined">expand</span>
            <span className="expand-label">Expand all</span>
            <span className="collapse-label">Collapse all</span>
          </a>
        </div>}
      </div>
      <div className="flex-wrap">
        {jurisdictionalInfoData?.AuditInformationItem?.sort(
          (value1: any, value2: any) => {
            const nameA = value1?.JurisdictionCode[0]?.Name?.toUpperCase();
            const nameB = value2?.JurisdictionCode[0]?.Name?.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          }
        ).map((state: any, id: any) => {
          return (
            <div
              className={
                state?.ItemID === "NA" ? "accordion disabled" : "accordion"
              }
              key={id}
            >
              <AuditInformationSummaryInfo
                selectedJurisdictions={selectedJurisdictions}
                state={state}
                clicked={clicked}
                setClicked={setClicked}
                restProps={restProps}
                expandAll={expandAll}
                noExceptionsText={noExceptionsText}
                auditSummaryText={auditSummaryText}
                payrollLimitationsList={payrollLimitationsList}
              />
            </div>
          );
        })}
      </div>
    </>
  );
};
