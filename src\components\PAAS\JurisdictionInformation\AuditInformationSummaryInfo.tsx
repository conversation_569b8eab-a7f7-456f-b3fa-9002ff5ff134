import { useRef } from "react";
import useAccordionJumpLink from "../../../hooks/paas/useAccordionJumpLink";

const AuditInformationSummaryInfo = (props: any) => {
  const {
    state,
    clicked,
    setClicked,
    expandAll,
    auditSummaryText,
    selectedJurisdictions,
    payrollLimitationsList,
  } = props;
  const scrollContainer = useRef<HTMLDivElement>(null);
  const { active, handleClick } = useAccordionJumpLink(
    state?.JurisdictionCode[0]?.Code,
    60,
    clicked,
    expandAll,
    scrollContainer,
    selectedJurisdictions
  );
  const convertDate = (dateToConvert: any) => {
    const date = dateToConvert
      .substring(0, 8)
      .replace(/(.{4})(.{2})(.{2})/, "$2-$3-$1");
    return date;
  };

  const renderStateInfo = (state: any) => {
    return (
      <section className="jurisdictional-audit-info">
        {state?.ExecutiveOfficers !== null && (
          <div className="payroll">
            <h3>{payrollLimitationsList[0].fields.Name.value}</h3>
            <div className="table-wrapper">
              {state?.ExecutiveOfficers?.sort((value1: any, value2: any) => {
                const nameA = value1?.Lob[0]?.Name?.toUpperCase();
                const nameB = value2?.Lob[0]?.Name?.toUpperCase();
                if (nameA < nameB) {
                  return 1;
                }
                if (nameA > nameB) {
                  return -1;
                }
                return 0;
              }).map((data: any) => {
                const minperweekCheck =
                  data?.EffectiveAmounts.filter((amount: any) => {
                    return amount?.MinperWeek === "";
                  }).length === data?.EffectiveAmounts?.length;
                const maxperweekCheck =
                  data?.EffectiveAmounts.filter((amount: any) => {
                    return amount?.MaxperWeek === "";
                  }).length === data?.EffectiveAmounts?.length;
                const flatAmountCheck =
                  data?.EffectiveAmounts.filter((amount: any) => {
                    return amount?.FlatAmount === "";
                  }).length === data?.EffectiveAmounts?.length;
                return (
                  <>
                    <table>
                      <thead>
                        <tr>
                          {!minperweekCheck && (
                            <th colSpan={2}>{data?.Lob[0]?.Name}</th>
                          )}
                          {!maxperweekCheck && (
                            <th colSpan={2}>{data?.Lob[0]?.Name}</th>
                          )}
                          {!flatAmountCheck && (
                            <th colSpan={2}>{data?.Lob[0]?.Name}</th>
                          )}
                        </tr>
                        <tr>
                          {!minperweekCheck && (
                            <>
                              <th colSpan={1}>Min per week</th>
                              <th colSpan={1}>Effective dates</th>
                            </>
                          )}
                          {!maxperweekCheck && (
                            <>
                              <th colSpan={1}>Max per week</th>
                              <th colSpan={1}>Effective dates</th>
                            </>
                          )}
                          {!flatAmountCheck && (
                            <>
                              <th colSpan={1}>Flat Amount</th>
                              <th colSpan={1}>Effective dates</th>
                            </>
                          )}
                        </tr>
                      </thead>
                      <tbody>
                        {data?.EffectiveAmounts.map((amount: any) => {
                          return (
                            <>
                              <tr>
                                {!minperweekCheck && (
                                  <>
                                    <td>{amount?.MinperWeek}</td>
                                    <td>
                                      {convertDate(amount?.MinEffectiveDates)}
                                    </td>
                                  </>
                                )}
                                {!maxperweekCheck && (
                                  <>
                                    <td>{amount?.MaxperWeek}</td>
                                    <td>
                                      {convertDate(amount?.MaxEffectiveDates)}
                                    </td>
                                  </>
                                )}
                                {!flatAmountCheck && (
                                  <>
                                    <td>{amount?.FlatAmount}</td>
                                    <td>
                                      {convertDate(
                                        amount?.AmountEffectiveDates
                                      )}
                                    </td>
                                  </>
                                )}
                              </tr>
                            </>
                          );
                        })}
                        <tr>
                          {
                            <td colSpan={6}>
                              <span
                                className={
                                  data?.FootNotes?.length > 0
                                    ? "has-tooltip"
                                    : ""
                                }
                              >
                                {"Footnote" +
                                  "(" +
                                  data?.FootNotes?.length +
                                  ")"}
                                {data?.FootNotes?.length > 0 && (
                                  <div
                                    className="tooltip"
                                    id="class-guide-{}"
                                    role="tooltip"
                                  >
                                    {data?.FootNotes.map(
                                      (footnote: any, index: any) => {
                                        return (
                                          <li
                                            dangerouslySetInnerHTML={{
                                              __html: footnote.Footnote,
                                            }}
                                            key={index}
                                          ></li>
                                        );
                                      }
                                    )}
                                  </div>
                                )}
                              </span>
                            </td>
                          }
                        </tr>
                      </tbody>
                    </table>
                  </>
                );
              })}
            </div>
            <p className="table-footnotes">
              <em
                dangerouslySetInnerHTML={{
                  __html: auditSummaryText?.replace("\n", "<br />"),
                }}
              ></em>
            </p>
          </div>
        )}
        {state?.IndividualPartners !== null && (
          <div className="payroll">
            <h3>{payrollLimitationsList[1].fields.Name.value}</h3>
            <div className="table-wrapper">
              {state?.IndividualPartners?.sort((value1: any, value2: any) => {
                const nameA = value1?.Lob[0]?.Name?.toUpperCase();
                const nameB = value2?.Lob[0]?.Name?.toUpperCase();
                if (nameA < nameB) {
                  return 1;
                }
                if (nameA > nameB) {
                  return -1;
                }
                return 0;
              }).map((data: any) => {
                const minperweekCheck =
                  data?.EffectiveAmounts.filter((amount: any) => {
                    return amount?.MinperWeek === "";
                  }).length === data?.EffectiveAmounts?.length;
                const maxperweekCheck =
                  data?.EffectiveAmounts.filter((amount: any) => {
                    return amount?.MaxperWeek === "";
                  }).length === data?.EffectiveAmounts?.length;
                const flatAmountCheck =
                  data?.EffectiveAmounts.filter((amount: any) => {
                    return amount?.FlatAmount === "";
                  }).length === data?.EffectiveAmounts?.length;
                return (
                  <>
                    <table>
                      <thead>
                        <tr>
                          {!minperweekCheck && (
                            <th colSpan={2}>{data?.Lob[0]?.Name}</th>
                          )}
                          {!maxperweekCheck && (
                            <th colSpan={2}>{data?.Lob[0]?.Name}</th>
                          )}
                          {!flatAmountCheck && (
                            <th colSpan={2}>{data?.Lob[0]?.Name}</th>
                          )}
                        </tr>
                        <tr>
                          {!minperweekCheck && (
                            <>
                              <th colSpan={1}>Min per week</th>
                              <th colSpan={1}>Effective dates</th>
                            </>
                          )}
                          {!maxperweekCheck && (
                            <>
                              <th colSpan={1}>Max per week</th>
                              <th colSpan={1}>Effective dates</th>
                            </>
                          )}
                          {!flatAmountCheck && (
                            <>
                              <th colSpan={1}>Flat Amount</th>
                              <th colSpan={1}>Effective dates</th>
                            </>
                          )}
                        </tr>
                      </thead>
                      <tbody>
                        {data?.EffectiveAmounts.map((amount: any) => {
                          return (
                            <>
                              <tr>
                                {!minperweekCheck && (
                                  <>
                                    <td>{amount?.MinperWeek}</td>
                                    <td>
                                      {convertDate(amount?.MinEffectiveDates)}
                                    </td>
                                  </>
                                )}
                                {!maxperweekCheck && (
                                  <>
                                    <td>{amount?.MaxperWeek}</td>
                                    <td>
                                      {convertDate(amount?.MaxEffectiveDates)}
                                    </td>
                                  </>
                                )}
                                {!flatAmountCheck && (
                                  <>
                                    <td>{amount?.FlatAmount}</td>
                                    <td>
                                      {convertDate(
                                        amount?.AmountEffectiveDates
                                      )}
                                    </td>
                                  </>
                                )}
                              </tr>
                            </>
                          );
                        })}
                        <tr>
                          {
                            <td colSpan={6}>
                              <span
                                className={
                                  data?.FootNotes?.length > 0
                                    ? "has-tooltip"
                                    : ""
                                }
                              >
                                {"Footnote" +
                                  "(" +
                                  data?.FootNotes?.length +
                                  ")"}
                                {data?.FootNotes?.length > 0 && (
                                  <div
                                    className="tooltip"
                                    id="class-guide-{}"
                                    role="tooltip"
                                  >
                                    {data?.FootNotes.map(
                                      (footnote: any, index: any) => {
                                        return (
                                          <li
                                            dangerouslySetInnerHTML={{
                                              __html: footnote.Footnote,
                                            }}
                                            key={index}
                                          ></li>
                                        );
                                      }
                                    )}
                                  </div>
                                )}
                              </span>
                            </td>
                          }
                        </tr>
                      </tbody>
                    </table>
                  </>
                );
              })}
            </div>
            <p className="table-footnotes">
              <em
                dangerouslySetInnerHTML={{
                  __html: auditSummaryText?.replace("\n", "<br />"),
                }}
              ></em>
            </p>
          </div>
        )}
        {state?.LimitedLiabilities !== null && (
          <div className="payroll liabilities">
            <h3>{payrollLimitationsList[2]?.fields?.Name?.value}</h3>
            <div className="table-wrapper">
              {state?.LimitedLiabilities?.map?.((limitedLiabilities: any) => {
                return (
                  <>
                    {limitedLiabilities?.LimitationEntities.length > 0 && (
                      <>
                        <div className="table-wrapper order-1">
                          {limitedLiabilities?.LimitationEntities?.sort(
                            (value1: any, value2: any) => {
                              const nameA = value1?.Lob[0]?.Name?.toUpperCase();
                              const nameB = value2?.Lob[0]?.Name?.toUpperCase();
                              if (nameA < nameB) {
                                return 1;
                              }
                              if (nameA > nameB) {
                                return -1;
                              }
                              return 0;
                            }
                          ).map((data: any) => {
                            const minperweekCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.MinperWeek === "";
                              }).length === data?.EffectiveAmounts?.length;
                            const maxperweekCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.MaxperWeek === "";
                              }).length === data?.EffectiveAmounts?.length;
                            const flatAmountCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.FlatAmount === "";
                              }).length === data?.EffectiveAmounts?.length;
                            return (
                              <>
                                <table>
                                  <thead>
                                    <tr>
                                      {!minperweekCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Limitation</strong>
                                        </th>
                                      )}
                                      {!maxperweekCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Limitation</strong>
                                        </th>
                                      )}
                                      {!flatAmountCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Limitation</strong>
                                        </th>
                                      )}
                                    </tr>
                                    <tr>
                                      {!minperweekCheck && (
                                        <>
                                          <th colSpan={1}>Min per week</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                      {!maxperweekCheck && (
                                        <>
                                          <th colSpan={1}>Max per week</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                      {!flatAmountCheck && (
                                        <>
                                          <th colSpan={1}>Flat Amount</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {data?.EffectiveAmounts.map(
                                      (amount: any) => {
                                        return (
                                          <>
                                            <tr>
                                              {!minperweekCheck && (
                                                <>
                                                  <td>{amount?.MinperWeek}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.MinEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                              {!maxperweekCheck && (
                                                <>
                                                  <td>{amount?.MaxperWeek}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.MaxEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                              {!flatAmountCheck && (
                                                <>
                                                  <td>{amount?.FlatAmount}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.AmountEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                            </tr>
                                          </>
                                        );
                                      }
                                    )}
                                    <tr>
                                      {" "}
                                      {
                                        <td colSpan={6}>
                                          <span
                                            className={
                                              limitedLiabilities?.FootNotes
                                                ?.length > 0
                                                ? "has-tooltip"
                                                : ""
                                            }
                                          >
                                            {"Footnote" +
                                              "(" +
                                              limitedLiabilities?.FootNotes
                                                ?.length +
                                              ")"}
                                            {limitedLiabilities?.FootNotes
                                              ?.length > 0 && (
                                              <div
                                                className="tooltip"
                                                id="class-guide-{}"
                                                role="tooltip"
                                              >
                                                {limitedLiabilities?.FootNotes.map(
                                                  (
                                                    footnote: any,
                                                    index: any
                                                  ) => {
                                                    return (
                                                      <li
                                                        dangerouslySetInnerHTML={{
                                                          __html:
                                                            footnote.Footnote,
                                                        }}
                                                        key={index}
                                                      ></li>
                                                    );
                                                  }
                                                )}
                                              </div>
                                            )}
                                          </span>
                                        </td>
                                      }
                                    </tr>
                                  </tbody>
                                </table>
                              </>
                            );
                          })}
                        </div>
                      </>
                    )}
                    {limitedLiabilities?.ManagerEntities.length > 0 && (
                      <>
                        <div className="table-wrapper order-2">
                          {limitedLiabilities?.ManagerEntities?.sort(
                            (value1: any, value2: any) => {
                              const nameA = value1?.Lob[0]?.Name?.toUpperCase();
                              const nameB = value2?.Lob[0]?.Name?.toUpperCase();
                              if (nameA < nameB) {
                                return 1;
                              }
                              if (nameA > nameB) {
                                return -1;
                              }
                              return 0;
                            }
                          ).map((data: any) => {
                            const minperweekCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.MinperWeek === "";
                              }).length === data?.EffectiveAmounts?.length;
                            const maxperweekCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.MaxperWeek === "";
                              }).length === data?.EffectiveAmounts?.length;
                            const flatAmountCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.FlatAmount === "";
                              }).length === data?.EffectiveAmounts?.length;
                            return (
                              <>
                                <table>
                                  <thead>
                                    <tr>
                                      {!minperweekCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Manager</strong>
                                        </th>
                                      )}
                                      {!maxperweekCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Manager</strong>
                                        </th>
                                      )}
                                      {!flatAmountCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Manager</strong>
                                        </th>
                                      )}
                                    </tr>
                                    <tr>
                                      {!minperweekCheck && (
                                        <>
                                          <th colSpan={1}>Min per week</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                      {!maxperweekCheck && (
                                        <>
                                          <th colSpan={1}>Max per week</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                      {!flatAmountCheck && (
                                        <>
                                          <th colSpan={1}>Flat Amount</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {data?.EffectiveAmounts.map(
                                      (amount: any) => {
                                        return (
                                          <>
                                            <tr>
                                              {!minperweekCheck && (
                                                <>
                                                  <td>{amount?.MinperWeek}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.MinEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                              {!maxperweekCheck && (
                                                <>
                                                  <td>{amount?.MaxperWeek}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.MaxEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                              {!flatAmountCheck && (
                                                <>
                                                  <td>{amount?.FlatAmount}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.AmountEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                            </tr>
                                          </>
                                        );
                                      }
                                    )}
                                    <tr>
                                      {
                                        <td colSpan={6}>
                                          <span
                                            className={
                                              limitedLiabilities?.FootNotes
                                                ?.length > 0
                                                ? "has-tooltip"
                                                : ""
                                            }
                                          >
                                            {"Footnote" +
                                              "(" +
                                              limitedLiabilities?.FootNotes
                                                ?.length +
                                              ")"}
                                            {limitedLiabilities?.FootNotes
                                              ?.length > 0 && (
                                              <div
                                                className="tooltip"
                                                id="class-guide-{}"
                                                role="tooltip"
                                              >
                                                {limitedLiabilities?.FootNotes.map(
                                                  (
                                                    footnote: any,
                                                    index: any
                                                  ) => {
                                                    return (
                                                      <li
                                                        dangerouslySetInnerHTML={{
                                                          __html:
                                                            footnote.Footnote,
                                                        }}
                                                        key={index}
                                                      ></li>
                                                    );
                                                  }
                                                )}
                                              </div>
                                            )}
                                          </span>
                                        </td>
                                      }
                                    </tr>
                                  </tbody>
                                </table>
                              </>
                            );
                          })}
                        </div>
                      </>
                    )}
                    {limitedLiabilities?.MemberEntities.length > 0 && (
                      <>
                        <div className="table-wrapper order-3">
                          {limitedLiabilities?.MemberEntities?.sort(
                            (value1: any, value2: any) => {
                              const nameA = value1?.Lob[0]?.Name?.toUpperCase();
                              const nameB = value2?.Lob[0]?.Name?.toUpperCase();
                              if (nameA < nameB) {
                                return 1;
                              }
                              if (nameA > nameB) {
                                return -1;
                              }
                              return 0;
                            }
                          ).map((data: any) => {
                            const minperweekCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.MinperWeek === "";
                              }).length === data?.EffectiveAmounts?.length;
                            const maxperweekCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.MaxperWeek === "";
                              }).length === data?.EffectiveAmounts?.length;
                            const flatAmountCheck =
                              data?.EffectiveAmounts.filter((amount: any) => {
                                return amount?.FlatAmount === "";
                              }).length === data?.EffectiveAmounts?.length;
                            return (
                              <>
                                <table>
                                  <thead>
                                    <tr>
                                      {!minperweekCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Member</strong>
                                        </th>
                                      )}
                                      {!maxperweekCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Member</strong>
                                        </th>
                                      )}
                                      {!flatAmountCheck && (
                                        <th colSpan={2}>
                                          {data?.Lob[0]?.Name}
                                          <br />
                                          <strong>Member</strong>
                                        </th>
                                      )}
                                    </tr>
                                    <tr>
                                      {!minperweekCheck && (
                                        <>
                                          <th colSpan={1}>Min per week</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                      {!maxperweekCheck && (
                                        <>
                                          <th colSpan={1}>Max per week</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                      {!flatAmountCheck && (
                                        <>
                                          <th colSpan={1}>Flat Amount</th>
                                          <th colSpan={1}>Effective dates</th>
                                        </>
                                      )}
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {data?.EffectiveAmounts.map(
                                      (amount: any) => {
                                        return (
                                          <>
                                            <tr>
                                              {!minperweekCheck && (
                                                <>
                                                  <td>{amount?.MinperWeek}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.MinEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                              {!maxperweekCheck && (
                                                <>
                                                  <td>{amount?.MaxperWeek}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.MaxEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                              {!flatAmountCheck && (
                                                <>
                                                  <td>{amount?.FlatAmount}</td>
                                                  <td>
                                                    {convertDate(
                                                      amount?.AmountEffectiveDates
                                                    )}
                                                  </td>
                                                </>
                                              )}
                                            </tr>
                                          </>
                                        );
                                      }
                                    )}
                                    <tr>
                                      {
                                        <td colSpan={6}>
                                          <span
                                            className={
                                              limitedLiabilities?.FootNotes
                                                ?.length > 0
                                                ? "has-tooltip"
                                                : ""
                                            }
                                          >
                                            {"Footnote" +
                                              "(" +
                                              limitedLiabilities?.FootNotes
                                                ?.length +
                                              ")"}
                                            {limitedLiabilities?.FootNotes
                                              ?.length > 0 && (
                                              <div
                                                className="tooltip"
                                                id="class-guide-{}"
                                                role="tooltip"
                                              >
                                                {limitedLiabilities?.FootNotes.map(
                                                  (
                                                    footnote: any,
                                                    index: any
                                                  ) => {
                                                    return (
                                                      <li
                                                        dangerouslySetInnerHTML={{
                                                          __html:
                                                            footnote.Footnote,
                                                        }}
                                                        key={index}
                                                      ></li>
                                                    );
                                                  }
                                                )}
                                              </div>
                                            )}
                                          </span>
                                        </td>
                                      }
                                    </tr>
                                  </tbody>
                                </table>
                              </>
                            );
                          })}
                        </div>
                      </>
                    )}
                  </>
                );
              })}
            </div>
            <p className="table-footnotes">
              <em
                dangerouslySetInnerHTML={{
                  __html: auditSummaryText?.replace("\n", "<br />"),
                }}
              ></em>
            </p>
          </div>
        )}
        {(state?.BenifitsFactor !== null ||
          state?.CoverageFactor !== null ||
          state?.ExpenseConstants !== null) && (
          <div className="payroll">
            <h3>{payrollLimitationsList[3].fields.Name.value}</h3>
            <div className="table-wrapper rate-changes">
              <table>
                <thead>
                  <tr>
                    <th>Expense Constant</th>
                    <th>Effective dates</th>
                  </tr>
                  <tr>
                    <th colSpan={2}>{state?.JurisdictionCode[0]?.Name}</th>
                  </tr>
                </thead>
                <tbody>
                  {state?.ExpenseConstants?.EffectiveAmounts?.map(
                    (amount: any, index: any) => {
                      return (
                        <tr key={index}>
                          <td>{amount?.EffectiveAmount}</td>
                          <td>{convertDate(amount?.EffectiveDate)}</td>
                        </tr>
                      );
                    }
                  )}
                  <tr>
                    <td colSpan={6}>
                      <span
                        className={
                          state?.ExpenseConstants?.Footnotes?.length > 0
                            ? "has-tooltip"
                            : ""
                        }
                      >
                        {"Footnote" +
                          "(" +
                          state?.ExpenseConstants?.Footnotes?.length +
                          ")"}
                        {state?.ExpenseConstants?.Footnotes?.length > 0 && (
                          <div
                            className="tooltip"
                            id="class-guide-{}"
                            role="tooltip"
                          >
                            {state?.ExpenseConstants?.Footnotes?.map(
                              (footnote: any, index: any) => {
                                return (
                                  <li
                                    dangerouslySetInnerHTML={{
                                      __html: footnote.Footnote,
                                    }}
                                    key={index}
                                  ></li>
                                );
                              }
                            )}
                          </div>
                        )}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table>
                <thead>
                  <tr>
                    <th>USL & HW Coverage Factor</th>
                    <th>Effective dates</th>
                  </tr>
                  <tr>
                    <th colSpan={2}></th>
                  </tr>
                </thead>
                <tbody>
                  {state?.CoverageFactor?.EffectiveAmounts?.map(
                    (amount: any, index: any) => {
                      return (
                        <tr key={index}>
                          <td>{amount?.EffectiveAmount}</td>
                          <td>{convertDate(amount?.EffectiveDate)}</td>
                        </tr>
                      );
                    }
                  )}
                  <tr>
                    <td colSpan={6}>
                      <span
                        className={
                          state?.CoverageFactor?.Footnotes?.length > 0
                            ? "has-tooltip"
                            : ""
                        }
                      >
                        {"Footnote" +
                          "(" +
                          state?.CoverageFactor?.Footnotes?.length +
                          ")"}
                        {state?.CoverageFactor?.Footnotes?.length > 0 && (
                          <div
                            className="tooltip"
                            id="class-guide-{}"
                            role="tooltip"
                          >
                            {state?.CoverageFactor?.Footnotes?.map(
                              (footnote: any, index: any) => {
                                return (
                                  <li
                                    dangerouslySetInnerHTML={{
                                      __html: footnote.Footnote,
                                    }}
                                    key={index}
                                  ></li>
                                );
                              }
                            )}
                          </div>
                        )}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table>
                <thead>
                  <tr>
                    <th>Benefits Only Factor</th>
                    <th>Effective dates</th>
                  </tr>
                  <tr>
                    <th colSpan={2}></th>
                  </tr>
                </thead>
                <tbody>
                  {state?.BenifitsFactor?.EffectiveAmounts?.map(
                    (amount: any, index: any) => {
                      return (
                        <tr key={index}>
                          <td>{amount?.EffectiveAmount}</td>
                          <td>{convertDate(amount?.EffectiveDate)}</td>
                        </tr>
                      );
                    }
                  )}
                  <tr>
                    <td colSpan={6}>
                      <span
                        className={
                          state?.BenifitsFactor?.Footnotes?.length > 0
                            ? "has-tooltip"
                            : ""
                        }
                      >
                        {"Footnote" +
                          "(" +
                          state?.BenifitsFactor?.Footnotes?.length +
                          ")"}
                        {state?.BenifitsFactor?.Footnotes?.length > 0 && (
                          <div
                            className="tooltip"
                            id="class-guide-{}"
                            role="tooltip"
                          >
                            {state?.BenifitsFactor?.Footnotes?.map(
                              (footnote: any, index: any) => {
                                return (
                                  <li
                                    dangerouslySetInnerHTML={{
                                      __html: footnote.Footnote,
                                    }}
                                    key={index}
                                  ></li>
                                );
                              }
                            )}
                          </div>
                        )}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className="table-footnotes">
              <em
                dangerouslySetInnerHTML={{
                  __html: auditSummaryText?.replace("\n", "<br />"),
                }}
              ></em>
            </p>
          </div>
        )}
        <div className="notes">
          <strong>Notes</strong>
          <br />
          <p
            dangerouslySetInnerHTML={{
              __html: state?.AuditSummaryNotes,
            }}
          ></p>
        </div>
      </section>
    );
  };
  return (
    <>
      <div
        className={
          active && state?.ItemID !== "NA"
            ? "accordionTab active"
            : "accordionTab"
        }
        ref={
          state?.JurisdictionCode[0]?.Code === clicked ? scrollContainer : null
        }
        onClick={() => {
          setClicked("");
          handleClick();
        }}
        data-testid="accordion-click"
      >
        <h2>
          {state?.JurisdictionCode[0]?.Name}
          {state?.ItemID === "NA" ? (
            <span className="badge info">Not applicable in this state</span>
          ) : (
            ""
          )}
        </h2>
        <span className="material-symbols-outlined icon">expand_more</span>
      </div>
      <div
        className={
          active && state?.ItemID !== "NA"
            ? "accordionContent active"
            : "accordionContent"
        }
        style={
          active && state?.ItemID !== "NA"
            ? { maxHeight: "151600px" }
            : { maxHeight: "0px" }
        }
      >
        <div className="flex-wrap">{renderStateInfo(state)}</div>
      </div>
    </>
  );
};

export default AuditInformationSummaryInfo;
