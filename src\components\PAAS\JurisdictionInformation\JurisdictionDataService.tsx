export const jurisdictionsDataApi = async (
  payload: any,
  accessToken: string,
  url: string
) => {
  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  }).then((res) => res.json());
  return response;
};

export const getRulesList = async (accessToken: string, url: string) => {
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  }).then((res) => res.json());
  return response;
};
