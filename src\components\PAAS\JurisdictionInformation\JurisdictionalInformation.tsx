import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import JurisdictionFilter from "./JurisdictionFilter";
import { useContext, useEffect, useState } from "react";
import StateLinks from "./StateLinks";
import { jurisdictionsDataApi } from "./JurisdictionDataService";
import { AuthContext } from "src/context/authContext";
import { TestAudit } from "./TestAudit";
import { WCStateExceptions } from "./WCStateExceptions";
import { RulesAndStateException } from "./RulesAndStateException";
import { AuditInformationSummary } from "./AuditInformationSummary";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
import useRedirectToBasePage from "./../../../hooks/paas/useRedirectToBasePage";
const JurisdictionalInformation = (props: any): JSX.Element => {
  const [stateshortcut, setStateshortcut] = useState([]);
  const [contentType, setContentType] = useState("");
  const [jurisdictionalInfoData, setJurisdictionalInfoData] = useState<any>([]);
  let selectedCustomerNumber = useGetSelectedCustomerNumber();
  const { accessToken, paasEntitlement } = useContext(AuthContext);
  const [jurisdictionsIds, setJurisdictionIds] = useState<any>([]);
  const [subTopicItemId, setSubTopicItemId] = useState("");
  const [clicked, setClicked] = useState("");
  const [ruleId, setRuleId] = useState("");
  const [legalEntityType, setLegalEntityType] = useState("All Types");

  useEffect(() => {
    getJurisdictionsAPI();
  }, [jurisdictionsIds, contentType, subTopicItemId, ruleId, legalEntityType]);
  const isEntitledForContentType = (
    contentType: string,
    subTopicItemId: string,
    ruleId: string,
    legalEntityType: string,
    paasEntitlement: any,
    selectedCustomerNumber: string
  ) => {
    const customer = paasEntitlement?.find(
      (customer: any) => customer?.customerNumber === selectedCustomerNumber
    );
    if (!customer?.customerPAASParticipation?.includes("LOB_WC")) {
      return false;
    }
    switch (contentType) {
      case "89aa272f-b6e4-4148-8383-527b2447d9ae":
        return subTopicItemId !== "";
      case "749514cb-f0b2-491d-b735-61989e024b2c":
        return ruleId !== "";
      case "4ac36b1d-ae2a-4dde-83fb-78df574c57c9":
        return legalEntityType !== "";
      case "df53375a-33b4-4d26-9a9b-0efd3b37d384":
      case "9635aedc-51c2-48ad-ab4c-8fec623538f0":
        return true;
      default:
        return false;
    }
  };
  const getJurisdictionsAPI = async () => {
    let payload = {};
    if (contentType === "89aa272f-b6e4-4148-8383-527b2447d9ae") {
      payload = {
        JurisdictionItemId: jurisdictionsIds.join(", ").toUpperCase(),
        ContentSubType: contentType,
        SubTopicItemId: subTopicItemId,
      };
    } else if (contentType === "749514cb-f0b2-491d-b735-61989e024b2c") {
      payload = {
        JurisdictionItemId: jurisdictionsIds.join(", ").toUpperCase(),
        ContentType: contentType,
        RuleId: ruleId,
      };
    } else if (contentType === "4ac36b1d-ae2a-4dde-83fb-78df574c57c9") {
      payload = {
        JurisdictionItemId: jurisdictionsIds.join(", ").toUpperCase(),
        ContentType: contentType,
        LegalEntityType: legalEntityType,
      };
    } else {
      payload = {
        JurisdictionItemId: jurisdictionsIds.join(", ").toUpperCase(),
        ContentSubType: contentType,
      };
    }
    let url = "";
    if (contentType === "df53375a-33b4-4d26-9a9b-0efd3b37d384") {
      url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetStateLinks`;
    } else if (contentType === "9635aedc-51c2-48ad-ab4c-8fec623538f0") {
      url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetTestAudits`;
    } else if (contentType === "89aa272f-b6e4-4148-8383-527b2447d9ae") {
      url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetWCStatutoryExceptions`;
    } else if (contentType === "749514cb-f0b2-491d-b735-61989e024b2c") {
      url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetRulesAndStateException`;
    } else if (contentType === "4ac36b1d-ae2a-4dde-83fb-78df574c57c9") {
      url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetAuditSummaryResults`;
    }
    if (
      isEntitledForContentType(
        contentType,
        subTopicItemId,
        ruleId,
        legalEntityType,
        paasEntitlement,
        selectedCustomerNumber
      )
    ) {
      try {
        const post = await jurisdictionsDataApi(payload, accessToken, url);
        setJurisdictionalInfoData(post);
      } catch (error) {
        console.error("Error fetching jurisdiction data:", error);
      }
    }
  };
  useRedirectToBasePage(!props?.isEEFlag, selectedCustomerNumber);
  return (
    <div className="jurisdictional-info">
      <div className="site flex-wrapper">
        <JurisdictionFilter
          setStateshortcut={setStateshortcut}
          setJurisdictionIds={setJurisdictionIds}
          jurisdictionsIds={jurisdictionsIds}
          stateshortcut={stateshortcut}
          contentTypeList={props?.fields?.ContentType}
          contentType={contentType}
          setContentType={setContentType}
          subTopicItemId={subTopicItemId}
          setSubTopicItemId={setSubTopicItemId}
          setLegalEntityType={setLegalEntityType}
          setRuleId={setRuleId}
          JurisdictionsData={props?.fields?.JurisdictionsData}
          setClicked={setClicked}
          setJurisdictionalInfoData={setJurisdictionalInfoData}
          restProps={props}
        />
        <div className="content-wrapper">
          {contentType === "df53375a-33b4-4d26-9a9b-0efd3b37d384" ? (
            <StateLinks
              gsLinksHeading={props?.fields?.GsLinksHeader}
              gsLinksInfo={props?.fields?.GsLinksInfo}
              selectedJsText={props?.fields?.SelectedJurisdictionsText}
              selectedJurisdictions={stateshortcut}
              jurisdictionalInfoData={jurisdictionalInfoData}
              bureauLinkText={props?.fields?.BureauLinkText}
              manualLinkText={props?.fields?.ManualLinkText}
              fraudDepartmentLinkText={props?.fields?.FraudDepartmentLinkText}
              WCCvgVerificationLinkText={
                props?.fields?.WCCvgVerificationLinkText
              }
            />
          ) : contentType === "9635aedc-51c2-48ad-ab4c-8fec623538f0" ? (
            <TestAudit
              TestAuditHeading={props?.fields?.TestAuditHeader}
              TestAuditInfo={props?.fields?.TestAuditInfo}
              selectedJsText={props?.fields?.SelectedJurisdictionsText}
              selectedJurisdictions={stateshortcut}
              jurisdictionalInfoData={jurisdictionalInfoData}
              restProps={props}
              clicked={clicked}
              setClicked={setClicked}
            />
          ) : contentType === "89aa272f-b6e4-4148-8383-527b2447d9ae" ? (
            <WCStateExceptions
              WCStateExceptionsHeader={props?.fields?.WCStateExceptionsHeader}
              WCStateExceptionsInfo={props?.fields?.WCStateExceptionsInfo}
              selectedJsText={props?.fields?.SelectedJurisdictionsText}
              selectedJurisdictions={stateshortcut}
              jurisdictionalInfoData={jurisdictionalInfoData}
              noExceptionsText={props?.fields?.NoExceptionsText}
              restProps={props}
            />
          ) : contentType === "749514cb-f0b2-491d-b735-61989e024b2c" ? (
            <RulesAndStateException
              RulesAndStateExceptionsHeader={
                props?.fields?.RulesAndStateExceptionsHeader
              }
              RulesAndStateExceptionDesc={
                props?.fields?.RulesAndStateExceptionDesc
              }
              NCCIRuleText={props?.fields?.NCCIRuleText}
              RulesList={props?.fields?.RulesList}
              ruleId={ruleId}
              setRuleId={setRuleId}
              selectedJsText={props?.fields?.SelectedJurisdictionsText}
              selectedJurisdictions={stateshortcut}
              jurisdictionalInfoData={jurisdictionalInfoData}
              noExceptionsText={props?.fields?.NoExceptionsText}
              restProps={props}
            />
          ) : contentType === "4ac36b1d-ae2a-4dde-83fb-78df574c57c9" ? (
            <AuditInformationSummary
              AISHeader={props?.fields?.AISHeader}
              AISInfo={props?.fields?.AISInfo}
              payrollLimitationsText={props?.fields?.PayrollLimitationsText}
              payrollLimitationsList={props?.fields?.PayrollLimitationsList}
              legalEntityType={legalEntityType}
              setLegalEntityType={setLegalEntityType}
              selectedJsText={props?.fields?.SelectedJurisdictionsText}
              selectedJurisdictions={stateshortcut}
              jurisdictionalInfoData={jurisdictionalInfoData}
              noExceptionsText={props?.fields?.NoExceptionsText}
              auditSummaryText={props?.dictionaryData?.paas_audit_summary_text}
              restProps={props}
            />
          ) : (
            ""
          )}
        </div>
      </div>
    </div>
  );
};

export default withSitecoreContext()(JurisdictionalInformation);
