import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { useContext, useEffect, useState } from "react";
import StateShortcut from "./StateShortcut";
import RulesAndStateInfo from "./RulesAndStateInfo";
import { monthNames } from "../NewsAndEvents/NewsAndEventsConstants";
import { getRulesList } from "./JurisdictionDataService";
import { AuthContext } from "src/context/authContext";

export const RulesAndStateException = (props: any) => {
  const {
    RulesAndStateExceptionsHeader,
    RulesAndStateExceptionDesc,
    NCCIRuleText,
    setRuleId,
    selectedJsText,
    selectedJurisdictions,
    jurisdictionalInfoData,
    restProps,
    noExceptionsText,
  } = props;

  const [clicked, setClicked] = useState("");
  const [pinned, setPinned] = useState(false);
  const [expandAll, setExpandAll] = useState(false);
  const [expandRuleDropdown, setExpandRuleDropdown] = useState(false);
  const [readMore, setReadMore] = useState(false);
  const [ruleSearchTerm, setRuleSearchTerm] = useState("");
  const [ruleName, setRuleName] = useState("");
  const [rulesList, setRulesList] = useState<any>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  const { accessToken } = useContext(AuthContext);

  let jurisdictions: any = [];
  selectedJurisdictions.map((js: any) => {
    jurisdictions.push(js?.code);
  });

  useEffect(() => {
    const changePinnedState = () => {
      if (window.scrollY > 750 || window.scrollY < 550) {
        setPinned(true);
      } else {
        setPinned(false);
      }
    };
    window.addEventListener("scroll", changePinnedState);
    getRulesListApi();
    return () => {
      window.removeEventListener("scroll", changePinnedState);
    };
  }, []);

  const getRulesListApi = async () => {
    const url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetRulesAndStateExceptionList`;
    const post = await getRulesList(accessToken, url);
    setRulesList(post);
  };

  const getQueryParams = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const rule = urlParams.get("rule");

    return {
      rule: rule ? decodeURIComponent(rule) : null,
    };
  };

  const updateUrlParams = (ruleName: string) => {
    const urlParams = new URLSearchParams(window.location.search);

    if (ruleName) {
      urlParams.set("rule", ruleName);
    } else {
      urlParams.delete("rule");
    }

    const newUrl = `${window.location.pathname}${
      urlParams.toString() ? "?" + urlParams.toString() : ""
    }`;
    window.history.replaceState({}, "", newUrl);
  };

  useEffect(() => {
    if (isInitialized && ruleName) {
      updateUrlParams(ruleName);
    }
  }, [ruleName, isInitialized]);

  useEffect(() => {
    const { rule } = getQueryParams();

    if (rule && rulesList?.RulesAndStateException && !isInitialized) {
      const matchedRule = rulesList.RulesAndStateException.find(
        (ruleItem: any) =>
          ruleItem.RuleTitle?.toLowerCase().includes(rule.toLowerCase()) ||
          ruleItem.RuleTitle?.toLowerCase().replace(/\s+/g, "") ===
            rule.toLowerCase().replace(/\s+/g, "")
      );

      if (matchedRule) {
        setRuleId(matchedRule.ItemID);
        setRuleName(matchedRule.RuleTitle);
      }

      setIsInitialized(true);
    }
  }, [rulesList, isInitialized, setRuleId]);

  const groupedRuleList = rulesList?.RulesAndStateException?.reduce(
    (x: any, y: any) => {
      (x[y?.RuleGroup?.Name] = x[y?.RuleGroup?.Name] || []).push(y);

      return x;
    },
    {}
  );

  let RuleListArray: any = [];
  if (groupedRuleList) {
    RuleListArray = Object?.values(groupedRuleList);
  }

  const date = new Date(
    jurisdictionalInfoData?.RulesAndStateExceptionItem?.EffectiveDate
  );
  const effectiveDate =
    date.getFullYear() !== 1
      ? monthNames[date.getMonth()] +
        " " +
        date.getDate() +
        " " +
        date.getFullYear()
      : "";

  return (
    <>
      <div
        className={
          pinned
            ? "jurisdictional-info-content-header is-pinned"
            : "jurisdictional-info-content-header"
        }
      >
        <div className="flex-wrapper">
          <Text field={RulesAndStateExceptionsHeader} tag="h2" />
        </div>
        <RichText field={RulesAndStateExceptionDesc} tag="p" />
        <div className="flex-wrapper rules-state">
          <div className="rules-state-dropdown">
            <div className="dropdown multiselect">
              <label>
                <Text field={NCCIRuleText} tag="strong" />
              </label>
              <button
                type="button"
                className={
                  expandRuleDropdown
                    ? "dropdown-button is-open"
                    : "dropdown-button"
                }
                onClick={() => setExpandRuleDropdown(!expandRuleDropdown)}
                data-testid="rule-dropdown-btn"
              >
                <span className="dropdown-text show">
                  {ruleName === "" ? "Select a rule to start" : ruleName}
                </span>
                <span className="material-icons">expand_less</span>
              </button>
              <div
                id="myDropdown"
                className={
                  expandRuleDropdown
                    ? "dropdown-container show"
                    : "dropdown-container"
                }
              >
                <div className="dropdown-input">
                  <input
                    type="text"
                    placeholder="enter a Keyword to filter"
                    onChange={(e) => setRuleSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex-wrapper">
                  <span className="dropdown-count">
                    {rulesList?.RulesAndStateException?.length} Rules
                  </span>
                </div>
                <form className="dropdown-content">
                  <fieldset id="rulesState">
                    {RuleListArray.map((ruleGroup: any) => {
                      return (
                        <>
                          {ruleGroup?.filter(
                            (rule: any) =>
                              rule?.RuleTitle?.toUpperCase().includes(
                                ruleSearchTerm.toUpperCase()
                              ) ||
                              ruleGroup[0]?.RuleGroup?.Name?.toUpperCase().includes(
                                ruleSearchTerm.toUpperCase()
                              )
                          ).length > 0 && (
                            <label>
                              <strong>{ruleGroup[0]?.RuleGroup?.Name}</strong>
                            </label>
                          )}
                          <ul>
                            {ruleGroup
                              ?.filter(
                                (rule: any) =>
                                  rule?.RuleTitle?.toUpperCase().includes(
                                    ruleSearchTerm.toUpperCase()
                                  ) ||
                                  ruleGroup[0]?.RuleGroup?.Name?.toUpperCase().includes(
                                    ruleSearchTerm.toUpperCase()
                                  )
                              )
                              .map((rule: any, index: any) => {
                                return (
                                  <li key={index}>
                                    <a
                                      href="#"
                                      data-category="Classification Codes"
                                      onClick={() => {
                                        setRuleId(rule?.ItemID);
                                        setExpandRuleDropdown(
                                          !expandRuleDropdown
                                        );
                                        setRuleName(rule?.RuleTitle);
                                        updateUrlParams(rule?.RuleTitle);
                                      }}
                                      data-testid="rule-btn"
                                    >
                                      {rule?.RuleTitle}
                                    </a>
                                  </li>
                                );
                              })}
                          </ul>
                        </>
                      );
                    })}
                  </fieldset>
                </form>
              </div>
            </div>
          </div>
        </div>
        {jurisdictionalInfoData?.RulesAndStateExceptionItem?.RuleText.length >
          0 && (
          <div className="flex-wrapper selected-rule show">
            <div className={readMore ? "show-more" : "show-less"}>
              <h4>{ruleName}</h4>
              <p
                dangerouslySetInnerHTML={{
                  __html:
                    jurisdictionalInfoData?.RulesAndStateExceptionItem
                      ?.RuleText,
                }}
              ></p>
            </div>
            <a className="read-more-btn" onClick={() => setReadMore(!readMore)}>
              {readMore ? "Read less" : "Read more"}
            </a>
            <div className="effective-date">
              <h4>Effective Date:</h4>
              <span className="date-value">{effectiveDate}</span>
            </div>
          </div>
        )}
        <div className="jurisdictional-info-skeleton jump-links">
          <div className="jurisdictional-info-skeleton-items show">
            <p>
              <Text field={selectedJsText} tag="strong" />
            </p>
            <div className="selected-jurisdictions">
              {selectedJurisdictions
                .sort((value1: any, value2: any) => {
                  const nameA = value1?.label?.toUpperCase();
                  const nameB = value2?.label?.toUpperCase();
                  if (nameA < nameB) {
                    return -1;
                  }
                  if (nameA > nameB) {
                    return 1;
                  }
                  return 0;
                })
                .map((state: any) => {
                  return (
                    <>
                      <StateShortcut
                        state={state}
                        setClicked={setClicked}
                        clicked={clicked}
                      />
                    </>
                  );
                })}
            </div>
          </div>
        </div>
        {selectedJurisdictions.length === 1 ? (
          <></>
        ) : (
          <div className="flex-wrapper">
            <a
              onClick={() => setExpandAll(!expandAll)}
              className={expandAll ? "expand-all open" : "expand-all closed"}
              data-testid="expand-all"
            >
              <span className="material-symbols-outlined">expand</span>
              <span className="expand-label">Expand all</span>
              <span className="collapse-label">Collapse all</span>
            </a>
          </div>
        )}
      </div>
      <div className="flex-wrap">
        {jurisdictionalInfoData?.RulesAndStateExceptionItem?.StateExceptions?.sort(
          (value1: any, value2: any) => {
            const nameA = value1?.JurisdictionCode[0]?.Name?.toUpperCase();
            const nameB = value2?.JurisdictionCode[0]?.Name?.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          }
        ).map((state: any, id: any) => {
          return (
            <div
              className={
                state?.ItemID === "NA" ? "accordion disabled" : "accordion"
              }
              key={id}
            >
              <RulesAndStateInfo
                selectedJurisdictions={selectedJurisdictions}
                state={state}
                clicked={clicked}
                setClicked={setClicked}
                restProps={restProps}
                expandAll={expandAll}
                noExceptionsText={noExceptionsText}
              />
            </div>
          );
        })}
      </div>
    </>
  );
};
