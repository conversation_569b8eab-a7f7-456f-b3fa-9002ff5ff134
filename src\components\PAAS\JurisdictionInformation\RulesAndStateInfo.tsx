import { useRef } from "react";
import { monthNames } from "../NewsAndEvents/NewsAndEventsConstants";
import useAccordionJumpLink from "src/hooks/paas/useAccordionJumpLink";

const RulesAndStateInfo = (props: any) => {
  const { state, clicked, setClicked, expandAll , selectedJurisdictions } = props;
  const scrollContainer = useRef<HTMLDivElement>(null);

  const { active, handleClick } = useAccordionJumpLink(
    state?.JurisdictionCode[0]?.Code,
    -120,
    clicked,
    expandAll,
    scrollContainer,
    selectedJurisdictions
  );

  const renderStateInfo = (heading: any, description: any) => {
    return (
      <section>
        <h3 className="bg-gray">{heading}</h3>
        <p
          dangerouslySetInnerHTML={{
            __html: description,
          }}
        ></p>
      </section>
    );
  };

  const date = new Date(state?.EffectiveDate);
  const effectiveDate =
    date.getFullYear() !== 1
      ? monthNames[date.getMonth()] +
        " " +
        date.getDate() +
        " " +
        date.getFullYear()
      : "";

  return (
    <>
      <div
       className={
        (active && state?.ItemID !== "NA")
          ? "accordionTab active"
          : "accordionTab"
      }
        ref={
          state?.JurisdictionCode[0]?.Code === clicked ? scrollContainer : null
        }
        onClick={() => {
          setClicked("");
          handleClick();
        }}
      >
        <h2>
          {state?.JurisdictionCode[0]?.Name}
          {state?.ItemID === "NA" ? (
            <span className="badge info">No State Exception</span>
          ) : (
            ""
          )}
        </h2>
        <span className="material-symbols-outlined icon">expand_more</span>
      </div>
      <div
        className={
          (active && state?.ItemID !== "NA")
            ? "accordionContent active"
            : "accordionContent"
        }
        style={
          (active && state?.ItemID !== "NA")
            ? { maxHeight: "100%" }
            : { maxHeight: "0px" }
        }
      >
        <div className="flex-wrap">
          {state?.ExceptionText?.length &&
            renderStateInfo("State Exception", state?.ExceptionText)}
          {state?.PaasComment?.length &&
            renderStateInfo("PAAS Comment", state?.PaasComment)}
          {state?.EffectiveDate?.length &&
            renderStateInfo("Effective Date", effectiveDate)}
        </div>
      </div>
    </>
  );
};

export default RulesAndStateInfo;
