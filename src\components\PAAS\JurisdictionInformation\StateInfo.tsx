import { useRef } from "react";
import useAccordionJumpLink from "src/hooks/paas/useAccordionJumpLink";

const StateInfo = (props: any) => {
  const { state, clicked, restProps, setClicked, expandAll, selectedJurisdictions } = props;
  const scrollContainer = useRef<HTMLDivElement>(null);

  const { active, handleClick } = useAccordionJumpLink(
    state?.Jurisdiction[0]?.Code,
    90,
    clicked,
    expandAll,
    scrollContainer,
    selectedJurisdictions
  );

  const renderStateInfo = (heading: any, description: any) => {
    return (
      <section>
        <h3 className="bg-gray">{heading}</h3>
        <p
          dangerouslySetInnerHTML={{
            __html: description,
          }}
        ></p>
      </section>
    );
  };

  return (
    <>
        <div
        className={
          (active && state?.ItemID !== "NA")
            ? "accordionTab active"
            : "accordionTab"
        }
        ref={state?.Jurisdiction[0]?.Code === clicked ? scrollContainer : null}
        onClick={() => {
          setClicked("");
          handleClick();
        }}
      >
        <h2>
          {state?.ItemName}
          {state?.ItemID === "NA" ? (
            <span className="badge info">Not applicable in this state</span>
          ) : (
            ""
          )}
        </h2>
        <span className="material-symbols-outlined icon">expand_more</span>
      </div>
      <div
        className={
          (active && state?.ItemID !== "NA")
            ? "accordionContent active"
            : "accordionContent"
        }
        style={
          (active && state?.ItemID !== "NA") 
            ? { maxHeight: "50000px" }
            : { maxHeight: "0px" }
        }
      >
        <div className="flex-wrap">
          {state?.Policy &&
            renderStateInfo(
              restProps?.fields?.PolicySelectionText?.value,
              state?.Policy
            )}
          {state?.Type &&
            renderStateInfo(
              restProps?.fields?.PolicyTypeText?.value,
              state?.Type
            )}
          {state?.Age &&
            renderStateInfo(
              restProps?.fields?.PolicyAgeText?.value,
              state?.Age
            )}
          {state?.Information &&
            renderStateInfo(
              restProps?.fields?.RequiredInformationText?.value,
              state?.Information
            )}
          {state?.Claims &&
            renderStateInfo(
              restProps?.fields?.ExaminedClaimsText?.value,
              state?.Claims
            )}
          {state?.Time &&
            renderStateInfo(
              restProps?.fields?.ResponseTimeText?.value,
              state?.Time
            )}
          {state?.Differences &&
            renderStateInfo(
              restProps?.fields?.ReportableDifferenceText?.value,
              state?.Differences
            )}
          {state?.MinStandards &&
            renderStateInfo(
              restProps?.fields?.MinimumStandardsText?.value,
              state?.MinStandards
            )}
          {state?.MaxStandards &&
            renderStateInfo(
              restProps?.fields?.MaximumStandardsText?.value,
              state?.MaxStandards
            )}
          {state?.Rebilling &&
            renderStateInfo(
              restProps?.fields?.RebillingRequirementsText?.value,
              state?.Rebilling
            )}
          {state?.StatisticalCards &&
            renderStateInfo(
              restProps?.fields?.ChangeStatisticalCardsText?.value,
              state?.StatisticalCards
            )}
          {state?.ContestingAudit &&
            renderStateInfo(
              restProps?.fields?.ContestingAnAuditText?.value,
              state?.ContestingAudit
            )}
          {state?.ContestTime &&
            renderStateInfo("Contest Time", state?.ContestTime)}
        </div>
      </div>
    </>
  );
};

export default StateInfo;
