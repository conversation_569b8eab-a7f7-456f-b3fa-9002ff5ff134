import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { useEffect, useState } from "react";

const StateLinks = (props: any) => {
  const {
    gsLinksHeading,
    gsLinksInfo,
    selectedJsText,
    selectedJurisdictions,
    jurisdictionalInfoData,
    bureauLinkText,
    manualLinkText,
    fraudDepartmentLinkText,
    WCCvgVerificationLinkText,
  } = props;

  const [pinned, setPinned] = useState(false);

  let jurisdictions: any = [];
  selectedJurisdictions.map((js: any) => {
    jurisdictions.push(js?.code);
  });

  useEffect(() => {
    const changePinnedState = () => {
      if (window.scrollY > 750 || window.scrollY < 550) {
        setPinned(true);
      } else {
        setPinned(false);
      }
    };
    window.addEventListener("scroll", changePinnedState);
    return () => {
      window.removeEventListener("scroll", changePinnedState);
    };
  }, []);

  return (
    <>
      <div
        className={
          pinned
            ? "jurisdictional-info-content-header is-pinned"
            : "jurisdictional-info-content-header"
        }
      >
        <div className="flex-wrapper">
          <Text field={gsLinksHeading} tag="h2" />
        </div>
        <RichText field={gsLinksInfo} tag="p" />
        <div className="jurisdictional-info-skeleton">
          <div className="jurisdictional-info-skeleton-items show">
            <p>
              <Text field={selectedJsText} tag="strong" />
            </p>
            <div className="selected-jurisdictions">
              {jurisdictions.join(", ")}
            </div>
          </div>
        </div>
      </div>
      <section className="jurisdictional-state-links">
        {jurisdictionalInfoData?.StateLinkItems?.sort(
          (value1: any, value2: any) => {
            const nameA = value1?.ItemName?.toUpperCase();
            const nameB = value2?.ItemName?.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          }
        ).map((info: any) => {
          return (
            <>
              <h3>{info.ItemName}</h3>
              <ul>
                {info?.BureauLink?.DisplayName ? (
                  <li>
                    <Text field={bureauLinkText} tag="strong" />:{" "}
                    <a target="_blank" href={info.BureauLink.Url}>
                      {info.BureauLink.DisplayName}
                      <span className="material-symbols-outlined">
                        open_in_new
                      </span>
                    </a>
                  </li>
                ) : (
                  ""
                )}
                {info?.ManualLink?.DisplayName ? (
                  <li>
                    <Text field={manualLinkText} tag="strong" />:{" "}
                    <a target="_blank" href={info.ManualLink.Url}>
                      {info.ManualLink.DisplayName}
                      <span className="material-symbols-outlined">
                        open_in_new
                      </span>
                    </a>
                  </li>
                ) : (
                  ""
                )}
                {info?.FraudDepartmentLink.DisplayName ? (
                  <li>
                    <Text field={fraudDepartmentLinkText} tag="strong" />:{" "}
                    <a target="_blank" href={info.FraudDepartmentLink.Url}>
                      {info.FraudDepartmentLink.DisplayName}
                      <span className="material-symbols-outlined">
                        open_in_new
                      </span>
                    </a>
                  </li>
                ) : (
                  ""
                )}
                {info?.WCCvgVerificationLink?.DisplayName ? (
                  <li>
                    <Text field={WCCvgVerificationLinkText} tag="strong" />:{" "}
                    <a target="_blank" href={info.WCCvgVerificationLink.Url}>
                      {info.WCCvgVerificationLink.DisplayName}
                      <span className="material-symbols-outlined">
                        open_in_new
                      </span>
                    </a>
                  </li>
                ) : (
                  ""
                )}
              </ul>
            </>
          );
        })}
      </section>
    </>
  );
};

export default StateLinks;
