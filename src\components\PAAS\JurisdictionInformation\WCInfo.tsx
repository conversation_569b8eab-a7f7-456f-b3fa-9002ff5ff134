import { useRef } from "react";
import useAccordionJumpLink from "src/hooks/paas/useAccordionJumpLink";

const WCInfo = (props: any) => {
  const { state, clicked, setClicked, expandAll, noExceptionsText, selectedJurisdictions } = props;
  const scrollContainer = useRef<HTMLDivElement>(null);

  const { active, handleClick } = useAccordionJumpLink(
    state?.Jurisdiction?.Code,
    90,
    clicked,
    expandAll,
    scrollContainer,
    selectedJurisdictions
  );

  const renderStateInfo = (heading: any, description: any) => {
    return (
      <section>
        <h3 className="bg-gray">
          State Statute: <span className="fw-normal">{heading}</span>
        </h3>
        <p
          dangerouslySetInnerHTML={{
            __html: description,
          }}
        ></p>
      </section>
    );
  };

  return (
    <>
       <div
        className={
          (active && state?.ItemID !== "NA")
            ? "accordionTab active"
            : "accordionTab"
        }
        ref={state?.Jurisdiction?.Code === clicked ? scrollContainer : null}
        onClick={() => {
          setClicked("");
          handleClick();
        }}
        data-testid="accordion-click"
      >
        <h2>
          {state?.Jurisdiction?.Name}
          {state?.ItemID === "NA" ? (
            <span className="badge info">{noExceptionsText.value}</span>
          ) : (
            <span className="item-count show">
              <small className="options-selected">
                {state?.StateStatute?.length}
              </small>
            </span>
          )}
        </h2>
        <span className="material-symbols-outlined icon">expand_more</span>
      </div>
      <div
        className={
          (active && state?.ItemID !== "NA")
            ? "accordionContent active"
            : "accordionContent"
        }
        style={
          (active && state?.ItemID !== "NA")
            ? { maxHeight: "1516px" }
            : { maxHeight: "0px" }
        }
      >
        <div className="flex-wrap">
          {state?.StateStatute?.length &&
            state?.StateStatute[0]?.StateStature &&
            state?.StateStatute?.map((state: any) => {
              return renderStateInfo(state?.StateStature, state?.StatutoryText);
            })}
        </div>
      </div>
    </>
  );
};

export default WCInfo;
