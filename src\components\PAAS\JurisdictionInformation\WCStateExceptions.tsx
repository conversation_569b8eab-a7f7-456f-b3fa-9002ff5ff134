import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { useEffect, useState } from "react";
import StateShortcut from "./StateShortcut";
import WCInfo from "./WCInfo";

export const WCStateExceptions = (props: any) => {
  const {
    WCStateExceptionsHeader,
    WCStateExceptionsInfo,
    selectedJsText,
    selectedJurisdictions,
    jurisdictionalInfoData,
    restProps,
    noExceptionsText,
  } = props;

  const [clicked, setClicked] = useState("");
  const [pinned, setPinned] = useState(false);
  const [expandAll, setExpandAll] = useState(false);
  const [filteredInfo, setFilteredInfo] = useState([]);

  let jurisdictions: any = [];
  selectedJurisdictions.map((js: any) => {
    jurisdictions.push(js?.code);
  });

  useEffect(() => {
    const changePinnedState = () => {
      if (window.scrollY > 750 || window.scrollY < 550) {
        setPinned(true);
      } else {
        setPinned(false);
      }
    };

    window.addEventListener("scroll", changePinnedState);
    return () => {
      window.removeEventListener("scroll", changePinnedState);
    };
  }, []);

  useEffect(() => {
    if (jurisdictionalInfoData.length === 0) {
      setFilteredInfo([]);
    }
    if (jurisdictionalInfoData?.WCStatutoryExceptionItems) {
      let filteredWCInfo: any = [];
      let wcInfo: any = [];
      wcInfo = jurisdictionalInfoData.WCStatutoryExceptionItems.map(
        (state: any) => {
          return {
            ...state,
            StateStatute: [state.StateStatute],
          };
        }
      );
      filteredWCInfo = Object.values(
        wcInfo?.reduce((acc: any, obj: any) => {
          if (!acc[obj.Jurisdiction.Code]) {
            acc[obj.Jurisdiction.Code] = obj;
          } else {
            acc[obj.Jurisdiction.Code].StateStatute =
              acc[obj.Jurisdiction.Code].StateStatute ?? [];
            acc[obj.Jurisdiction.Code].StateStatute = acc[
              obj.Jurisdiction.Code
            ].StateStatute.concat(obj.StateStatute);
          }
          return acc;
        }, {})
      );
      setFilteredInfo(filteredWCInfo);
    }
  }, [jurisdictionalInfoData]);

  return (
    <>
      <div
        className={
          pinned
            ? "jurisdictional-info-content-header is-pinned"
            : "jurisdictional-info-content-header"
        }
      >
        <div className="flex-wrapper">
          <Text field={WCStateExceptionsHeader} tag="h2" />
        </div>
        <RichText field={WCStateExceptionsInfo} tag="p" />
        <div className="jurisdictional-info-skeleton jump-links">
          <div className="jurisdictional-info-skeleton-items show">
            <p>
              <Text field={selectedJsText} tag="strong" />
            </p>
            <div className="selected-jurisdictions">
              {selectedJurisdictions
                .sort((value1: any, value2: any) => {
                  const nameA = value1?.label?.toUpperCase();
                  const nameB = value2?.label?.toUpperCase();
                  if (nameA < nameB) {
                    return -1;
                  }
                  if (nameA > nameB) {
                    return 1;
                  }
                  return 0;
                })
                .map((state: any) => {
                  return (
                    <>
                      <StateShortcut
                        state={state}
                        setClicked={setClicked}
                        clicked={clicked}
                      />
                    </>
                  );
                })}
            </div>
          </div>
        </div>
        {selectedJurisdictions.length===1? <></>: <div className="flex-wrapper">
          <a
            onClick={() => setExpandAll(!expandAll)}
            className={expandAll ? "expand-all open" : "expand-all closed"}
            data-testid="expand-all"
          >
            <span className="material-symbols-outlined">expand</span>
            <span className="expand-label">Expand all</span>
            <span className="collapse-label">Collapse all</span>
          </a>
        </div>}
      </div>
      <div className="flex-wrap">
        {filteredInfo
          ?.sort((value1: any, value2: any) => {
            const nameA = value1?.Jurisdiction?.Name?.toUpperCase();
            const nameB = value2?.Jurisdiction?.Name?.toUpperCase();
            if (nameA < nameB) {
              return -1;
            }
            if (nameA > nameB) {
              return 1;
            }
            return 0;
          })
          .map((state: any, id: any) => {
            return (
              <div
                className={
                  state?.ItemID === "NA" ? "accordion disabled" : "accordion"
                }
                key={id}
              >
              <WCInfo
                  selectedJurisdictions={selectedJurisdictions}
                  state={state}
                  clicked={clicked}
                  setClicked={setClicked}
                  restProps={restProps}
                  expandAll={expandAll}
                  noExceptionsText={noExceptionsText}
                />
              </div>
            );
          })}
      </div>
    </>
  );
};
