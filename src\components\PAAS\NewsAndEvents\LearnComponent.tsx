import {
  Field,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import Card from "./LearnComponentCardItem";
import React, { useState } from "react";

type LearnComponentProps = ComponentProps & {
  fields: {
    Heading: Field<string>;
    NoOfItems: Field<string>;
    CarouselData: {
      fields: {
        Description: Field<string>;
        Image: any;
        Title: Field<string>;
        TileUrl: Field<string>;
      };
    }[];
  };
};

const LearnComponent = (props: LearnComponentProps): JSX.Element => {
  const fields = props.fields;
  const [currentIndex, setCurrentIndex] = useState(0);
  const noOfSlides = 4;
  const totalItems = fields.CarouselData.length;

  const nextSlide = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex + 1) % (totalItems - (noOfSlides - 1))
    );
  };

  const prevSlide = () => {
    setCurrentIndex(
      (prevIndex) =>
        (prevIndex - 1 + (totalItems - (noOfSlides - 1))) %
        (totalItems - (noOfSlides - 1))
    );
  };

  return (
    <section className="carousel">
      <div className="site">
        <div className="flex-wrapper">
          <Text field={fields.Heading} tag="h2" />
          <div className="tabs">
            <div className="tabbed flex-wrapper carousel-nav">
              <div className="nav-wrapper">
                <nav>
                  <button
                    className="tertiary material-icons"
                    onClick={prevSlide}
                    data-testid="carousel-left-click"
                  >
                    chevron_left
                  </button>
                  <button
                    className="tertiary material-icons"
                    onClick={nextSlide}
                    data-testid="carousel-right-click"
                  >
                    chevron_right
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
        <div className="cards carousel-cards flex-wrapper">
          {fields.CarouselData.map(
            (
              item: {
                fields: {
                  Description: Field<string>;
                  Image: any;
                  Title: Field<string>;
                  TileUrl: Field<string>;
                };
              },
              itemId: number
            ) => {
              return (
                <React.Fragment key={itemId}>
                  <Card
                    image={item?.fields?.Image}
                    description={item?.fields?.Description}
                    title={item?.fields?.Title}
                    tileUrl={item?.fields?.TileUrl}
                    heading={fields.Heading}
                    currentIndex={currentIndex}
                  />
                </React.Fragment>
              );
            }
          )}
        </div>
      </div>
    </section>
  );
};

export default withDatasourceCheck()<LearnComponentProps>(LearnComponent);
