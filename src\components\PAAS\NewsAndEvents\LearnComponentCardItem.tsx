import {
  Image,
  Text,
  RichText,
  Field,
} from "@sitecore-jss/sitecore-jss-nextjs";

const LearnComponentCardItem = (props: {
  description: Field<string>;
  image: any;
  title: Field<string>;
  tileUrl: Field<string>;
  heading: Field<string>;
  currentIndex: number;
}): JSX.Element => {
  const { image, description, title, tileUrl, heading, currentIndex } = props;

  return (
    <div
      className="card four"
      data-interaction="click"
      data-refinement-title={title?.value}
      data-region={heading?.value}
      style={{
        transform: `translateX(-${currentIndex * 100}%)`,
        transition: "transform 0.5s",
      }}
    >
      <Image field={image} />
      <a href={tileUrl?.value} target="_blank">
        <Text className="cardTitle" field={title} tag="h3" />
      </a>
      <RichText field={description} tag="p" />
    </div>
  );
};

export default LearnComponentCardItem;
