import {
  Field,
  Image,
  RichText,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import { monthNames } from "./NewsAndEventsConstants";
import { useEffect } from "react";
import { removeImageAttributes } from "../PaasUtilities/RemoveInlineCss";

type CardsProps = ComponentProps & {
  fields: {
    FeaturedTileCards: {
      fields: {
        TileUrl: Field<string>;
        EndDate: Field<string>;
        Date: Field<string>;
        Header: Field<string>;
        Description: Field<string>;
        TileImage: any;
      };
    }[];
    Heading: Field<string>;
  };
};

const Cards = (props: CardsProps): JSX.Element => {
  useEffect(() => {
    removeImageAttributes();
  });
  return (
    <>
      <section className="hover-lift-cards">
        <div className="site">
          <Text field={props?.fields?.Heading} tag="h2" />
          <div className="cards flex-wrapper card-wrap">
            {props?.fields?.FeaturedTileCards.map(
              (
                news: {
                  fields: {
                    EndDate: Field<string>;
                    Date: Field<string>;
                    Header: Field<string>;
                    Description: Field<string>;
                    TileImage: any;
                    TileUrl: Field<string>;
                  };
                },
                id: number
              ) => {
                const date = new Date(news?.fields?.Date?.value);
                const convertedDate = new Date(
                  date.getTime() - date.getTimezoneOffset() * -60000
                );
                const dateValue =
                  convertedDate.getFullYear() > 1
                    ? monthNames[convertedDate.getMonth()] +
                      " " +
                      convertedDate.getDate() +
                      " " +
                      convertedDate.getFullYear()
                    : "";
                return (
                  <article
                    key={id}
                    className="card four"
                    data-interaction="click"
                    data-refinement-title={news?.fields?.Header.value}
                    data-region={props?.fields?.Heading?.value}
                  >
                    <a
                      href={news?.fields?.TileUrl?.value}
                      target="_blank"
                      className="flex-wrapper card-section"
                    >
                      <Text field={news?.fields?.Header} tag="h3" />
                      <Image field={news?.fields?.TileImage} />
                      <RichText field={news?.fields?.Description} tag="p" />
                      <p>
                        <time>{dateValue}</time>
                      </p>
                    </a>
                  </article>
                );
              }
            )}
          </div>
        </div>
      </section>
    </>
  );
};

export default withDatasourceCheck()<CardsProps>(Cards);
