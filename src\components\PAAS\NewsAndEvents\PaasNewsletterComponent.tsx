import {
  Field,
  Image,
  RichText,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type BannerGreyProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    Description: Field<string>;
    EmailToUs: Field<string>;
    Image: any;
    MailSubject: Field<string>;
    ButtonLink: Field<string>;
  };
};

const BannerGrey = (props: BannerGreyProps): JSX.Element => {
  return (
    <section
      className="paas-monthly-newsletter background-lt-grey"
      data-interaction="click"
      data-refinement-title={props?.fields?.Title.value}
      data-region={props?.fields?.Title.value}
    >
      <div className="site flex-wrapper">
        <div>
          <Text field={props?.fields?.Title} tag="h2" />
          <RichText field={props?.fields?.Description} />
          <div className="call-to-action">
            <a
              className="primary"
              href={`mailto:${props?.fields?.EmailToUs?.value}?subject=${props?.fields?.MailSubject?.value}`}
              target="blank"
              data-region={props?.fields?.Title?.value}
              data-interaction="click"
              data-title={props?.fields?.ButtonLink?.value}
            >
              {" "}
              <Text field={props?.fields?.ButtonLink} />
            </a>
          </div>
        </div>
        <div>
          <Image field={props?.fields?.Image} />
        </div>
      </div>
    </section>
  );
};

export default withDatasourceCheck()<BannerGreyProps>(BannerGrey);
