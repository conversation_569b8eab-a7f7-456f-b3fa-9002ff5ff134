export const CLASS_GUIDES = "Class Guides";
export const CLASS_GUIDE = "Class Guide";
export const FAQ = "FAQ";
export const FAQS = "FAQs";
export const WC_STATE_STATUTORY_EXCEPTION = "WC State Statutory Exception";
export const WC_STATE_STATUTORY_EXCEPTIONS = "WC State Statutory Exceptions";
export const INFOLINKS = "InfoLinks";
export const RATING_CARDS = "Rating Cards";
export const TRAINING_MANUALS = "Training Manuals";
export const BULLETIN = "Bulletin";
export const BULLETINS = "Bulletins";
export const EDUCATIONAL_BULLETINS = "Educational Bulletins";
export const INDUSTRY_GUIDES = "Industry Guides";
export const ALL_BULLETINS = "All Bulletins";
export const JURISDICTION_INFORMATION = "Jurisdiction Information";
export const JURISDICTION_INFORMATIONS = "Jurisdiction Informations";
export const GENERAL_LIABILITY = "General Liability";
export const FACETS_ARRAY = [
  "Lines of Business (LOBs)",
  "Content Type",
  "Jurisdictions",
  "Category",
];
export const JURISDICTION_KEY = "js";
export const DATE_RANGE_KEY = "dtg";
export const LOB_KEY = "lob";
export const SUB_LOB_KEY = "sublob";
export const CONTENT_TYPE_KEY = "ctype";
export const CATEGORY_KEY = "category";
export const TOPICS_KEY = "tp";

export const TABLE_HEADER = {
  Title: "Title",
  Code: "Code",
  LOB: "LOB",
  ContentType: "Content Type",
  Jurisdiction: "Jurisdiction",
  contenttype: "contenttype",
  ClassCode: "classcode",
};
export const SEARCH_REGEX = /(<([^>]+)>)/gi; //NOSONAR
export const GenAI_STREAM = /\d+\)/; //NOSONAR
