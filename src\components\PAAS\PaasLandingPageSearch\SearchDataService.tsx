export const searchDataApi = async (
  url: string,
  payload: any,
  accessToken: string
) => {
  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify(payload),
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + accessToken,
    },
  }).then((res) => res.json());
  return response;
};

export const getSuggestions = async (
  e: any,
  signal: any,
  accessToken: string
) => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/Suggestions?searchText=${e.target.value}`,
    {
      signal,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + accessToken,
      },
    }
  ).then((res) => res.json());
  return response;
};
