import React, {
  useState,
  useEffect,
  useRef,
  useContext,
  useCallback,
  useMemo,
} from "react";
import { useRouter } from "next/router";
import FacetLayout from "./LandingPageFacetLayout";
import SearchResultLayout from "./LandingPageSearchResultLayout";
import ClassGuideLayout from "../ClassGuide/ClassGuideParentComponent";
import { AuthContext } from "src/context/authContext";
import {
  formFacetFilterOptionsData,
  convertTableData,
} from "./SearchUtilities";
import { searchDataApi } from "./SearchDataService";
import {
  JURISDICTION_KEY,
  LOB_KEY,
  CONTENT_TYPE_KEY,
  CATEGORY_KEY,
  SUB_LOB_KEY,
} from "./SearchConstants";

import { withDatasourceCheck } from "@sitecore-jss/sitecore-jss-nextjs";
import {
  SearchProps,
  contentType,
  dataType,
  facetType,
  requestParamsType,
} from "../PaasUtilities/CustomTypes";
import _ from "lodash";

/*test*/
const SearchParentComponent = (props: SearchProps): React.JSX.Element => {
  const [data, setData] = useState([]);
  const [currentPage, setcurrentPage] = useState(1);
  const [itemsPerPage, setitemsPerPage] = useState(10);
  const [pageNumberLimit] = useState(3);
  const [totalResults, setTotalResults] = useState(0);
  const [pages, setPages] = useState([]);
  const [maxPageNumberLimit, setmaxPageNumberLimit] = useState(3);
  const [minPageNumberLimit, setminPageNumberLimit] = useState(0);
  const [facets, setFacets] = useState<FacetGroup[]>([]);
  const [pagination, setPagination] = useState<number>(0);
  const [isSpinner, setIsSpiner] = useState(true);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentTableItem, setCurrentTableItem] = useState<any>([]);
  const [sortColumn, setSortColumn] = useState<string>("");
  const [sortDirection, setSortDirection] = useState<string>("");

  const [lobValue, setlobValue] = useState([]);
  const [ctValue, setctValue] = useState([]);
  const [jsValue, setjsValue] = useState([]);
  const [csValue, setcsValue] = useState([]);
  const [subLobValue, setSubLobValue] = useState([]);
  const [tagCount, setTagCount] = useState<number>(0);
  const [maxResults, setMaxResults] = useState<number>(0);
  const [isTable, setIsTable] = useState(true);
  const [paasSearchClicked, setPaasSearchClicked] = useState(false);
  const router = useRouter();
  const queryParam = router?.query;
  const { userProductProfilesList } = useContext(AuthContext);

  type FetchParams = {
    queryParam: Record<string, any>;
    requestParams: requestParamsType;
    setRequestParams: (params: requestParamsType) => void;
    setIsSpiner: (isLoading: boolean) => void;
    callSearchAPI: (
      params: requestParamsType,
      isFirstRestfulCall?: boolean
    ) => void;
    setLobValue: (value: any[]) => void;
    setCtValue: (value: any[]) => void;
    setJsValue: (value: any[]) => void;
    setCsValue: (value: any[]) => void;
    setInitialRender: (isInitial: boolean) => void;
    setTagCount: (count: number) => void;
    setSearchTerm: (term: string) => void;
  };

  type FacetValue = {
    Id: string;
    Name: string;
    Key: string;
    Count: number;
    Selected: boolean;
  };

  type FacetGroup = {
    Key: string;
    Name: string;
    Values: FacetValue[];
  };

  const [requestParams, setRequestParams] = useState<requestParamsType>({
    queryParameter: "",
    pageNumb: 1,
    dropDownCount: 25,
    sortDirection: sortDirection,
    sortItem: sortColumn,
    lob: [],
    cs: [],
    ct: [],
    js: [],
    sublob: [],
  });

  const [searchParams, setSearchParams] = useState("");

  const [initialRender, setInitialRender] = useState(true);
  const isFirstMounting = useRef(true);
  const startIndex: number = (currentPage - 1) * itemsPerPage;
  const endIndex: number = startIndex + itemsPerPage;
  const fromResult: number = startIndex + 1;
  const toResult: number = Math.min(endIndex, pagination);
  const { accessToken } = useContext(AuthContext);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const updateDigitalDataSearchInfo = (
    searchTerm: string,
    searchSource: string,
    searchSuccess: string
  ) => {
    window.digitalData.search.info.searchterm = searchTerm;
    window.digitalData.search.info.searchSource = searchSource;
    window.digitalData.search.info.searchsuccess = searchSuccess;
  };
  const updateDigitalDataSearchAttributes = (checkedFacetTitles: string[]) => {
    window.digitalData.search.refinements = checkedFacetTitles.join(",");
  };
  const dispatchSearchEvent = () => {
    const evt = new CustomEvent("event-action-search");
    document?.body.dispatchEvent(evt);
  };
  const handleSearchSuccess = (PAASSearchItems: any[], Facets: any[]) => {
    const checkedFacetTitles: string[] = [];
    const orderedFacets = formFacetFilterOptionsData(Facets);
    orderedFacets?.forEach((facet: any) => {
      const checkedFacet = facet?.Values?.filter(
        (value: any) => value?.Selected
      );
      if (checkedFacet?.length !== 0) {
        checkedFacetTitles.push(facet?.Name);
      }
    });
    updateDigitalDataSearchAttributes(checkedFacetTitles);
    dispatchSearchEvent();
  };
  const handleSearchAnalytics = (
    PAASSearchItems: any[],
    Facets: any[],
    searchTerm: string,
    searchSource: string
  ) => {
    if (PAASSearchItems?.length !== 0 || Facets?.length !== 0) {
      if (PAASSearchItems?.length !== 0 && PAASSearchItems !== undefined) {
        updateDigitalDataSearchInfo(searchTerm, searchSource, "yes");
      } else {
        updateDigitalDataSearchInfo(searchTerm, searchSource, "no");
        window.digitalData.search.attributes = {};
      }
      handleSearchSuccess(PAASSearchItems, Facets);
    } else {
      updateDigitalDataSearchInfo(searchTerm, searchSource, "no");
      window.digitalData.search.attributes = {};
    }
  };
  const searchAnalytics = (
    response: any,
    searchTerm: any,
    searchSource: any
  ) => {
    const { PaasSearchItems, Facets } = response;
    if (searchParams === sessionStorage.getItem("requestParams")) {
      return;
    }
    setSearchParams(sessionStorage.getItem("requestParams") as string);
    if (typeof window !== "undefined" && window.digitalData) {
      handleSearchAnalytics(PaasSearchItems, Facets, searchTerm, searchSource);
    }
  };

  /* this  is to call search api on queryParam change */
  const fetchDataOnQueryParamChange = ({
    queryParam,
    requestParams,
    setRequestParams,
    setIsSpiner,
    callSearchAPI,
    setLobValue,
    setCtValue,
    setJsValue,
    setCsValue,
    setInitialRender,
    setTagCount,
    setSearchTerm,
  }: FetchParams) => {
    const tagCount = Number(sessionStorage.getItem("tagCount"));

    if (queryParam?.path?.includes("search")) {
      const sessionRequestParams: requestParamsType = {
        ...requestParams,
        pageNumb: 1,
        dropDownCount: 25,
        sortDirection: "",
        sortItem: "",
        lob: [],
        cs: [],
        ct: [],
        js: [],
        sublob: [],
        queryParameter: queryParam,
      };
      const { keyword } = queryParam;
      setTagCount(tagCount);
      if (keyword) {
        setSearchTerm(keyword);
        sessionStorage.setItem("searchTerm", keyword);
      }
      setRequestParams(sessionRequestParams);
      sessionStorage.setItem(
        "requestParams",
        JSON.stringify(sessionRequestParams)
      );
      callSearchAPI(sessionRequestParams, true);
      setLobValue([]);
      setCtValue([]);
      setJsValue([]);
      setCsValue([]);
      return;
    }

    const { keyword, js, lob, cs } = queryParam;

    // Handle keyword search
    if (keyword) {
      setSearchTerm(keyword);
      sessionStorage.setItem("searchTerm", keyword);
      setInitialRender(true);
      setTagCount(0);

      const freshRequestParams: requestParamsType = {
        ...requestParams,
        pageNumb: 1,
        dropDownCount: 25,
        sortDirection: "",
        sortItem: "",
        lob: [],
        cs: [],
        ct: [],
        js: [],
        sublob: [],
        queryParameter: queryParam,
      };

      setRequestParams(freshRequestParams);
      sessionStorage.setItem(
        "requestParams",
        JSON.stringify(freshRequestParams)
      );
      callSearchAPI(freshRequestParams, true);
      setLobValue([]);
      setCtValue([]);
      setJsValue([]);
      setCsValue([]);
      return;
    }

    // Prepare fresh request parameters based on available filters
    const freshRequestParams: requestParamsType = {
      ...requestParams,
      pageNumb: 1,
      dropDownCount: 25,
      lob: lob ? [lob] : [],
      ct: [],
      js: js ? [js] : [],
      cs: cs ? [cs] : [],
    };

    // Handle filter-based search
    if (js || lob || cs) {
      setInitialRender(true);
      setRequestParams(freshRequestParams);
      callSearchAPI(freshRequestParams, true);
      setLobValue(lob ? [lob] : []);
      setCtValue([]);
      setJsValue(js ? [js] : []);
      setCsValue(cs ? [cs] : []);
      return;
    }

    setIsSpiner(false);
    setInitialRender(false);
  };

  /* This is rendered for the first time when component mounts */
  useEffect(() => {
    if (!(initialRender && isFirstMounting.current)) return;

    const params: FetchParams = {
      queryParam,
      requestParams,
      setRequestParams,
      setIsSpiner,
      callSearchAPI,
      setLobValue: setlobValue,
      setCtValue: setctValue,
      setJsValue: setjsValue,
      setCsValue: setcsValue,
      setInitialRender,
      setTagCount,
      setSearchTerm,
    };

    fetchDataOnQueryParamChange(params);
    return () => {
      isFirstMounting.current = false;
    };
  }, []);

  /* this is called when queryParam changes(route changes) */
  useEffect(() => {
    if (initialRender || paasSearchClicked) {
      if (paasSearchClicked) {
        setPaasSearchClicked(false);
        setIsTable(isTable);
      }
      return;
    }
    if (currentTableItem.length === 0) {
      setIsTable(true);
      setmaxPageNumberLimit(3);
      setminPageNumberLimit(0);

      const params = {
        queryParam,
        requestParams,
        setRequestParams,
        setIsSpiner,
        callSearchAPI,
        setLobValue: setlobValue,
        setCtValue: setctValue,
        setJsValue: setjsValue,
        setCsValue: setcsValue,
        setInitialRender,
        setTagCount,
        setSearchTerm,
      };

      fetchDataOnQueryParamChange(params);
    }

    if (!queryParam.Keyword) {
      setTotalResults(0);
    }
  }, [queryParam]);

  /*this is called when user clicks on any UI events inside search page */
  useEffect(() => {
    if (initialRender) return;

    const { keyword } = queryParam;
    const updatedRequestParams = { ...requestParams };
    const existingParams = sessionStorage.getItem("requestParams");
    const hasParamsChanged =
      existingParams !== JSON.stringify(updatedRequestParams);

    if (keyword) {
      updatedRequestParams.queryParameter = queryParam;
      sessionStorage.setItem(
        "requestParams",
        JSON.stringify(updatedRequestParams)
      );
      sessionStorage.setItem("tagCount", tagCount.toString());

      if (!isSpinner || hasParamsChanged) {
        callSearchAPI(updatedRequestParams);
      }
    } else {
      callSearchAPI(updatedRequestParams, false, paasSearchClicked);
    }
  }, [requestParams, paasSearchClicked]);

  /* This handles the user click on page number */
  const handlePageNumberClick = useCallback(
    (
      event:
        | React.MouseEvent<HTMLAnchorElement>
        | React.KeyboardEvent<HTMLAnchorElement>
    ) => {
      scrollToTop();

      const pageNumber = Number(event.currentTarget.id);

      // Update requestParams with the new page number
      setRequestParams((prevParams) => ({
        ...prevParams,
        pageNumb: pageNumber,
      }));
    },
    []
  );

  /* this handles the results per page widget under the table */
  const handleResultsPerPage = useCallback(
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      setmaxPageNumberLimit(3);
      setminPageNumberLimit(0);
      scrollToTop();

      const resultsPerPage = Number(event.target.value);

      // Update requestParams with the new dropDownCount and reset page number
      setRequestParams((prevParams) => ({
        ...prevParams,
        dropDownCount: resultsPerPage,
        pageNumb: 1,
      }));
    },
    []
  );

  /* for rendering page numbers */
  const renderPageNumbers = useMemo(() => {
    return pages?.map((number: number) => {
      // Check if the number is within the limits
      if (number < maxPageNumberLimit + 1 && number > minPageNumberLimit) {
        return (
          <li className="page-item page-number" key={number}>
            <a
              id={number.toString()}
              onClick={(e) => handlePageNumberClick(e)}
              onKeyUp={(e) => e.key === "Enter" && handlePageNumberClick(e)}
              className={
                currentPage === number ? "page-link active" : "page-link"
              }
              data-testid="page-number-click"
              tabIndex={0}
            >
              {number}
            </a>
          </li>
        );
      }
      return null;
    });
  }, [
    pages,
    maxPageNumberLimit,
    minPageNumberLimit,
    currentPage,
    handlePageNumberClick,
  ]);

  /* to retrieve the customer number from active profile */
  const setCustomerNumberFromProfile = () => {
    const selectedProfiles = localStorage.getItem("selectedProfiles");
    const parsedProfiles = selectedProfiles
      ? JSON.parse(selectedProfiles)
      : null;

    if (parsedProfiles?.PAAS?.customerNumber) {
      return parsedProfiles.PAAS.customerNumber;
    }

    const defaultCustomerProfile =
      userProductProfilesList?.find(
        (profile: any) => profile.feature === "PAAS"
      )?.customers || [];

    return defaultCustomerProfile[0]?.customerNumber;
  };

  /* to get the data from search api */
  const callSearchAPI = async (
    requestParams: requestParamsType,
    isFirstRestfulCall?: boolean,
    isPaasSearchClicked?: boolean
  ) => {
    let isMounted = true;
    const {
      queryParameter: { keyword },
      dropDownCount,
      pageNumb,
      lob,
      sublob,
      js,
      ct,
      cs,
      sortItem,
      sortDirection,
    } = requestParams;

    const payload = {
      Keyword: keyword || sessionStorage.getItem("searchTerm"),
      PageSize: dropDownCount.toString(),
      PageNumber: pageNumb,
      LineOfBusiness: lob.toString(),
      SubLob: sublob.toString(),
      Jurisdiction: js.toString(),
      ContentSubType: ct.toString(),
      ClasGuideCategory: cs.toString(),
      MaxResults: maxResults < 25 ? requestParams.dropDownCount : maxResults,
      SortItem: sortItem,
      SortOrder: sortDirection,
      CustomerNumber: setCustomerNumberFromProfile(),
      IsFirstRestfulCall: isFirstRestfulCall || isPaasSearchClicked,
    };

    try {
      const url = requestParams.hasOwnProperty("queryParameter")
        ? `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/SearchResult`
        : "";

      setIsSpiner(true);
      const post = await searchDataApi(url, payload, accessToken);
      if (isMounted) {
        const {
          PaasSearchItems,
          Facets,
          PaginationInfo,
          StatusCode,
          TotalResult,
        } = post;
        setData(PaasSearchItems);
        if (isFirstRestfulCall && StatusCode === 200 && TotalResult > 0) {
          setFacets(Facets);
        }
        setPagination(PaginationInfo?.RecordCount);
        setTotalResults(PaasSearchItems?.length);
        setcurrentPage(PaginationInfo?.CurrentPage);
        setitemsPerPage(PaginationInfo?.PageSize);
        setTotalPages(PaginationInfo?.TotalPages);
        setMaxResults(PaginationInfo?.RecordCount);
        setPages(
          Array.from({ length: PaginationInfo?.TotalPages }, (_, i) => i + 1)
        );
        setInitialRender(false);
      }

      if (typeof window !== "undefined") {
        searchAnalytics(
          post,
          requestParams.hasOwnProperty("queryParameter") ? keyword : "",
          queryParam?.searchSource || ""
        );
      }

      return post;
    } catch (error) {
      console.error("Error in Search API:", error);
    } finally {
      setIsSpiner(false);
      isMounted = false;
    }
  };

  const updateFacetSelections = ({
    facetKey,
    compareList,
    isSelected,
  }: {
    facetKey: string;
    compareList: { Id: string }[];
    isSelected: boolean;
  }) => {
    setFacets((prevFacets) =>
      prevFacets.map((facetGroup) => {
        if (facetGroup.Key !== facetKey) return facetGroup;

        return {
          ...facetGroup,
          Values: facetGroup.Values.map((item) => {
            const shouldUpdate = compareList.some((x) => x.Id === item.Id);
            return shouldUpdate ? { ...item, Selected: isSelected } : item;
          }),
        };
      })
    );
  };

  /* handles toggling of All Bulletins checkbox */
  const handleAllBulletinsCheckbox = useCallback(
    (
      event: React.ChangeEvent<HTMLInputElement>,
      allBulletins: contentType[]
    ) => {
      setmaxPageNumberLimit(3);
      setminPageNumberLimit(0);

      const facetKey = event.target.dataset.key!;
      const isChecked = event.target.checked;
      updateFacetSelections({
        facetKey,
        compareList: allBulletins,
        isSelected: isChecked,
      });

      const newCtValue = isChecked
        ? _.uniq([...ctValue, ...allBulletins.map((bulletin) => bulletin.Id)])
        : ctValue.filter(
            (id) => !allBulletins.some((bulletin) => bulletin.Id === id)
          );

      setTagCount(isChecked ? tagCount + 3 : tagCount - 3);
      setctValue(newCtValue);
      setRequestParams((prevParams) => ({
        ...prevParams,
        ct: newCtValue,
        pageNumb: 1,
      }));
    },
    [ctValue, tagCount]
  );

  /* handles toggling of Class guides and jurisdiction information checkbox */
  const handleClassGuidesCheckbox = useCallback(
    (
      event: React.ChangeEvent<HTMLInputElement>,
      classGuides: contentType[]
    ) => {
      setmaxPageNumberLimit(3);
      setminPageNumberLimit(0);

      const facetKey = event.target.dataset.key!;
      const isChecked = event.target.checked;
      updateFacetSelections({
        facetKey,
        compareList: classGuides,
        isSelected: isChecked,
      });

      const newCtValue = isChecked
        ? [...ctValue, ...classGuides.map((classGuide) => classGuide.Id)]
        : ctValue.filter(
            (id) => !classGuides.some((classGuide) => classGuide.Id === id)
          );

      setTagCount(tagCount + (isChecked ? 1 : -1));
      setctValue(newCtValue);
      setRequestParams((prevParams) => ({
        ...prevParams,
        ct: newCtValue,
        pageNumb: 1,
      }));
    },
    [ctValue, tagCount]
  );

  /* handles user click on facets */
  const handleFacetChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, classGuides: object[]) => {
      setmaxPageNumberLimit(3);
      setminPageNumberLimit(0);

      const facetKey = event.target.dataset.key;
      const value = event.target.value;
      const isChecked = event.target.checked;
      const isCloseAction = event.target.innerHTML === "close";

      const updateTagCount = (increment: boolean) => {
        setTagCount((prevTagCount) => prevTagCount + (increment ? 1 : -1));
      };

      const updateValueArray = (array: string[], value: string) => {
        const index = array.indexOf(value);
        if (index !== -1) {
          array.splice(index, 1);
        }
      };

      const updateFacetsSelectedState = (value: string, selected: boolean) => {
        setFacets((prevFacets) =>
          prevFacets.map((facetGroup) => {
            if (facetGroup.Key !== facetKey) return facetGroup;
            return {
              ...facetGroup,
              Values: facetGroup.Values.map((item: any) =>
                item.Id === value ? { ...item, Selected: selected } : item
              ),
            };
          })
        );
      };

      const handleCheckedFacet = (key: string, valueArray: string[]) => {
        valueArray.push(value);
        if (valueArray.length === new Set(valueArray).size) {
          updateTagCount(true);
        }
        const uniqueValues = Array.from(new Set(valueArray));
        updateFacetsSelectedState(value, true);
        setRequestParams((prevParams) => ({
          ...prevParams,
          [key]: uniqueValues,
          pageNumb: 1,
        }));
        return uniqueValues;
      };

      const processUncheckedValues = (valueArray: string[]) => {
        if (isCloseAction) {
          updateValueArray(valueArray, event.target.id);
          updateFacetsSelectedState(event.target.id, false);

          // Handle class guides if they exist
          classGuides?.forEach((classGuide: contentType) => {
            updateValueArray(valueArray, classGuide.Id);
            updateFacetsSelectedState(classGuide.Id, false);
          });
        } else {
          updateValueArray(valueArray, value);
          updateFacetsSelectedState(value, false);
        }
      };

      const handleUncheckedFacet = (key: string, valueArray: string[]) => {
        updateTagCount(false);
        processUncheckedValues(valueArray);
        setRequestParams((prevParams) => ({
          ...prevParams,
          [key]: valueArray,
          pageNumb: 1,
        }));
        return valueArray;
      };

      const handleFacet = (key: string, valueArray: string[]) => {
        return isChecked
          ? handleCheckedFacet(key, valueArray)
          : handleUncheckedFacet(key, valueArray);
      };

      const facetStateMap: Record<
        string,
        {
          key: string;
          value: string[];
          setter: React.Dispatch<React.SetStateAction<string[]>>;
        }
      > = {
        [LOB_KEY]: { key: "lob", value: lobValue, setter: setlobValue },
        [CONTENT_TYPE_KEY]: { key: "ct", value: ctValue, setter: setctValue },
        [JURISDICTION_KEY]: { key: "js", value: jsValue, setter: setjsValue },
        [CATEGORY_KEY]: { key: "cs", value: csValue, setter: setcsValue },
        [SUB_LOB_KEY]: {
          key: "sublob",
          value: subLobValue,
          setter: setSubLobValue,
        },
      };

      const facetConfig = facetKey ? facetStateMap[facetKey] : undefined;

      if (facetConfig) {
        facetConfig.setter(handleFacet(facetConfig.key, facetConfig.value));
      }
    },
    [lobValue, ctValue, jsValue, csValue, subLobValue]
  );

  /* handles clear all tags button  */
  const clearAllTags = useCallback((facets: facetType[]) => {
    const updatedFacets =
      facets?.map((facet: facetType) => ({
        ...facet,
        Values:
          facet.Values?.map((option: contentType) => ({
            ...option,
            Selected: false,
          })) || [],
      })) || [];

    setFacets(updatedFacets);

    // Clear all value arrays
    [lobValue, ctValue, csValue, jsValue].forEach((arr) => (arr.length = 0));

    setTagCount(0);
    setTotalResults(0);
    router.push("/PAAS/search");
    sessionStorage.removeItem("requestParams");
    sessionStorage.removeItem("tagCount");
    setCurrentTableItem([]);
  }, []);

  /* handles table column sorting */
  const handleSortClick = useCallback(
    (column: string) => {
      const isSameColumn = column === sortColumn;
      const newSortDirection =
        isSameColumn && sortDirection === "asc" ? "desc" : "asc";

      setRequestParams((prevParams) => ({
        ...prevParams,
        sortItem: column,
        sortDirection: isSameColumn ? newSortDirection : "asc",
      }));

      setSortDirection(newSortDirection);
      setSortColumn(column);
    },
    [sortColumn, sortDirection]
  );

  /* handling pagination buttons click */
  const handleNextbtn = useCallback(() => {
    scrollToTop();

    setRequestParams((prevParams) => ({
      ...prevParams,
      pageNumb: currentPage + 1,
    }));

    const newPage = currentPage + 1;
    if (newPage > maxPageNumberLimit) {
      setmaxPageNumberLimit((prevMax) => prevMax + pageNumberLimit);
      setminPageNumberLimit((prevMin) => prevMin + pageNumberLimit);
    }

    setcurrentPage(newPage);
  }, [currentPage, maxPageNumberLimit, pageNumberLimit]);

  const handleExtremeNextbtn = useCallback(() => {
    scrollToTop();

    if (pages.length > 0) {
      const lastPage = totalPages;

      setRequestParams((prevParams) => ({
        ...prevParams,
        pageNumb: lastPage,
      }));

      setmaxPageNumberLimit(lastPage);
      setminPageNumberLimit(
        lastPage % pageNumberLimit === 0
          ? lastPage - (pageNumberLimit + 1) + 1
          : lastPage - ((lastPage % pageNumberLimit) - 1) - 1
      );
    }
  }, [pages.length, totalPages, pageNumberLimit]);

  const handleExtremePrevbtn = useCallback(() => {
    scrollToTop();

    if (pages.length > 0) {
      const firstPage = pages[0];

      setRequestParams((prevParams) => ({
        ...prevParams,
        pageNumb: firstPage,
      }));

      setcurrentPage(firstPage);
      setmaxPageNumberLimit(pageNumberLimit);
      setminPageNumberLimit(firstPage - 1);
    }
  }, [pages, pageNumberLimit]);

  const handlePrevbtn = useCallback(() => {
    scrollToTop();

    const newPage = currentPage - 1;

    setRequestParams((prevParams) => ({
      ...prevParams,
      pageNumb: newPage,
    }));

    if (newPage % pageNumberLimit === 0) {
      setmaxPageNumberLimit(newPage);
      setminPageNumberLimit(newPage - pageNumberLimit);
    }

    setcurrentPage(newPage);
  }, [currentPage, pageNumberLimit]); /* end of pagination handlers */

  /* handles user click for a specific row item in tableView or ListView  */
  const getUrl = (selectedTableItem: dataType) => {
    if (!selectedTableItem) return "";

    const { ContentType, ParentItemID, ItemID, Lobs, ContentSubType } =
      selectedTableItem;
    const cleanId = (id: string) => id.replace(/[{()}]/g, "");

    let path = "";

    if (ContentType?.includes("Industry Guide")) {
      path = `/PAAS/industryguide/?id=${cleanId(
        ParentItemID
      )}&chapterid=${cleanId(ItemID)}`;
    } else if (ContentType?.includes("Training Manual")) {
      path = `/PAAS/training-manual/?id=${cleanId(
        ParentItemID
      )}&chapterid=${cleanId(ItemID)}`;
    } else {
      const lobCodes = ContentType?.includes("Class Guide")
        ? Lobs?.map((lob) => lob?.Code).join(",")
        : "";

      const contentType = ContentType?.includes("Bulletin")
        ? ContentSubType
        : ContentType;
      if (lobCodes) {
        path = `/PAAS/search/?contentType=${lobCodes} ${contentType}&id=${cleanId(
          ItemID
        )}`;
      } else
        path = `/PAAS/search/?contentType=${contentType}&id=${cleanId(ItemID)}`;
    }

    return path;
  };

  const onTableItemClick = (selectedTableItem: dataType) => {
    setCurrentTableItem(selectedTableItem);
    return getUrl(selectedTableItem);
  };

  const hasAnyFiltersSelected =
    lobValue.length > 0 ||
    ctValue.length > 0 ||
    jsValue.length > 0 ||
    csValue.length > 0 ||
    subLobValue.length > 0;

    console.log('props?.fields', props?.fields);
  return (
    <>
      {queryParam.hasOwnProperty("contentType") ? (
        <>
          <ClassGuideLayout
            searchTerm={searchTerm}
            currentTableItem={currentTableItem}
            setCurrentTableItem={setCurrentTableItem}
            setPaasSearchClicked={setPaasSearchClicked}
            classGuideTabs={props?.fields?.ClassGuideTabs}
            faqTabs={props?.fields?.FaqTabs}
            restProps={props?.fields}
          />
        </>
      ) : (
        <div className="site flex-wrapper">
          <>
            <FacetLayout
              facets={formFacetFilterOptionsData(facets)}
              handleFacetChange={handleFacetChange}
              totalResults={totalResults}
              handleAllBulletinsCheckbox={handleAllBulletinsCheckbox}
              handleClassGuidesCheckbox={handleClassGuidesCheckbox}
              accessToken={accessToken}
              setCurrentTableItem={setCurrentTableItem}
              hasAnyFiltersSelected={hasAnyFiltersSelected}
            />
            <SearchResultLayout
              facets={formFacetFilterOptionsData(facets)}
              handleFacetChange={handleFacetChange}
              tagCount={tagCount}
              onTableItemClick={onTableItemClick}
              getUrl={getUrl}
              clearAllTags={clearAllTags}
              textFields={props?.fields}
              isSpinner={isSpinner}
              requestParams={requestParams}
              data={convertTableData(data)}
              totalResults={totalResults}
              setTotalResults={setTotalResults}
              fromResult={fromResult}
              toResult={toResult}
              pagination={pagination}
              itemsPerPage={itemsPerPage}
              renderPageNumbers={renderPageNumbers}
              currentPage={currentPage}
              pages={pages}
              totalPages={totalPages}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              handleSortClick={handleSortClick}
              handleResultsPerPage={handleResultsPerPage}
              handleExtremePrevbtn={handleExtremePrevbtn}
              handlePrevbtn={handlePrevbtn}
              handleNextbtn={handleNextbtn}
              handleExtremeNextbtn={handleExtremeNextbtn}
              isTable={isTable}
              setIsTable={setIsTable}
            />
          </>
        </div>
      )}
    </>
  );
};

export default withDatasourceCheck()<SearchProps>(SearchParentComponent);
