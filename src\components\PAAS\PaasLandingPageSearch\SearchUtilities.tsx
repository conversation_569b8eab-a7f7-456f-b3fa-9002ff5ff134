import { decodeHTMLEntities } from "components/PAAS/PaasUtilities/DecodeHtmlEntities";
import {
  LOB_KEY,
  CONTENT_TYPE_KEY,
  JURISDICTION_KEY,
  CATEGORY_KEY,
  SUB_LOB_KEY,
  BULLETIN,
  CLASS_GUIDE,
  FAQ,
  WC_STATE_STATUTORY_EXCEPTION,
  SEARCH_REGEX,
} from "./SearchConstants";
import { dataType, facetType } from "../PaasUtilities/CustomTypes";

/* Converting facets data to get the desired view */
export const formFacetFilterOptionsData = (facets: facetType[]) => {
  const orderedFacets: facetType[] = [];
  facets?.map((facet: facetType) => {
    if (facet.Key === LOB_KEY) {
      orderedFacets.push(facet);
    }
  });
  facets?.map((facet: facetType) => {
    if (facet.Key === CONTENT_TYPE_KEY) {
      orderedFacets.push(facet);
    }
  });
  facets?.map((facet: facetType) => {
    if (facet.Key === JURISDICTION_KEY) {
      orderedFacets.push(facet);
    }
  });
  facets?.map((facet: facetType) => {
    if (facet.Key === CATEGORY_KEY) {
      orderedFacets.push(facet);
    }
  });
  facets?.map((facet: facetType) => {
    if (facet.Key === SUB_LOB_KEY) {
      orderedFacets.push(facet);
    }
  });
  return orderedFacets;
};

/* Converting tale data to get the desired Table view */
export const convertTableData = (data: dataType[]) => {
  return data?.map((result: dataType) => {
    const stateArray: string[] = [];
    const lobArray: string[] = [];
    const lobNameArray: string[] = [];
    let contentBasedNotes: string = "";
    result?.Jurisdiction?.map((state: { Code: string; Name: string }) => {
      stateArray.push(state.Code);
    });
    result?.Lobs?.map((lob: { Code: string; Name: string }) => {
      lobArray.push(lob.Code);
      lobNameArray.push(lob.Name);
    });
    if (result.ContentType === CLASS_GUIDE) {
      if (
        decodeHTMLEntities(result.Notes).replace(SEARCH_REGEX, "").length > 500
      ) {
        contentBasedNotes =
          "Note: " +
          decodeHTMLEntities(result.Notes)
            .replace(SEARCH_REGEX, "")
            .replace(/\s{2,}/g, "")
            .replace(/\&nbsp;/g, " ")
            .slice(0, 500)
            .trim() +
          "...";
      } else {
        contentBasedNotes =
          "Note: " +
          decodeHTMLEntities(result.Notes)
            .replace(SEARCH_REGEX, "")
            .replace(/\s{2,}/g, "")
            .replace(/\&nbsp;/g, " ")
            .slice(0, 500);
      }
    } else if (result.ContentType === FAQ) {
      contentBasedNotes = decodeHTMLEntities(result.Notes)
        .replace(SEARCH_REGEX, "")
        .replace(/\s{2,}/g, "")
        .replace(/\&nbsp;/g, " ");
    } else if (
      result.ContentSubType === WC_STATE_STATUTORY_EXCEPTION ||
      result.ContentType === BULLETIN
    ) {
      if (
        decodeHTMLEntities(result.Notes).replace(SEARCH_REGEX, "").length > 200
      ) {
        contentBasedNotes =
          decodeHTMLEntities(result.Notes)
            .replace(SEARCH_REGEX, "")
            .replace(/\s{2,}/g, "")
            .replace(/\&nbsp;/g, " ")
            .slice(0, 200)
            .trim() + "...";
      } else {
        contentBasedNotes = decodeHTMLEntities(result.Notes)
          .replace(SEARCH_REGEX, "")
          .replace(/\s{2,}/g, "")
          .replace(/\&nbsp;/g, " ")
          .slice(0, 200);
      }
    }
    return {
      ...result,
      jurisdictionArray: stateArray,
      lobsArray: lobArray,
      lobsNameArray: lobNameArray,
      contentNotes: contentBasedNotes,
    };
  });
};
