export const removeInlineCss = (text: string) => {
  const htmlNode = document.createElement("div");
  htmlNode.innerHTML = text;
  htmlNode
    .querySelectorAll("*:not(table,img,tbody,tr,td)")
    .forEach((node) => node.removeAttribute("style"));
  htmlNode
    .querySelectorAll("*:not(table,img,tbody,tr,td)")
    .forEach((node) => node.removeAttribute("size"));
  return htmlNode.innerHTML;
};

export const removeImageAttributes = () => {
  document
    .querySelectorAll("img")
    .forEach((node: any) => node.removeAttribute("width"));
  document
    .querySelectorAll("img")
    .forEach((node: any) => node.removeAttribute("height"));
};
