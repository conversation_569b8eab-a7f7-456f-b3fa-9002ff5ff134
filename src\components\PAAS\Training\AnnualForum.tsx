import {
  RichText,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
//test
const AnnualForum = (props: any): JSX.Element => {
  return (
    <section className="background-lt-grey">
      <Text field={props?.fields?.Heading} tag="h2" />
      <RichText field={props?.fields?.Description} tag="p" />
      <button className="primary">
        {props?.fields?.EventInformation?.value?.text}
      </button>
    </section>
  );
};

export default withDatasourceCheck()(AnnualForum);
