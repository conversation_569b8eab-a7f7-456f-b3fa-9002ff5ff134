import {
  RichText,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";

const ProfessionalDevelopment = (props: any): JSX.Element => {
  return (
    <section className="background-lt-grey">
      <Text field={props?.fields?.Heading} tag="h2" />
      <RichText field={props?.fields?.Description} />
    </section>
  );
};

export default withDatasourceCheck()(ProfessionalDevelopment);
