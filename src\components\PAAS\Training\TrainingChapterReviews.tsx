import { useState } from "react";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";

export const TrainingChapterReviews = (props: any) => {
  const { review } = props;
  const [openDetails, setOpenDetails] = useState(false);
  return (
    <>
      <p>
        <strong
          dangerouslySetInnerHTML={{
            __html: removeInlineCss(review?.Question),
          }}
        ></strong>
      </p>
      <div className="accordion">
        <div
          className={openDetails ? "accordionTab active" : "accordionTab"}
          role="button"
          aria-expanded="false"
          aria-controls="accordion-content-0"
          onClick={() => {
            setOpenDetails(!openDetails);
          }}
        >
          <h3>View Answer</h3>
          <span className="material-symbols-outlined icon">expand_more</span>
        </div>
        <div
          className={
            openDetails ? "accordionContent active" : "accordionContent"
          }
        >
          <div className="flex-wrap">
            <p
              dangerouslySetInnerHTML={{
                __html: removeInlineCss(review?.Answer),
              }}
            ></p>
          </div>
        </div>
      </div>
    </>
  );
};
