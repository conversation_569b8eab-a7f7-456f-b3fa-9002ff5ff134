import {
  Field,
  RichText,
  Text,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { useState, useContext, useEffect } from "react";
import { AuthContext } from "src/context/authContext";
import { useRouter } from "next/router";
import { TrainingContent } from "./TrainingManualContent";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";

const TrainingManual = (props: any): JSX.Element => {
  const [activeAccordions, setActiveAccordions] = useState<any>({
    wc: true,
    gl: true,
    ca: true,
  });
  const selectedCustomerNumber = useGetSelectedCustomerNumber();
  const { paasEntitlement } = useContext(AuthContext);
  const router = useRouter();
  const queryParameter = router.query;

  const onTrainingManualClick = (trainingManualId: string) => {
    router.push(`/PAAS/training-manual/?id=${formatId(trainingManualId)}`);
  };

  const onChapterClick = (chapter: any, trainingManualID: string) => {
    router.push(
      `/PAAS/training-manual/?id=${formatId(
        trainingManualID
      )}&chapterid=${formatId(chapter?.id)}`
    );
  };

  const formatId = (id: string) => {
    return id
      ?.toUpperCase()
      .replace(/(.{8})(.{4})(.{4})(.{4})(.{12})/, "$1-$2-$3-$4-$5");
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const elements = document.getElementsByClassName("rich-text-component");
      Array.from(elements).forEach((element) => {
        element.querySelectorAll("*").forEach((node: any) => {
          node.removeAttribute("style");
        });
      });
    }
  }, []);

  const renderSection = (
    sectionType: string,
    heading: Field<string>,
    description: any,
    chapters: any
  ) => {
    const isActive = activeAccordions[sectionType];
    return (
      <>
        <div className="content-container">
          <h3>
            <Text
              field={heading}
              tag="a"
              onClick={() => onTrainingManualClick(chapters?.id)}
            />
          </h3>
          <RichText
            field={description}
            tag="p"
            className="rich-text-component"
          />
        </div>
        <div className="accordion">
          {chapters && (
            <div
              className={`accordionTab ${isActive ? "active" : ""}`}
              id="contentOverview"
              onClick={() =>
                setActiveAccordions((prev: any) => ({
                  ...prev,
                  [sectionType]: !prev[sectionType],
                }))
              }
            >
              <h4>Content Overview</h4>
              <span
                className="material-symbols-outlined icon"
                data-testid={`${sectionType}toggle`}
              >
                expand_more
              </span>
            </div>
          )}
          <div className={`accordionContent ${isActive ? "active" : ""}`}>
            <div className="site mobile">
              <div>
                {chapters?.children?.results
                  ?.slice(0, chapters?.children?.results?.length / 2)
                  .map((chapter: any, index: number) => (
                    <div key={chapter?.id || index} className="chapter">
                      <p className="no-margin-bottom">
                        {chapter?.Title?.value?.length > 0 && (
                          <a
                            onClick={() =>
                              onChapterClick(chapter, chapters?.id)
                            }
                            data-testid={`${sectionType}-chapter-click`}
                          >
                            <Text field={chapter?.Title} />
                          </a>
                        )}
                      </p>
                    </div>
                  ))}
              </div>
              <div>
                {chapters?.children?.results
                  ?.slice(
                    chapters?.children?.results?.length / 2,
                    chapters?.children?.results?.length
                  )
                  .map((chapter: any, index: number) => (
                    <div key={chapter?.id || index} className="chapter">
                      <p className="no-margin-bottom">
                        {chapter?.Title?.value?.length > 0 && (
                          <a
                            onClick={() =>
                              onChapterClick(chapter, chapters?.id)
                            }
                            data-testid={`${sectionType}-chapter-click`}
                          >
                            <Text field={chapter?.Title} />
                          </a>
                        )}
                      </p>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  return (
    <>
      {!queryParameter.hasOwnProperty("id") ? (
        <>
          <Text
            field={props?.fields?.data?.TrainingManualData?.Heading}
            tag="h2"
            className="no-margin-bottom no-margin-top"
          />
          <RichText
            field={props?.fields?.data?.TrainingManualData?.Description}
            tag="p"
            className="rich-text-component"
          />
          {paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_WC") &&
            renderSection(
              "wc",
              props?.fields?.data?.TrainingManualData?.WCHeading,
              props?.fields?.data?.TrainingManualData?.WCDescription,
              props?.fields?.data?.WorkerCompensationChapters
            )}
          {paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_GL") &&
            renderSection(
              "gl",
              props?.fields?.data?.TrainingManualData?.GLHeading,
              props?.fields?.data?.TrainingManualData?.GLDescription,
              props?.fields?.data?.GeneralLiabilityChapters
            )}
          {paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_CA") &&
            renderSection(
              "ca",
              props?.fields?.data?.TrainingManualData?.CAHeading,
              props?.fields?.data?.TrainingManualData?.CADescription,
              props?.fields?.data?.CommercialAutoChapters
            )}
        </>
      ) : (
        <TrainingContent
          trainingManualList={
            props?.fields?.data?.TrainingManualData?.TrainingManualList
              ?.targetItems
          }
        />
      )}
    </>
  );
};

export default withDatasourceCheck()(TrainingManual);
