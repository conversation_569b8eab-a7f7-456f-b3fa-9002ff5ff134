import React from "react";
import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { useRouter } from "next/router";

const TrainingManualBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const { isEEFlag } = rendering;
  const router = useRouter();
  const queryParameter = router.query;
  return (
    <>
      {!queryParameter.hasOwnProperty("id") ? (
        <section className="training-manuals">
          <div className="site flex-wrapper">
            <div className="content-wrapper">
              {route && (
                <Placeholder
                  name="jss-training-manual-left"
                  rendering={route}
                />
              )}
            </div>
            {!queryParameter.hasOwnProperty("id") ? (
              <aside>
                {route && (
                  <Placeholder
                    name="jss-training-manual-right"
                    isEEFlag={isEEFlag}
                    rendering={route}
                  />
                )}
              </aside>
            ) : (
              ""
            )}
          </div>
        </section>
      ) : (
        <>
          {route && (
            <Placeholder name="jss-training-manual-left" rendering={route} />
          )}
          {!queryParameter.hasOwnProperty("id") ? (
            <div className="training-manual-right">
              {route && (
                <Placeholder
                  name="jss-training-manual-right"
                  isEEFlag={isEEFlag}
                  rendering={route}
                />
              )}
            </div>
          ) : (
            ""
          )}
        </>
      )}
    </>
  );
};

export default withSitecoreContext()(TrainingManualBase);
