import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import { TrainingChapterReviews } from "./TrainingChapterReviews";
import { removeInlineCss } from "../PaasUtilities/RemoveInlineCss";

export const TrainingManualChapter = ({
  training,
  parent,
  setParent,
  index,
  chapterShortcutSelected,
}: any) => {
  const [openDetails, setOpenDetails] = useState(false);
  const router = useRouter();
  const queryParameter = router.query;
  const scrollContainer = useRef<HTMLDivElement>(null);
  const sectionScrollContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const isActive = parent === training?.ItemID.replace(/[{()}]/g, "");
    setOpenDetails(isActive);
  }, [parent, training?.ItemID]);

  useEffect(() => {
    const isActiveChapter = !queryParameter.chapterid
      ? index === 0
      : queryParameter.chapterid === training?.ItemID.replace(/[{()}]/g, "");
    setOpenDetails(isActiveChapter);
  }, [queryParameter, index, training?.ItemID]);

  useEffect(() => {
    if (!scrollContainer.current) return;

    scrollContainer.current?.scrollIntoView({ block: "center" });
    //mounting to let accordion load
    const timer = setTimeout(() => {
      if (scrollContainer.current) {
        scrollContainer.current.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
      }
    }, 1);

    // unmount
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!sectionScrollContainer.current) return;

    sectionScrollContainer.current?.scrollIntoView({ block: "center" });
    //mounting to let accordion load
    const timer = setTimeout(() => {
      if (sectionScrollContainer.current) {
        sectionScrollContainer.current.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
      }
    }, 1);

    // unmount
    return () => clearTimeout(timer);
  }, [chapterShortcutSelected]);

  const handleAccordionToggle = () => {
    setOpenDetails((prev) => !prev);
    setParent("");
  };

  return (
    <div className="accordion">
      <div
        className={`accordionTab ${openDetails ? "active" : ""}`}
        role="button"
        aria-expanded={openDetails}
        aria-controls="accordion-content-0"
        onClick={handleAccordionToggle}
        ref={
          queryParameter.chapterid === training?.ItemID.replace(/[{()}]/g, "")
            ? scrollContainer
            : null
        }
      >
        <h2>{training?.Title}</h2>
        <span className="material-symbols-outlined icon">expand_more</span>
      </div>
      <div className={`accordionContent ${openDetails ? "active" : ""}`}>
        <div className="flex-wrap">
          {training?.SectionList?.map((section: any) => (
            <section key={section?.Heading.trim()}>
              <h3
                className="bg-gray"
                id={section?.Heading.trim()}
                ref={
                  chapterShortcutSelected ===
                  `${section?.Heading.trim()} ${training?.ItemID.replace(
                    /[{()}]/g,
                    ""
                  )}`
                    ? sectionScrollContainer
                    : null
                }
              >
                {section?.Heading}
              </h3>
              <p
                dangerouslySetInnerHTML={{
                  __html: removeInlineCss(section?.SectionText),
                }}
              />
            </section>
          ))}
          <section>
            <h3
              className="bg-gray"
              id={`Chapter Review ${training?.ItemID.replace(/[{()}]/g, "")}`}
              ref={
                chapterShortcutSelected ===
                `Chapter Review ${training?.ItemID.replace(/[{()}]/g, "")}`
                  ? sectionScrollContainer
                  : null
              }
            >
              Chapter Review
            </h3>
            {training.ReviewList.map((review: any) => (
              <TrainingChapterReviews key={review.id} review={review} />
            ))}
          </section>
        </div>
      </div>
    </div>
  );
};
