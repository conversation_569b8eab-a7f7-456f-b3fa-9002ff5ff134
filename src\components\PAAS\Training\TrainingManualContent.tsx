import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/authContext";
import { getTrainingDataApi } from "./TrainingManualDataService";
import { TrainingManualChapter } from "./TrainingManualChapter";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";
import useRedirectToBasePage from "./../../../hooks/paas/useRedirectToBasePage";

export const TrainingContent = (props: any) => {
  const { trainingManualList } = props;
  const router = useRouter();
  const queryParameter = router.query;
  const { accessToken } = useContext(AuthContext);
  const [trainingData, setTrainingData] = useState<any>([]);
  const [chapterShortcutSelected, setChapterShortcutSelected] = useState("");
  const [chapterHeadingSelected, setChapterHeadingSelected] = useState("");
  const [parent, setParent] = useState("");
  const { paasEntitlement } = useContext(AuthContext);
  const selectedCustomerNumber = useGetSelectedCustomerNumber();
  const [isWC, setIsWC] = useState<boolean>(false);

  useEffect(() => {
    getTrainingAPI();
  }, [queryParameter]);

  const getTrainingAPI = async () => {
    const payload = {
      TrainingManualID: queryParameter?.id,
      ContentType: "Training Manual Chapter",
    };
    try {
      const url = `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/PAAS/GetTrainingManuals`;
      const post = await getTrainingDataApi(url, payload, accessToken);
      setTrainingData(post);
      if (post) {
        setIsWC(post?.Lobs?.map((lob: any) => lob?.Code)[0] === "WC");
      }
    } catch (error) {
      console.error("Training API not working:", error);
    }
  };

  const onBreadcrumbsClick = () => {
    router.push("/PAAS/training-manual");
  };

  const onTrainingManualClick = (trainingManual: any) => {
    router.push(
      `/PAAS/training-manual/?id=${trainingManual?.id
        ?.toUpperCase()
        .replace(/(.{8})(.{4})(.{4})(.{4})(.{12})/, "$1-$2-$3-$4-$5")}`
    );
  };

  useRedirectToBasePage(isWC, selectedCustomerNumber);

  const renderTrainingChapters = () => {
    return trainingData?.TrainingManualChapter?.map((training: any) => (
      <li className={training?.Title} key={training?.ItemID}>
        <details
          open={
            !queryParameter.chapterid
              ? trainingData?.TrainingManualChapter?.indexOf(training) === 0
              : queryParameter.chapterid ===
                training?.ItemID.replace(/[{()}]/g, "")
          }
        >
          <summary>
            <div className="lines-item">
              {training?.ItemName.substring(0, training?.ItemName.indexOf("-"))}
            </div>
          </summary>
          {training?.SectionList?.map((section: any) => {
              const { Heading } = section;
              const isSelected = chapterHeadingSelected === Heading;
             return (
              <>
            <div
              className={`btn-wrapper ${isSelected ? "selected" : ""}`}
              onClick={() => {
                setChapterShortcutSelected(
                  `${section?.Heading} ${training?.ItemID.replace(
                    /[{()}]/g,
                    ""
                  )}`
                );
                setChapterHeadingSelected(section?.Heading)
                setParent(training?.ItemID.replace(/[{()}]/g, ""));
              }}
              key={section?.Heading}
              data-testid="shortcut-click"
            >
              <a>{section?.Heading}</a>
            </div>
           </>
            );
          })}
          <div
            className={
              chapterShortcutSelected ===
              `Chapter Review ${training?.ItemID.replace(/[{()}]/g, "")}`
                ? "btn-wrapper selected"
                : "btn-wrapper"
            }
            onClick={() => {
              setChapterShortcutSelected(
                `Chapter Review ${training?.ItemID.replace(/[{()}]/g, "")}`
              );
              setParent(training?.ItemID.replace(/[{()}]/g, ""));
            }}
            data-testid="shortcut-click"
          >
            <a>Chapter Review</a>
          </div>
        </details>
      </li>
    ));
  };

  const renderTrainingManuals = () => {
    return trainingManualList?.map((trainingManual: any) => {
      const isDisabled =
        (trainingManual?.id === "807DF3BFC9CB4B26B4640A4072441759" &&
          !paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_WC")) ||
        (trainingManual?.id === "2ED7CA42387F4B3DB665813F9E44B51F" &&
          !paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_GL")) ||
        (trainingManual?.id === "F3DF218D809E461EBA83B7E686AF6124" &&
          !paasEntitlement
            ?.find(
              (customer: any) =>
                customer?.customerNumber === selectedCustomerNumber
            )
            ?.customerPAASParticipation?.includes("LOB_CA"));

      if (isDisabled) return null;

      return (
        <li key={trainingManual?.id}>
          <label htmlFor={trainingManual?.name}>
            <input
              name="TrainingManuals"
              type="radio"
              id={trainingManual?.name}
              value=""
              checked={
                queryParameter.id ===
                trainingManual?.id
                  ?.toUpperCase()
                  .replace(/(.{8})(.{4})(.{4})(.{4})(.{12})/, "$1-$2-$3-$4-$5")
              }
              onClick={() => onTrainingManualClick(trainingManual)}
              data-testid="industry-guide-radio-button"
            />
            <span>{trainingManual?.name}</span>
          </label>
        </li>
      );
    });
  };

  return (
    <div className="industry-guides">
      <div className="breadcrumbs">
        <nav>
          <a
            onClick={onBreadcrumbsClick}
            data-testid="industry-guide-breadcrumbs"
          >
            Training
          </a>
          <strong>{trainingData?.TrainingManualName}</strong>
        </nav>
      </div>
      <div className="site flex-wrapper">
        <aside className="filter thin">
          <section className="with-show-more-filters background-lt-grey">
            <h2 className="filter-toggle-mobile" aria-controls="search-filters">
              Filters
              <a aria-label="Filter icon button" role="button">
                <span className="material-icons">filter_list</span>
              </a>
            </h2>
            <ul className="link-list link-list-closed">
              {renderTrainingChapters()}
              <li className="industry-guides">
                <details open={trainingManualList?.length > 0}>
                  <summary>
                    <div className="lines-item">Training Manuals</div>
                  </summary>
                  <form
                    className="radio-group"
                    aria-label="Content type filter options"
                  >
                    <fieldset>
                      <ul>{renderTrainingManuals()}</ul>
                    </fieldset>
                  </form>
                </details>
              </li>
            </ul>
          </section>
        </aside>
        <div className="content-wrapper">
          <div className="jurisdictional-info-content-header">
            <h2>{trainingData?.TrainingManualName}</h2>
          </div>
          <div className="flex-wrap">
            {trainingData?.TrainingManualChapter?.map((training: any) => (
              <TrainingManualChapter
                key={training?.ItemID}
                index={trainingData?.TrainingManualChapter?.indexOf(training)}
                training={training}
                parent={parent}
                setParent={setParent}
                chapterShortcutSelected={chapterShortcutSelected}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
