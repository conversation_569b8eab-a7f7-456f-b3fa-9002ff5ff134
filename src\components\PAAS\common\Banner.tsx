import { Field, withDatasourceCheck } from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type BannerProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const Banner = (props: any): JSX.Element => {
  return (
    <div className="sub-header">
      <div className="hub-header">
        <div className="site flex-wrapper">
          <h1>{props?.dictionaryData?.headings_product_heading}</h1>
        </div>
      </div>
    </div>
  );
};

Banner.displayName = "Banner";

export default withDatasourceCheck()<BannerProps>(Banner);
