import {
  withDatasourceCheck,
  Text,
  RichText,
  Field,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type LegalDisclaimerProps = ComponentProps & {
  fields: {
    CautionText: Field<string>;
    LegalDisclaimer: Field<string>;
  };
};

const LegalDisclaimer = (props: LegalDisclaimerProps): JSX.Element => {
  return (
    <div className="legal-disclaimer-wrapper">
      <div className="disclaimer-content">
        <Text field={props?.fields?.CautionText} tag="b" />:
        <RichText field={props.fields?.LegalDisclaimer} tag="span" />
      </div>
    </div>
  );
};

export default withDatasourceCheck()<LegalDisclaimerProps>(LegalDisclaimer);
