import {
  Field,
  Text,
  withDatasource<PERSON>heck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import { useRouter } from "next/router";
import { useOktaAuth } from "src/context/oktaAuth";
import { useContext } from "react";
import { AuthContext } from "src/context/authContext";
import useGetSelectedCustomerNumber from "./../../../hooks/paas/useGetSelectedCustomerNumber";

type TabsProps = ComponentProps & {
  fields: {
    tabList: {
      fields: {
        Phrase: Field<string>;
      };
      id: string;
    }[];
  };
  dictionaryData: any;
  isEEFlag: any;
};

export const tabActive = (
  queryParam: any,
  val: {
    fields: {
      Phrase: Field<string>;
    };
    id: string;
  }
) => {
  const routeToIdMap: { [key: string]: string } = {
    "News-and-Events": "1612f243-da74-4e36-9e0e-8d3cb202de0b",
    jurisdiction: "1d2097df-d812-42e1-b5bf-a30250cac1a9",
    search: "520bc154-5bd7-4066-bc2d-92a31bb7afa2",
    paasAI: "da5ca5fa-0eda-4a0f-bcad-073ec92dc603",
    contactus: "c723baed-9e8f-40fb-8936-4317c0b75751",
    "training-manual": "775a8cee-b0fa-4384-9128-6fedcfe65d28",
    industryguide: "5f4b3194-3b53-49c8-9c85-1510954e94c4",
  };

  const paasRoute = queryParam?.path[1];
  if (queryParam?.path && routeToIdMap[paasRoute] === val.id) {
    return "tab active";
  } else {
    return "tab";
  }
};

const Tabs = (props: TabsProps): JSX.Element => {
  const router = useRouter();
  const queryParam = router?.query;
  let selectedCustomerNumber = useGetSelectedCustomerNumber();
  const { paasEntitlement } = useContext(AuthContext);

  const { nextSessionUserEmail } = useOktaAuth();

  const handleClick = (val: {
    fields: {
      Phrase: Field<string>;
    };
    id: string;
  }) => {
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("requestParams");
      sessionStorage.removeItem("tagCount");
    }
    if (val.id === "1612f243-da74-4e36-9e0e-8d3cb202de0b") {
      router.push(`/PAAS/News-and-Events`);
    } else if (val.id === "1d2097df-d812-42e1-b5bf-a30250cac1a9") {
      router.push("/PAAS/jurisdiction");
    } else if (val.id === "da5ca5fa-0eda-4a0f-bcad-073ec92dc603") {
      router.push("/PAAS/paasAI");
    } else if (val.id === "520bc154-5bd7-4066-bc2d-92a31bb7afa2") {
      router.push("/PAAS/search");
    } else if (val.id === "c723baed-9e8f-40fb-8936-4317c0b75751") {
      router.push("/PAAS/contactus");
    } else if (val.id === "775a8cee-b0fa-4384-9128-6fedcfe65d28") {
      router.push("/PAAS/training-manual");
    } else if (val.id === "5f4b3194-3b53-49c8-9c85-1510954e94c4") {
      router.push("/PAAS/industryguide");
    } else {
      router.push("/PAAS");
    }
  };

  return (
    <section className="tabs-content">
      <div className="tabs">
        <div className="tabbed flex-wrapper">
          <div className="nav-wrapper">
            <nav>
              {props?.fields?.tabList?.map(
                (
                  val: {
                    fields: {
                      Phrase: Field<string>;
                    };
                    id: string;
                  },
                  id: number
                ) => {
                  if (
                    val?.id === "c1cd6601-0bdd-473d-9c03-edb41516c770" ||
                    val?.id === "f6ea97e6-681d-406b-a171-db44b46dd251" ||
                    (val?.id === "da5ca5fa-0eda-4a0f-bcad-073ec92dc603" &&
                      !props.isEEFlag &&
                      !(nextSessionUserEmail !== undefined)) ||
                    (val?.id === "1d2097df-d812-42e1-b5bf-a30250cac1a9" &&
                      !props.isEEFlag &&
                      paasEntitlement
                        ?.find(
                          (customer: any) =>
                            customer?.customerNumber === selectedCustomerNumber
                        )
                        ?.customerPAASParticipation?.includes("LOB_WC") ===
                        false)
                  ) {
                    return;
                  }
                  return (
                    <>
                      <a
                        key={id}
                        className={tabActive(queryParam, val)}
                        data-testid="tabs-button"
                        onClick={() => handleClick(val)}
                      >
                        {val?.id === "da5ca5fa-0eda-4a0f-bcad-073ec92dc603" && (
                          <span>Beta</span>
                        )}
                        <Text field={val?.fields?.Phrase} />
                      </a>
                    </>
                  );
                }
              )}
            </nav>
          </div>
        </div>
      </div>
    </section>
  );
};

export default withDatasourceCheck()<TabsProps>(Tabs);
