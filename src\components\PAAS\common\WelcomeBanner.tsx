import {
  Text,
  withDatasourceCheck,
  RichText,
  Image,
  Field,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type WelcomeBannerProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    Description: Field<string>;
    Image: any;
  };
};

const WelcomeBanner = (props: WelcomeBannerProps): JSX.Element => {
  return (
    <>
      <section className="paas-section-hero background-lt-blue">
        <div className="paas-section-hero-content">
          <Text field={props.fields.Title} tag="h2" />
          <RichText field={props.fields.Description} />
        </div>
        <Image field={props?.fields.Image} />
      </section>
    </>
  );
};

export default withDatasourceCheck()<WelcomeBannerProps>(WelcomeBanner);
