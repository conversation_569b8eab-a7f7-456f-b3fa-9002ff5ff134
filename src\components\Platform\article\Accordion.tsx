import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import FIAccordion from "components/filing-intelligence/FIAccordion";

export const Accordion = (props: any): JSX.Element => {
  return (
    <>
      {props?.EEFlag === true ? (
        <section className="references" data-testid="references">
          <details>
            <Text field={props.fields.Title} tag="summary" />
            <div className="reference-listing">
              <RichText tag="div" field={props.fields.Content} />
            </div>
          </details>
        </section>
      ) : props?.fields?.Title?.value && props?.fields?.Content?.value ? (
        <section className="references" data-testid="references">
          <details>
            <Text field={props.fields.Title} tag="summary" />
            <div className="reference-listing">
              {props.fields?.product?.value !== "FI" ? (
                <RichText tag="div" field={props.fields.Content} />
              ) : (
                <FIAccordion
                  staticData={props.staticData}
                  filingData={props.fields.filingData}
                  content={props.fields.Content}
                  data={props.data}
                  tab={props.tab}
                  selectedState={props.selectedState}
                />
              )}
            </div>
          </details>
        </section>
      ) : (
        ""
      )}
    </>
  );
};

export default Accordion;
