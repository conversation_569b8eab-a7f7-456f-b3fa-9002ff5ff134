import {
  Text,
  Field,
  Image,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import NextImage from "components/common/NextImage";
import { ComponentProps } from "lib/component-props";

type ArticleBannerProps = ComponentProps & {
  fields: {
    Title: Field<string>;
    ShortDescription: Field<string>;
    BannerImage: any;
  };
  EEFlag: any;
};
const ArticleBanner = (props: ArticleBannerProps): JSX.Element => {
  const EEFlag = props?.EEFlag;
  return (
    <section className="hero topic-detail background-md-blue-2">
      <div className="site flex-wrapper">
        <div className="messaging">
          <Text field={props.fields.Title} tag="h1" />
          <Text field={props.fields.ShortDescription} tag="p" />
        </div>
        {EEFlag ? (
          <Image field={props?.fields?.BannerImage} />
        ) : (
          props?.fields?.BannerImage.value?.src && (
            <NextImage
              src={props?.fields?.BannerImage.value?.src}
              alt={
                props?.fields?.BannerImage.value?.alt || "Image not available"
              }
              width={432}
              height={275}
            />
          )
        )}
      </div>
    </section>
  );
};

export default withDatasourceCheck()<ArticleBannerProps>(ArticleBanner);
