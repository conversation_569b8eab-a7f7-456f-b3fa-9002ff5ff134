import { Text, Image } from "@sitecore-jss/sitecore-jss-nextjs";
import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { useMemo } from "react";
import { getFolderName } from "../utils/query";
import NextImage from "components/common/NextImage";

const ArticleBannerBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext || {};
  const { path } = rendering;
  const EEFlag = rendering?.EEFlag;
  const BannerDate = route?.fields?.BannerDate || {};
  BannerDate.value = BannerDate?.value ? BannerDate.value.split("T")[0] : "";

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const dateParts = BannerDate?.value.split("-");
  const yearVal = dateParts[0];
  const monthVal = months[Number(dateParts[1]) - 1];
  const dayVal = String(dateParts[2]).padStart(2, "0");

  const formattedDate = `${monthVal} ${dayVal}, ${yearVal}`;

  const folderPath = useMemo(() => getFolderName(path), [path]);
  const removeMetaTag = folderPath === "Emerging Issues" ? true : false;

  return (
    <section className="hero article topic-detail background-lt-grey">
      <div className="site flex-wrapper">
        <div className="messaging">
          <Text field={route?.fields?.Title} tag="h1" />
          <p className="article-meta">
            <small>
              <time>{formattedDate}</time>
            </small>
            {!removeMetaTag && <small>{folderPath}</small>}
          </p>
          <Placeholder
            name="jss-share"
            EEFlag={EEFlag}
            props={route?.fields}
            rendering={route}
          />
        </div>
        {EEFlag ? (
          <Image field={route?.fields?.BannerImage} />
        ) : route?.fields?.BannerImage?.value?.src ? (
          <NextImage
            src={route?.fields?.BannerImage?.value?.src}
            alt={
              route?.fields?.BannerImage?.value?.alt || "Image not available"
            }
            width={432}
            height={275}
          />
        ) : (
          ""
        )}
      </div>
    </section>
  );
};
export default withSitecoreContext()(ArticleBannerBase);
