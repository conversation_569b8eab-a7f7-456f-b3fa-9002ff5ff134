import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const ArticlePageBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const EEFlag = rendering?.isEEFlag;
  const itemId = rendering?.sitecoreContext?.itemId;
  return (
    <main className="hub-page emerging-issues">
      <Placeholder name="jss-sub-header" rendering={route} />
      <article>
        <Placeholder
          name="jss-article-banner"
          EEFlag={EEFlag}
          rendering={route}
          path={"Emerging-Issues/Articles"}
        />
        <div className="site flex-wrapper">
          <div className="content-wrapper guest-user">
            <Placeholder
              name="jss-article-main-section"
              fieldsVal={route?.fields}
              EEFlag={EEFlag}
              rendering={route}
            />
            {/* <Placeholder
              name="jss-powerbi"
              fieldsVal={route?.fields}
              rendering={route}
              EEFlag={rendering?.isEEFlag}
              signinFlag={rendering?.signinFlag}
            /> */}
          </div>
          <aside>
            <Placeholder
              name="jss-article-sidebar"
              EEFlag={EEFlag}
              props={route?.fields}
              rendering={route}
              isSideBar
            />
          </aside>
        </div>
      </article>
      <Placeholder
        name="jss-powerbi"
        fieldsVal={route?.fields}
        rendering={route}
        EEFlag={rendering?.isEEFlag}
        signinFlag={rendering?.signinFlag}
      />
      <Placeholder
        name="jss-main-relatedinsight"
        EEFlag={EEFlag}
        rendering={route}
        itemId={itemId}
      />
      <Placeholder
        name="jss-article-darkbanner"
        EEFlag={EEFlag}
        rendering={route}
      />
    </main>
  );
};

export default withSitecoreContext()(ArticlePageBase);
