import { Image, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import React from "react";
import LinkedInIcon from "assets/assets/linkedin-blue.svg";
import TwiiterIcon from "assets/assets/twitter-blue.svg";
import EmailIcon from "assets/assets/envelope-blue.svg";
import { useRouter } from "next/router";
import NextImage from "components/common/NextImage";

const AuthorBio = (props: any): JSX.Element => {
  const data = props?.sitecoreContext?.route?.fields?.Authors || [];
  const authorPage = props?.sitecoreContext?.route?.fields;
  const EEFlag = props?.EEFlag;

  const router: any = useRouter();
  const defaultImage = `${process.env.PUBLIC_URL}/-/media/Project/NeoCore/Author-Profile-Pictures/AuthorBio-Default.png?h=128&iar=0&w=128&hash=ADDED82EF5E68A141F8EDDD565AC700C`;
  const tempAuth = { ...authorPage };
  delete tempAuth.Authors;
  const authorArray = EEFlag
    ? data
    : data?.filter((val: any) => !val.fields["Make author inactive"]?.value);

  return (
    <section className="author">
      <div className="site">
        {authorArray?.length > 0 ? (
          // For article page author bio component
          <>
            {authorArray?.map((val: any, id: any) => {
              return (
                <div key={id} className="bio flex-wrapper">
                  {val?.fields?.Image?.value?.src &&
                    (EEFlag ? (
                      <img
                        src={val?.fields?.Image?.value?.src}
                        className="author-photo"
                        alt={val?.fields?.Image?.value?.alt}
                      />
                    ) : (
                      <NextImage
                        src={val?.fields?.Image?.value?.src}
                        className="author-photo"
                        alt={
                          val?.fields?.Image?.value?.alt ||
                          "Image not available"
                        }
                        width={90}
                        height={90}
                      />
                    ))}
                  <div className="bio details with-link">
                    <strong>
                      <a
                        data-region="Author Bio"
                        data-title={val.displayName}
                        data-testid="author-name"
                        data-context=""
                        data-interaction="click"
                        onClick={(e) => {
                          e.preventDefault();
                          router.push(val.url);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault(); // Prevent the default action of pressing Enter
                            router.push(val.url);
                          }
                        }}
                        tabIndex={0}
                      >
                        {val.fields["First Name"].value}{" "}
                        {val.fields["Last Name"].value}
                      </a>
                      {val?.fields?.Designation?.value ? ", " : ""}
                    </strong>
                    <b> {val?.fields?.Designation?.value}</b>
                    <p>{val.fields?.Biography?.value} </p>
                    <div className="social-icons">
                      {val?.fields?.LinkedIn?.value && (
                        <a
                          href={val?.fields?.Linkedin?.value}
                          data-region="Author Bio"
                          data-title="LinkedIn"
                          data-interaction="click"
                          target="_blank"
                        >
                          <NextImage
                            width={18}
                            height={21}
                            src={LinkedInIcon.src}
                            alt="Linkedin"
                          />
                        </a>
                      )}
                      {val?.fields?.Twitter?.value && (
                        <a
                          href={val?.fields?.Twitter?.value}
                          data-region="Author Bio"
                          data-title="Twitter"
                          data-interaction="click"
                          target="_blank"
                        >
                          <NextImage
                            width={18}
                            height={21}
                            src={TwiiterIcon.src}
                            alt="Twitter "
                          />
                        </a>
                      )}
                      {val?.fields?.Email?.value && (
                        <a
                          data-region="Author Bio"
                          data-title="Email"
                          data-interaction="click"
                          href={`mailto:${val?.fields?.Email?.value}`}
                        >
                          <NextImage
                            width={18}
                            height={21}
                            src={EmailIcon.src}
                            alt="Email"
                          />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        ) : (
          // For author bio page
          <div className="bio flex-wrapper author-test">
            {authorPage?.Image?.value.src &&
              (EEFlag ? (
                <Image field={authorPage?.Image} className="author-photo" />
              ) : (
                <>
                  {
                    <NextImage
                      src={
                        authorPage["Make author inactive"]?.value
                          ? defaultImage
                          : authorPage?.Image?.value.src
                      }
                      alt={
                        authorPage?.Image?.value.alt || "AuthorBio-Default.png"
                      }
                      className="author-photo"
                      width={225}
                      height={225}
                    />
                  }
                </>
              ))}

            <div className="bio details with-link">
              <strong>
                <Text field={authorPage["First Name"]} />
                &nbsp;
                <Text field={authorPage["Last Name"]} />
                {authorPage?.Designation?.value ? ", " : ""}
              </strong>
              <Text field={authorPage?.Designation} tag="b" />
              {EEFlag && <p>{authorPage?.Biography?.value}</p>}
              <>
                {!authorPage["Make author inactive"]?.value && !EEFlag && (
                  <p>{authorPage?.Biography?.value}</p>
                )}
              </>
              {EEFlag && (
                <div className="social-icons">
                  {authorPage?.LinkedIn?.value && (
                    <a
                      href={authorPage?.Linkedin?.value}
                      data-region="Author Bio"
                      data-title="LinkedIn"
                      data-interaction="click"
                      target="_blank"
                    >
                      <img src={LinkedInIcon.src} alt="Linkedin" />
                    </a>
                  )}
                  {authorPage?.Twitter?.value && (
                    <a
                      href={authorPage?.Twitter?.value}
                      data-region="Author Bio"
                      data-title="Twitter"
                      data-interaction="click"
                      target="_blank"
                    >
                      <img src={TwiiterIcon.src} alt="Twitter " />
                    </a>
                  )}
                  {authorPage?.Email?.value && (
                    <a
                      data-region="Author Bio"
                      data-title="Email"
                      data-interaction="click"
                      href={`mailto:${authorPage?.Email?.value}`}
                    >
                      <img src={EmailIcon.src} alt="Email" />
                    </a>
                  )}
                </div>
              )}
              <>
                {!authorPage["Make author inactive"]?.value && !EEFlag && (
                  <div className="social-icons">
                    {authorPage?.LinkedIn?.value && (
                      <a
                        href={authorPage?.Linkedin?.value}
                        data-region="Author Bio"
                        data-title="LinkedIn"
                        data-interaction="click"
                        target="_blank"
                      >
                        <NextImage
                          width={18}
                          height={21}
                          src={LinkedInIcon.src}
                          alt="Linkedin"
                        />
                      </a>
                    )}
                    {authorPage?.Twitter?.value && (
                      <a
                        href={authorPage?.Twitter?.value}
                        data-region="Author Bio"
                        data-title="Twitter"
                        data-interaction="click"
                        target="_blank"
                      >
                        <NextImage
                          width={18}
                          height={21}
                          src={TwiiterIcon.src}
                          alt="Twitter "
                        />
                      </a>
                    )}
                    {authorPage?.Email?.value && (
                      <a
                        data-region="Author Bio"
                        data-title="Email"
                        data-interaction="click"
                        href={`mailto:${authorPage?.Email?.value}`}
                      >
                        <NextImage
                          width={18}
                          height={21}
                          src={EmailIcon.src}
                          alt="Email"
                        />
                      </a>
                    )}
                  </div>
                )}
              </>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default withSitecoreContext()(AuthorBio);
