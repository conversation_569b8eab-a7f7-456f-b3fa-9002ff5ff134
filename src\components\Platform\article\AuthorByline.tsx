import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import { useRouter } from "next/router";
import React from "react";

export const AuthorByline = (props: any): JSX.Element => {
  const data = props?.sitecoreContext?.route?.fields?.Authors || [];
  const router: any = useRouter();
  return (
    <address>
      <div className="by-column">
        <b>By</b>
      </div>
      <div className="authors-column">
        {data?.map((bylineVal: any, id: any) => {
          const designationObj = bylineVal.fields?.Designation;
          Object.keys(designationObj).forEach(
            (k) => (designationObj[k] = designationObj[k].trim())
          );
          return (
            <div key={id}>
              <b>
                <a
                  data-region="Author Byline"
                  data-title={bylineVal.displayName}
                  data-interaction="click"
                  tabIndex={0}
                  data-testid="author-name-byline"
                  onClick={(e) => {
                    e.preventDefault();
                    router.push(bylineVal.url);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault(); // Prevent the default action of pressing Enter
                      router.push(bylineVal.url);
                    }
                  }}
                >
                  {bylineVal.fields["First Name"].value}{" "}
                  {bylineVal.fields["Last Name"].value}
                </a>
              </b>
              {bylineVal?.fields?.Designation?.value ? ", " : ""}
              {bylineVal.fields?.Designation.value ? (
                <span> {bylineVal.fields?.Designation.value}</span>
              ) : (
                ""
              )}
            </div>
          );
        })}
      </div>
    </address>
  );
};

export default withSitecoreContext()(AuthorByline);
