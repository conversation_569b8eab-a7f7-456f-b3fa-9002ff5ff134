import { RichText } from "@sitecore-jss/sitecore-jss-nextjs";
import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import { useEffect } from "react";

const Content = (props: any): JSX.Element => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      const domElement = document.getElementsByClassName("major-content")[0];
      domElement.querySelectorAll("*").forEach((node: any) => {
        node.removeAttribute("width");
        node.removeAttribute("height");
        node.style.removeProperty("width");
        node.style.removeProperty("height");
      });
    }
  }, []);

  return (
    <section className="major-content">
      <RichText
        field={props.sitecoreContext?.route?.fields?.Content}
        tag="div"
      />
    </section>
  );
};

export default withSitecoreContext()(Content);
