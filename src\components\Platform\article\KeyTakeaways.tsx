import {
  Field,
  Text,
  RichText,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import { useEffect } from "react";

type KeyTakeawaysProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

export const KeyTakeaways = (props: any): JSX.Element => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      const domElement = document.getElementsByClassName("key-takeaways")[0];
      domElement.querySelectorAll("*").forEach((node: any) => {
        node.style.removeProperty("width");
        node.style.removeProperty("height");
      });
    }
  }, []);
  return (
    <section className="key-takeaways">
      <Text field={props?.fields?.Title} tag="h2" />
      <RichText field={props?.fields?.Content} tag="div" />
    </section>
  );
};

export default withDatasourceCheck()<KeyTakeawaysProps>(KeyTakeaways);
