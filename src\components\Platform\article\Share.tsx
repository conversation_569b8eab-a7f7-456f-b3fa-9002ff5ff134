import { useState } from "react";
import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
// import "@fortawesome/fontawesome-free/css/all.min.css";

// import LinkedInIcon from "assets/assets/linkedin.svg";
// import TwiiterIcon from "assets/assets/twitter.svg";
// import EmailIcon from "assets/assets/envelope-solid.svg";
// import FaceBookIcon from "assets/assets/facebook-f.svg";

const Share = (props: any): JSX.Element => {
  const fields = props?.fields;

  console.log("fields::", fields);

  const [shareToggle, setShareToggle] = useState(false);

  // const getImageSrc = (imageType: string) => {
  //   const imgType = imageType.toLowerCase();
  //   if (imgType.indexOf("twitter") > -1) {
  //     return TwiiterIcon.src;
  //   } else if (imgType.indexOf("linkedin") > -1) {
  //     return LinkedInIcon.src;
  //   } else if (imgType.indexOf("mail") > -1) {
  //     return EmailIcon.src;
  //   } else if (imgType.indexOf("facebook") > -1) {
  //     return FaceBookIcon.src;
  //   }
  // };

  const getShareURL = (shareURL: string, item: any) => {
    // Check if window is defined (to avoid errors during server-side rendering)
    if (typeof window !== "undefined") {
      const urlElement = document.querySelector('meta[property="og:url"]');
      const currentPage = urlElement?.getAttribute("content") || "";
      console.log("currentPage::", currentPage);
      const pageTitleElement = document.querySelector(
        'meta[property="og:title"]'
      );
      const pageTitle = pageTitleElement?.getAttribute("content") || "";

      const pageContentElement = document.querySelector(
        'meta[property="og:description"]'
      );
      const pageContent = pageContentElement?.getAttribute("content") || "";
      const type = item?.displayName?.toLowerCase();

      const extraLine = pageContent.length < 110 ? "%0D%0A" : "";
      if (type.indexOf("mail") > -1) {
        const bodyText = `From Verisk Analytics%0D%0A%0D%0A${
          pageTitle || ""
        }%0D%0A${pageContent.trim()}${extraLine}%0D%0A${currentPage}`;
        return shareURL
          ? shareURL.replace("{url}", pageTitle).replace("{title}", bodyText)
          : shareURL;
      }
      return shareURL
        ? shareURL.replace("{url}", currentPage).replace("{title}", pageTitle)
        : shareURL;
    }

    // Return a default or fallback URL if window is not available
    return shareURL;
  };

  return (
    <div className="share-save">
      <a
        className="tertiary sharing"
        id="share"
        data-title={fields?.shareCollectionTitle?.value}
        data-region={"Share"}
        data-context={""}
        data-interaction="click"
        onClick={() => {
          setShareToggle(!shareToggle);
        }}
      >
        <span className="material-icons sharing">share</span>
        <span className="sharing"> {fields?.shareCollectionTitle?.value}</span>
      </a>
      {shareToggle && (
        <div className="popup sharing">
          <a
            href="#"
            className="close"
            onClick={() => {
              setShareToggle(false);
            }}
          >
            <span className="material-icons">close</span>
          </a>

          {fields?.shareItems?.map((item: any) => {
            // Clean up the class (remove "Shareicon" if present)
            let cleanedClass = item.fields?.shareCssClass?.value
              .replace("Shareicon", "")
              .trim();

            return (
              <a
                key={item.fields?.shareTitle?.value}
                data-region="Share"
                data-interaction="click"
                data-title="Share"
                data-context={item.fields?.shareTitle?.value}
                href={getShareURL(item.fields?.shareLink?.value, item)}
                target="_blank"
                className="share-item"
              >
                <span className="share-icon">
                  <i className={cleanedClass}></i>
                  {/* <i className="fa-brands fa-x"></i> */}
                </span>
                <span className="share-text">
                  {item.fields?.shareTitle?.value}
                </span>
              </a>
            );
          })}
        </div>
      )}
    </div>
  );
};
export default withSitecoreContext()(Share);
