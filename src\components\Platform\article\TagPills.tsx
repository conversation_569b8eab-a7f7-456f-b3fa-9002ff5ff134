import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import { useRouter } from "next/router";

const TagPills = (props: any): React.JSX.Element => {
  const router: any = useRouter();
  let searchSource: any = "Pill";
  const lobTags = props.sitecoreContext?.route?.fields?.["LOB Tags"];
  const JurisdictionTags =
    props.sitecoreContext?.route?.fields?.["Jurisdiction Tags"];
  const TopicTags = props.sitecoreContext?.route?.fields?.["Topic"];
  const sortedLobTags =
    lobTags != ""
      ? [...lobTags].sort((a, b) => a.displayName.localeCompare(b.displayName))
      : [];
  const sortedJurisdictionTags =
    JurisdictionTags != ""
      ? [...JurisdictionTags].sort((a, b) =>
          a.displayName.localeCompare(b.displayName)
        )
      : [];
  const sortedTopicTags =
    TopicTags != ""
      ? [...TopicTags].sort((a, b) =>
          a.displayName.localeCompare(b.displayName)
        )
      : [];
  return (
    <section className="pills flex-wrapper tag-pills-alignment">
      {sortedTopicTags?.map((arr: any) => {
        const tagCode: any = arr.fields?.Code?.value;
        return (
          <a
            key={tagCode}
            className="tertiary"
            data-region="Tag Pill"
            data-title={arr.displayName}
            data-context=""
            data-interaction="click"
            data-testid="topic-tags"
            onClick={(e) => {
              e.preventDefault();
              router.push(
                "/search?tp=" + tagCode + "&searchSource=" + searchSource
              );
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault(); // Prevent the default action of pressing Enter
                router.push(
                  "/search?tp=" + tagCode + "&searchSource=" + searchSource
                );
              }
            }}
            tabIndex={0}
          >
            {arr.displayName}
          </a>
        );
      })}
      {sortedJurisdictionTags?.map((arr: any) => {
        const tagCode: any = arr.fields?.Code?.value;
        return (
          <a
            key={tagCode}
            className="tertiary"
            data-region="Tag Pill"
            data-title={arr.displayName}
            data-context=""
            data-interaction="click"
            data-testid="jurisdiction-tags"
            onClick={(e) => {
              e.preventDefault();
              router.push(
                "/search?js=" + tagCode + "&searchSource=" + searchSource
              );
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault(); // Prevent the default action of pressing Enter
                router.push(
                  "/search?js=" + tagCode + "&searchSource=" + searchSource
                );
              }
            }}
            tabIndex={0}
          >
            {arr.displayName}
          </a>
        );
      })}
      {sortedLobTags?.map((arr: any) => {
        const tagCode: any = arr.fields?.Code?.value;
        return (
          <a
            key={tagCode}
            className="tertiary"
            data-region="Tag Pill"
            data-title={arr.displayName}
            data-context=""
            data-interaction="click"
            data-testid="lob-tags"
            onClick={(e) => {
              e.preventDefault();
              router.push(
                "/search?lob=" + tagCode + "&searchSource=" + searchSource
              );
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault(); // Prevent the default action of pressing Enter
                router.push(
                  "/search?lob=" + tagCode + "&searchSource=" + searchSource
                );
              }
            }}
            tabIndex={0}
          >
            {arr.displayName}
          </a>
        );
      })}
    </section>
  );
};

export default withSitecoreContext()(TagPills);
