import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const AuthorBioBase = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const isEEFlag = rendering?.isEEFlag;
  return (
    <main className="author-bio emerging-issues">
      {/* <div className="sub-header">
    {route && <Placeholder name="jss-author-bio-subheader" rendering={route} />}
    </div> */}
      {route && (
        <Placeholder
          name="jss-author-bio"
          rendering={route}
          EEFlag={isEEFlag}
        />
      )}
      {route && (
        <Placeholder
          name="jss-author-bio-explore-insights"
          EEFlag={isEEFlag}
          templateId={route.templateId}
          itemID={route.itemId}
          rendering={route}
        />
      )}
    </main>
  );
};

export default withSitecoreContext()(AuthorBioBase);
