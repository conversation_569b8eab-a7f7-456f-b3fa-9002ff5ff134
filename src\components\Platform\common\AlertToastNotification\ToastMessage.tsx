import { useMemo } from "react";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import WarningIcon from "@mui/icons-material/Warning";
import InfoIcon from "@mui/icons-material/Info";
import { RichText } from "@sitecore-jss/sitecore-jss-nextjs";

interface ToastMessage {
  type: "success" | "error" | "info";
  title?: string;
  description?: string;
}

const ToastMessage = ({ type, title, description }: ToastMessage) => {
  const statusIcon = useMemo(
    () =>
      ({
        success: <CheckCircleIcon className="toast-success-icon" />,
        error: <WarningIcon className="toast-error-icon" />,
        info: <InfoIcon className="toast-error-icon" />,
      }[type]),
    [type]
  );

  return (
    <div className="toast-content" data-testid="toast-content">
      <div>{statusIcon}</div>
      <div>
        <div className="toast-title">{title}</div>
        <RichText field={{ value: description }} />
      </div>
    </div>
  );
};

export default ToastMessage;
