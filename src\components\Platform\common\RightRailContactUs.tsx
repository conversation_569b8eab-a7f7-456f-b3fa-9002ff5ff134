import {
  Text,
  Field,
  withDatasourceCheck,
  RichText,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type RightRailContactUsProps = ComponentProps & {
  fields: {
    Heading: Field<string>;
    Content: Field<string>;
  };
};

const RightRailContactUs = (props: any): JSX.Element => {
  return (
    <section className="contact-team background-lt-grey">
      <div className="site">
        <Text field={props?.fields?.Heading} tag="h2" />

        <div className="with-cta">
          <RichText field={props?.fields?.Content} tag="p" />
          <div className="call-to-action no-padding">
            <a className="secondary" href={props?.fields?.Button?.value?.href}>
              {props?.fields?.Button?.value?.text}
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default withDatasourceCheck()<RightRailContactUsProps>(
  RightRailContactUs
);
