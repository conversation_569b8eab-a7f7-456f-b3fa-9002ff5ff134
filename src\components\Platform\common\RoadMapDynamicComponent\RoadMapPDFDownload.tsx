import Loader from "components/common/Loader";
import React, { useEffect, useState, useRef } from "react";

const PDFDownloadButton = (props: any) => {
  const [isSpinner, setIsSpinner] = useState(false);
  const [readyToDownload, setReadyToDownload] = useState(false);
  const [errorInDownload, setErrorInDownload] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  const pdfRef = useRef<any>(null);
  const isFirstMounting = useRef(true);
  const [downloadTriggered, setDownloadTriggered] = useState(true);

  const handleDownloadPDF = async (id: any) => {
    setInitialLoad(false);

    const jsPDF = (await import("jspdf")).default;
    const html2canvas = (await import("html2canvas")).default;
    const selector = `.hub-page-${id}`;
    const input = document.querySelector(selector);
    if (!input) {
      console.error("Element not found");
      return;
    }

    // Create a hidden clone of the table for PDF generation
    const clonedInput: any = input.cloneNode(true);
    clonedInput.style.position = "absolute";
    clonedInput.style.top = "-9999px";
    clonedInput.style.width = "1200px"; // Set the input width to a fixed value similar to the web view

    // Ensure the cloned table displays the full project titles
    const projectCells = clonedInput.querySelectorAll(".row-item a");
    projectCells.forEach((cell: any) => {
      cell.style.whiteSpace = "normal";
      cell.style.overflow = "visible";
      cell.style.textOverflow = "unset";
      cell.style.color = "#3f3f3f";
    });

    // Hide the download button in the cloned element
    const clonedDownloadButton = clonedInput.querySelector(".tertiary.small");
    if (clonedDownloadButton) {
      clonedDownloadButton.style.display = "none";
    }

    // Explicitly set the border styles for the table and its cells
    const tables = clonedInput.querySelectorAll("table");
    tables.forEach((table: any) => {
      table.style.borderCollapse = "collapse";
    });
    const trs = clonedInput.querySelectorAll("tr td");
    trs.forEach((tr: any) => {
      tr.style.borderBottom = "2px solid #dbdbdb";
    });

    const ths = clonedInput.querySelectorAll("th");
    ths.forEach((th: any) => {
      th.style.border = "1px solid #dbdbdb";
    });
    const theadFirsttr = clonedInput.querySelectorAll(
      ".table-container thead tr:first-child th:not(:first-child)"
    );
    theadFirsttr.forEach((td: any) => {
      td.style.borderRight = "thin solid #a3a3a3";
    });

    const theadtrLastChildFourthth = clonedInput.querySelectorAll(
      ".table-container table thead tr:last-child th:nth-child(4n)"
    );
    theadtrLastChildFourthth.forEach((td: any) => {
      td.style.borderRight = "thin solid #a3a3a3";
    });

    const theadthLastChild = clonedInput.querySelectorAll(
      ".table-container table thead tr:first-child th:last-child"
    );
    theadthLastChild.forEach((td: any) => {
      td.style.borderRight = "thin solid #dbdbdb";
    });

    const theadtrFirstthFirst = clonedInput.querySelectorAll(
      ".table-container table thead tr:first-child th:first-child "
    );
    theadtrFirstthFirst.forEach((td: any) => {
      td.style.borderRight = "thin solid #a3a3a3";
    });
    const tds1last = clonedInput.querySelectorAll(
      ".table-container table thead tr:last-child th:last-child "
    );
    tds1last.forEach((td: any) => {
      td.style.borderRight = "thin solid #dbdbdb";
    });
    document.body.appendChild(clonedInput);

    const pdf = new jsPDF("l", "pt", "a4");
    const margin = 20; // Define the margin
    let currentHeight = margin;

    // Set PDF metadata
    pdf.setProperties({
      title: `${props?.tabName} Roadmap Gantt`,
    });

    // Function to add canvas image to PDF
    const addCanvasImageToPDF = (
      canvas: any,
      pdf: any,
      margin: any,
      currentHeight: any
    ) => {
      const imgData = canvas.toDataURL("image/png", 0.8); // Moderate compression
      const imgProps = pdf.getImageProperties(imgData);
      const imgHeight =
        (imgProps.height * (pdf.internal.pageSize.getWidth() - 2 * margin)) /
        imgProps.width;
      pdf.addImage(
        imgData,
        "PNG",
        margin,
        currentHeight,
        pdf.internal.pageSize.getWidth() - 2 * margin,
        imgHeight
      );
      return currentHeight + imgHeight;
    };
    const SpaceAboveHeader = 20;
    currentHeight += SpaceAboveHeader;

    pdf.text(props?.pdfHeader, margin, currentHeight);
    currentHeight += 20;

    // Add current date to the top of the PDF
    pdf.setFont("helvetica", "italic");
    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Downloaded ${props?.formattedDate}`, margin, currentHeight);
    currentHeight += 20;

    // Add "Projects by LOB" header row
    const tableHeaderCanvas = await html2canvas(
      clonedInput.querySelector(".table-container thead"),
      { scale: 1.5 }
    );
    currentHeight = addCanvasImageToPDF(
      tableHeaderCanvas,
      pdf,
      margin,
      currentHeight
    );

    // Add table rows one by one
    const tableRows = clonedInput.querySelectorAll(".table-container tbody tr");
    for (let row of tableRows) {
      row.style.height = "auto";
      const rowCanvas = await html2canvas(row, { scale: 1.5 });
      const rowImgData = rowCanvas.toDataURL("image/png", 0.8);
      const rowImgProps = pdf.getImageProperties(rowImgData);
      const rowImgHeight =
        (rowImgProps.height * (pdf.internal.pageSize.getWidth() - 2 * margin)) /
        rowImgProps.width;

      if (
        currentHeight + rowImgHeight >
        pdf.internal.pageSize.getHeight() - margin
      ) {
        pdf.addPage();
        currentHeight = margin;
        currentHeight = addCanvasImageToPDF(
          tableHeaderCanvas,
          pdf,
          margin,
          currentHeight
        ); // Re-add header row
      }

      currentHeight = addCanvasImageToPDF(
        rowCanvas,
        pdf,
        margin,
        currentHeight
      );
    }
    // Remove the cloned element
    document.body.removeChild(clonedInput);
    // download pdf
    pdfRef.current = pdf;
    setReadyToDownload(true);
    setIsSpinner(false);
  };

  const onLoadPdfDownload = async (id: any) => {
    try {
      if (pdfRef.current === null) {
        await handleDownloadPDF(id);
      }
      return () => {
        if (pdfRef.current) {
          pdfRef.current = null;
        }
      };
    } catch (error) {
      setErrorInDownload(true);
      console.log(errorInDownload, "errorInDownload");
      setIsSpinner(false);
    }
  };

  useEffect(() => {
    if (initialLoad && isFirstMounting.current) {
      isFirstMounting.current = false;
      onLoadPdfDownload(props?.id);
    }
    return () => {
      if (pdfRef.current) {
        isFirstMounting.current = false;
        pdfRef.current = null;
      }
    };
  }, [props?.id]);

  const clickToDownload = () => {
    if (readyToDownload && pdfRef.current) {
      pdfRef.current.save(`${props?.tabName} Roadmap Gantt`);
      setDownloadTriggered(true);
    } else if (!readyToDownload) {
      setDownloadTriggered(false);
      setIsSpinner(true);
    }
  };

  useEffect(() => {
    if (readyToDownload && !downloadTriggered) {
      pdfRef.current.save(`${props?.tabName} Roadmap Gantt`);
      setDownloadTriggered(true);
    }
  }, [readyToDownload, downloadTriggered]);

  return (
    <>
      {isSpinner && !readyToDownload && <Loader />}
      {!isSpinner && (
        <button
          onClick={clickToDownload}
          className="tertiary small"
          aria-disabled={!readyToDownload || downloadTriggered}
        >
          <span className="material-icons">download</span>
          Download Timeline PDF
        </button>
      )}
    </>
  );
};

export default PDFDownloadButton;
