import React, { useContext } from "react";
import RoadmapDynamicTableHeaderTopImpactInfo from "./RoadmapDynamicTableHeaderTopImpactInfo";
import RoadmapDynamicTableRow from "./RoadmapDynamicTableRow";
import { AuthContext } from "src/context/authContext";
import NoRodamapData from "./utilities/NoRoadmapData";
import {
  filterRoadmapData,
  shouldDisplayLOB,
  shouldDisplayProject,
} from "./utilities/helpers";
import PDFDownloadButton from "./RoadMapPDFDownload";
import { RichText } from "@sitecore-jss/sitecore-jss-nextjs";

const RoadmapDynamicTable = (props: any) => {
  const context = useContext(AuthContext);

  if (!context?.roadmapData?.[props?.tabName]) {
    return <NoRodamapData tabName={props?.tabName} />;
  }
  const previousYear = new Date().getFullYear() - 1;
  const propdata = context?.roadmapData?.[props?.tabName]?.pageOne?.results;
  const tableTitle = props?.fields?.["Table Title"]?.value;
  const shortDescription = props?.fields?.["Short Description"];
  const result = filterRoadmapData(propdata);
  const pdfHeader = `${props?.tabName} Project Timeline ${previousYear}-${
    previousYear + 3
  }`;

  const now = new Date();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  const year = now.getFullYear() % 100;

  const formattedDate = `${month}/${day}/${year}`;

  if (result.length === 0) {
    return <NoRodamapData tabName={props?.tabName} />;
  }

  const generateYearHeaders = () => {
    const headers = [];
    for (let i = 0; i < 4; i++) {
      headers.push(
        <th key={`year-${previousYear + i}`} colSpan={4}>
          {previousYear + i}
        </th>
      );
    }
    return headers;
  };

  const generateQuarterHeaders = () => {
    const headers = [];
    const quarters = ["Q1", "Q2", "Q3", "Q4"];

    for (let i = 0; i < 4; i++) {
      for (const quarter of quarters) {
        headers.push(<th key={`${quarter}-${i}`}>{quarter}</th>);
      }
    }
    return headers;
  };

  return (
    <section
      className={`site tabNav tabContent commercial-lines hub-page-${props?.id}`}
    >
      <div className="site flex-wrapper">
        <div className="container">
          <div className="container-header">
            <h2>
              Project Timeline {previousYear}-{previousYear + 3}
            </h2>
            <RichText tag="p" field={shortDescription} />
          </div>
          <div className="flex-wrapper table-top-info">
            <RoadmapDynamicTableHeaderTopImpactInfo
              data={props?.fields?.["Business Impact Categories"]}
              text={props?.fields?.["Business Impact Text"]}
            />
            <PDFDownloadButton
              id={props?.id}
              tabName={props?.tabName}
              pdfHeader={pdfHeader}
              formattedDate={formattedDate}
            />
          </div>

          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th rowSpan={2} colSpan={2} className="row-label">
                    {tableTitle}
                  </th>
                  {/* Need to map over years from  current to +4 */}
                  {generateYearHeaders()}
                </tr>
                <tr>{generateQuarterHeaders()}</tr>
              </thead>
              <tbody>
                {result.map((group) =>
                  shouldDisplayLOB(group?.items) ? (
                    <React.Fragment key={group?.LOB}>
                      <tr className="section-row">
                        <td className="section-row-title" colSpan={2}>
                          {group?.LOB}
                        </td>
                        <td colSpan={4}></td>
                        <td colSpan={4}></td>
                        <td colSpan={4}></td>
                        <td colSpan={4}></td>
                      </tr>
                      {group?.items?.map((row: any, index: number) =>
                        shouldDisplayProject(
                          row?.From?.value,
                          row?.To?.value
                        ) ? (
                          <RoadmapDynamicTableRow
                            key={row.ID}
                            index={index}
                            rowData={row}
                          />
                        ) : null
                      )}
                    </React.Fragment>
                  ) : null
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RoadmapDynamicTable;
