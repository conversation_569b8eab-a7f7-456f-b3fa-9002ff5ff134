import React from "react";
import RoadmapDynamicTableTooltip from "./utilities/Tooltip";
import { RichText } from "@sitecore-jss/sitecore-jss-nextjs";

const RoadmapDynamicComponentHeaderTopImpactInfo = ({ data, text }: any) => {
  return (
    <div className="flex-wrapper legend">
      <span className="legend-label">
        {text?.value ? text?.value + ":" : "Business Impact:"}
      </span>
      <ul>
        {data.map((item: any, index: number) => (
          <li className="legend-item" key={index}>
            <a
              href="#"
              className="has-tooltip"
              data-template="legend-low"
              data-tippy-placement="top"
              aria-describedby={`legend-${item?.fields?.ImpactTitle?.value}`}
              tabIndex={-1}
            >
              <RoadmapDynamicTableTooltip
                legend={item?.fields?.ImpactTitle?.value}
                id={`legend-${item?.fields?.ImpactTitle?.value}`}
                content={
                  <RichText
                    field={item?.fields?.ImpactValue}
                    className="tooltip-content"
                    aria-hidden="true"
                    role="tooltip"
                  />
                }
              />
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default RoadmapDynamicComponentHeaderTopImpactInfo;
