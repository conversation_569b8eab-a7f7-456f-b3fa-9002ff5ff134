import React from "react";
import RoadmapDynamicTableTooltip from "./utilities/Tooltip";
import RowTooltipContent from "./utilities/RowTooltipContent";
import { getStartAndEndColumn } from "./utilities/helpers";
import Link from "next/link";

const RoadmapDynamicTableRow = ({ rowData, index }: any) => {
  const numCols = 17;
  const cells = [];
  let isImpactPrior = false;
  let isImpactAfter = false;

  const badge = rowData?.Status?.targetItem?.Name?.value;
  const impact = rowData?.BusinessImpact?.targetItem?.Name?.value;
  const projectName = rowData?.Title?.value;
  const projectURL = rowData?.TitleLink;
  const projectDescription =
    rowData?.ItemType === "TopicDetails"
      ? rowData?.Description?.value
      : rowData?.Content?.value;
  const startDate = rowData?.From?.value;
  const endDate = rowData?.To?.value;

  const { startColumnIndex, endColumnIndex } = getStartAndEndColumn(
    startDate,
    endDate
  );
  let timelineStartIndex = startColumnIndex;
  if (startColumnIndex < 1) {
    isImpactPrior = true;
    timelineStartIndex = 1;
  }
  let timelineEndIndex = endColumnIndex;
  if (endColumnIndex > 16) {
    isImpactAfter = true;
    timelineEndIndex = 16;
  }
  let timelineBarWidth = timelineEndIndex - timelineStartIndex + 1;

  let timelineBarBorderClasses = `impact-start${
    isImpactPrior ? " impact-prior" : ""
  }${isImpactAfter ? " impact-after" : ""}`;

  for (let i = 1; i < numCols; i++) {
    if (i === timelineStartIndex) {
      cells.push(
        <td key={i} className={timelineBarBorderClasses}>
          <div className={`impact timeline-bar-width-${timelineBarWidth}`}>
            <Link
              href="javascript:void(0)"
              className="has-tooltip"
              data-template={`row-${index}`}
              data-tippy-placement="top"
              aria-describedby={`legend-${index}`}
              tabIndex={-1}
            >
              <RoadmapDynamicTableTooltip
                legend={`${impact} impact`}
                id={`legend-${index}`}
                content={
                  <RowTooltipContent
                    rowid={`row${index}`}
                    tooltipHeader={projectName}
                    impact={impact}
                    tooltipBody={projectDescription}
                  />
                }
              />
            </Link>
          </div>
        </td>
      );
    } else {
      cells.push(<td key={i}></td>);
    }
  }

  return (
    <tr>
      <td colSpan={2} className="row-label">
        <div className="row-item">
          {projectURL?.Url ? (
            <Link href={projectURL?.Url} target={projectURL?.target}>
              {projectName}
            </Link>
          ) : (
            <span>{projectName}</span>
          )}
          {badge && <div className="badge">{badge}</div>}
        </div>
      </td>
      {cells}
    </tr>
  );
};

export default RoadmapDynamicTableRow;
