const RowTooltipContent = ({
  rowid,
  tooltipHeader,
  impact,
  tooltipBody,
}: any) => {
  return (
    <div
      id={rowid}
      aria-hidden="true"
      role="tooltip"
      className="tooltip-content"
      data-testid="tooltip-content"
    >
      <h3>{tooltipHeader}</h3>
      <div>
        <span className="impact-tooltip-label">Business Impact:&nbsp;</span>
        <span className="impact-tooltip-text">{impact}</span>
      </div>

      <p>{tooltipBody} </p>
    </div>
  );
};

export default RowTooltipContent;
