function getQuarter(date: any) {
  const month = date.getMonth() + 1; // getMonth is zero-based
  if (month >= 1 && month <= 3) return 1;
  if (month >= 4 && month <= 6) return 2;
  if (month >= 7 && month <= 9) return 3;
  else return 4;
}

function getColumnIndex(date: any) {
  const previousYear = new Date().getFullYear() - 1;
  const year = date.getFullYear();
  const quarter = getQuarter(date);
  if (year < previousYear) {
    return 0;
  } else if (year > previousYear + 3) {
    return 17;
  }
  return (year - previousYear) * 4 + quarter;
}

export function getStartAndEndColumn(startDateStr: string, endDateStr: string) {
  const startDate = new Date(startDateStr);
  const endDate = new Date(endDateStr);

  if (startDate > endDate) {
    throw new Error("Start date must be earlier than or equal to end date");
  }

  const startColumnIndex = getColumnIndex(startDate);
  const endColumnIndex = getColumnIndex(endDate);

  return {
    startColumnIndex,
    endColumnIndex,
  };
}

export function filterRoadmapData(propdata: any) {
  const filteredData = propdata?.filter((item: any) => {
    const impact = item?.BusinessImpact?.targetItem?.Name?.value;
    const lobValue = item?.LOB?.targetItems[0]?.Name?.value;
    const fromValue = item?.From?.value;
    const toValue = item?.To?.value;
    return impact && lobValue && fromValue && toValue;
  });

  const groupedData = filteredData?.reduce((acc: any, item: any) => {
    const lobName: string = item?.LOB?.targetItems[0]?.Name?.value;
    if (!acc[lobName]) {
      acc[lobName] = [];
    }
    acc[lobName].push(item);
    return acc;
  }, {});

  const result = Object.keys(groupedData)
    .sort() // Sort LOB names
    .map((key) => ({
      LOB: key,
      items: groupedData[key].sort(
        (a: any, b: any) => a?.Title?.value?.localeCompare(b?.Title?.value) // Sort items by Title.value
      ),
    }));

  return result;
}

export const shouldDisplayProject = (
  startDateStr: string,
  endDateStr: string
) => {
  const startYear = new Date(startDateStr).getFullYear();
  const endYear = new Date(endDateStr).getFullYear();
  const previousYear = new Date().getFullYear() - 1;

  return endYear < previousYear || startYear > previousYear + 4 ? false : true;
};

export const shouldDisplayLOB = (lobGroupProjects: any) => {
  return lobGroupProjects.some((row: any) =>
    shouldDisplayProject(row?.From?.value, row?.To?.value)
  );
};
