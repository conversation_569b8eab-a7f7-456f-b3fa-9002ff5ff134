import { useState, useEffect, useRef } from "react";
import {
  withSitecoreContext,
  Text,
  RichText,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { RichTextContent } from "../RichTextContent";
import { AlertBanner } from "components/common/AlertBanner";
import { DynamicEventsBase } from "components/Platform/lob/DynamicEventsBase";
import RoadmapDynamicTable from "./RoadMapDynamicComponent/RoadmapDynamicTable";
import { BannerDarkTheme } from "../home/<USER>";
import { UpcomingPanels } from "../panels/mypanels/UpcomingPanels";
import { PastPanels } from "../panels/mypanels/PastPanels";
import PanelSearch from "../panels/panelsearch/PanelSearch";
import { Accordion } from "../article/Accordion";
import { ContactTeam } from "./ContactTeam";
import { useRouter } from "next/router";

const TabsBase = (rendering: any) => {
  const [activeTab, setActiveTab] = useState(0);
  const elementRef = useRef<HTMLDivElement>(null);
  const [leftArrowDisable, setLeftArrowDisable] = useState(true);
  const [rightArrowDisable, setRightArrowDisable] = useState(false);
  const [showArrows, setShowArrows] = useState(false);
  const { route } = rendering?.sitecoreContext;
  const router = useRouter();
  const { query } = router;

  const InfoBanner = rendering?.fields?.Tabs[activeTab]?.fields?.TabItem?.find(
    (item: any) => item?.displayName === "InfoBanner"
  );

  useEffect(() => {
    if (query?.tabName) {
      const index = rendering?.fields?.Tabs.findIndex(
        (tab: any) => tab?.fields?.["Tab Anchor Name"]?.value === query.tabName
      );
      if (index !== -1) {
        setActiveTab(index);
      }
    } else {
      setActiveTab(0);
    }
  }, [query?.tabName]);

  // useEffect(() => {
  //   let index = 0;
  //   if (query?.tabName) {
  //     index = rendering?.fields?.Tabs.findIndex(
  //       (tab: any) => tab?.fields?.["Tab Anchor Name"]?.value === query?.tabName
  //     );
  //   }
  // }, []);

  const handleTabClick = (index: any) => {
    setActiveTab(index);
    const tabName =
      rendering?.fields?.Tabs[index].fields?.["Tab Anchor Name"]?.value;
    delete query.keyword;
    const restOfQuery = query;
    router.replace(
      { pathname: router.pathname, query: { ...restOfQuery, tabName } },
      undefined,
      { shallow: true } // Use shallow routing to prevent full page reload
    );
  };
  useEffect(() => {
    const handleResize = () => {
      const tabContainerScrollWidth = elementRef.current?.scrollWidth ?? 0;
      const tabContainerClientWidth = elementRef.current?.clientWidth ?? 0;
      setShowArrows(tabContainerScrollWidth > tabContainerClientWidth);
      if (tabContainerScrollWidth <= tabContainerClientWidth) {
        // Tabs fit within the container, disable right arrow
        setRightArrowDisable(true);
      } else {
        setRightArrowDisable(false);
      }
    };

    // Initial check on component mount
    handleResize();

    // Event listener for window resize
    window?.addEventListener("resize", handleResize);

    // Cleanup the event listener on component unmount
    return () => {
      window?.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleHorizantalScroll = (
    element: any,
    speed: number,
    distance: number,
    step: number,
    button: string
  ) => {
    let scrollAmount = 0;
    const slideTimer = setInterval(() => {
      element.scrollLeft += step;
      scrollAmount += Math.abs(step);
      if (scrollAmount >= distance) {
        clearInterval(slideTimer);
      }
      if (element.scrollLeft === 0) {
        setLeftArrowDisable(true);
      } else {
        setLeftArrowDisable(false);
      }
      if (button == "left") {
        setRightArrowDisable(false);
      }
      if (
        Math.ceil(element.scrollLeft) >=
        element.scrollWidth - element.clientWidth
      ) {
        // Scroll position is at the rightmost
        setRightArrowDisable(true);
      } else {
        // Scroll position is not at the rightmost
        setRightArrowDisable(false);
      }
    }, speed);
  };

  return (
    <>
      <section className=" tabs-content">
        {(rendering?.fields?.Title?.value ||
          rendering?.fields?.Content?.value) && (
          <div className="site">
            <Text field={rendering?.fields?.Title} tag="h2" className="title" />
            <RichText
              field={rendering?.fields?.Content}
              tag="div"
              className="tabs-description"
            />
          </div>
        )}
        <div className="site tabs responsive-tabs">
          <div className="tabbed flex-wrapper">
            <div className="nav-wrapper">
              <nav>
                {showArrows && !leftArrowDisable && (
                  <button
                    data-testid="tab-prev-btn"
                    className="tabNav prev"
                    tabIndex={0}
                    onClick={() => {
                      handleHorizantalScroll(
                        elementRef.current,
                        25,
                        100,
                        -10,
                        "left"
                      );
                    }}
                  >
                    <span className="material-symbols-outlined">
                      chevron_left
                    </span>
                  </button>
                )}
                <div className="tab-container" ref={elementRef}>
                  {rendering?.fields?.Tabs?.map((tab: any, index: any) => (
                    <a
                      key={index}
                      className={activeTab === index ? "tab active" : "tab"}
                      onClick={() => handleTabClick(index)}
                      onKeyUp={(e) =>
                        e.key === "Enter" && handleTabClick(index)
                      }
                      data-current={activeTab === index ? "active" : ""}
                      tabIndex={0}
                    >
                      {tab?.fields?.["Tab Name"]?.value
                        ? tab.fields?.["Tab Name"].value
                        : tab?.name}
                    </a>
                  ))}
                </div>
                {showArrows && !rightArrowDisable && (
                  <button
                    data-testid="tab-next-btn"
                    className="tabNav next"
                    tabIndex={0}
                    onClick={() => {
                      handleHorizantalScroll(
                        elementRef.current,
                        25,
                        100,
                        10,
                        "right"
                      );
                    }}
                  >
                    <span className="material-symbols-outlined">
                      chevron_right
                    </span>
                  </button>
                )}
              </nav>
              {route &&
                route.placeholders &&
                route.placeholders["jss-glossary-link"] && (
                  <Placeholder name="jss-glossary-link" rendering={route} />
                )}
            </div>
          </div>
        </div>
      </section>
      <div className="tab-content">
        {rendering?.fields?.Tabs[activeTab]?.fields?.TabItem?.map(
          (item: any) => {
            return (
              <div key={item.id} className="tab-item">
                {item?.fields?.ComponentName?.value == "Rich Text" && (
                  <RichTextContent {...item} />
                )}
                {item?.fields?.ComponentName?.value == "Alert Banner" && (
                  <AlertBanner {...item} />
                )}

                {item?.fields?.ComponentName?.value == "Events Dynamic" && (
                  <DynamicEventsBase {...item} />
                )}
                {item?.fields?.ComponentName?.value ==
                  "Roadmap Dynamic Table" && (
                  <RoadmapDynamicTable
                    {...item}
                    tabName={
                      rendering?.fields?.Tabs[activeTab]?.fields?.["Tab Name"]
                        ?.value
                        ? rendering?.fields?.Tabs[activeTab]?.fields?.[
                            "Tab Name"
                          ].value
                        : rendering?.fields?.Tabs[activeTab]?.name
                    }
                  />
                )}
                {item?.fields?.ComponentName?.value == "BannerDarkTheme" && (
                  <BannerDarkTheme {...item} />
                )}
                {item?.fields?.ComponentName?.value == "UpcomingPanels" && (
                  <UpcomingPanels
                    {...item}
                    UpcomingData={rendering?.cardData?.upcomingPanelResult}
                  />
                )}
                {item?.fields?.ComponentName?.value == "PastPanels" && (
                  <PastPanels
                    {...item}
                    PastData={rendering?.cardData?.pastPanelResult}
                  />
                )}
                {item?.fields?.ComponentName?.value == "PanelsSearch" && (
                  <PanelSearch
                    {...item}
                    bannerDescription={InfoBanner}
                    isEEFlag={rendering?.isEEFlag}
                  />
                )}
                {item?.fields?.ComponentName?.value == "Accordion" && (
                  <Accordion {...item} />
                )}
                {item?.fields?.ComponentName?.value == "ContactTeam" && (
                  <ContactTeam {...item} />
                )}
              </div>
            );
          }
        )}
      </div>
    </>
  );
};
export default withSitecoreContext()(TabsBase);
