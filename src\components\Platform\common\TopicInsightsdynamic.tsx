import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import React, { useState, useEffect, useContext, useCallback } from "react";
import Loader from "../../common/Loader";
import ErrorMessage from "../../common/ErrorMessage";
import { AuthContext } from "src/context/authContext";
import { useRouter } from "next/router";
import { RemoveHtmlTags } from "../widgets/htmlUtils";

type TopicInsightsdynamicProps = ComponentProps & {
  fields: {
    heading: Field<string>;
    Title: Field<string>;
    IntialLoad: any;
    "Loadmore Text": any;
    LoadMore: any;
    "Topic Tags": any;
    "Jurisdiction Tags": any;
    "LOB Tags": any;
    EngagementType: any;
  };
};

const TopicInsightsdynamic = (
  props: TopicInsightsdynamicProps
): JSX.Element => {
  const tpValue = props?.fields?.["Topic Tags"];
  const jsValue = props?.fields?.["Jurisdiction Tags"];
  const lobValue = props?.fields?.["LOB Tags"];
  const engagementType = props?.fields?.["EngagementType"];
  const router = useRouter();

  const IntialLoad = Number(props?.fields?.IntialLoad?.name) || 6;
  const LoadMore = Number(props?.fields?.LoadMore?.name) || 6;

  const [apiData, setApiData] = useState<{ SearchResult: any }>();
  const [resultCount, setresultCount] = useState(IntialLoad);
  const [showArticle, setshowArticle] = useState(false);
  const [isSpinner, setIsSpiner] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { isEditing } = useContext(AuthContext);
  const [TotalResults, setTotalResults] = useState(0);

  const context = useContext(AuthContext);
  const { PageState } = context;
  const PreviewMode =
    PageState === "edit" || PageState === "preview" ? true : false;
  const topicTags = tpValue?.map((tpVal: any) => tpVal.id).join(",") || "";
  const jsTags = jsValue?.map((jsVal: any) => jsVal.id).join(",") || "";
  const lobTags = lobValue?.map((lobVal: any) => lobVal.id).join(",") || "";
  const engagementTypeId =
    engagementType?.map((val: any) => val.id).join(",") || "";

  const initialLoadData = async () => {
    try {
      const apiUrl = PreviewMode
        ? `${process.env.NEXT_PUBLIC_SITECORE_CM_API_HOST}`
        : `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}`;

      const post = await fetch(
        `${apiUrl}/RecentInsightByTopic/results?topicTag=${topicTags}&jurisdictionTags=${jsTags}&lobTags=${lobTags}&et=${engagementTypeId}&il=${IntialLoad}&lm=${resultCount}`
      ).then((res) => res.json());
      setIsSpiner(false);
      setApiData(post);
      setTotalResults(post?.TotalResults);
      if (post?.SearchResult?.length > IntialLoad) {
        setshowArticle(true);
      }
    } catch (error) {
      setErrorMessage(
        "We are experiencing an issue loading Topic Inisghts and are working to resolve it. Thank you for your patience."
      );
      setIsError(true);
      setIsSpiner(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  };

  const LoadMoreData = async () => {
    try {
      const apiUrl = PreviewMode
        ? `${process.env.NEXT_PUBLIC_SITECORE_CM_API_HOST}`
        : `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}`;

      const post = await fetch(
        `${apiUrl}/RecentInsightByTopic/results?topicTag=${topicTags}&jurisdictionTags=${jsTags}&lobTags=${lobTags}&et=${engagementTypeId}&il=${IntialLoad}&lm=${resultCount}`
      ).then((res) => res.json());
      setIsSpiner(false);
      setApiData(post);
      if (post?.SearchResult?.length > IntialLoad) {
        setshowArticle(true);
      }
    } catch (error) {
      setErrorMessage(
        "We are experiencing an issue loading Topic Inisghts and are working to resolve it. Thank you for your patience."
      );
      setIsError(true);
      setIsSpiner(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  };

  const loadMoreHandler = useCallback(
    (e: any) => {
      e.preventDefault();
      if (e.keyCode === 13 || e.type === "click") {
        const newCount = resultCount + LoadMore;
        setresultCount(newCount);
        if (apiData?.SearchResult?.length <= newCount) {
          setshowArticle(false);
        }
      }
    },
    [resultCount, apiData, LoadMore]
  );

  const [initialPageLoad, setInitialPageLoad] = useState(true);

  useEffect(() => {
    if (!router.isReady) return;

    // reset and reload data on route change or prop updates
    setIsSpiner(true);
    setresultCount(IntialLoad);
    setshowArticle(false);
    setInitialPageLoad(true);
    initialLoadData();
  }, [
    router.isReady,
    router.pathname,
    tpValue,
    jsValue,
    lobValue,
    engagementType,
  ]);

  useEffect(() => {
    if (!initialPageLoad) {
      LoadMoreData();
    } else {
      setInitialPageLoad(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [resultCount]);

  if (isError) {
    return (
      <section className="recent-insights guest-user">
        <Text field={props?.fields?.Title} tag="h2" />
        <div className="cards flex-wrapper">
          <ErrorMessage message={errorMessage} />
        </div>
      </section>
    );
  }

  if (apiData?.SearchResult?.length === 0 || apiData?.SearchResult === null) {
    if (!isEditing) {
      return <></>;
    }
    return (
      <section className="recent-insights guest-user">
        <Text field={props?.fields?.Title} tag="h2" />
        <div className="cards flex-wrapper">
          <div className="card">"No Results found"</div>
        </div>
      </section>
    );
  }
  return (
    <section className="recent-insights guest-user">
      {apiData?.SearchResult?.length > 0 && (
        <Text field={props?.fields?.Title} tag="h2" />
      )}
      <div className="cards flex-wrapper">
        {isSpinner ? (
          <Loader />
        ) : (
          <>
            {apiData?.SearchResult?.length > 0 && (
              <>
                {apiData?.SearchResult?.slice(0, resultCount).map(
                  (article: any, id: any) => {
                    return (
                      <article
                        className="card"
                        key={id}
                        onClick={() => {
                          if (window) {
                            window.location = article.Link;
                          }
                        }}
                        data-testid="article-Link"
                      >
                        <h3>
                          <a
                            data-region={props?.fields?.Title?.value}
                            data-title={article?.Title}
                            data-interaction="click"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              router.push(article.Link);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault(); // Prevent the default action of pressing Enter
                                router.push(article.Link);
                              }
                            }}
                            tabIndex={0}
                            data-testid="title-link"
                          >
                            {RemoveHtmlTags(article.Title)}
                          </a>
                        </h3>
                        <p>
                          <time>{article.PublishedDate}</time> -{" "}
                          {RemoveHtmlTags(article.ShortDescription)}
                        </p>
                      </article>
                    );
                  }
                )}
              </>
            )}
          </>
        )}

        {apiData?.SearchResult?.length > 0 &&
          TotalResults - resultCount - IntialLoad > 0 && (
            <>
              {showArticle ? (
                <div className="call-to-action">
                  <a
                    className="secondary"
                    tabIndex={0}
                    role="button"
                    onKeyDown={(e) => loadMoreHandler(e)}
                    onClick={(e) => {
                      loadMoreHandler(e);
                    }}
                    data-testid="loadmore"
                  >
                    <Text field={props?.fields?.["Loadmore Text"]} />
                  </a>
                </div>
              ) : (
                ""
              )}
            </>
          )}
      </div>
    </section>
  );
};
export default withDatasourceCheck()<TopicInsightsdynamicProps>(
  TopicInsightsdynamic
);
