import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const TwoColumnsSubLayout = (rendering: any): JSX.Element => {
  const { route } = rendering?.sitecoreContext;
  const isEEFlag = rendering?.isEEFlag || false;
  return (
    <>
      <div className="site flex-wrapper">
        <div className="left-rail-section content-wrapper">
          <Placeholder
            isEEFlag={isEEFlag}
            name="jss-left-rail-section"
            fieldsVal={route.fields}
            rendering={route}
            EEFlag={isEEFlag}
          />
        </div>
        <aside className="right-rail">
          <Placeholder
            name="jss-right-rail-section"
            isEEFlag={isEEFlag}
            fieldsVal={route.fields}
            rendering={route}
            EEFlag={isEEFlag}
            isSideBar
          />
        </aside>
      </div>
    </>
  );
};

export default withSitecoreContext()(TwoColumnsSubLayout);
