import { useState } from "react";

const usePagination = (
  initialPage = 1,
  itemPerPage = 10,
  pageLimit = 3,
  ...props: any
) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [maxPageNumberLimit, setMaxPageNumberLimit] = useState(pageLimit);
  const [minPageNumberLimit, setMinPageNumberLimit] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [pages, setPages] = useState([]);
  const [pagination, setPagination] = useState<any>();
  const [itemsPerPage, setitemsPerPage] = useState(itemPerPage);
  const [hideResultsPerPage, setHideResultsPerPage] = useState(false);
  const [resultsPerPage, setResultsPerPage] = useState(() => {
    return [props?.fields?.["Results per page options"]];
  });
  const [totalResults, setTotalResults] = useState(pagination);

  const resetPagination = () => {
    setCurrentPage(initialPage);
    setMaxPageNumberLimit(pageLimit);
    setMinPageNumberLimit(0);
    setTotalPages(0);
    setPages([]);
    setPagination(undefined);
    setitemsPerPage(itemPerPage);
    setHideResultsPerPage(false);
    setResultsPerPage([props?.fields?.["Results per page options"]]);
    setTotalResults(undefined);
  };

  const handleNext = (
    setRequestParams: any,
    requestParams: any,
    scrollToTop: any
  ) => {
    scrollToTop();
    setRequestParams({ ...requestParams, pageNumb: currentPage + 1 });

    if (currentPage + 1 > maxPageNumberLimit) {
      setMaxPageNumberLimit(maxPageNumberLimit + pageLimit);
      setMinPageNumberLimit(minPageNumberLimit + pageLimit);
    }
  };

  const handleExtremeNext = (
    setRequestParams: any,
    requestParams: any,
    scrollToTop: any
  ) => {
    scrollToTop();
    if (pages?.length > 0) {
      const lastPage = totalPages;
      setRequestParams({ ...requestParams, pageNumb: lastPage });
      if (lastPage % pageLimit !== 0) {
        setMaxPageNumberLimit(lastPage);
        setMinPageNumberLimit(lastPage - ((lastPage % pageLimit) - 1) - 1);
      } else {
        setMaxPageNumberLimit(lastPage);
        setMinPageNumberLimit(lastPage - (pageLimit + 1) + 1);
      }
    }
  };

  const handleExtremePrev = (
    setRequestParams: any,
    requestParams: any,
    scrollToTop: any
  ) => {
    scrollToTop();
    if (pages.length > 0) {
      const firstPage = pages[0];
      setRequestParams({ ...requestParams, pageNumb: firstPage });
      setCurrentPage(firstPage);
      setMaxPageNumberLimit(pageLimit);
      setMinPageNumberLimit(firstPage - 1);
    }
  };

  const handlePrev = (
    e: any,
    setRequestParams: any,
    requestParams: any,
    scrollToTop: any
  ) => {
    e.preventDefault();
    scrollToTop();
    setRequestParams({ ...requestParams, pageNumb: currentPage - 1 });

    if ((currentPage - 1) % pageLimit === 0) {
      setMaxPageNumberLimit(currentPage - 1);
      setMinPageNumberLimit(currentPage - pageLimit - 1);
    }
  };

  const handleClick = (
    event: any,
    setRequestParams: any,
    requestParams: any,
    scrollToTop: any
  ) => {
    scrollToTop();
    setRequestParams({ ...requestParams, pageNumb: Number(event.target.id) });
  };

  const renderPageNumbers = (
    requestParams: any,
    setRequestParams: any,
    scrollToTop: any,
    pages: any
  ) =>
    pages?.map((number: any) => {
      if (number < maxPageNumberLimit + 1 && number > minPageNumberLimit) {
        return (
          <li
            className={
              currentPage === number
                ? "page-item page-number active"
                : "page-item page-number"
            }
            key={number + "Page"}
          >
            <a
              id={number}
              tabIndex={0}
              onKeyUp={(e) =>
                e.key === "Enter" &&
                handleClick(e, setRequestParams, requestParams, scrollToTop)
              }
              onClick={(e) =>
                handleClick(e, setRequestParams, requestParams, scrollToTop)
              }
              className={
                currentPage === number ? "page-link active" : "page-link"
              }
            >
              {number}
            </a>
          </li>
        );
      } else {
        return null;
      }
    });

  const resultsPerPageCount = (count: any, initialArray: any) => {
    if (count <= 10) {
      setHideResultsPerPage(true);
    } else if (count <= 25) {
      setHideResultsPerPage(false);
      setResultsPerPage([...initialArray.slice(0, 2)]);
    } else if (count <= 100) {
      setHideResultsPerPage(false);
      setResultsPerPage([...initialArray.slice(0, 3)]);
    } else if (count <= 250 || count > 250) {
      setHideResultsPerPage(false);
      setResultsPerPage([...initialArray]);
    }
  };

  const handleSelect = (
    event: any,
    setRequestParams: any,
    requestParams: any,
    scrollToTop: any
  ) => {
    scrollToTop();
    setRequestParams({
      ...requestParams,
      dropDownCount: Number(event.target?.value),
      pageNumb: 1,
    });
    setMaxPageNumberLimit(3);
    setMinPageNumberLimit(0);
  };

  return {
    currentPage,
    setCurrentPage,
    maxPageNumberLimit,
    setMaxPageNumberLimit,
    minPageNumberLimit,
    setMinPageNumberLimit,
    totalResults,
    setTotalResults,
    totalPages,
    setTotalPages,
    pages,
    setPages,
    pagination,
    setPagination,
    itemPerPage,
    setitemsPerPage,
    hideResultsPerPage,
    setHideResultsPerPage,
    resultsPerPage,
    setResultsPerPage,
    handleNext,
    handleExtremeNext,
    handleExtremePrev,
    handlePrev,
    handleSelect,
    renderPageNumbers,
    resultsPerPageCount,
    itemsPerPage,
    resetPagination,
  };
};

export default usePagination;
