import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import Loader from "components/common/Loader";
import { ComponentProps } from "lib/component-props";
import React, {
  useCallback,
  useContext,
  useEffect,
  useState,
  useMemo,
} from "react";
import InternalExternalLink from "../utils/InternalExternalLink";
import { AuthContext } from "src/context/authContext";
import ErrorMessage from "../../common/ErrorMessage";
import { useRouter } from "next/router";

type VeriskActivityHorizontalDynamicProps = ComponentProps & {
  fields: {
    heading: Field<string>;
    IntialLoad: Field<string>;
  };
  EEflag: boolean;
};

const VeriskActivityHorizontalDynamic = (props: any): JSX.Element => {
  const initialLoad = Number(props?.fields?.InitialLoad?.name) || 4;
  const loadMoreValue = Number(props?.fields?.LoadMore) || 4;
  const [data, setData] = useState<any | null>(null);
  const [isSpinner, setIsSpinner] = useState(true);
  const [, setIsError] = useState(false);
  const [isloadMoreError, setIsloadMoreError] = useState(false);
  const [, setRecentErrorMessage] = useState("");
  const [totalResults, setTotalResults] = useState(1);
  const [pageIndex, setPageIndex] = useState(1);
  const router = useRouter();
  const { asPath } = router;

  const resetPagination = () => setPageIndex(1); // Reset to page 1
  const topictags = useMemo(
    () =>
      props?.fields?.TopicTags?.map(
        (arr: any) => arr.fields?.Code?.value
      )?.join(","),
    [props?.fields?.TopicTags]
  );

  const lobtags = useMemo(
    () =>
      props?.fields?.LOBTags?.map((arr: any) => arr.fields?.Code?.value)?.join(
        ","
      ),
    [props?.fields?.LOBTags]
  );
  const jurisdictiontags = useMemo(
    () =>
      props?.fields.JurisdictionTags?.map(
        (arr: any) => arr.fields?.Code?.value
      )?.join(","),
    [props?.fields.JurisdictionTags]
  );
  const context = useContext(AuthContext);
  const { accessToken, PageState } = context;
  const PreviewMode =
    PageState === "edit" || PageState === "preview" ? true : false;

  useEffect(() => {
    if (!props?.EEflag) {
      resetPagination();
      inititalfetchData(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [topictags, lobtags, jurisdictiontags, asPath]);

  const inititalfetchData = async (pageNumber: number) => {
    setPageIndex(1);
    const params = {
      VAType: "Horizontal",
      TopicTag: topictags,
      LobTag: lobtags,
      JurisdictionTag: jurisdictiontags,
      InitialLoad: initialLoad,
      LoadMore: loadMoreValue,
      PageIndex: pageNumber,
    };
    try {
      const apiUrl = PreviewMode
        ? `${process.env.NEXT_PUBLIC_SITECORE_CM_API_HOST}`
        : `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}`;
      const response = await fetch(`${apiUrl}/VeriskActivity/GetResult`, {
        method: "post",
        body: JSON.stringify({ ...params }),
        headers: new Headers({
          "Content-Type": "application/json",
          Authorization: "Bearer " + accessToken,
        }),
      });
      if (!response.ok) {
        throw new Error("Api failed to fetch the data from server");
      }
      const jsonData: any = await response.json();
      setData(jsonData.VeriskActivities);
      const ItemLength = jsonData?.ResultCount;
      setTotalResults(ItemLength);
      setIsSpinner(false);
    } catch (error) {
      setRecentErrorMessage(
        "We are experiencing an issue loading verisk activity and are working to resolve it. Thank you for your patience."
      );
      setIsError(true);
      setIsSpinner(false);
      console.error("Error fetching verisk activity data:", error);
    }
  };

  const fetchData = async (pageNumber: number) => {
    const params = {
      VAType: "Horizontal",
      TopicTag: topictags,
      LobTag: lobtags,
      JurisdictionTag: jurisdictiontags,
      InitialLoad: initialLoad,
      LoadMore: loadMoreValue,
      PageIndex: pageNumber,
    };
    try {
      setIsSpinner(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}/VeriskActivity/GetResult?${pageNumber},${lobtags}}`,
        {
          method: "post",
          body: JSON.stringify({ ...params }),
          headers: new Headers({
            "Content-Type": "application/json",
            Authorization: "Bearer " + accessToken,
          }),
        }
      );
      //In case api failed to fetch the data and got ok=false
      if (!response.ok) {
        throw new Error("Api failed to fetch the data from server");
      }
      const jsonData = await response.json();
      setData((prevItems: any) => [...prevItems, ...jsonData.VeriskActivities]);
      setIsSpinner(false);
    } catch (error) {
      setRecentErrorMessage(
        "We are experiencing an issue loading verisk activity and are working to resolve it. Thank you for your patience."
      );
      setIsloadMoreError(true);
      setIsSpinner(false);
      console.error("Error fetching verisk activity data:", error);
    }
  };

  const loadMoreHandler = useCallback(() => {
    setPageIndex((prevIndex) => prevIndex + 1);
    fetchData(pageIndex + 1);
  }, [pageIndex]);

  return (
    <section className="verisk-activity background-lt-blue">
      <div className="site">
        <h2>
          <Text field={props.fields.Title} />
        </h2>
        {!props?.EEflag ? (
          <>
            <div className="cards no-padding flex-wrapper">
              {data?.map((activity: any, id: any) => {
                return (
                  <div
                    key={id}
                    className="card four"
                    data-testid="vahd-data-card"
                  >
                    <small>
                      <time>{activity.Date}</time>
                    </small>
                    <h3 className="no-margin">{activity.Title}</h3>
                    {activity?.Description && (
                      <p className="no-margin-bottom">{activity.Description}</p>
                    )}
                    <div className="agenda-link-group ">
                      {activity.Link1 && (
                        // <a href={activity?.Link1?.Url} target="_blank" className="agenda-link-group">
                        //   {activity?.Link1?.Title}
                        //   {activity.Link1.LinkType === 'external' ? <span className="material-icons">open_in_new</span> : ""}
                        // </a>
                        <InternalExternalLink
                          link={{
                            url: activity?.Link1?.Url || "",
                            text: activity?.Link1?.Title || "",
                            target: activity?.Link1?.Target || "",
                          }}
                          metaData={{
                            "data-region": "Verisk Activity",
                            "data-title":
                              activity?.Title !== ""
                                ? activity?.Title
                                : "Verisk Activity",
                            "data-interaction": "click",
                            "data-context": activity?.Link1?.Title,
                          }}
                        />
                      )}
                      {activity.Link2 && (
                        // <a href={activity?.Link2?.Url} target="_blank" className="agenda-link-group">
                        //   {activity?.Link2?.Title}
                        //   {activity.Link2.LinkType === 'external' ? <span className="material-icons">open_in_new</span> : ""}
                        // </a>
                        <InternalExternalLink
                          link={{
                            url: activity?.Link2?.Url || "",
                            text: activity?.Link2?.Title || "",
                            target: activity?.Link2?.Target || "",
                          }}
                          metaData={{
                            "data-region": "Verisk Activity",
                            "data-title":
                              activity?.Title !== ""
                                ? activity?.Title
                                : "Verisk Activity",
                            "data-interaction": "click",
                            "data-context": activity?.Link2?.Title,
                          }}
                        />
                      )}
                      {activity.Link3 && (
                        <InternalExternalLink
                          link={{
                            url: activity?.Link3?.Url || "",
                            text: activity?.Link3?.Title || "",
                            target: activity?.Link3?.Target || "",
                          }}
                          metaData={{
                            "data-region": "Verisk Activity",
                            "data-title":
                              activity?.Title !== ""
                                ? activity?.Title
                                : "Verisk Activity",
                            "data-interaction": "click",
                            "data-context": activity?.Link3?.Title,
                          }}
                        />
                      )}
                    </div>
                  </div>
                );
              })}
              {isSpinner ? <Loader /> : ""}
            </div>
            {!isloadMoreError && totalResults > data?.length && (
              <div className={`call-to-action`}>
                <a
                  className="secondary"
                  onClick={loadMoreHandler}
                  role="button"
                  data-region="Verisk Activity"
                  data-title={
                    props?.fields?.Title?.value !== ""
                      ? props?.fields?.Title?.value
                      : "Verisk Activity"
                  }
                  data-interaction="click"
                  data-context="Load more"
                  onKeyUp={(e) => e.key === "Enter" && loadMoreHandler()}
                  tabIndex={0}
                >
                  Load more
                </a>
              </div>
            )}
          </>
        ) : (
          <ErrorMessage
            dataTestId="horizontal-dynamic-activity-err"
            message={
              "We are experiencing an issue loading verisk activity and are working to resolve it. Thank you for your patience."
            }
          />
        )}
      </div>
    </section>
  );
};

export default withDatasourceCheck()<VeriskActivityHorizontalDynamicProps>(
  VeriskActivityHorizontalDynamic
);
