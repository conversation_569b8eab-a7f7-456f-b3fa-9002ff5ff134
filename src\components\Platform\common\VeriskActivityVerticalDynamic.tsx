import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import { useContext, useEffect, useState, useMemo } from "react";
import Loader from "../../common/Loader";
import ErrorMessage from "../../common/ErrorMessage";
import InternalExternalLink from "../utils/InternalExternalLink";
import { AuthContext } from "src/context/authContext";

type VeriskActivityVerticalDynamicProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
  EEFlag: boolean;
};

const VeriskActivityVerticalDynamic = (props: any): JSX.Element => {
  const { fields, EEFlag } = props;
  const [initialLoad, setIntialLoad] = useState(3);
  const [loadMoreValue, setLoadMoreValue] = useState(0);
  const [response, setresponse]: any = useState({});
  const [intialResponse, setIntialResponse]: any = useState({});
  const [loadmoreVisible, setloadmoreVisible] = useState(true);
  const [isSpinner, setIsSpiner] = useState(true);
  const [isError, setIsError] = useState(false);
  const [showAll, setShowAll] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [pageIndex, setPageIndex] = useState(1);

  const topictags = useMemo(
    () =>
      fields.TopicTags.map((arr: any) => arr.fields?.Code?.value)?.join(","),
    [fields?.TopicTags]
  );
  const lobtags = useMemo(
    () => fields.LOBTags.map((arr: any) => arr.fields?.Code?.value)?.join(","),
    [fields?.LOBTags]
  );

  const jurisdictiontags = useMemo(
    () =>
      fields.JurisdictionTags.map((arr: any) => arr.fields?.Code?.value)?.join(
        ","
      ),
    [fields.JurisdictionTags]
  );

  const EnableDeepDivePageFilter =
    fields?.["Enable Deep Dive Page Filter"]?.value;

  const context = useContext(AuthContext);
  const { accessToken, PageState } = context;
  const PreviewMode =
    PageState === "edit" || PageState === "preview" ? true : false;

  const deepDivePage =
    props?.path
      ?.toString()
      ?.toLocaleUpperCase()
      ?.split("/")
      ?.indexOf("LOB-DEEP-DIVE-PAGES") === 1
      ? true
      : false;

  useEffect(() => {
    {
      data(true, pageIndex);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [topictags, lobtags, jurisdictiontags]);

  const showMoreItems = (e: any) => {
    e.preventDefault();
    const pageNumber = pageIndex + 1;
    setPageIndex(pageNumber);
    data(false, pageNumber);
    setloadmoreVisible(false);
    setShowAll(true);
  };

  const showLessItems = (e: any) => {
    e.preventDefault();
    setresponse(intialResponse);
    setloadmoreVisible(true);
    setShowAll(false);
  };

  const data = async (intialLoadData: any = false, pageNumber: number) => {
    const params = {
      VAType: "Vertical",
      TopicTag: topictags,
      LobTag: lobtags,
      JurisdictionTag: jurisdictiontags,
      InitialLoad: initialLoad,
      LoadMore: loadMoreValue,
      PageIndex: pageNumber,
      deepdivepage:
        deepDivePage && EnableDeepDivePageFilter ? props?.itemId : "",
    };
    try {
      const apiUrl = PreviewMode
        ? `${process.env.NEXT_PUBLIC_SITECORE_CM_API_HOST}`
        : `${process.env.NEXT_PUBLIC_SITECORE_API_HOST}`;
      const response = await fetch(`${apiUrl}/VeriskActivity/GetResult`, {
        method: "post",
        body: JSON.stringify({ ...params }),
        headers: new Headers({
          "Content-Type": "application/json",
          Authorization: "Bearer " + accessToken,
        }),
      });
      const jsonData: any = await response.json();
      setIntialLoad(3);
      setLoadMoreValue(0);

      if (intialLoadData) {
        setresponse(jsonData);
        setIntialResponse(jsonData);
        if (
          (EEFlag && jsonData.VeriskActivities.length <= 0) ||
          (EEFlag && topictags == "" && lobtags == "" && jurisdictiontags == "")
        ) {
          setIsError(true);
          setErrorMessage("No Records Found");
          return;
        }
      } else {
        const newData = [
          ...intialResponse.VeriskActivities,
          ...jsonData.VeriskActivities,
        ];
        const newJsonData = {
          VeriskActivities: newData,
          ResultCount: jsonData.ResultCount,
        };
        setresponse(newJsonData);
      }
      setIsSpiner(false);
    } catch (error) {
      setErrorMessage(
        "We are experiencing an issue loading Verisk Activity and are working to resolve it. Thank you for your patience."
      );
      setIsError(true);
      setIsSpiner(false);
    }
  };

  if (isError) {
    return (
      <section className="verisk-activity with-show-more background-lt-grey">
        <Text field={props?.fields?.Title} tag="h2" />
        <ErrorMessage
          dataTestId="vertical-dynamic-activity-err"
          message={errorMessage}
        />
      </section>
    );
  }
  const render = (response: any) => {
    return (
      <section className="verisk-activity with-show-more background-lt-grey">
        {/* <h2>Verisk Activity</h2> */}
        <h2>
          <Text field={props?.fields?.Title} />
        </h2>
        {isSpinner ? (
          <Loader />
        ) : (
          <>
            <ul
              className={`link-list ${
                showAll ? "show-all" : null
              } link-list-with-vertical-scroll`}
            >
              {response?.VeriskActivities?.map((val: any, id: any) => {
                return (
                  <li key={id} data-testid="vavd-data-card">
                    <time>{val.Date}</time>
                    <h3>{val.Title}</h3>
                    <div className="agenda-link-group">
                      {val.Link1 && (
                        <InternalExternalLink
                          link={{
                            url: val?.Link1?.Url || "",
                            text: val?.Link1?.Title || "",
                            target: val?.Link1?.Target || "",
                          }}
                          metaData={{
                            "data-region": "Verisk Activity",
                            "data-title":
                              val?.Title !== ""
                                ? val?.Title
                                : "Verisk Activity",
                            "data-interaction": "click",
                            "data-context": val?.Link1?.Title,
                          }}
                        />
                      )}
                      {val.Link2 && (
                        <InternalExternalLink
                          link={{
                            url: val?.Link2?.Url || "",
                            text: val?.Link2?.Title || "",
                            target: val?.Link2?.Target || "",
                          }}
                          metaData={{
                            "data-region": "Verisk Activity",
                            "data-title":
                              val?.Title !== ""
                                ? val?.Title
                                : "Verisk Activity",
                            "data-interaction": "click",
                            "data-context": val?.Link2?.Title,
                          }}
                        />
                      )}
                      {val.Link3 && (
                        <InternalExternalLink
                          link={{
                            url: val?.Link3?.Url || "",
                            text: val?.Link3?.Title || "",
                            target: val?.Link3?.Target || "",
                          }}
                          metaData={{
                            "data-region": "Verisk Activity",
                            "data-title":
                              val?.Title !== ""
                                ? val?.Title
                                : "Verisk Activity",
                            "data-interaction": "click",
                            "data-context": val?.Link3?.Title,
                          }}
                        />
                      )}
                    </div>
                  </li>
                );
              })}
            </ul>
            {response?.ResultCount > 3 && loadmoreVisible && (
              <a
                href="#showMore"
                className="show-more-activities"
                onClick={showMoreItems}
                data-region="Verisk Activity"
                data-title={
                  props?.fields?.Title?.value !== ""
                    ? props?.fields?.Title?.value
                    : "Verisk Activity"
                }
                data-interaction="click"
                data-context="Load More Activity"
              >
                Load More Activity
              </a>
            )}
            {!loadmoreVisible && (
              <a
                href="#showLess"
                className="show-less-activities"
                onClick={showLessItems}
                data-region="Verisk Activity"
                data-title={
                  props?.fields?.Title?.value !== ""
                    ? props?.fields?.Title?.value
                    : "Verisk Activity"
                }
                data-interaction="click"
                data-context="Load Less Activity"
              >
                Load Less Activity
              </a>
            )}
          </>
        )}
      </section>
    );
  };
  return (
    <>
      {!EEFlag &&
      (topictags != "" ||
        lobtags != "" ||
        jurisdictiontags != "" ||
        (deepDivePage && EnableDeepDivePageFilter === true)) &&
      response?.VeriskActivities?.length > 0
        ? render(response)
        : EEFlag
        ? render(response)
        : null}
    </>
  );
};

export default withDatasourceCheck()<VeriskActivityVerticalDynamicProps>(
  VeriskActivityVerticalDynamic
);
