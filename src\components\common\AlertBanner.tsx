import {
  withData<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Text,
  RichText,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import { useCallback, useEffect } from "react";
import { useOktaAuth } from "src/context/oktaAuth";
import { descriptionWithCustomrID } from "./RichTextWithCustomerId";
import { useContext } from "react";
import { AuthContext } from "src/context/authContext";

type AlertBannerProps = ComponentProps & {
  fields: any;
};

export const AlertBanner = (props: AlertBannerProps): React.JSX.Element => {
  const { fields } = props;
  const { Lock: isLockVisible, Title, Content: Description } = fields || {};
  const RightRail = props?.fields?.["Right Rail"]?.value;
  const context = useContext(AuthContext);
  const RichTextWithCustomerID: any = descriptionWithCustomrID({
    field: Description,
    customerId: context?.customerNumber,
  });

  const BackgroundColor: string = RightRail
    ? "grey"
    : fields?.[`BackGround Color`]?.displayName || "white";

  const { handleLogin } = useOktaAuth();

  const redirectToSignProcess = useCallback(
    (e: Event) => {
      e.stopPropagation();
      e.preventDefault();
      handleLogin();
    },
    [handleLogin]
  );

  useEffect(() => {
    const anchorElement = document?.querySelectorAll(
      "a[class='alert-sign-in']"
    );
    if (anchorElement) {
      anchorElement?.forEach((item) => {
        item.addEventListener("click", redirectToSignProcess);
      });
    }
    return () => {
      anchorElement?.forEach((item) => {
        item.removeEventListener("click", redirectToSignProcess);
      });
    };
  }, [redirectToSignProcess]);

  if (RightRail) {
    return (
      <section
        className={`alert-banner vertical-alert-banner alert-banner__${BackgroundColor.toLowerCase()}`}
      >
        <div className="site alert-banner__container">
          <Text field={Title} tag="h2" />
          <div className="align-side-by-side">
            <span>
              {isLockVisible?.value && (
                <span className="material-icons">lock</span>
              )}
              <RichText
                field={RichTextWithCustomerID}
                tag="span"
                className="align-side-by-side__editor"
              />
            </span>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section
      className={`alert-banner alert-banner__${BackgroundColor.toLowerCase()}`}
    >
      <div className="site alert-banner__container">
        <Text field={Title} tag="h2" />
        <div className="align-side-by-side">
          {isLockVisible?.value && <span className="material-icons">lock</span>}
          <RichText
            field={RichTextWithCustomerID}
            tag="div"
            className="align-side-by-side__editor"
          />
        </div>
      </div>
    </section>
  );
};

export default withDatasourceCheck()<AlertBannerProps>(AlertBanner);
