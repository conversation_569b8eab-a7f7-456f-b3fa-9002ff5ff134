import { withSitecoreContext } from "@sitecore-jss/sitecore-jss-nextjs";
import NotificationPreferencesHeader from "./NotificationPreferencesHeader";
import NotificationPreferencesDropdown from "./ProductSelection/NotificationPreferencesDropdown";
import UpdatePreferences from "./ProductSelection/UpdatePreferences";
import CheckboxForm from "./ProductSelection/CheckboxForm";
import { fetchPreferences } from "./NotificationService";
import { useSession } from "next-auth/react";
import { useContext, useEffect, useMemo, useState, useRef } from "react";
import { AuthContext } from "src/context/authContext";
import {
  toTitleCase,
  normalizeString,
} from "../AlertsAndNotifications/ProductSelection/Utils/StringUtils";
import {
  getContentTypeResults,
  getJurisdictionResults,
  getLobResults,
  getJurisdictionEntitlements,
  getLobEntitlements,
} from "../AlertsAndNotifications/ProductSelection/Utils/PreferenceUtils";

const NotificationPreferences = ({ rendering }: any): React.JSX.Element => {
  const { data: session } = useSession();
  const { userLicensesAlert } = useContext(AuthContext) || {};

  const [selectedProduct, setSelectedProduct] = useState<string>("");
  const [selectedProfile, setSelectedProfile] = useState<string>("");
  const [selections, setSelections] = useState<any>({});
  const [responseData, setresponseData] = useState<any>({});
  const [isChecked, setIsChecked] = useState(false);
  const [totalCount, settotalCount] = useState(0);
  const [shouldHideUpdateBox, setshouldHideUpdateBox] = useState(true);
  const [formKey, setFormKey] = useState(0);
  const [globalNotificationValue, setGlobalNotificationValue] =
    useState<boolean>(false);
  const [initialEmailToggleMap, setInitialEmailToggleMap] = useState<
    Record<string, boolean>
  >({});
  const [isUpdating, setIsUpdating] = useState(false);

  const userPreferenceHandlersRef = useRef<{
    handleSaveOrCancelPreferences: () => void;
  }>({
    handleSaveOrCancelPreferences: () => {},
  });

  const notificationData = rendering?.fields?.data?.PreferencesData || {};

  const contentTypeResults = useMemo(
    () => getContentTypeResults(rendering),
    [rendering]
  );

  const lobResults = useMemo(() => getLobResults(rendering), [rendering]);

  const jurisdictionResults = useMemo(() => getJurisdictionResults(), []);

  const lobEntitlements = useMemo(
    () => getLobEntitlements(selectedProfile, userLicensesAlert),
    [selectedProfile, userLicensesAlert]
  );

  const jurisdictionEntitlements = useMemo(
    () =>
      getJurisdictionEntitlements(
        selectedProduct,
        selectedProfile,
        userLicensesAlert
      ),
    [selectedProduct, selectedProfile, userLicensesAlert]
  );

  useEffect(() => {
    if (session && selectedProduct && selectedProfile && !isUpdating) {
      const email = session?.user?.email || "";

      fetchPreferences(
        email,
        toTitleCase(selectedProduct),
        selectedProfile
      ).then((data) => {
        setresponseData((prev: any) => {
          const emailAllowed = data?.IsNotificationAllowed ?? false;
          const key = `${selectedProduct}-${selectedProfile}`;

          setInitialEmailToggleMap((prev) => ({
            ...prev,
            [key]: emailAllowed,
          }));

          setIsChecked(emailAllowed);

          const newPrefs = data?.UserPreferences || [];
          const prevPrefs = prev?.UserPreferences || [];
          const updatedPrefs = [...prevPrefs];

          newPrefs.forEach((newPref: any) => {
            const existingIndex = updatedPrefs.findIndex(
              (p: any) =>
                normalizeString(p.ProductName) ===
                normalizeString(newPref.ProductName)
            );

            if (existingIndex !== -1) {
              const existing =
                updatedPrefs[existingIndex].NotificationPreferences || [];
              const incoming = newPref.NotificationPreferences || [];

              const mergedMap = new Map();
              existing.forEach((np: any) =>
                mergedMap.set(np.CustomerNumber, np)
              );
              incoming.forEach((np: any) =>
                mergedMap.set(np.CustomerNumber, np)
              );

              updatedPrefs[existingIndex].NotificationPreferences = Array.from(
                mergedMap.values()
              );
            } else {
              updatedPrefs.push(newPref);
            }
          });

          return {
            ...prev,
            IsNotificationAllowed: data?.IsNotificationAllowed,
            UserEmail: data?.UserEmail,
            UserPreferences: updatedPrefs,
          };
        });
      });
    }
  }, [session, selectedProduct, selectedProfile, isUpdating]);

  const handleSelectionChange = (updatedSelections: any) => {
    setSelections(updatedSelections);
  };

  const handleCheckboxChange = (newIsChecked: boolean) => {
    const key = `${selectedProduct}-${selectedProfile}`;
    const isProductProfileSelected = selectedProduct && selectedProfile;
    const originalIsChecked = isProductProfileSelected
      ? initialEmailToggleMap?.[key] ?? false
      : globalNotificationValue ?? false;

    setIsChecked(newIsChecked);

    if (!isProductProfileSelected) {
      settotalCount(newIsChecked !== originalIsChecked ? 1 : 0);
      setshouldHideUpdateBox(newIsChecked === originalIsChecked);
    }
  };

  const handleCancelNotification = () => {
    if (userPreferenceHandlersRef?.current?.handleSaveOrCancelPreferences) {
      userPreferenceHandlersRef.current.handleSaveOrCancelPreferences();
    }

    setshouldHideUpdateBox(true);
    settotalCount(0);
    setFormKey((prev) => prev + 1);
  };

  const handleUpdateSuccess = () => {
    if (userPreferenceHandlersRef?.current?.handleSaveOrCancelPreferences) {
      userPreferenceHandlersRef.current.handleSaveOrCancelPreferences();
    }
    setFormKey((prev) => prev + 1);
  };

  const storeUserPreferenceHandlers = (handlers: {
    handleSaveOrCancelPreferences: () => void;
  }) => {
    userPreferenceHandlersRef.current = handlers;
  };

  // console.log(
  //   "count check after update",
  //   isUpdating,
  //   totalCount,
  //   !shouldHideUpdateBox,
  //   selectedProduct,
  //   selectedProfile
  // );

  return (
    <div className="notification-preferences-page">
      <main className="hub-page emerging-issues">
        <div className="sub-header">
          <div className="hub-header">
            <div className="site flex-wrapper">
              <h1>{notificationData?.Heading?.title}</h1>
            </div>
          </div>
        </div>
        <section className="notification-preferences-container">
          <div className="main-wrapper notification-preferences-wrapper">
            <NotificationPreferencesHeader
              fields={rendering.fields}
              handleCheckboxChange={handleCheckboxChange}
              setGlobalNotificationValue={setGlobalNotificationValue}
              emailNotification={responseData?.IsNotificationAllowed}
            />
            <NotificationPreferencesDropdown
              selectedProduct={selectedProduct}
              setSelectedProduct={setSelectedProduct}
              selectedProfile={selectedProfile}
              setSelectedProfile={setSelectedProfile}
              fields={rendering.fields}
            />
            <CheckboxForm
              key={formKey}
              selectedProduct={selectedProduct}
              selectedProfile={selectedProfile}
              contentTypeResults={contentTypeResults}
              jurisdictionResults={jurisdictionResults}
              lobResults={lobResults}
              onSelectionChange={handleSelectionChange}
              settotalCount={settotalCount}
              setshouldHideUpdateBox={setshouldHideUpdateBox}
              responseData={responseData}
              isChecked={isChecked}
              lobEntitlements={lobEntitlements}
              jurisdictionEntitlements={jurisdictionEntitlements}
              initialEmailToggleMap={initialEmailToggleMap}
              storeUserPreferenceHandlers={storeUserPreferenceHandlers}
            />
          </div>
        </section>
      </main>

      {totalCount > 0 &&
        !shouldHideUpdateBox &&
        selectedProduct &&
        selectedProfile && (
          <UpdatePreferences
            selections={selections}
            selectedProduct={selectedProduct}
            selectedProfile={selectedProfile}
            totalCount={totalCount}
            settotalCount={settotalCount}
            setIsUpdating={setIsUpdating}
            shouldHideUpdateBox={shouldHideUpdateBox}
            handleCancelNotification={handleCancelNotification}
            setresponseData={setresponseData}
            setInitialEmailToggleMap={setInitialEmailToggleMap}
            onUpdateSuccess={handleUpdateSuccess}
          />
        )}
      {totalCount > 0 && <div className="pop-up" />}
    </div>
  );
};

export default withSitecoreContext()(NotificationPreferences);
