import {
  withSitecoreContext,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";

const NotificationPreferencesBase = (rendering: any): React.JSX.Element => {
  const { route } = rendering?.sitecoreContext || {};

  return (
    <main className="hub-page emerging-issues">
      <Placeholder
        name="jss-main"
        fieldsVal={route?.fields}
        rendering={route}
      />
    </main>
  );
};

export default withSitecoreContext()(NotificationPreferencesBase);
