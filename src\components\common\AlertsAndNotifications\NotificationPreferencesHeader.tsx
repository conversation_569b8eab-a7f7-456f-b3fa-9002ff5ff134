import React, { useState, useEffect } from "react";
import { getGlobalNotification } from "./NotificationService";
import { useSession } from "next-auth/react";

const NotificationPreferencesHeader = (props: {
  fields: any;
  handleCheckboxChange: (isChecked: boolean) => void;
  setGlobalNotificationValue: (val: boolean) => void;
  emailNotification?: boolean;
}): React.JSX.Element => {
  const { data: session } = useSession();
  const notificationData: any = props?.fields?.data?.PreferencesData || {};

  const [isToggleChecked, setIsToggleChecked] = useState<boolean>(
    !!props.emailNotification
  );
  const [isSubscriptionChecked, setIsSubscriptionChecked] =
    useState<boolean>(false); // default unchecked

  console.log("Notification Data:", notificationData);
  // Fetch global notification toggle status
  useEffect(() => {
    async function fetchGlobalNotificationStatus() {
      try {
        const globalNotification = await getGlobalNotification(
          session?.user?.email || ""
        );
        // console.log("Global Notification Status:", globalNotification);
        setIsToggleChecked(globalNotification); // set toggle only
        props.setGlobalNotificationValue(globalNotification);
      } catch (error) {
        console.error("Error fetching global notification status:", error);
      }
    }

    if (session?.user?.email) {
      fetchGlobalNotificationStatus();
    }
  }, [session?.user?.email]);

  // If props.emailNotification changes
  useEffect(() => {
    if (props.emailNotification !== undefined) {
      setIsToggleChecked(!!props.emailNotification);
    }
  }, [props.emailNotification]);

  // Email toggle change
  const handleToggleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    console.log("Email toggle changed:", checked);
    setIsToggleChecked(checked);
    props.handleCheckboxChange(checked); // notify parent
  };

  // Email subscription checkbox change
  const handleSubscriptionCheckboxChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const checked = event.target.checked;
    console.log("Email subscription checkbox changed:", checked);
    setIsSubscriptionChecked(checked);
  };

  return (
    <div>
      <div className="notification-preferences-header">
        <h1 className="notification-header-title">
          {notificationData?.SubHeading?.title}
        </h1>

        <p className="notification-header-desc desc-text">
          {notificationData?.Description?.title}
        </p>

        <div className="email-notifications">
          <div className="notification-toggle">
            <h3 className="email-notifications-title">
              {notificationData?.EmailNotificationTitle?.title}
            </h3>
            <div className="toggle-container">
              <label className="switch" aria-label="Toggle Email Notifications">
                <input
                  id="email-toggle"
                  type="checkbox"
                  className="content-toggle"
                  checked={isToggleChecked}
                  onChange={handleToggleChange}
                />
                <div className="slider round"></div>
                <span id="toggle-text" className="toggle-text">
                  {isToggleChecked ? "On" : "Off"}
                </span>
              </label>
            </div>
          </div>

          <p className="email-notifications-desc desc-text">
            {notificationData?.EmailConsentText?.title}
          </p>

          <p className="notification-checkbox">
            <label
              className="email-notif-checkbox"
              aria-label="Email Subscription"
            >
              <input
                type="checkbox"
                checked={isSubscriptionChecked}
                onChange={handleSubscriptionCheckboxChange}
              />
              <span className="checkmark"></span>
            </label>
            <span className="desc-text checkbox-text">
              {notificationData?.EmailSubscriptionText?.title}
            </span>
          </p>
        </div>
      </div>

      <div
        dangerouslySetInnerHTML={{
          __html: notificationData?.NotificationSummary?.title,
        }}
      ></div>

      <div className="notification-separator">
        <div className="notification-separator-line"></div>
      </div>
    </div>
  );
};

export default NotificationPreferencesHeader;
