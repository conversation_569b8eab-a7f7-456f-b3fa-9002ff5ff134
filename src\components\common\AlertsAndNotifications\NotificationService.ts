import axios from "axios";

// Fetch Preferences
export async function fetchPreferences(
  emailId: string,
  product: string,
  profile: string
) {
  const url = `/api/preferences?type=preference&userEmail=${emailId}&productName=${encodeURIComponent(
    product
  )}&profileId=${encodeURIComponent(profile)}`;
  try {
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching preferences:", error);
    throw error;
  }
}

// Fetch Global Notification
export async function getGlobalNotification(emailId: string) {
  const url = `/api/preferences?type=globalnotification&userEmail=${emailId}`;
  try {
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching global notification status:", error);
    throw error;
  }
}

// Save Preferences
export async function savePreferences(finalJson: any) {
  const url = `/api/preferences`;
  try {
    const response = await axios.post(url, finalJson, {
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error saving preferences:", error);
    throw error;
  }
}
