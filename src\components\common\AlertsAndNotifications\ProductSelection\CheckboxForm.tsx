import React, { useState, useEffect, useRef } from "react";
import useUserPreference from "./UserPreference";
import CheckboxSection from "./CheckboxSection";
import { useSession } from "next-auth/react";
import { PRODUCT_CODES } from "./Utils/StringUtils";

import {
  calculatePreferencesChanges,
  extractPreferenceData,
} from "./Utils/PreferenceDataUtils";

interface CheckboxFormProps {
  selectedProduct?: string;
  selectedProfile?: string;
  contentTypeResults?: { fields: { name: string; value: string }[] }[];
  lobResults?: { code: { value: string }; displayLabel: { value: string } }[];
  jurisdictionResults?: {
    code: { value: string };
    displayLabel: { value: string };
  }[];
  responseData?: any;
  lobEntitlements?: string[];
  jurisdictionEntitlements?: string[];
  settotalCount: (count: number) => void;
  setshouldHideUpdateBox: (hide: boolean) => void;
  onSelectionChange?: (selectedOptions: any) => void;
  isChecked: boolean;
  initialEmailToggleMap?: Record<string, boolean>;
  storeUserPreferenceHandlers?: (handlers: {
    handleSaveOrCancelPreferences: () => void;
  }) => void;
}

const CheckboxForm: React.FC<CheckboxFormProps> = ({
  selectedProduct = "",
  selectedProfile = "",
  contentTypeResults = [],
  lobResults = [],
  jurisdictionResults = [],
  responseData,
  settotalCount,
  onSelectionChange,
  lobEntitlements = [],
  jurisdictionEntitlements = [],
  setshouldHideUpdateBox,
  initialEmailToggleMap,
  isChecked,
  storeUserPreferenceHandlers,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const {
    contentCheckedState,
    lobCheckedState,
    jurisdictionCheckedState,
    handleChange,
    handleAllToggle,
    handleMatchMyOrder,
    contentWarning,
    lobWarning,
    jurisdictionWarning,
    toggleAllContent,
    toggleAllLob,
    toggleAllJurisdiction,
    isMatchMyOrderChecked,
    handleSaveOrCancelPreferences,
  } = useUserPreference({
    selectedProduct,
    selectedProfile,
    contentTypeResults,
    lobResults,
    jurisdictionResults,
    responseData,
    jurisdictionEntitlements,
    lobEntitlements,
    setShouldHideUpdateBox: setshouldHideUpdateBox,
  });

  const [toggleAll, setToggleAll] = useState(true);
  const { data: session } = useSession();
  const productProfileKey = `${selectedProduct}-${selectedProfile}`;

  useEffect(() => {
    if (storeUserPreferenceHandlers) {
      storeUserPreferenceHandlers({
        handleSaveOrCancelPreferences,
      });
    }
  }, [handleSaveOrCancelPreferences, storeUserPreferenceHandlers]);

  const updatePreferences = (currentIsChecked: boolean) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!responseData) {
        setError("Missing response data");
        return;
      }

      const { totalModifications, modifiedKeys } = calculatePreferencesChanges({
        contentCheckedState,
        lobCheckedState,
        jurisdictionCheckedState,
        isChecked: currentIsChecked,
        initialEmailToggleMap,
        productProfileKey,
        responseData,
        productCodes: PRODUCT_CODES,
      });

      if (settotalCount) {
        settotalCount(totalModifications);
      }

      const hasWarnings = contentWarning || lobWarning || jurisdictionWarning;
      if (setshouldHideUpdateBox) {
        setshouldHideUpdateBox(totalModifications === 0 || hasWarnings);
      }

      if (onSelectionChange) {
        const isProductProfileSelected =
          selectedProduct?.trim() && selectedProfile?.trim();

        let finalSelections;
        if (isProductProfileSelected) {
          finalSelections = extractPreferenceData({
            keys: modifiedKeys,
            contentCheckedState,
            lobCheckedState,
            jurisdictionCheckedState,
            isChecked: currentIsChecked,
            isMatchMyOrderChecked,
            responseData,
            userEmail: session?.user?.email ?? undefined,
            userName: session?.user?.name ?? undefined,
            productCodes: PRODUCT_CODES,
          });
        } else {
          finalSelections = {
            IsNotificationAllowed: currentIsChecked,
            UserEmail: session?.user?.email,
            UserName: session?.user?.name,
            UserPreferences: [],
          };
        }

        onSelectionChange(finalSelections);
      }
    } catch (err) {
      console.error("Error updating preferences:", err);
      setError("Failed to update preferences. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const prevDataRef = useRef<any>(null);

  useEffect(() => {
    const currentKey = `${selectedProduct}-${selectedProfile}`;
    const currentData = {
      content: contentCheckedState[currentKey],
      lob: lobCheckedState[currentKey],
      jurisdiction: jurisdictionCheckedState[currentKey],
      isChecked,
    };
    const isSame =
      JSON.stringify(prevDataRef.current) === JSON.stringify(currentData);

    if (isSame) return;

    prevDataRef.current = currentData;

    updatePreferences(isChecked);
  }, [
    contentCheckedState,
    lobCheckedState,
    jurisdictionCheckedState,
    isChecked,
    selectedProduct,
    selectedProfile,
  ]);

  if (!selectedProduct || !selectedProfile) {
    return null;
  }

  const handleContentChange = (key: string) => handleChange(key, "content");
  const handleLobChange = (key: string) => handleChange(key, "lob");
  const handleJurisdictionChange = (key: string) =>
    handleChange(key, "jurisdiction");

  const handleContentAllToggle = () => handleAllToggle("content");
  const handleLobAllToggle = () => handleAllToggle("lob");
  const handleJurisdictionAllToggle = () => handleAllToggle("jurisdiction");

  return (
    <div
      data-testid="checkbox-form"
      id="selected-product-container"
      className="selected-product-container"
      style={{ display: "block" }}
    >
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner">Loading...</div>
        </div>
      )}

      {error && (
        <div className="error-message" role="alert">
          {error}
          <button
            className="close-error"
            onClick={() => setError(null)}
            aria-label="Close error message"
          >
            ×
          </button>
        </div>
      )}

      <div id="selected-product" className="selected-product">
        <div className="selected-product-wrapper">
          <div className="selected-product-header">
            <div className="notification-toggle">
              <h4
                id="selected-product-title"
                className="selected-product-title"
              >
                {selectedProduct}
              </h4>
              <div className="toggle-container">
                <label className="switch">
                  <input
                    id="content-toggle"
                    type="checkbox"
                    data-testid="content-toggle"
                    className="content-toggle"
                    checked={toggleAll}
                    onChange={() => setToggleAll(!toggleAll)}
                  />
                  <div className="slider round"></div>
                  <span id="toggle-text" className="toggle-text">
                    {toggleAll ? "On" : "Off"}
                  </span>
                </label>
              </div>
            </div>
            <p className="notification-checkbox">
              <label
                className="email-notif-checkbox"
                aria-label="Email Notification"
              >
                <input
                  data-testid="email-notification-checkbox"
                  type="checkbox"
                  checked={isMatchMyOrderChecked}
                  onChange={handleMatchMyOrder}
                  disabled={!toggleAll}
                />
                <span className="checkmark"></span>&nbsp;
              </label>
              <span className="checkbox-desc desc-text checkbox-text">
                Match My Order (ISO will automatically update your alert
                subscription when your company's order is modified)
              </span>
            </p>
          </div>
          {/* Content Type Section */}
          {selectedProduct !== PRODUCT_CODES.SFH && (
            <>
              <CheckboxSection
                title="Content Type"
                section="content"
                results={contentTypeResults}
                checkedState={contentCheckedState}
                selectedProduct={selectedProduct}
                selectedProfile={selectedProfile}
                handleChange={handleContentChange}
                handleAllToggle={handleContentAllToggle}
                toogleAllContent={toggleAllContent}
                toogleAll={toggleAll}
                warning={contentWarning}
              />
              <CheckboxSection
                title="Line of Business"
                section="lob"
                results={lobResults}
                selectedProduct={selectedProduct}
                selectedProfile={selectedProfile}
                checkedState={lobCheckedState}
                handleChange={handleLobChange}
                handleAllToggle={handleLobAllToggle}
                toogleAllContent={toggleAllLob}
                toogleAll={toggleAll}
                warning={lobWarning}
                lobEntitlements={lobEntitlements}
              />
            </>
          )}
          {/* Jurisdiction Section */}
          <CheckboxSection
            title="Jurisdiction"
            section="jurisdiction"
            results={jurisdictionResults}
            checkedState={jurisdictionCheckedState}
            selectedProduct={selectedProduct}
            selectedProfile={selectedProfile}
            handleChange={handleJurisdictionChange}
            handleAllToggle={handleJurisdictionAllToggle}
            toogleAllContent={toggleAllJurisdiction}
            toogleAll={toggleAll}
            warning={jurisdictionWarning}
            jurisEntitlementList={jurisdictionEntitlements}
          />
        </div>
      </div>
    </div>
  );
};

export default CheckboxForm;
