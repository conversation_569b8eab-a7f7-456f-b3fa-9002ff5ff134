import React from "react";

interface CheckboxSectionProps {
  title: string;
  section: "content" | "lob" | "jurisdiction";
  results: any[];
  checkedState: Record<string, any>;
  handleChange: Function;
  handleAllToggle: Function;
  selectedProduct: string;
  selectedProfile: string;
  toogleAll: boolean;
  lobEntitlements?: string[];
  jurisEntitlementList?: string[];
  toogleAllContent: boolean;
  warning: boolean;
}

const CheckboxSection: React.FC<CheckboxSectionProps> = ({
  title,
  section,
  results,
  checkedState,
  handleChange,
  selectedProduct,
  selectedProfile,
  handleAllToggle,
  lobEntitlements,
  jurisEntitlementList,
  toogleAll,
  toogleAllContent,
  warning,
}) => {
  return (
    <form className="options">
      <h5 className="label-text">{title}</h5>
      <label className="main-label custom-checkbox">
        <input
          type="checkbox"
          className={
            section === "content"
              ? "content-checkbox"
              : `all-${section}-checkbox`
          }
          id={
            section === "content"
              ? "all-content-checkbox"
              : `all-${section}-checkbox`
          }
          checked={toogleAllContent}
          onChange={() => handleAllToggle(!toogleAllContent)}
          disabled={
            !toogleAll ||
            (section === "lob" ? lobEntitlements?.length === 0 : false) ||
            (section === "jurisdiction" &&
            selectedProduct.toLowerCase().trim() ===
              "state filing handbook & forms"
              ? jurisEntitlementList?.length === 0
              : false)
          }
        />
        <span className="checkmark"></span>&nbsp; All {title}
      </label>

      {section === "content" ? (
        <ul>
          {results.map((item: any) => {
            const key =
              item?.code?.value ||
              item?.fields?.find((field: any) => field.name === "Key")?.value;
            const label =
              item?.displayLabel?.value ||
              item?.fields?.find((field: any) => field.name === "Phrase")
                ?.value;
            const isChecked =
              checkedState?.[`${selectedProduct}-${selectedProfile}`]?.[key] ||
              false;

            return (
              key &&
              label && (
                <li key={key}>
                  <label className="custom-checkbox">
                    <input
                      type="checkbox"
                      checked={isChecked}
                      onChange={() => handleChange(key, section)}
                      disabled={!toogleAll}
                    />
                    <span className="checkmark"></span>&nbsp;
                    {label}
                  </label>
                </li>
              )
            );
          })}
        </ul>
      ) : (
        results.map((item: any) => {
          const key =
            item?.code?.value ||
            item?.fields?.find((field: any) => field.name === "Key")?.value;
          const label =
            item?.displayLabel?.value ||
            item?.fields?.find((field: any) => field.name === "Phrase")?.value;

          const isChecked =
            checkedState?.[`${selectedProduct}-${selectedProfile}`]?.[key] ||
            false;

          const normalizedKey = (key || "").toUpperCase().trim();

          const isDisabled =
            (section === "lob"
              ? !(lobEntitlements ?? []).some(
                  (e) => (e || "").toUpperCase().trim() === normalizedKey
                )
              : !toogleAll) ||
            (section === "jurisdiction" &&
            selectedProduct.toLowerCase().trim() ===
              "state filing handbook & forms"
              ? !(jurisEntitlementList ?? []).some(
                  (j) => (j || "").toUpperCase().trim() === normalizedKey
                )
              : !toogleAll);

          return (
            key &&
            label && (
              <label key={key} className="custom-checkbox">
                <input
                  type="checkbox"
                  className={`all-${section}-checkbox`}
                  checked={isChecked}
                  onChange={() => handleChange(key, section)}
                  disabled={isDisabled}
                />
                <span className="checkmark"></span>&nbsp;
                {label}
              </label>
            )
          );
        })
      )}

      {warning && (
        <div className="warning-message" id={`${section}-warning`}>
          <span className="material-icons">error</span>&nbsp; You must select at
          least one {title}.
        </div>
      )}
    </form>
  );
};

export default CheckboxSection;
