import React, { useContext, useState, useEffect } from "react";
import { AuthContext } from "src/context/authContext";

//Convert into Title Case
function convertToTitleCase(input: string): string {
  return input
    .split(" ")
    .map((word) => {
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(" ");
}

// Add basic service line product  here
const basicServiceLineProducts: any = [
  "LOB Hubs",
  "Actuarial Hub",
  "Executive Insights",
  "Insights",
  "Panels",
];

//If product name is not equal to Features then add mapping here
const productMapping: any = {
  "Filing Intelligence": "Filing Intelligence",
  "Legislative Monitoring": "Legislative Monitoring",
  "Premium Audit Advisory Service": "PAAS",
  "State Filing Handbook & Forms": "STATE FILING HANDBOOK & FORMS",
};

export const checkProductAvailability: any = (product: string) => {
  return productMapping?.[product] || product;
};

interface NotificationPreferencesDropdownProps {
  selectedProduct: string;
  setSelectedProduct: (value: string) => void;
  selectedProfile: string;
  setSelectedProfile: (value: string) => void;
  [key: string]: any;
}

const NotificationPreferencesDropdown: React.FC<
  NotificationPreferencesDropdownProps
> = ({
  selectedProduct,
  setSelectedProduct,
  selectedProfile,
  setSelectedProfile,
  ...props
}) => {
  const { userProductProfilesList = [] } = useContext(AuthContext) || {};
  const [availableProducts, setAvailableProducts] = useState<any[]>([]);
  const [availableProfiles, setAvailableProfiles] = useState<any[]>([]);
  const fields: any = props?.fields?.data;
  const products: any = React.useMemo(
    () => fields?.PreferencesData?.Product?.Items || [],
    [fields]
  );

  // Filter and map products dynamically based on visibility config
  useEffect(() => {
    const allowedProducts = userProductProfilesList
      ?.flatMap((item: any) => {
        // Check if the item is "Basic Line Service"
        if (item?.feature?.toLowerCase().includes("basic line service")) {
          // Add all products with serviceLine "Basic" and isVisible true
          return products
            ?.filter((productItem: any) => {
              return basicServiceLineProducts?.some(
                (productName: string) =>
                  productName?.toLowerCase() ===
                  productItem?.Name?.value.toLowerCase()
              );
            })
            .map((product: any) => ({
              value: product?.Name?.value?.toLowerCase(),
              label: convertToTitleCase(product?.Name?.value),
            }));
        }
        // Otherwise, find the product in config
        const product = products.find((productItem: any) => {
          const product = checkProductAvailability(productItem?.Name?.value);
          return product?.toLocaleLowerCase() === item?.feature?.toLowerCase();
        });
        // Include the product if it's found and visible
        if (product) {
          return {
            value: item.feature.toLowerCase(),
            label: convertToTitleCase(item.feature),
          };
        }

        // If no match, return an empty array
        return [];
      })
      .sort((a: any, b: any) => a?.label?.localeCompare(b?.label)); // Sort alphabetically

    setAvailableProducts(allowedProducts);
  }, [userProductProfilesList, products]);

  useEffect(() => {
    const selectedProductInList: any = products?.find((product: any) => {
      const productItem = checkProductAvailability(product?.Name?.value);
      return productItem.toLowerCase() === selectedProduct;
    });
    // Check if the selected product is part of the "Basic" service line
    if (
      basicServiceLineProducts?.some(
        (productName: string) =>
          productName?.toLowerCase() ===
          selectedProductInList?.Name?.value.toLowerCase()
      )
    ) {
      // Handle "Basic Line Service" for all Basic service line products
      const basicLineServiceFeature = userProductProfilesList?.find(
        (item: any) => item?.feature?.toLowerCase() === "basic line service"
      );

      if (basicLineServiceFeature) {
        if (basicLineServiceFeature?.customers?.length === 1) {
          setSelectedProfile(
            basicLineServiceFeature?.customers[0]?.customerNumber
          );
          setAvailableProfiles([
            {
              value: basicLineServiceFeature?.customers[0]?.customerNumber,
              label: convertToTitleCase(
                basicLineServiceFeature?.customers[0]?.customerName
              ),
            },
          ]);
        } else {
          setAvailableProfiles(
            basicLineServiceFeature?.customers
              ?.map((customer: any) => ({
                value: customer?.customerNumber,
                label: convertToTitleCase(customer?.customerName),
              }))
              .sort((a: any, b: any) => a?.label?.localeCompare(b?.label))
          );
        }
      } else {
        // If "Basic Line Service" not found, clear profiles
        setAvailableProfiles([]);
      }
    } else {
      // For non-Basic products, directly map their profiles
      const selectedFeature = userProductProfilesList?.find(
        (item: any) => item?.feature?.toLowerCase() === selectedProduct
      );

      if (selectedFeature) {
        if (selectedFeature?.customers?.length === 1) {
          setSelectedProfile(selectedFeature?.customers[0]?.customerNumber);
          // setSelectedProfile("no-profile");
          setAvailableProfiles([
            {
              value: selectedFeature?.customers[0]?.customerNumber,
              label: convertToTitleCase(
                selectedFeature?.customers[0]?.customerName
              ),
            },
          ]);
        } else {
          setAvailableProfiles(
            selectedFeature?.customers
              ?.map((customer: any) => ({
                value: customer?.customerNumber,
                label: convertToTitleCase(customer?.customerName),
              }))
              ?.sort((a: any, b: any) => a?.label?.localeCompare(b?.label))
          );
        }
      } else {
        // If no match is found, clear profiles
        setAvailableProfiles([]);
      }
    }
  }, [selectedProduct, userProductProfilesList, products, setSelectedProfile]);

  // Handle product selection
  const handleProductChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event?.target?.value;
    setSelectedProduct(selectedValue);
    setSelectedProfile(""); // Reset profile selection
  };

  // Handle profile selection
  const handleProfileChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedProfile(event?.target?.value);
  };

  return (
    <div className="notification-product-selection">
      <div className="notification-product-selection-wrapper">
        {/* Product Selection Dropdown */}
        <div className="notification-select-dropdown">
          <label
            htmlFor="product-select"
            className="notification-label label-text"
            data-testid="product-select-label" // Added test ID
          >
            {fields?.PreferencesData?.ChooseProduct?.title}
          </label>
          <select
            className="notification-select"
            id="product-select"
            value={selectedProduct}
            onChange={handleProductChange}
            data-testid="product-select" // Added test ID
          >
            <option value="">Select an Option</option>
            {availableProducts?.map((product) => (
              <option key={product?.value} value={product?.value}>
                {product?.label}
              </option>
            ))}
          </select>
        </div>

        {/* Profile Selection Dropdown */}
        <div
          id="profile-dropdown"
          className={
            selectedProduct && availableProfiles?.length > 0
              ? ""
              : "profile-dropdown"
          }
        >
          <div className="notification-select-dropdown">
            <label
              htmlFor="profile-select"
              className="notification-label label-text"
              data-testid="profile-select-label"
            >
              {fields?.PreferencesData?.ChooseProfile?.title}
            </label>

            <select
              className="notification-select"
              id="profile-select"
              value={selectedProfile}
              onChange={handleProfileChange}
              disabled={!availableProfiles?.length}
              data-testid="profile-select"
            >
              {availableProfiles.length > 1 && (
                <option value="">Select an Option</option>
              )}
              {availableProfiles?.map((profile) => (
                <option key={profile?.value} value={profile?.value}>
                  {profile?.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationPreferencesDropdown;
