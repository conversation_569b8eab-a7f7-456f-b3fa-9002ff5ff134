import { savePreferences, fetchPreferences } from "../NotificationService";
import { toast, Slide } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";
import { useBackTopTopVal } from "src/hooks/sfh/useBackToTop";
import { capitalizeWords } from "./Utils/StringUtils";

const toastConfig: any = {
  position: "bottom-center",
  autoClose: 6000,
  hideProgressBar: true,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  theme: "light",
  transition: Slide,
};

interface UpdatePreferencesProps {
  selections: any;
  selectedProduct: string;
  selectedProfile: string;
  totalCount: number;
  settotalCount: (count: number) => void;
  setIsUpdating: (isUpdating: boolean) => void;
  shouldHideUpdateBox: boolean;
  setresponseData: (data: any) => void;
  handleCancelNotification: () => void;
  setInitialEmailToggleMap: any;
  onUpdateSuccess: () => void;
}

const UpdatePreferences = ({
  selections,
  selectedProduct,
  selectedProfile,
  totalCount,
  settotalCount,
  setIsUpdating,
  shouldHideUpdateBox,
  setresponseData,
  setInitialEmailToggleMap,
  handleCancelNotification,
  onUpdateSuccess,
}: UpdatePreferencesProps): React.JSX.Element => {
  const [, footerVisible] = useBackTopTopVal();

  const handleUpdatePreferences = async () => {
    setIsUpdating(true);
    try {
      const response = await savePreferences(selections);

      if (response) {
        const freshData = await fetchPreferences(
          selections.UserEmail,
          capitalizeWords(selectedProduct),
          capitalizeWords(selectedProfile)
          // selections.UserPreferences[0]?.NotificationPreferences[0]?.CustomerNumber
        );

        // Update initialEmailToggleMap
        const productProfileKey = `${selections.UserPreferences[0]?.ProductName}-${selections.UserPreferences[0]?.NotificationPreferences[0]?.CustomerNumber}`;
        setInitialEmailToggleMap((prev: any) => ({
          ...prev,
          [productProfileKey]: freshData?.IsNotificationAllowed ?? false,
        }));

        setresponseData(freshData);
        settotalCount(0);

        toast.success(
          <ToastMessage
            type="success"
            title="Success!"
            description="Your Notifications settings were Saved"
          />,
          toastConfig
        );

        onUpdateSuccess();
        handleCancelNotification();
      } else {
        toast.error(
          <ToastMessage
            type="error"
            title="Error"
            description="Failed to update preferences. Please try again."
          />,
          toastConfig
        );
      }
    } catch (error) {
      console.error("Error updating preferences:", error);
      toast.error(
        <ToastMessage
          type="error"
          title="Error"
          description="Failed to update preferences. Please try again."
        />,
        toastConfig
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      <div className="notifications"></div>
      {!shouldHideUpdateBox && totalCount > 0 && (
        <div>
          <div
            className={
              `${
                footerVisible
                  ? "preferences-above-footer"
                  : "preferences-bottom"
              }` + " preferences-footer"
            }
          >
            <div className="btn-container">
              <button
                className="tertiary cancel"
                onClick={handleCancelNotification}
              >
                Cancel
              </button>
              <button
                className="primary save"
                onClick={handleUpdatePreferences}
              >
                Update Preferences
              </button>
              <p className="setting-txt">
                You have changed {totalCount} settings.
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UpdatePreferences;
