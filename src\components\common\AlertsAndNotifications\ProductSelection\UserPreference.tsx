import { useState, useMemo } from "react";
import {
  SectionType,
  UserPreferenceProps,
  CheckedStateMap,
} from "./Utils/PreferanceInterface";
import { PRODUCT_CODES } from "./Utils/StringUtils";

const SECTION_STATIC_CONFIG: Record<
  SectionType,
  {
    name: SectionType;
    requiresEntitlements:
      | boolean
      | ((isStateFilingHandbook: boolean) => boolean);
    isStateFilingHandbookDisabled: (isStateFilingHandbook: boolean) => boolean;
    preferenceKey: string;
  }
> = {
  content: {
    name: "content",
    requiresEntitlements: false,
    isStateFilingHandbookDisabled: (isStateFilingHandbook) =>
      isStateFilingHandbook,
    preferenceKey: "ContentType",
  },
  lob: {
    name: "lob",
    requiresEntitlements: true,
    isStateFilingHandbookDisabled: (isStateFilingHandbook) =>
      isStateFilingHandbook,
    preferenceKey: "Lob",
  },
  jurisdiction: {
    name: "jurisdiction",
    requiresEntitlements: (isStateFilingHandbook) => isStateFilingHandbook,
    isStateFilingHandbookDisabled: () => false,
    preferenceKey: "Jurisdiction",
  },
};

export default function useUserPreference({
  selectedProduct,
  selectedProfile,
  contentTypeResults,
  lobResults,
  jurisdictionResults,
  responseData,
  jurisdictionEntitlements,
  lobEntitlements,
  setShouldHideUpdateBox,
}: UserPreferenceProps) {
  const [contentCheckedState, setContentCheckedState] =
    useState<CheckedStateMap>({});
  const [lobCheckedState, setLobCheckedState] = useState<CheckedStateMap>({});
  const [jurisdictionCheckedState, setJurisdictionCheckedState] =
    useState<CheckedStateMap>({});

  const [contentWarning, setContentWarning] = useState(false);
  const [lobWarning, setLobWarning] = useState(false);
  const [jurisdictionWarning, setJurisdictionWarning] = useState(false);

  const [changesCount, setChangesCount] = useState(0);
  const [toggleAllContent, setToggleAllContent] = useState(false);
  const [toggleAllLob, setToggleAllLob] = useState(false);
  const [toggleAllJurisdiction, setToggleAllJurisdiction] = useState(false);
  const [isMatchMyOrderChecked, setIsMatchMyOrderChecked] = useState(false);
  const [modifiedKeys, setModifiedKeys] = useState<Set<string>>(new Set());
  const [dirtyKeys, setDirtyKeys] = useState(new Set()); // <-- Track unsaved changes

  // Compute current key
  const key = `${selectedProduct}-${selectedProfile}`;
  const isStateFilingHandbook =
    selectedProduct.toLowerCase() === PRODUCT_CODES.SFH;

  // Gets state and setter for a section
  function getSectionState(section: SectionType) {
    switch (section) {
      case "content":
        return {
          checkedState: contentCheckedState,
          setCheckedState: setContentCheckedState,
          toggleAll: toggleAllContent,
          setToggleAll: setToggleAllContent,
          warning: contentWarning,
          setWarning: setContentWarning,
          entitlements: [] as string[],
        };
      case "lob":
        return {
          checkedState: lobCheckedState,
          setCheckedState: setLobCheckedState,
          toggleAll: toggleAllLob,
          setToggleAll: setToggleAllLob,
          warning: lobWarning,
          setWarning: setLobWarning,
          entitlements: lobEntitlements,
        };
      case "jurisdiction":
        return {
          checkedState: jurisdictionCheckedState,
          setCheckedState: setJurisdictionCheckedState,
          toggleAll: toggleAllJurisdiction,
          setToggleAll: setToggleAllJurisdiction,
          warning: jurisdictionWarning,
          setWarning: setJurisdictionWarning,
          entitlements: jurisdictionEntitlements,
        };
    }
  }

  // Gets the items for a section
  function getSectionItems(section: SectionType) {
    switch (section) {
      case "content":
        return contentTypeResults;
      case "lob":
        return lobResults;
      case "jurisdiction":
        return jurisdictionResults;
    }
  }

  function getRequiresEntitlements(sectionType: SectionType): boolean {
    const requirement = SECTION_STATIC_CONFIG[sectionType].requiresEntitlements;
    return typeof requirement === "function"
      ? requirement(isStateFilingHandbook)
      : requirement;
  }

  function getIsDisabledForSFH(sectionType: SectionType): boolean {
    return SECTION_STATIC_CONFIG[sectionType].isStateFilingHandbookDisabled(
      isStateFilingHandbook
    );
  }

  function isCheckboxDisabled(
    sectionType: SectionType,
    checkboxKey: string
  ): boolean {
    if (!getRequiresEntitlements(sectionType)) {
      return false;
    }

    const normalizedKey = (checkboxKey || "").toUpperCase().trim();
    const entitlements = getSectionState(sectionType).entitlements;

    // Check if key exists in entitlements
    return !(entitlements || []).some(
      (e) => (e || "").toUpperCase().trim() === normalizedKey
    );
  }

  useMemo(() => {
    if (
      !selectedProduct ||
      !selectedProfile ||
      !responseData?.UserPreferences
    ) {
      return;
    }
    // added for dirty keys
    if (dirtyKeys.has(key)) {
      // If there are unsaved changes for this product/profile, don't overwrite them!
      updateWarningsAndToggleStates();
      return;
    }

    if (modifiedKeys.has(key)) {
      updateWarningsAndToggleStates(); // added for dirty keys
      return;
    }

    const productPref = responseData?.UserPreferences?.find(
      (p: any) => p.ProductName.toLowerCase() === selectedProduct.toLowerCase()
    );

    const profilePref = productPref?.NotificationPreferences?.find(
      (p: any) => p.CustomerNumber.toString() === selectedProfile.toString()
    );

    const matchMyOrder = profilePref?.IsMatchMyOrder === "true";
    const dataMissing = !responseData;

    function createStateMap(
      items: any[],
      defaultValue: boolean,
      prefKey?: string
    ) {
      return items.reduce((acc: any, item: any) => {
        const itemKey =
          item.code?.value ||
          item.fields?.find((field: any) => field.name === "Key")?.value;

        if (itemKey) {
          if (prefKey && profilePref) {
            acc[itemKey] =
              profilePref.ProfilePreferences?.[prefKey]?.includes(itemKey);
          } else {
            acc[itemKey] = defaultValue;
          }
        }
        return acc;
      }, {});
    }

    // Initialize each section
    Object.keys(SECTION_STATIC_CONFIG).forEach((sectionType) => {
      const section = sectionType as SectionType;
      const items = getSectionItems(section);
      const { setCheckedState } = getSectionState(section);
      const preferenceKey = SECTION_STATIC_CONFIG[section].preferenceKey;

      let defaultValue = false;
      if (matchMyOrder || dataMissing) {
        defaultValue = true;
      }

      // For SFH product, content and lob are always false
      if (getIsDisabledForSFH(section)) {
        defaultValue = false;
      }

      const stateMap = createStateMap(
        items,
        defaultValue,
        !matchMyOrder && !dataMissing ? preferenceKey : undefined
      );

      setCheckedState((prev) => ({
        ...prev,
        [key]: stateMap,
      }));
    });
    updateWarningsAndToggleStates(); //added for dirty keys
  }, [
    selectedProduct,
    selectedProfile,
    responseData,
    modifiedKeys,
    contentTypeResults,
    lobResults,
    jurisdictionResults,
    key,
    dirtyKeys, // <-- Add dirtyKeys to dependencies
  ]);

  //helper function to calculate warning and toggle states
  // This function is used to calculate warning and toggle states for each section
  function calculateWarningAndToggle({
    sectionState,
    sectionType,
    entitlements,
    getIsDisabledForSFH,
    getRequiresEntitlements,
    isCheckboxDisabled,
  }: {
    sectionState: any;
    sectionType: any;
    entitlements: any;
    getIsDisabledForSFH: any;
    getRequiresEntitlements: any;
    isCheckboxDisabled: any;
  }) {
    const hasWarning =
      Object.values(sectionState).length > 0 &&
      !Object.values(sectionState).some(Boolean);

    const shouldSetWarning = getIsDisabledForSFH(sectionType)
      ? false
      : entitlements.length === 0 && getRequiresEntitlements(sectionType)
      ? false
      : hasWarning;

    let hasEnabledCheckboxes = false;
    let allEnabledAreChecked = true;

    Object.keys(sectionState).forEach((checkboxKey) => {
      const isDisabled = isCheckboxDisabled(sectionType, checkboxKey);

      if (!isDisabled) {
        hasEnabledCheckboxes = true;
        if (!sectionState[checkboxKey]) {
          allEnabledAreChecked = false;
        }
      }
    });

    const toggleAll = hasEnabledCheckboxes && allEnabledAreChecked;
    return { shouldSetWarning, toggleAll };
  }

  function updateWarningsAndToggleStates() {
    if (!key) return;

    // Process each section
    Object.keys(SECTION_STATIC_CONFIG).forEach((sectionType) => {
      const section = sectionType as SectionType;
      const { checkedState, setWarning, setToggleAll, entitlements } =
        getSectionState(section);

      const sectionState = checkedState[key] || {};
      // Using the helper function to calculate warning and toggle states
      const { shouldSetWarning, toggleAll } = calculateWarningAndToggle({
        sectionState,
        sectionType: section,
        entitlements,
        getIsDisabledForSFH,
        getRequiresEntitlements,
        isCheckboxDisabled,
      });

      setWarning(shouldSetWarning);
      setToggleAll(toggleAll);
    });

    updateMatchMyOrderState();
    updateUpdateBoxVisibility();
  }

  function updateMatchMyOrderState() {
    if (!key) return;

    // Helper to check if any enabled checkboxes are unchecked
    function hasAnyUncheckedEnabled(section: SectionType): boolean {
      const { checkedState } = getSectionState(section);
      const sectionState = checkedState[key] || {};

      return Object.keys(sectionState).some((checkboxKey) => {
        const isDisabled = isCheckboxDisabled(section, checkboxKey);
        return !isDisabled && !sectionState[checkboxKey];
      });
    }

    // Determine if match my order should be checked
    let isAnyUnchecked = false;
    if (isStateFilingHandbook) {
      isAnyUnchecked = hasAnyUncheckedEnabled("jurisdiction");
    } else {
      isAnyUnchecked =
        hasAnyUncheckedEnabled("content") ||
        hasAnyUncheckedEnabled("lob") ||
        hasAnyUncheckedEnabled("jurisdiction");
    }

    setIsMatchMyOrderChecked(!isAnyUnchecked);
  }

  function updateUpdateBoxVisibility() {
    const hasWarnings = contentWarning || lobWarning || jurisdictionWarning;
    const shouldHideUpdateBox = changesCount === 0 || hasWarnings;
    setShouldHideUpdateBox(shouldHideUpdateBox);
  }

  useMemo(() => {
    updateWarningsAndToggleStates();
  }, [
    contentCheckedState,
    lobCheckedState,
    jurisdictionCheckedState,
    changesCount,
  ]);

  function handleChange(itemKey: string, sectionType: SectionType) {
    if (!key) return;
    setDirtyKeys((prev) => new Set(prev).add(key)); // <--- Add this for dirty tracking

    const { checkedState, setCheckedState } = getSectionState(sectionType);
    const currentValue = checkedState[key]?.[itemKey];

    setCheckedState((prevState) => ({
      ...prevState,
      [key]: {
        ...(prevState[key] || {}),
        [itemKey]: !currentValue,
      },
    }));

    trackProductProfileModified();
    setChangesCount((prevCount) => prevCount + 1);
  }

  //Helper function to update the checked state of a section
  function updateSectionCheckedState({
    setCheckedState,
    checkedState,
    key,
    sectionType,
    value,
    isCheckboxDisabled,
  }: {
    setCheckedState: any;
    checkedState: any;
    key: string;
    sectionType: any;
    value: boolean;
    isCheckboxDisabled: any;
  }) {
    const sectionState = checkedState[key] || {};
    const updatedSectionState = Object.keys(sectionState).reduce(
      (acc: any, checkboxKey) => {
        const isDisabled = isCheckboxDisabled(sectionType, checkboxKey);
        acc[checkboxKey] = isDisabled ? false : value;
        return acc;
      },
      {}
    );

    setCheckedState((prevState: any) => ({
      ...prevState,
      [key]: updatedSectionState,
    }));
  }

  //handleAllToggle
  function handleAllToggle(sectionType: SectionType) {
    if (!key) return;
    setDirtyKeys((prev) => new Set(prev).add(key)); // <--- Add this for dirty tracking

    const { checkedState, setCheckedState, toggleAll } =
      getSectionState(sectionType);
    const newState = !toggleAll;

    // helper code
    updateSectionCheckedState({
      setCheckedState,
      checkedState,
      key,
      sectionType,
      value: newState,
      isCheckboxDisabled,
    });

    trackProductProfileModified();

    // If toggling on, also set match my order
    if (newState) {
      setIsMatchMyOrderChecked(newState);
    }

    setChangesCount((prevCount) => prevCount + 1);
  }

  //handleMatchMyOrder
  function handleMatchMyOrder() {
    if (!key) return;
    setDirtyKeys((prev) => new Set(prev).add(key)); // <--- Add this for dirty tracking

    const newState = !isMatchMyOrderChecked;

    // Process each section
    Object.keys(SECTION_STATIC_CONFIG).forEach((sectionTypeStr) => {
      const sectionType = sectionTypeStr as SectionType;
      const { checkedState, setCheckedState } = getSectionState(sectionType);

      // Determine state based on product rules
      const finalState = getIsDisabledForSFH(sectionType) ? false : newState;

      //helper code
      updateSectionCheckedState({
        setCheckedState,
        checkedState,
        key,
        sectionType,
        value: finalState,
        isCheckboxDisabled,
      });
    });

    trackProductProfileModified();

    setIsMatchMyOrderChecked(newState);
    setChangesCount((prevCount) => prevCount + 1);
  }

  function trackProductProfileModified() {
    setModifiedKeys((prevKeys) => {
      const newKeys = new Set(prevKeys);
      newKeys.add(key);
      return newKeys;
    });
  }

  function handleSaveOrCancelPreferences() {
    setModifiedKeys(new Set());
    // Add this below: dirtyKeys is used to track unsaved changes
    setDirtyKeys((prev) => {
      const newSet = new Set(prev);
      newSet.delete(key); // Remove current key from dirty set
      return newSet;
    });
  }

  return {
    contentCheckedState,
    lobCheckedState,
    jurisdictionCheckedState,
    changesCount,
    setContentCheckedState,
    setLobCheckedState,
    setJurisdictionCheckedState,
    handleMatchMyOrder,
    handleChange,
    handleAllToggle,
    setIsMatchMyOrderChecked,
    contentWarning,
    lobWarning,
    jurisdictionWarning,
    toggleAllContent,
    toggleAllLob,
    toggleAllJurisdiction,
    isMatchMyOrderChecked,
    handleSaveOrCancelPreferences,
  };
}
