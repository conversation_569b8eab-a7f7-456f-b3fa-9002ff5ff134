export type SectionType = "content" | "lob" | "jurisdiction";

export interface UserPreferenceProps {
  selectedProduct: string;
  selectedProfile: string;
  contentTypeResults: any[];
  lobResults: any[];
  jurisdictionResults: any[];
  responseData: any;
  jurisdictionEntitlements: string[];
  lobEntitlements: string[];
  setShouldHideUpdateBox: (value: boolean) => void;
}

export interface CheckedStateMap {
  [key: string]: Record<string, boolean>;
}

export interface PreferenceState {
  [productProfileKey: string]: {
    [item: string]: boolean;
  };
}

export interface UserPreference {
  ProductName: string;
  NotificationPreferences: {
    CustomerNumber: string;
    IsMatchMyOrder: string;
    ProfilePreferences: {
      ContentType?: string[];
      Lob?: string[];
      Jurisdiction: string[];
    };
  }[];
}

export interface ModificationResult {
  modifiedCount: number;
  modifiedKeys: string[];
}

export interface PreferenceResult {
  IsNotificationAllowed: boolean;
  UserEmail: string;
  UserName: string;
  UserPreferences: any[];
}

export interface ProfilePreferencesData {
  ContentType?: string[];
  Lob?: string[];
  Jurisdiction: string[];
}

export interface ProductCodes {
  SFH: string;
  [key: string]: string;
}

export interface CalculatePreferencesChangesParams {
  contentCheckedState: PreferenceState;
  lobCheckedState: PreferenceState;
  jurisdictionCheckedState: PreferenceState;
  isChecked: boolean;
  initialEmailToggleMap?: Record<string, boolean>;
  productProfileKey: string;
  responseData?: { UserPreferences: UserPreference[] };
  productCodes: ProductCodes;
}

export interface ProcessPreferenceKeyParams {
  key: string;
  contentCheckedState: PreferenceState;
  lobCheckedState: PreferenceState;
  jurisdictionCheckedState: PreferenceState;
  isMatchMyOrderChecked: boolean;
  productCodes: ProductCodes;
}

export interface ProcessPreferenceKeyResult {
  productId: string;
  profileId: string;
  profileData: {
    isMatchMyOrder: boolean;
    profilePreferences: ProfilePreferencesData;
  } | null;
}

export interface ExtractPreferenceDataParams {
  keys: string[];
  contentCheckedState: PreferenceState;
  lobCheckedState: PreferenceState;
  jurisdictionCheckedState: PreferenceState;
  isChecked: boolean;
  isMatchMyOrderChecked: boolean;
  responseData?: { UserPreferences: UserPreference[] };
  userEmail?: string;
  userName?: string;
  productCodes: ProductCodes;
}
