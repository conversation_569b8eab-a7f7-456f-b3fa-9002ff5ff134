import {
  ModificationResult,
  PreferenceState,
  UserPreference,
  ProductCodes,
  CalculatePreferencesChangesParams,
  ProcessPreferenceKeyParams,
  ProcessPreferenceKeyResult,
  ExtractPreferenceDataParams,
  PreferenceResult,
  ProfilePreferencesData,
} from "./PreferanceInterface";
import { capitalizeWords } from "./StringUtils";

export function checkModifications(
  stateData: PreferenceState | undefined,
  userPreferences: UserPreference[] | undefined,
  typeKey: keyof ProfilePreferencesData,
  productCodes: ProductCodes
): ModificationResult {
  if (!stateData) {
    return { modifiedCount: 0, modifiedKeys: [] };
  }

  let modifications = 0;
  const modifiedKeys = new Set<string>();
  const normalize = (str: string) => str?.trim().toLowerCase() || "";

  try {
    Object.keys(stateData).forEach((key: string) => {
      if (!key || !key.includes("-")) {
        return;
      }

      const [productId, profileId] = key.split("-");
      const product = userPreferences?.find(
        (p) => normalize(p.ProductName) === normalize(productId)
      );
      const profile = product?.NotificationPreferences?.find(
        (np) => np.CustomerNumber === profileId
      );

      if (
        productId === productCodes.SFH &&
        (typeKey === "ContentType" || typeKey === "Lob")
      ) {
        return;
      }

      const currentState = stateData[key];
      if (!currentState) return;

      if (!profile) {
        Object.values(currentState).forEach((checked) => {
          if (checked) {
            modifications++;
            modifiedKeys.add(key);
          }
        });
      } else if (
        profile.ProfilePreferences &&
        profile.IsMatchMyOrder === "true"
      ) {
        Object.values(currentState).forEach((checked) => {
          if (!checked) {
            modifications++;
            modifiedKeys.add(key);
          }
        });
      } else if (profile.ProfilePreferences) {
        const savedPrefsArray = profile.ProfilePreferences[typeKey] ?? [];
        const savedPrefsSet = new Set(savedPrefsArray);

        Object.entries(currentState).forEach(([item, checked]) => {
          const shouldBeChecked = savedPrefsSet.has(item);
          if (checked !== shouldBeChecked) {
            modifications++;
            modifiedKeys.add(key);
          }
        });
      }
    });
  } catch (error) {
    console.error(`Error in checkModifications for ${typeKey}:`, error);
    return { modifiedCount: 0, modifiedKeys: [] };
  }

  return {
    modifiedCount: modifications,
    modifiedKeys: Array.from(modifiedKeys),
  };
}

export function calculatePreferencesChanges(
  params: CalculatePreferencesChangesParams
): { totalModifications: number; modifiedKeys: string[] } {
  try {
    const {
      contentCheckedState,
      lobCheckedState,
      jurisdictionCheckedState,
      isChecked,
      initialEmailToggleMap,
      productProfileKey,
      responseData,
      productCodes,
    } = params;

    if (!productProfileKey || !responseData?.UserPreferences) {
      return { totalModifications: 0, modifiedKeys: [] };
    }

    const originalIsNotificationAllowed =
      initialEmailToggleMap?.[productProfileKey] ?? false;

    const contentModifications = checkModifications(
      contentCheckedState,
      responseData.UserPreferences,
      "ContentType",
      productCodes
    );

    const lobModifications = checkModifications(
      lobCheckedState,
      responseData.UserPreferences,
      "Lob",
      productCodes
    );

    const jurisdictionModifications = checkModifications(
      jurisdictionCheckedState,
      responseData.UserPreferences,
      "Jurisdiction",
      productCodes
    );

    let totalModifications =
      contentModifications.modifiedCount +
      lobModifications.modifiedCount +
      jurisdictionModifications.modifiedCount;

    // Count email toggle change as a modification
    if (isChecked !== originalIsNotificationAllowed) {
      totalModifications += 1;
    }

    // Combine all modified keys
    const modifiedKeys = [
      ...contentModifications.modifiedKeys,
      ...lobModifications.modifiedKeys,
      ...jurisdictionModifications.modifiedKeys,
    ];

    return {
      totalModifications,
      modifiedKeys: [...new Set(modifiedKeys)],
    };
  } catch (error) {
    console.error("Error in calculatePreferencesChanges:", error);
    return { totalModifications: 0, modifiedKeys: [] };
  }
}

export function processPreferenceKey(
  params: ProcessPreferenceKeyParams
): ProcessPreferenceKeyResult {
  try {
    const {
      key,
      contentCheckedState,
      lobCheckedState,
      jurisdictionCheckedState,
      isMatchMyOrderChecked,
      productCodes,
    } = params;

    if (!key || !key.includes("-")) {
      return { productId: "", profileId: "", profileData: null };
    }

    const [productId, profileId] = key.split("-");
    if (!productId || !profileId) {
      return { productId: "", profileId: "", profileData: null };
    }

    const getCheckedItems = (state: PreferenceState, key: string): string[] => {
      try {
        if (!state || !key || !state[key]) return [];
        return Object.keys(state[key]).filter((item) => state[key][item]);
      } catch (error) {
        console.error(`Error getting checked items for ${key}:`, error);
        return [];
      }
    };

    const contentTypes = getCheckedItems(contentCheckedState, key);
    const lobs = getCheckedItems(lobCheckedState, key);
    const jurisdictions = getCheckedItems(jurisdictionCheckedState, key);

    const areAllChecked = (state: PreferenceState, key: string): boolean => {
      try {
        if (!state || !key || !state[key]) return false;
        const values = Object.values(state[key]);
        return values.length > 0 && values.every(Boolean);
      } catch (error) {
        console.error(
          `Error checking if all items are checked for ${key}:`,
          error
        );
        return false;
      }
    };

    const allContentChecked = areAllChecked(contentCheckedState, key);
    const allLobChecked = areAllChecked(lobCheckedState, key);
    const allJurisdictionChecked = areAllChecked(jurisdictionCheckedState, key);

    const isSFH = productId === productCodes.SFH;
    const isMatchMyOrder = isMatchMyOrderChecked
      ? true
      : isSFH
      ? allJurisdictionChecked
      : allContentChecked && allLobChecked && allJurisdictionChecked;

    const profilePreferences: ProfilePreferencesData = {
      Jurisdiction: jurisdictions,
    };

    if (isMatchMyOrder) {
      profilePreferences.ContentType = contentTypes;
      profilePreferences.Lob = lobs;
    } else {
      if (isSFH) {
        profilePreferences.ContentType = [];
        profilePreferences.Lob = [];
      } else {
        profilePreferences.ContentType = contentTypes;
        profilePreferences.Lob = lobs;
      }
    }

    return {
      productId,
      profileId,
      profileData: {
        isMatchMyOrder,
        profilePreferences,
      },
    };
  } catch (error) {
    console.error("Error in processPreferenceKey:", error);
    return { productId: "", profileId: "", profileData: null };
  }
}

export function extractPreferenceData(
  params: ExtractPreferenceDataParams
): PreferenceResult {
  const {
    keys,
    contentCheckedState,
    lobCheckedState,
    jurisdictionCheckedState,
    isChecked,
    isMatchMyOrderChecked,
    responseData,
    userEmail,
    userName,
    productCodes,
  } = params;

  try {
    const result: PreferenceResult = {
      IsNotificationAllowed: isChecked,
      UserEmail: userEmail ?? "",
      UserName: userName ?? "",
      UserPreferences: [],
    };

    if (!keys || keys.length === 0) {
      result.UserPreferences = responseData?.UserPreferences || [];
      return result;
    }

    keys.forEach((key: string) => {
      const { productId, profileId, profileData } = processPreferenceKey({
        key,
        contentCheckedState,
        lobCheckedState,
        jurisdictionCheckedState,
        isMatchMyOrderChecked,
        productCodes,
      });

      if (!profileData) return;

      let product = result.UserPreferences.find(
        (p) => p.ProductName === capitalizeWords(productId)
      );

      if (!product) {
        product = {
          ProductName: capitalizeWords(productId),
          NotificationPreferences: [],
        };
        result.UserPreferences.push(product);
      }

      product.NotificationPreferences.push({
        CustomerNumber: profileId,
        UserPreferenceId: "",
        IsMatchMyOrder: profileData.isMatchMyOrder,
        ProfilePreferences: profileData.profilePreferences,
        isNotificationAllowed: isChecked,
      });
    });

    return result;
  } catch (error) {
    console.error("Error in extractPreferenceData:", error);
    return {
      IsNotificationAllowed: isChecked,
      UserEmail: userEmail ?? "",
      UserName: userName ?? "",
      UserPreferences: responseData?.UserPreferences || [],
    };
  }
}
