import jurisdictionJson from "../../../../../data/sfh/jurisdiction.json";

import { NORMALIZED_PRODUCT_MAP } from "./StringUtils";

export function getJurisdictionResults(): any[] {
  const values = jurisdictionJson?.[0]?.Values || [];
  return values
    .map((item: any) => ({
      code: { value: item.Key },
      displayLabel: { value: item.Name },
    }))
    .sort((a, b) => a.displayLabel.value.localeCompare(b.displayLabel.value));
}

const getNestedResults = (data: any, key: string): any[] =>
  data?.fields?.data?.[key]?.children?.results ?? [];

export function getContentTypeResults(data: any): any[] {
  return getNestedResults(data, "ContentType");
}

export function getLobResults(data: any): any[] {
  return getNestedResults(data, "LOB");
}

export function getJurisdictionEntitlements(
  selectedProduct: string,
  selectedProfile: string,
  userLicensesAlert: any
): string[] {
  const productKey =
    NORMALIZED_PRODUCT_MAP[selectedProduct.toLowerCase().trim()];
  if (productKey !== "SFH") return [];

  const match = userLicensesAlert?.SFHJurisdictionsByCustomer?.find(
    (entry: any) => String(entry.customerNumber) === String(selectedProfile)
  );

  return match?.customerSFHJurisdictions || [];
}

export function getLobEntitlements(
  selectedProfile: string,
  userLicensesAlert: any
): string[] {
  const match = userLicensesAlert?.UserLobParticipationByCustomer?.find(
    (entry: any) => entry.customerNumber === selectedProfile
  );

  return match?.customerLobParticipation || [];
}
