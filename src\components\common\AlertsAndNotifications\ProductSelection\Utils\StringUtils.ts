export const toTitleCase = (str: string): string => {
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const normalizeString = (str: string): string => {
  return str.trim().toLowerCase();
};

export const capitalizeWords = (str: string): string => {
  return str.replace(/\b\w/g, (char) => char.toUpperCase());
};

export const PRODUCT_CODES = {
  SFH: "state filing handbook & forms",
  LMON: "legislative monitoring",
  FORMS: "forms",
};

export const NORMALIZED_PRODUCT_MAP: Record<
  string,
  keyof typeof PRODUCT_CODES
> = {
  "state filing handbook & forms": "SFH",
  "legislative monitoring": "LMON",
  forms: "FORMS",
};
