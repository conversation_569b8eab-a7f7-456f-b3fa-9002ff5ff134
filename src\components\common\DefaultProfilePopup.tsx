import React, { useContext, useState, useCallback, useMemo } from "react";
import { AuthContext } from "src/context/authContext";
import { checkIsServiceLine } from "components/Platform/utils/profileUtils";
import { useRouter } from "next/router";
import Loader from "./Loader";
import Cookies from "js-cookie";

const DefaultProfilePopup = () => {
  const { userProductProfilesList = [], signinFlag } = useContext(AuthContext);
  const router = useRouter();
  const {
    product = "",
    visitPageUrl = "",
    pdfUrl = "",
  } = router.query as {
    product: string;
    visitPageUrl: string;
    pdfUrl?: string;
  };

  const [temporarySelectedProfile, setTemporarySelectedProfile] = useState<
    any | null
  >(null);
  const [savedProfile, setSavedProfile] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const feature = checkIsServiceLine(product) ? "Basic Line Service" : product;

  const profileList = useMemo(() => {
    return (
      userProductProfilesList.find(
        (profile: any) => profile.feature === feature
      )?.customers || []
    );
  }, [userProductProfilesList, feature]);

  const handleProfileSelect = useCallback((profile: any) => {
    setTemporarySelectedProfile(profile);
  }, []);

  const handleSaveAndNavigate = () => {
    if (!temporarySelectedProfile) return;
    setIsLoading(true);

    setSavedProfile(temporarySelectedProfile);

    const selectedProfiles = JSON.parse(
      localStorage.getItem("selectedProfiles") || "{}"
    );
    selectedProfiles[product] = { ...temporarySelectedProfile };

    localStorage.setItem("selectedProfiles", JSON.stringify(selectedProfiles));
    Cookies.set("selectedProfiles", JSON.stringify(selectedProfiles), {
      path: "/",
      sameSite: "strict",
      expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    });

    const visitedPages = JSON.parse(
      localStorage.getItem("visitedPages") || "{}"
    );
    if (!visitedPages[product]) {
      visitedPages[product] = true;
      localStorage.setItem("visitedPages", JSON.stringify(visitedPages));
    }

    router.replace(visitPageUrl);
  };

  if (!product || !visitPageUrl || !signinFlag || profileList.length < 2) {
    console.log("Redirection");
    router.push("/");
    return null;
  }

  return (
    <>
      {isLoading && (
        <div className="modal-container-loader">
          <Loader />
        </div>
      )}
      {!isLoading && (
        <div className="modal-container-switch-popup media-model">
          <div className="profile-modal">
            <div className="profile-content">
              <ul className="link-list">
                <li>
                  <h2 className="heading" data-testid="select-heading">
                    Select Profile
                  </h2>
                </li>
                <li>
                  <p className="description">
                    Your user ID is associated with multiple companies that
                    license this product. To proceed, select the company that
                    entitles you to product access.
                  </p>
                </li>
              </ul>

              <ul className="link-list scrollable-list">
                {profileList.map(({ customerName, customerNumber }: any) => {
                  const isSelected =
                    temporarySelectedProfile?.customerNumber === customerNumber;
                  const isSaved =
                    savedProfile?.customerNumber === customerNumber;

                  return (
                    <li
                      key={customerNumber}
                      className={isSelected ? "choosen-profile" : "profile"}
                    >
                      <button
                        onClick={() =>
                          handleProfileSelect({ customerName, customerNumber })
                        }
                        data-testid="profile-anchor"
                        className="profile-link"
                      >
                        {customerName}
                        {isSaved && " *Current Profile"}
                      </button>
                    </li>
                  );
                })}
              </ul>
            </div>

            <div className="call-to-action">
              <a
                className="primary"
                type="button"
                data-testid="select-profile-anc"
                href={pdfUrl}
                onClick={(e) => {
                  e.preventDefault();
                  handleSaveAndNavigate();
                }}
              >
                Select Profile
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DefaultProfilePopup;
