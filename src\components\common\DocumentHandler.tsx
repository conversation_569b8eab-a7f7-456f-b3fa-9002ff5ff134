import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";

const DocumentHandler = () => {
  const [docUrl, setDocUrl] = useState("");
  const [fileType, setFileType] = useState("");
  const router = useRouter();

  // Access the query parameters directly
  const { query } = router;
  let queryParam = query.url as string | undefined;
  const lastSegment = queryParam?.split("/").pop();

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    let documentUrl = urlParams?.get("url");
    if (documentUrl) {
      documentUrl = sanitizeUrl(documentUrl);
      const decodedUrl = decodeURIComponent(documentUrl);
      setDocUrl(decodedUrl);
      setFileType(getFileType(decodedUrl));
    }
  }, []);

  const sanitizeUrl = (relativePath: any) => {
    const sanitizedUrl = relativePath.replace(/^(["']|(["'])$)/g, "");
    return `${process.env.PUBLIC_URL}${sanitizedUrl}`;
  };

  const getFileType = (url: any) => {
    if (url.endsWith(".pdf")) return "pdf";
    if (url.endsWith(".xlsx") || url.endsWith(".xls")) return "excel";
    if (url.endsWith(".docx") || url.endsWith(".doc")) return "word";
    return "link";
  };

  useEffect(() => {
    if (docUrl && fileType) {
      if (fileType === "pdf") {
        // Redirect the current window to the PDF URL.
        window.location.href = docUrl;
      } else {
        // For other file types, trigger a download.
        const link = document.createElement("a") as any;
        link.href = docUrl;
        link.target = "_blank";
        link.download = docUrl.split("/").pop();
        link.click();
        router.push("/");
      }
    }
  }, [docUrl, fileType]);

  return (
    <section className="major-content background-lt-white rich-text-component reimagine">
      <div className="site">
        <div>
          <div>
            <div className="site flex-wrapper microsite roadmap">
              <div className="microsite-header">
                <section className="content-wrapper">
                  <div className="microsite-content padding-right">
                    <h2 className="no-margin-top">
                      Your <span className="highlight">document</span> is almost
                      ready!
                    </h2>
                    <div>
                      <p>Please wait, your document is being processed...</p>
                      <p>{lastSegment}</p>
                      {fileType === "pdf" ? (
                        <p>
                          We are preparing your PDF to open. Please be patient
                          as this may take a moment.
                        </p>
                      ) : fileType === "word" || fileType === "excel" ? (
                        <p>
                          We are preparing your {fileType} for download. Please
                          be patient as this may take a moment.
                        </p>
                      ) : (
                        <p>No document found or unsupported type</p>
                      )}
                      <p>
                        If your document does not download automatically after a
                        short delay, please click the link to start the download
                        manually,{" "}
                        <a href={sanitizeUrl(queryParam)}>click here</a>.
                      </p>
                    </div>
                    {/* <a href={sanitizeUrl(docUrl)} className="primary">
                      Learn More
                    </a> */}
                  </div>
                  <img
                    src="https://core.verisk.com/-/media/microsite/reimagine-home-whatIsRI.png"
                    alt="Reimagine Image"
                    className="microsite-img-width padding-left"
                  />
                </section>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DocumentHandler;
