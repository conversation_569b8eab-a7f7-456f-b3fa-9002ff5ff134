import {
  Text,
  Image,
  Field,
  Link,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";
import NextImage from "./NextImage";

type FooterProps = ComponentProps & {
  fields: {
    Copyright: Field<string>;
    FooterLeftImage: any;
    FooterLinks: any;
    FooterRightImage: any;
  };
  isEEFlag: any;
};
const Footer = (props: any): React.JSX.Element => {
  const fields = props?.fields;
  const isEEFlag = props?.isEEFlag;
  return (
    <footer>
      <div className="site flex-wrapper">
        <a
          href="https://www.verisk.com/"
          target="_blank"
          className="logo"
          data-region="Footer"
          data-title="Verisk Footer Logo"
          data-interaction="click"
          data-context=""
        >
          {isEEFlag === true ? (
            <Image field={props?.fields?.FooterRightImage} />
          ) : (
            <NextImage
              src={fields?.FooterLeftImage?.value?.src}
              alt={fields?.FooterLeftImage?.value?.alt || "Image not available"}
              width={169}
              height={48}
            />
          )}
        </a>
        <div className="copyright-footer-links">
          <Text field={props?.fields?.Copyright} tag="small" />
          {/* <small>Copyright © 2023 Insurance Services Office, Inc. All rights reserved.</small> */}
          <small>
            {fields?.FooterLinks?.map((link: any, id: number) => {
              return (
                <Link
                  key={id}
                  rel="noopener noreferrer"
                  target="_blank"
                  field={link?.fields?.Link}
                  data-region="Footer"
                  data-title={link?.fields?.Link?.value?.text}
                  data-interaction="click"
                  data-context=""
                />
              );
            })}
            {/* OneTrust Cookies Settings button start */}
            <a
              className="optanon-toggle-display"
              href="#onetrust-pc-btn-handler"
            >
              Cookie Preferences
            </a>
            {/* OneTrust Cookies Settings button end */}
          </small>
        </div>
        <div>
          {isEEFlag === true ? (
            <Image field={fields?.FooterRightImage} />
          ) : (
            <NextImage
              src={fields?.FooterRightImage?.value?.src}
              alt={
                fields?.FooterRightImage?.value?.alt || "Image not available"
              }
              width={140}
              height={36}
            />
          )}
          <strong>
            VERISK ANALYTICS<sup>®</sup>
          </strong>
        </div>
      </div>
    </footer>
  );
};

export default withDatasourceCheck()<FooterProps>(Footer);
