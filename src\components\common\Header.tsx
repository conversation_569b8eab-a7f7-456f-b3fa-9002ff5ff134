import {
  Text,
  RichText,
  Image,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Placeholder,
} from "@sitecore-jss/sitecore-jss-nextjs";
import Link from "next/link";
import { ComponentProps } from "lib/component-props";
import React, { useEffect, useState, useContext } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "../../context/authContext";
import { useOktaAuth } from "../../context/oktaAuth";
import { cleanPreviousProductProfile } from "components/Platform/utils/profileUtils";

type HeaderProps = ComponentProps & {
  fields: any;
};

//For Adobe analytics
const FireEventActionSearch = () => {
  const evt = new CustomEvent("event-action-search");
  document?.body.dispatchEvent(evt);
};
const scopedSearchEligibility = (
  SearchOptions: any,
  eligibilityFeatures: any
) => {
  // Count the number of eligible product matches
  const matches = SearchOptions?.filter((element: any) => {
    const productFeature = element?.fields?.Feature?.fields?.Feature?.value;
    return (
      (productFeature && eligibilityFeatures?.includes(productFeature)) ||
      element?.fields["is Default"]?.value
    );
  });

  // Check if there are more than 2 matching products
  return matches?.length > 1;
};

const defaultScopeSearchOption = (
  pageProductType: string,
  eligibilityFeatures: any,
  SearchOptions: any,
  signinFlag: any
) => {
  let product: any = pageProductType;

  const currentDefaultSearchOption = SearchOptions?.find(
    (option: any) =>
      option?.fields?.Product?.fields?.["Display Label"]?.value === product
  );
  const allEligibleOptions: any = SearchOptions?.filter((item: any) => {
    return eligibilityFeatures?.includes(
      item?.fields?.Feature?.fields?.Feature?.value
    );
  });
  const defaultSearchOption =
    SearchOptions?.find(
      (option: any) => option?.fields["is Default"]?.value === true
    ) ?? allEligibleOptions?.[0];
  return currentDefaultSearchOption &&
    signinFlag &&
    eligibilityFeatures?.includes(
      currentDefaultSearchOption?.fields?.Feature?.fields?.Feature?.value
    )
    ? currentDefaultSearchOption?.fields?.Name?.value
    : defaultSearchOption?.fields?.Name?.value;
};
const LinkWrapper = ({ children, hasDescription }: any) => {
  return hasDescription ? <div>{children}</div> : <>{children}</>;
};
const ColumnLayout: any = (column: any, val: any, pageProductType: any) => {
  const menuItems: any = column?.fields?.menuitem || [];
  const menuitem: any = column?.fields?.menuitem?.[0]?.fields || {};
  const isMenuItems: any = menuitem?.hasOwnProperty("Display Type") || false;
  const displayType: any = isMenuItems ? menuitem?.["Display Type"]?.value : "";

  //Return if no items present
  if (menuItems?.length === 0) return;
  if (isMenuItems) {
    if (displayType === "Product" || displayType === "") {
      return (
        <div role="group">
          {column?.fields?.menuitem?.map((item: any) => (
            <React.Fragment key={item?.id}>
              {item?.fields?.["Title and Link"]?.value?.href ? (
                <Link
                  href={item?.fields?.["Title and Link"]?.value?.href}
                  className="heading"
                  data-region={val?.displayName}
                  data-title={item?.fields["Title and Link"]?.value?.text}
                  target={item?.fields?.["Title and Link"]?.value?.target || "_self"} 
                  data-interaction="nav"
                  data-context=""
                  id="product-links"
                  onClick={() => {
                    cleanPreviousProductProfile(
                      item?.fields?.["Product Type"]?.fields?.["Display Label"]
                        ?.value,
                      pageProductType
                    );
                  }}
                >
                  {item?.fields["Title and Link"]?.value?.text}
                </Link>
              ) : (
                <Text
                  field={item?.fields?.["Column Title"]}
                  className="heading"
                  tag="b"
                  aria-level={2}
                />
              )}
              <div className="group1-content">
                <RichText field={item?.fields?.Description} tag="p" />
              </div>
            </React.Fragment>
          ))}
        </div>
      );
    } else {
      const firstItem = column?.fields?.menuitem?.[0]?.fields;
      return (
        <div role="group">
          <Text
            field={firstItem?.["Column Title"]}
            role="heading"
            tag="b"
            aria-level={2}
          />
          <RichText
            field={firstItem?.Description}
            className="group2-content"
            tag="div"
          />
          <p>
            {firstItem?.["Title and Link"]?.value?.text && (
              <a
                href={firstItem?.["Title and Link"]?.value?.href}
                target="_blank"
                role="menu-item"
                data-region={val?.displayName}
                data-title={firstItem?.["Title and Link"]?.value?.text}
                data-interaction="nav"
                data-context=""
              >
                {firstItem?.["Title and Link"]?.value?.text}
              </a>
            )}
          </p>
        </div>
      );
    }
  } else {
    return (
      <div role="group">
        <Text
          field={menuitem?.["Column Title"]}
          role="heading"
          tag="b"
          aria-level={2}
        />

        {menuitem?.Links?.map((linkObj: any) => {
          const link = linkObj?.fields?.Link;
          const linkDesc = linkObj?.fields?.["Link Description"];
          return link?.value?.href ? (
            <LinkWrapper key={linkObj?.id} hasDescription={linkDesc?.value}>
              <Link
                key={linkObj?.id}
                href={link?.value?.href}
                role="menu-item"
                data-region={val?.displayName}
                data-title={link?.value?.text}
                target={link?.value?.target || "_self"}
                data-interaction="nav"
                onClick={() => {
                  cleanPreviousProductProfile(
                    menuitem?.["Product Type"]?.fields?.["Display Label"]
                      ?.value ||
                      linkObj?.fields?.["Product Type"]?.fields?.[
                        "Display Label"
                      ]?.value,
                    pageProductType
                  );
                }}
                data-context="megamenu-links"
              >
                {link?.value?.text}
              </Link>
              {linkDesc?.value && (
                <span role="note">
                  <Text field={linkDesc} />
                </span>
              )}
            </LinkWrapper>
          ) : null;
        })}
        {menuitem?.["ViewAll Link"]?.value?.href && (
          <div className="sub-group">
            <Link
              href={menuitem?.["ViewAll Link"]?.value?.href}
              target={menuitem?.["ViewAll Link"]?.value?.target || "_self"}
              role="menu-item"
              data-region={val?.displayName}
              data-title={menuitem?.["ViewAll Link"]?.value?.text}
              data-interaction="nav"
              data-context=""
            >
              {menuitem?.["ViewAll Link"]?.value?.text}{" "}
            </Link>
          </div>
        )}
      </div>
    );
  }
};

const MobileMegaMenuLayout: any = (
  column: any,
  handleMenuItemClick: any,
  pageProductType: any
) => {
  // Extracting menu items and menu item details with optional chaining
  const menuItems = column?.fields?.menuitem || [];
  const menuitem = column?.fields?.menuitem?.[0]?.fields;
  const isMenuItems: any = menuitem?.hasOwnProperty("Display Type") || false;
  const displayType: any = isMenuItems ? menuitem?.["Display Type"]?.value : "";

  // Return early if no menu items
  if (!menuItems?.length) return;

  // Render logic for different menu item types
  if (isMenuItems) {
    if (displayType === "Product" || displayType === "") {
      // Render links for main column menu items
      return (
        <>
          {column?.fields?.menuitem?.map((item: any) => {
            const link = item?.fields?.["Title and Link"]?.value;
            return (
              link?.href && (
                <Link
                  key={item?.id}
                  href={item?.fields?.["Title and Link"]?.value?.href}
                  target={
                    item?.fields?.["Title and Link"]?.value?.target || "_self"}
                  className="sub-heading"
                  id="product-links-mob"
                  onClick={() =>
                    handleMenuItemClick(
                      item?.fields?.["Product Type"]?.fields?.["Display Label"]
                        ?.value,
                      pageProductType
                    )
                  }
                  data-region="Header Mobile"
                  data-title={link?.text}
                  data-interaction="nav"
                  data-context=""
                  data-testid="handle-click"
                >
                  {item?.fields?.["Title and Link"]?.value?.text}{" "}
                </Link>
              )
            );
          })}
        </>
      );
    } else {
      // Render links for other menu item types
      return (
        <div className="menu-group">
          {menuitem?.["Title and Link"]?.value?.href && (
            <Link
              role="menu-item"
              className="two"
              target={menuitem?.["Title and Link"]?.value?.target || "_self"}
              onClick={handleMenuItemClick}
              href={menuitem?.["Title and Link"]?.value?.href}
              data-region="Header Mobile"
              data-title={menuitem?.["Title and Link"]?.value?.text}
              data-interaction="nav"
              data-context=""
            >
              {menuitem?.["Title and Link"]?.value?.text}{" "}
            </Link>
          )}
        </div>
      );
    }
  } else {
    // Render links for column menu items

    return (
      <div className="menu-group">
        {menuitem?.Links?.map((linkObj: any) => {
          const link = linkObj?.fields?.Link;
          // const linkDesc = linkObj?.fields?.["Link Description"];
          return (
            link?.value?.href && (
              <Link
                key={linkObj?.id}
                role="menu-item"
                className="two"
                href={link?.value?.href}
                target={link?.value?.target || "_self"}
                data-region="Header Mobile"
                data-title={link?.text}
                data-interaction="nav"
                onClick={() => {
                  handleMenuItemClick(
                    menuitem?.["Product Type"]?.fields?.["Display Label"]
                      ?.value,
                    pageProductType
                  );
                }}
                data-context=""
              >
                {link?.value?.text}{" "}
              </Link>
            )
          );
        })}
        {menuitem?.["ViewAll Link"]?.value?.href && (
          <Link
            role="menu-item"
            className="two"
            onClick={handleMenuItemClick}
            href={menuitem?.["ViewAll Link"]?.value?.href}
            target={menuitem?.["ViewAll Link"]?.value?.target || "_self"}
            data-region="Header Mobile"
            data-title={menuitem?.["ViewAll Link"]?.value?.text}
            data-interaction="nav"
            data-context=""
          >
            {menuitem?.["ViewAll Link"]?.value?.text}
          </Link>
        )}
      </div>
    );
  }
};

const Header = (props: any): React.JSX.Element => {
  const context = useContext(AuthContext);
  const { isEEFlag = false } = props;
  const fields = props.fields || [];

  const openSwitchProfilePopup: any = props?.openSwitchProfilePopup;
  const showSwitchProfLinkInHead: any = props?.showSwitchProfLinkInHead;
  const pageProductType: any = props?.product || "";
  const [SearchVal, setSearchVal] = useState<any>("");
  const [, /* buttonDisable */ setbuttonDisable] = useState(true);
  const [activeRoute, setActiveRoute] = useState("");
  const [insightsToggle, setInsightsToggle] = useState(false);
  const [actuarialToggle, setActuarialToggle] = useState(false);
  const [productToggle, setProductToggle] = useState(false);
  const [lobToggle, setLobToggle] = useState(false);
  const [toggleUserDD, setToggleUserDD] = useState(false);
  const [toggleResourcesDD, setToggleResourcesDD] = useState(false);

  const [showOnover, setShowOnHover] = useState(true);
  const router = useRouter();
  const { signinFlag, eligibilityFeatures } = context;
  const {
    handleLogin,
    sendToLogout,
    handleChangePassword,
    nextSessionUserName,
  } = useOktaAuth();

  let isProductAccess: any = scopedSearchEligibility(
    fields?.SearchOptions,
    eligibilityFeatures
  );
  const [showSearchDropdown] = useState(() => signinFlag && isProductAccess);

  let defaultOption: any = defaultScopeSearchOption(
    pageProductType,
    eligibilityFeatures,
    fields?.SearchOptions,
    signinFlag
  );

  const [selectedValue, setSelectedValue] = useState(defaultOption);

  const searchBoxHandler = (e: any) => {
    setSearchVal(e.target.value);
    if (
      e.target.value.trim() === "" ||
      e.target.value === null ||
      e.target.value === ""
    ) {
      setbuttonDisable(true);
    } else {
      setbuttonDisable(false);
    }
  };

  const handleClick = () => {
    if (SearchVal) handleSearch();
  };

  const handleKeyPress = (e: any) => {
    if (e.key === "Enter" && SearchVal) handleSearch();
  };

  const handleSearch = () => {
    // For Adobe analytics
    if (!isEEFlag) FireEventActionSearch();

    const selectedOption = fields?.SearchOptions?.find(
      (option: any) => option?.fields?.Name?.value === selectedValue
    );
    const selectedProduct: any =
      selectedOption?.fields?.Product?.fields?.["Display Label"]?.value;

    if (selectedOption) {
      let url = `${
        selectedOption?.fields?.Link?.value?.href
      }?keyword=${encodeURIComponent(SearchVal)}`;
      if (
        selectedOption?.fields?.Name?.value?.toLowerCase()?.includes("panels")
      ) {
        url = `${
          selectedOption?.fields?.Link?.value?.href
        }?tabName=Search&keyword=${encodeURIComponent(SearchVal)}`;
      }

      cleanPreviousProductProfile(selectedProduct, pageProductType);
      sessionStorage.removeItem("requestParams");
      sessionStorage.removeItem("tagCount");
      router.push(url);
      setSearchVal("");
    }
  };

  useEffect(() => {
    defaultOption = defaultScopeSearchOption(
      pageProductType,
      eligibilityFeatures,
      fields?.SearchOptions,
      signinFlag
    );
    setSelectedValue(defaultOption);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.asPath]);

  useEffect(() => {
    if (
      router.asPath === "/" &&
      (insightsToggle || lobToggle || actuarialToggle || productToggle)
    ) {
      setToggle(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const routePath = router.asPath;
    setActiveRoute("");
    if (routePath === "/") {
      setActiveRoute("Home");
    }
    if (routePath.toLocaleUpperCase().includes("INSIGHTS")) {
      setActiveRoute("Insights");
    }
    if (
      routePath
        .replace(/%20/g, "-")
        .replace(/\s+/g, "-")
        .toLocaleUpperCase()
        .includes("LOB-PAGES") ||
      routePath
        .replace(/%20/g, "-")
        .replace(/\s+/g, "-")
        .toLocaleUpperCase()
        .includes("LOB-DEEP-DIVE-PAGES")
    ) {
      setActiveRoute("Line of Business");
    }
    if (routePath.toLocaleUpperCase().includes("SEARCH")) {
      setActiveRoute("Search");
    }
    if (
      routePath
        .replace(/%20/g, "-")
        .replace(/\s+/g, "-")
        .toLocaleUpperCase()
        .includes("ACTUARIAL-HUB-PAGES")
    ) {
      setActiveRoute("Actuarial Hub");
    }
    setShowOnHover(false);
  }, [router.asPath]);

  const [isToggled, setToggle] = useState(false);

  const handleClicktoggle = () => {
    setToggle(!isToggled);
  };

  const handleMenuItemClick = (visitProduct: any, pageProductType: any) => {
    cleanPreviousProductProfile(visitProduct, pageProductType);
    // Close the menu when you click on an element with the className "two"
    setToggle(false);
    setInsightsToggle(false);
    setLobToggle(false);
    setActuarialToggle(false);
  };

  const toggleMobile = (displayName: any) => {
    if (displayName === "Insights") {
      setInsightsToggle(!insightsToggle);
    }
    if (displayName === "Line of Business") {
      setLobToggle(!lobToggle);
    }
    if (displayName === "Actuarial Hub") {
      setActuarialToggle(!actuarialToggle);
    }
    if (displayName === "Products") {
      setProductToggle(!productToggle);
    }
  };

  const resetToDefaultToogle = () => {
    setInsightsToggle(false);
    setLobToggle(false);
    setToggle(false);
    setActuarialToggle(false);
  };

  const onSearchOptionsChange = (event: any) => {
    setSelectedValue(event.target.value);
  };
  const handleFocus = () => {
    setShowOnHover(true);
  };

  const NotificationPreferences = () => {
    router.push(`/Notification-Preferences`);
  };

  return (
    <header className="guest-header">
      <div id="guest" className="flex-wrapper site top-header">
        <a
          href="https://www.verisk.com/"
          target="_blank"
          data-region="Header"
          data-title="Header logo"
          data-interaction="nav"
          data-context=""
          className="logo"
        >
          <Image field={fields?.Logo} />
        </a>
        <div className="search-box">
          {showSearchDropdown && (
            <div className="select-wrapper">
              <select
                title="search-box"
                value={selectedValue}
                onChange={(event) => onSearchOptionsChange(event)}
              >
                {fields?.SearchOptions?.filter((item: any) => {
                  return (
                    eligibilityFeatures?.includes(
                      item?.fields?.Feature?.fields?.Feature?.value
                    ) || item?.fields["is Default"]?.value
                  );
                }).map((item: any) => {
                  return (
                    <option key={item?.name} value={item?.fields?.Name?.value}>
                      {item?.fields?.Name?.value || item?.name}
                    </option>
                  );
                })}
              </select>
            </div>
          )}
          <input
            type="text"
            aria-label="search bar"
            value={SearchVal}
            onKeyPress={handleKeyPress}
            onChange={(e) => searchBoxHandler(e)}
            placeholder={fields?.SearchBoxText?.value}
            data-region="Header"
            data-title={fields?.SearchBoxText?.value}
            data-interaction="nav"
            data-context=""
            data-testid="search_input"
          />
          <button
            id="mainSearch"
            className="material-icons primary"
            onKeyPress={() => {
              handleClick();
            }}
            onClick={() => handleClick()}
            data-region="Header"
            data-title="search"
            data-interaction="nav"
            data-context=""
            data-testid="search-button"
          >
            search
          </button>
        </div>
        <Link
          className="isodotnet"
          href={fields?.Externallink1?.value?.href}
          target={fields?.Externallink1?.value?.target || "_self"}
          data-region="Header"
          data-title={fields?.Externallink1?.value?.text}
          data-interaction="nav"
          data-context=""
        >
          {fields?.Externallink1?.value?.text}
          <sup className="material-symbols-outlined">open_in_new</sup>
        </Link>
      </div>
      <hr />
      <div className="flex-wrapper site">
        <nav>
          {!isEEFlag && (
            <ul
              className="non-mobile"
              data-testid="mega-menu-item"
              onMouseOver={handleFocus}
            >
              {fields?.MegaMenu?.map((val: any) => {
                return (
                  <React.Fragment key={val?.id + val?.displayName}>
                    {val?.fields?.column?.length > 0 ? (
                      <li
                        className={
                          activeRoute === val.displayName
                            ? "menu-dropdown current"
                            : "menu-dropdown"
                        }
                      >
                        <a
                          href="#"
                          data-region="Header"
                          data-title={val?.displayName}
                          data-interaction="nav"
                          data-context=""
                        >
                          <span>{val?.displayName}</span>
                          <span className="material-icons">expand_more</span>
                        </a>
                        {showOnover && (
                          <div
                            className={`mega-menu ${
                              val?.fields?.column?.length < 3 ? "two" : "three"
                            }`}
                            role="menu"
                          >
                            {val?.fields?.column?.map(
                              (column: any, id: any) => {
                                return (
                                  <React.Fragment key={id + val?.displayName}>
                                    {ColumnLayout(column, val, pageProductType)}
                                  </React.Fragment>
                                );
                              }
                            )}
                          </div>
                        )}
                      </li>
                    ) : (
                      <li
                        key={val?.id + val?.displayName}
                        className={activeRoute === "Home" ? "current" : ""}
                      >
                        <Link
                          href={val?.fields?.link?.value?.href}
                          target={val?.fields?.link?.value?.target || "_self"}
                          data-region="Header"
                          data-title={val?.fields?.link?.value?.text}
                          data-interaction="nav"
                          data-context=""
                        >
                          {" "}
                          {val?.fields?.link?.value?.text}{" "}
                        </Link>
                      </li>
                    )}
                  </React.Fragment>
                );
              })}
            </ul>
          )}
          <div className="mobile-navigation">
            <a
              className="material-icons"
              id="hamburger-menu"
              onClick={handleClicktoggle}
              style={isToggled ? { display: "none" } : { display: "block" }}
              data-region="Header Mobile"
              data-title="Mobile Menu open"
              data-interaction="nav"
              data-context=""
              data-testid="mobile-menu"
            >
              menu
            </a>
            <a
              className="material-icons"
              id="closeMenu"
              onClick={handleClicktoggle}
              style={isToggled ? { display: "block" } : { display: "none" }}
              data-region="Header Mobile"
              data-title="Mobile Menu close"
              data-interaction="nav"
              data-context=""
            >
              close
            </a>
            <div
              id="mobile_navigation"
              style={isToggled ? { display: "block" } : { display: "none" }}
            >
              {!isEEFlag && (
                <ul>
                  {fields?.MegaMenu?.map((val: any, id: any) => {
                    return (
                      <React.Fragment key={val?.id + val?.displayName}>
                        {val?.fields?.column?.length > 0 ? (
                          <li key={val?.id + val?.displayName}>
                            <span
                              onClick={() => toggleMobile(val?.displayName)}
                              data-testid={val?.displayName + " " + "toggle"}
                            >
                              <strong className="heading">
                                {val?.displayName}
                                {((id === 1 && !insightsToggle) ||
                                  (id === 2 && !lobToggle) ||
                                  (id === 3 && !actuarialToggle) ||
                                  (id === 4 && !productToggle)) && (
                                  <span className="material-icons expand_more">
                                    expand_more
                                  </span>
                                )}
                                {((id === 1 && insightsToggle) ||
                                  (id === 2 && lobToggle) ||
                                  (id === 3 && actuarialToggle) ||
                                  (id === 4 && productToggle)) && (
                                  <span className="material-symbols-outlined expand_less">
                                    expand_less
                                  </span>
                                )}
                              </strong>
                            </span>
                            {((id === 1 && insightsToggle) ||
                              (id === 2 && lobToggle) ||
                              (id === 3 && actuarialToggle) ||
                              (id === 4 && productToggle)) && (
                              <div className="menu-group EI active">
                                {val?.fields?.column?.map(
                                  (column: any, id: any) => {
                                    return (
                                      <React.Fragment
                                        key={id + val?.displayName}
                                      >
                                        {MobileMegaMenuLayout(
                                          column,
                                          handleMenuItemClick,
                                          pageProductType
                                        )}
                                      </React.Fragment>
                                    );
                                  }
                                )}
                              </div>
                            )}
                          </li>
                        ) : (
                          <li
                            key={val?.id + val?.displayName}
                            onClick={() => {
                              resetToDefaultToogle();
                            }}
                            data-testid="reset-toggle"
                          >
                            <Link
                              className="heading"
                              href={val?.fields?.link?.value?.href}
                              target={val?.fields?.link?.value?.target || "_self"}
                              data-region="Header Mobile"
                              data-title={val?.fields?.link?.value?.text}
                              data-interaction="nav"
                              data-context=""
                            >
                              {val?.fields?.link?.value?.text}{" "}
                            </Link>
                          </li>
                        )}
                      </React.Fragment>
                    );
                  })}
                </ul>
              )}
              {(context?.signinFlag || isEEFlag) && (
                <>
                  <hr />
                  <Placeholder
                    name="jss-sidenav"
                    rendering={props?.routeValues}
                    product={pageProductType}
                    onClickHandler={handleMenuItemClick} // Close the menu when clicking on an element
                  />
                </>
              )}
            </div>
          </div>
        </nav>
        {!signinFlag ? (
          <Link
            onClick={handleLogin}
            href={fields?.Signin?.value?.text}
            target={fields?.Signin?.value?.target || "_self"}
            className="primary"
            data-testid="login"
          >
            {fields?.Signin?.value?.text}{" "}
          </Link>
        ) : (
          <nav className="account-alerts-support">
            <ul className="user-account">
              <li className="menu-dropdown resources-dropdown">
                <div
                  className="resources-button"
                  onClick={() => setToggleResourcesDD(!toggleResourcesDD)}
                  data-testid="resources"
                >
                  <span className="material-symbols-outlined">support</span>
                  Resources
                  <span className="material-symbols-outlined">expand_more</span>
                </div>

                {toggleResourcesDD && (
                  <div className="dropdown-content">
                    <a
                      href="https://www.verisk.com/company/contact/"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Support
                    </a>
                    <a
                      href="https://education.verisk.com/isoeducation/"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      ISO Education
                    </a>
                    <a
                      href="https://www.verisk.com/resources/webinars/"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Webinars
                    </a>
                  </div>
                )}
              </li>

              <li className="menu-dropdown">
                <div
                  className="account"
                  onClick={() => setToggleUserDD(!toggleUserDD)}
                  data-testid="account"
                >
                  <a href="#">
                    <span className="material-symbols-outlined">
                      account_circle
                    </span>
                    {nextSessionUserName}
                    <span className="material-symbols-outlined">
                      {" "}
                      expand_more
                    </span>
                  </a>
                </div>
                {toggleUserDD && (
                  <div className="dropdown-content">
                    <a onClick={NotificationPreferences}>
                      Notification Preferences
                    </a>
                    <a onClick={handleChangePassword}>Change Password</a>
                    {showSwitchProfLinkInHead && (
                      <a
                        onClick={() => openSwitchProfilePopup(true)}
                        data-testid="switch-account"
                      >
                        Switch Account
                      </a>
                    )}
                    {!context?.isInternalUser &&
                      !context?.IsFederatedUser &&
                      fields?.["Show MFA Link"]?.value && (
                        <a
                          href={`${process.env.NEXT_PUBLIC_GATEWAY_LOGIN_URL}/app/core/account-settings`}
                          data-testid="mfa"
                          target="_blank"
                        >
                          Configure MFA
                        </a>
                      )}
                    <a role="button" onClick={sendToLogout}>
                      {" "}
                      Sign Out{" "}
                    </a>
                  </div>
                )}
              </li>
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};

export default withDatasourceCheck()<HeaderProps>(Header);
