import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type HubHeaderProps = ComponentProps & {
  fields: {
    IsBeta: any;
    Title: Field<string>;
  };
};

const HubHeader = (props: HubHeaderProps): JSX.Element => {
  return (
    <>
      <div className="sub-header">
        <div className="hub-header">
          <div className="site flex-wrapper">
            {props?.fields?.IsBeta?.value && (
              <span className="beta-tag">Beta</span>
            )}
            <Text field={props?.fields?.Title} tag="h1" />
          </div>
        </div>
      </div>
      {/* <AlertBanner
        isLockVisible
        BackgroundColor="#ffc60066"
        Description={" SignIn flag"}
      /> */}
    </>
  );
};

export default withDatasourceCheck()<HubHeaderProps>(HubHeader);
