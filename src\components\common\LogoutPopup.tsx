import React, { useRef, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useIdleTimer } from "react-idle-timer";
import { useOktaAuth } from "../../context/oktaAuth";

const THREE_MINUTES_IN_MS = 3 * 60000;

const FOUR_HOURS_IN_SEC = 4 * 60 * 60;

const IDLE_TIMER_IN_MS =
  (Number(process.env.NEXT_PUBLIC_IDLE_TIMER_IN_SEC) || FOUR_HOURS_IN_SEC) *
  1000;

const PROMPT_BEFORE_IDLE_IN_MS = process.env
  .NEXT_PUBLIC_PROMPT_BEFORE_IDLE_IN_SEC
  ? Number(process.env.NEXT_PUBLIC_PROMPT_BEFORE_IDLE_IN_SEC) * 1000
  : THREE_MINUTES_IN_MS;

const millisToMinutesAndSeconds = (millis: number | null) => {
  if (!millis) {
    return "";
  }
  const minutes = Math.floor(millis / 60000);
  const seconds: number = Number(((millis % 60000) / 1000).toFixed(0));
  return `${minutes > 0 ? `${minutes}m ` : ""}${seconds}s`;
};

type Props = {
  // Required for testing as there will be only one tab in jest testing environment
  emitOnSelf?: boolean;
};

const LogoutPopup = ({ emitOnSelf }: Props): JSX.Element | null => {
  const { handleLogin, sendToLogout } = useOktaAuth();
  const nextSession = useSession() as any;
  const [idlePromptOpen, setIdlePromptOpen] = useState("");

  const onIdle = () => {
    if (nextSession?.status === "authenticated") {
      localStorage.setItem("idleLogout", "signin");
      setIdlePromptOpen("signin");
      sendToLogout();
    }
  };

  const onPrompt = () => {
    if (
      nextSession?.status === "authenticated" &&
      idlePromptOpen !== "signin"
    ) {
      setIdlePromptOpen("continue");
    }
  };

  const onMessage = (action: string) => {
    // THIS FUNCTION IS TO EXECUTE ACTIONS ACROSS TABS
    if (action === "continue") {
      setIdlePromptOpen("");
      reset();
    }
    if (action === "signout") {
      // NOTE: this may need to be more specific to an action across tabs
      sendToLogout();
    }
    if (action === "signin") {
      // NOTE: this may need to be more specific to an action across tabs
      handleLogin();
    }
    if (action === "close") {
      setIdlePromptOpen("");
    }
  };

  const { getRemainingTime, reset, message } = useIdleTimer({
    timeout: IDLE_TIMER_IN_MS, // Idle timer set to 4 hours
    onIdle,
    onPrompt,
    onMessage,
    promptBeforeIdle: PROMPT_BEFORE_IDLE_IN_MS,
    crossTab: true,
    syncTimers: 200,
    leaderElection: true,
    debounce: 500,
  });

  const [remainingTime, setRemainingTime] = useState(null);

  const idleIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);

  useEffect(() => {
    setIdlePromptOpen(localStorage.getItem("idleLogout") || "");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (idleIntervalRef.current) {
      clearInterval(idleIntervalRef.current);
    }
    if (idlePromptOpen === "continue") {
      idleIntervalRef.current = setInterval(
        () => setRemainingTime(getRemainingTime() as any),
        500
      );
    }
    return () => {
      if (idleIntervalRef.current) {
        clearInterval(idleIntervalRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idlePromptOpen]);

  return idlePromptOpen ? (
    <div className="modal-container">
      {idlePromptOpen === "continue" ? (
        <div className="inactivity-modal">
          <div className="inactivity-content">
            <h2>
              <span className="material-symbols-outlined">watch_later</span>{" "}
              Your session will expire soon.
            </h2>
            <p>
              We will sign you out in{" "}
              <b>
                <span id="inactivityTimer" data-testid="inactivityTimer">
                  {millisToMinutesAndSeconds(remainingTime)}
                </span>
                .
              </b>
            </p>
            <p>Click keep working to remain signed in.</p>
          </div>
          <div className="call-to-action">
            <button
              className="tertiary"
              role="button"
              type="button"
              onClick={() => {
                sendToLogout();
                message("signout", emitOnSelf);
              }}
            >
              Sign out
            </button>
            <button
              className="primary"
              role="button"
              id="closeModalBtn"
              type="button"
              onClick={() => {
                setIdlePromptOpen("");
                reset();
                message("continue", emitOnSelf);
              }}
            >
              Keep working
            </button>
          </div>
        </div>
      ) : (
        <div className="inactivity-modal">
          <h2>Your session has expired due to inactivity.</h2>
          <div className="call-to-action">
            <button
              className="tertiary"
              role="button"
              id="closeModalBtn"
              type="button"
              onClick={() => {
                localStorage.removeItem("idleLogout");
                setIdlePromptOpen("");
                message("close", emitOnSelf);
              }}
            >
              Close
            </button>
            <button
              className="primary"
              role="button"
              type="button"
              onClick={() => {
                message("close", emitOnSelf);
                handleLogin();
                message("signin", emitOnSelf);
              }}
            >
              Sign in
            </button>
          </div>
        </div>
      )}
    </div>
  ) : null;
};
export default LogoutPopup;
