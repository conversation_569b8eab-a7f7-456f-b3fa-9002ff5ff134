import { AuthContext } from "src/context/authContext";
import React, { useContext, useState, useCallback } from "react";
import Cookies from "js-cookie";
import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";
import { useRouter } from "next/router";
import { isBasicServiceLineProduct } from "components/Platform/utils/mediaProfileHelper";

const MediaProfilePopup = (props: any): React.JSX.Element | null => {
  const router = useRouter();
  const { userProductProfilesList = [] } = useContext(AuthContext) || {};
  const product: any = router?.query?.product || "";
  const Feature: any = isBasicServiceLineProduct(product)
    ? "Basic Line Service"
    : product;
  const profileArray =
    userProductProfilesList?.find((profile: any) => profile.feature === Feature)
      ?.customers || [];
  const fields: any = props?.fields;
  const [profileList] = useState<any>(profileArray);
  const [temporarySelectedProfile, setTemporarySelectedProfile] = useState<
    any | null
  >(null);
  const [savedProfile, setSavedProfile] = useState<any | null>(null);
  const [showPopup, setShowPopup] = useState<boolean>(true);

  const handleProfileSelect = useCallback((profile: any) => {
    setTemporarySelectedProfile(profile);
  }, []);

  const selectedProfiles = JSON.parse(
    localStorage.getItem("selectedProfiles") ?? "{}"
  );

  const availableProfile = selectedProfiles?.[product];

  const handleSaveAndNavigate = () => {
    if (temporarySelectedProfile && typeof window !== "undefined") {
      setSavedProfile(temporarySelectedProfile);
      selectedProfiles[product] = {
        ...temporarySelectedProfile,
      };
      Cookies.set(
        "selectedProfilesForMedia",
        JSON.stringify(selectedProfiles),
        {
          path: "/",
          sameSite: "strict",
          expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        }
      );
      const downloadUrl = `/api/download?pdfUrl=${router?.query?.prvURL}`;
      window.open(downloadUrl, "_blank");
      setShowPopup(false);
      router.push("/");
    }
  };

  if (
    !product ||
    !router?.query?.prvURL ||
    profileArray.length < 2 ||
    availableProfile?.customerNumber
  ) {
    router.push("/");
    return null;
  }

  if (!showPopup) return null;

  return (
    <>
      <div className="modal-container-switch-popup media-model">
        <div className="profile-modal">
          <div className="profile-content">
            <ul className="link-list">
              <li>
                <Text
                  className="heading"
                  field={fields?.["Select Profile"]}
                  tag="h2"
                  data-testid="select-profile-head"
                />
              </li>
              <li>
                <RichText
                  className="description"
                  field={fields?.Message}
                  tag="p"
                />
              </li>
            </ul>
            <ul className="link-list scrollable-list">
              {profileList?.map(({ customerName, customerNumber }: any) => (
                <li
                  key={customerNumber}
                  className={
                    temporarySelectedProfile?.customerNumber === customerNumber
                      ? "choosen-profile"
                      : "profile"
                  }
                >
                  <button
                    onClick={() =>
                      handleProfileSelect({ customerName, customerNumber })
                    }
                    data-testid="profile-anchor"
                    className="profile-link"
                  >
                    {customerName}
                    {savedProfile?.customerNumber === customerNumber &&
                      " *Current Profile"}
                  </button>
                </li>
              ))}
            </ul>
          </div>
          <div className="call-to-action">
            <a
              className="primary"
              type="button"
              data-testid="select-profile-anc"
              href={router?.query?.pdfUrl as string}
              onClick={(e) => {
                e.preventDefault();
                handleSaveAndNavigate();
              }}
              target="_blank"
            >
              Select Profile
            </a>
          </div>
        </div>
      </div>
    </>
  );
};

export default MediaProfilePopup;
