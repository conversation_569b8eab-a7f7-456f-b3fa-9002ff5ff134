import React, { useEffect } from "react";
import { useRouter } from "next/router";
import Loader from "components/common/Loader";
import Cookies from "js-cookie";

const MediaRedirect: React.FC = () => {
  const router = useRouter();
  const beRedirectURL = Cookies.get("RedirectURL") || "";
  // const previousPageUrl = Cookies.get("pagePath") || "/";

  useEffect(() => {
    if (beRedirectURL && beRedirectURL !== "") {
      Cookies.remove("RedirectURL");
      router.push(beRedirectURL);
    }
  }, [beRedirectURL, router]);

  useEffect(() => {
    if (!beRedirectURL.endsWith(".pdf")) {
      const interval = setInterval(() => {
        const downloadedFileFlag = Cookies.get("downloadfile");
        if (downloadedFileFlag) {
          Cookies.remove("downloadfile");
          Cookies.remove("pagePath");
          window.close();
        }
      }, 2000); // Check every 2000 milliseconds

      return () => clearInterval(interval); // Cleanup interval on component unmount
    }

    return undefined; // Ensure the cleanup function always returns undefined
  }, [beRedirectURL]);

  return (
    <div className="modal-container with-page-loader">
      <Loader />
    </div>
  );
};

export default MediaRedirect;
