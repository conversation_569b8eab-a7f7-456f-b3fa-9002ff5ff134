import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type MegaMenuProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const MegaMenu = (props: MegaMenuProps): JSX.Element => (
  <div>
    <p>MegaMenu Component</p>
    <Text field={props.fields.heading} />
  </div>
);

export default withDatasourceCheck()<MegaMenuProps>(MegaMenu);
