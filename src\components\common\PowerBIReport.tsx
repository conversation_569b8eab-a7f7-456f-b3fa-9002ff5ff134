import { useEffect, useState, useContext } from "react";
import { AuthContext } from "../../context/authContext";
import { withDatasourceCheck } from "@sitecore-jss/sitecore-jss-nextjs";
import ErrorMessage from "./ErrorMessage";
declare global {
  interface Window {
    report?: any; // Adjust the type accordingly
  }
}

function PowerBIReport(props: any): JSX.Element {
  // const externalData = useComponentProps<EmbedData>(props.rendering.uid);
  const [PowerBIEmbedComponent, setPowerBIEmbedComponent] = useState<
    any | null
  >(null);
  const [PowerBIEmbedModel, setPowerBIEmbedModel] = useState<any | null>(null);
  const [isClient, setIsClient] = useState(false);
  const context = useContext(AuthContext);
  const reportId = props?.fields?.ReportId?.value;
  const pageName = props?.fields?.InitialPage?.value;
  const showBottomNavigationPane = props?.fields?.ShowBottomNavPane?.value; // Set based on content author's choice

  useEffect(() => {
    if (typeof window !== "undefined") {
      setIsClient(true);

      // Dynamically import both libraries
      Promise.all([import("powerbi-client-react"), import("powerbi-client")])
        .then(([reactModule, clientModule]) => {
          setPowerBIEmbedComponent(() => reactModule.PowerBIEmbed);
          setPowerBIEmbedModel(() => clientModule);
        })
        .catch((error) => {
          console.error("Failed to load PowerBI libraries:", error);
        });
    }
  }, []);

  const powerBIErroMessage =
    "We are experiencing an issue loading the reports and are working to resolve it. Thank you for your patience.";

  return (
    <>
      <section className="powerbi-report-container">
        {isClient &&
          PowerBIEmbedComponent &&
          context?.powerBIToken?.accessToken && (
            <PowerBIEmbedComponent
              embedConfig={{
                type: "report", // Supported types: report, dashboard, tile, visual, qna, paginated report and create
                id: reportId,
                embedUrl: context?.powerBIToken?.embedUrl,
                accessToken: context?.powerBIToken?.accessToken,
                tokenType: 1, // Use models.TokenType.Aad for SaaS embed
                pageName: pageName,
                settings: {
                  panes: {
                    filters: {
                      expanded: false,
                      visible: false,
                    },
                    pageNavigation: {
                      visible: showBottomNavigationPane,
                    },
                  },
                  background:
                    PowerBIEmbedModel?.models?.BackgroundType.Transparent,
                },
              }}
              cssClassName={"report-container site"}
            />
          )}
        {!context?.powerBIToken?.accessToken && (
          <ErrorMessage
            dataTestId="power-bi-err"
            message={powerBIErroMessage}
          />
        )}
        {/* {context?.isEditing && <p>PowerBI Component</p> } */}
      </section>
      {!context.isEditing && (
        <section className="site flex-wrapper disclaimer-container">
          <p>
            This data visualization is best viewed on screen resolutions larger
            than 1024px.
          </p>
        </section>
      )}
    </>
  );
}

export default withDatasourceCheck()(PowerBIReport);
