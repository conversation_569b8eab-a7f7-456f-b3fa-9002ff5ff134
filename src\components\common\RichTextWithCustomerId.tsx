function replaceCustomerIdInAnchorsOnly(
  html: string,
  customerId: string
): string {
  if (!html || !customerId) return html;

  // Create a DOM parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");

  // Update only <a> tags with `customerId={0}` in the href
  const anchors = doc.querySelectorAll('a[href*="customerId="]');
  anchors.forEach((anchor) => {
    const href = anchor.getAttribute("href");
    if (href) {
      const updatedHref = href.replace(
        "customerId=",
        `customerId=${encodeURIComponent(customerId)}`
      );
      anchor.setAttribute("href", updatedHref);
    }
  });

  return doc.body.innerHTML;
}

type RichTextWithCustomerIdProps = {
  field: any;
  customerId: string;
};

export const descriptionWithCustomrID = ({
  field,
  customerId,
}: RichTextWithCustomerIdProps) => {
  const rawHtml = field?.value || field?.editable || "";

  const transformedHtml = replaceCustomerIdInAnchorsOnly(rawHtml, customerId);

  return { value: transformedHtml };
};
