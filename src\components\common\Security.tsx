import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type SecurityProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const Security = (props: SecurityProps): JSX.Element => (
  <div>
    <Text field={props?.fields?.heading} />
  </div>
);

export default withDatasourceCheck()<SecurityProps>(Security);
