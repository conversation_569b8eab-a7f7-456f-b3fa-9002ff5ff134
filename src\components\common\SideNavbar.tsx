import {
  Text,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import Link from "next/link";

import { ComponentProps } from "lib/component-props";
import React, { useEffect, useRef, useState, useContext } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "../../context/authContext";
import { cleanPreviousProductProfile } from "components/Platform/utils/profileUtils";
import { checkFeature } from "lib/entitlement";

type SideNavbarProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const SideNavbar = (props: any): React.JSX.Element => {
  const [isChecked, setIsChecked] = useState(true);
  const checkboxRef = useRef<HTMLInputElement | null>(null);
  const router = useRouter();
  const context = useContext(AuthContext);
  const eligibilityFeatures = context?.eligibilityFeatures || [];

  useEffect(() => {
    setIsChecked(true); // Set isChecked to true when router.asPath changes
  }, [router?.asPath]);

  const toggleCheckbox = () => {
    setIsChecked((prev) => !prev); // Toggle the isChecked state
  };
  const fields = props?.fields;
  const pageProductType: any = props?.product;

  const filteredNavItems = fields?.navitems
    ?.map((navItem: any) => {
      const filteredNavChild = navItem?.fields?.navchild?.filter(
        (navChild: any) =>
          eligibilityFeatures?.includes(
            checkFeature(
              navChild?.fields?.EligibilityStatus?.fields?.Feature?.value
            )
          )
      );
      // Return a new object only if filteredNavChild is not empty
      if (filteredNavChild?.length > 0) {
        return {
          ...navItem,
          fields: {
            ...navItem?.fields,
            navchild: filteredNavChild,
          },
        };
      }
      return null; // Return null if navchild is empty to filter it out later
    })
    ?.filter(Boolean); // Remove null items

  if (filteredNavItems?.length === 0) return <></>;

  return (
    <>
      <input
        type="checkbox"
        id="slide"
        name="slide"
        className="desktop-view"
        value=""
        checked={isChecked}
        // defaultChecked={true}
        ref={checkboxRef}
        onChange={toggleCheckbox}
      />
      <label
        htmlFor="slide"
        className="toggle desktop-view"
        data-testid="navbar-checkbox"
        onClick={toggleCheckbox}
      >
        <span className="material-icons less">expand_less</span>
        <span className="material-icons more">expand_more</span>
      </label>
      <aside className="workspace-nav">
        <nav className="desktop-view">
          {filteredNavItems?.map((val: any) => {
            return (
              <React.Fragment key={val?.id}>
                <div key={val?.id} className="side-nav heading">
                  <span className="material-icons">
                    <Text field={val?.fields?.style} />{" "}
                  </span>{" "}
                  <Text field={val?.fields?.heading} />
                </div>
                <ul>
                  {val?.fields?.navchild?.map((navitem: any) => {
                    return (
                      <li key={navitem?.id}>
                        <Link
                          href={navitem?.fields?.link?.value?.href}
                          id="side-nav-link-lap"
                          onClick={() =>
                            cleanPreviousProductProfile(
                              navitem?.fields?.EligibilityStatus?.fields
                                ?.Feature?.value,
                              pageProductType
                            )
                          }
                        >
                          <Text field={navitem?.fields?.heading} />
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </React.Fragment>
            );
          })}
        </nav>
      </aside>
      <ul className="mobile-view side-nav">
        {filteredNavItems?.map((val: any) => {
          return (
            <li key={val?.id} className="test">
              <strong className="heading">
                <Text field={val?.fields?.heading} />
              </strong>
              <ul className="menu-group active">
                {val?.fields?.navchild?.map((navitem: any) => {
                  return (
                    <li key={navitem?.id}>
                      <Link
                        href={navitem?.fields?.link?.value?.href}
                        id="side-nav-link-mob"
                        onClick={() =>
                          props?.onClickHandler(
                            navitem?.fields?.EligibilityStatus?.fields?.Feature
                              ?.value,
                            pageProductType
                          )
                        }
                      >
                        <Text field={navitem?.fields?.heading} />
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </li>
          );
        })}
      </ul>
    </>
  );
};

export default withDatasourceCheck()<SideNavbarProps>(SideNavbar);
