import { useState, useMemo } from "react";
import Select, { components } from "react-select";
import LockIcon from "@mui/icons-material/Lock";

interface StateDropdown {
  stateOptions: any; // Array of state options for the dropdown
  selectedState: string; // Currently selected state
  setSelectedState: any; // Function to set the selected state
  setMultistateTextInd: any;
  entitlement: any; // Object containing entitlement information
}

// Function to sort options based on the input
export const sortOptions = (options: any[], stateInput: any) => {
  return options.sort((a, b) => {
    // Check if the option labels start with the input
    const aStartsWithInput = a.label
      .toLowerCase()
      .startsWith(stateInput.toLowerCase());
    const bStartsWithInput = b.label
      .toLowerCase()
      .startsWith(stateInput.toLowerCase());

    // Check if the option values match the input exactly
    const aIsExactMatch = a.value.toLowerCase() === stateInput.toLowerCase();
    const bIsExactMatch = b.value.toLowerCase() === stateInput.toLowerCase();

    // Sorting logic based on the conditions
    if (aIsExactMatch && !bIsExactMatch) {
      return -1;
    } else if (!aIsExactMatch && bIsExactMatch) {
      return 1;
    } else if (aStartsWithInput && !bStartsWithInput) {
      return -1;
    } else if (!aStartsWithInput && bStartsWithInput) {
      return 1;
    } else {
      return a.label.localeCompare(b.label);
    }
  });
};

const StateDropdown = ({
  stateOptions,
  selectedState,
  setSelectedState,
  setMultistateTextInd,
  entitlement,
}: StateDropdown) => {
  const [stateInput, setStateInput] = useState(""); // State for input in the dropdown

  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
      border: 0,
      boxShadow: "none",
    }),
  };

  const removeStates: string[] = ["AS", "FM", "FED", "MH", "MP", "PW"];
  stateOptions = stateOptions.filter(
    (item: { value: string }) => !removeStates.includes(item.value)
  );

  const entitlementStates = Object.keys(entitlement);

  const entitledStateOptions = stateOptions.filter((item: any) =>
    entitlementStates.includes(item.value)
  );

  const nonEntitledStates = stateOptions.filter(
    (item: any) => !entitlementStates.includes(item.value)
  );

  let nonEntitledStateOptions = nonEntitledStates.map((item: any) => ({
    label: item.label,
    value: item.value,
    icon: <LockIcon />,
    isDisabled: true,
  }));

  const allStateOptions = useMemo(() => {
    const sortedEntitledStateOptions = sortOptions(
      entitledStateOptions,
      stateInput
    );
    const sortedNonEntitledStateOptions = sortOptions(
      nonEntitledStateOptions,
      stateInput
    );

    return [
      { options: sortedEntitledStateOptions },
      { options: sortedNonEntitledStateOptions },
    ];
  }, [entitledStateOptions, nonEntitledStateOptions, stateInput]);

  const handleInput = (inputState: any) => {
    setStateInput(inputState);
  };

  const { Option } = components;
  const IconOption = (props: any) => {
    const { label, icon } = props.data;
    const startIndex = label.toLowerCase().indexOf(stateInput.toLowerCase());
    const endIndex = startIndex + stateInput.length;

    if (typeof window !== "undefined" && window.digitalData?.product?.FI) {
      window.digitalData.product.FI.filing_ID = "";
      window.digitalData.product.FI.form_number = "";
      window.digitalData.product.FI.rule_number = "";
      window.digitalData.product.FI.filing_topic = "";
      window.digitalData.product.FI.service_type = "";
      window.digitalData.product.FI.LOB = "";
    }

    return (
      <Option {...props}>
        {icon && <span>{icon}</span>}
        {startIndex !== -1 ? (
          <span>
            {label.substring(0, startIndex)}
            <span style={{ fontWeight: "bold" }}>
              {label.substring(startIndex, endIndex)}
            </span>
            {label.substring(endIndex)}
          </span>
        ) : (
          label
        )}
      </Option>
    );
  };

  return (
    <Select
      aria-labelledby="State dropdown"
      name="state"
      className="select-state"
      instanceId={`select-state ${selectedState}`}
      placeholder={
        <div className="select-state-placeholder">Choose your state</div>
      }
      options={allStateOptions}
      components={{ IndicatorSeparator: () => null, Option: IconOption }}
      maxMenuHeight={140}
      styles={reactSelectStyle}
      onChange={(e: any) => {
        setSelectedState(e.value);
        setMultistateTextInd(false);
        window.digitalData.page.pageInfo.page_jurisdiction = e.value;
        window.digitalData.product.FI.jurisdiction = e.value;

        let evt = new CustomEvent("event-view-end");
        const selectStateElements =
          document.getElementsByClassName("select-state");
        if (selectStateElements.length > 0) {
          selectStateElements[0].dispatchEvent(evt);
        }
      }}
      defaultValue={entitledStateOptions[0]}
      onInputChange={handleInput}
      value={entitledStateOptions.find(
        (option: any) => option.value === selectedState
      )}
      inputValue={stateInput}
    />
  );
};

export default StateDropdown;
