import {
  Link,
  Field,
  withDatasourceCheck,
} from "@sitecore-jss/sitecore-jss-nextjs";
import { ComponentProps } from "lib/component-props";

type SubHeaderProps = ComponentProps & {
  fields: {
    heading: Field<string>;
  };
};

const SubHeader = (props: any): JSX.Element => {
  return (
    <>
      <div className="sub-header">
        <nav>
          <div className="site flex-wrapper">
            <Link
              data-interaction="click"
              data-title={props?.fields?.TitleWithLink?.value?.text}
              data-region="SubHeader"
              field={props?.fields?.TitleWithLink}
            />
          </div>
        </nav>
      </div>
    </>
  );
};

export default withDatasourceCheck()<SubHeaderProps>(SubHeader);
