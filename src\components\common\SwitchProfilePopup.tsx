import React, { useState, useEffect, useContext, useCallback } from "react";
import { AuthContext } from "src/context/authContext";
import { useRouter } from "next/router";
import {
  checkProductAvailability,
  checkIsServiceLine,
} from "components/Platform/utils/profileUtils";
import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";

import Cookies from "js-cookie";
import Loader from "./Loader";
type Profile = {
  customerName: string;
  customerNumber: string;
};

type Props = {
  setShowSwitchProfilePopup?: (show: boolean) => void;
  setShowSwitchProfLinkInHead?: (show: boolean) => void;
  showSwitchProfilePopup?: boolean;
  product?: string;
  props?: any;
  fields?: any;
};

const SwitchProfilePopup = ({
  product = "",
  setShowSwitchProfLinkInHead = () => {},
  showSwitchProfilePopup = false,
  setShowSwitchProfilePopup = () => {},
  ...props
}: Props): React.JSX.Element | null => {
  const isProfileSwitchEnable: any =
    props?.fields?.data?.item?.IsProfileSwitchingEnabled?.boolValue || false;
  const fields: any = props?.fields;
  const currentPageProduct: any = checkProductAvailability(product);
  const [savedProfile, setSavedProfile] = useState<Profile | null>(null);
  const [temporarySelectedProfile, setTemporarySelectedProfile] =
    useState<Profile | null>(null);
  const [profileList, setProfileList] = useState<Profile[]>([]);
  const { userProductProfilesList = [] } = useContext(AuthContext) || {};
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const loadSavedProfile = () => {
      const selectedProfiles = JSON.parse(
        localStorage.getItem("selectedProfiles") ?? "{}"
      );
      const savedProfile = selectedProfiles[currentPageProduct];
      setSavedProfile(savedProfile);
      setTemporarySelectedProfile(savedProfile);
    };

    loadSavedProfile();
    return () => {
      setShowSwitchProfLinkInHead(false);
      setShowSwitchProfilePopup(false);
    };
  }, [currentPageProduct, router?.asPath]);

  useEffect(() => {
    const getProfile = () => {
      const Feature: any = checkIsServiceLine(currentPageProduct)
        ? "Basic Line Service"
        : currentPageProduct;
      const profileArray =
        userProductProfilesList?.find(
          (profile: any) => profile.feature === Feature
        )?.customers || [];
      if (profileArray?.length === 1) {
        const profile: any = profileArray?.[0];
        const selectedProfiles = JSON.parse(
          localStorage.getItem("selectedProfiles") ?? "{}"
        );
        selectedProfiles[currentPageProduct] = {
          ...profile,
        };
        localStorage.setItem(
          "selectedProfiles",
          JSON.stringify(selectedProfiles)
        );
        Cookies.set("selectedProfiles", JSON.stringify(selectedProfiles), {
          path: "/",
          sameSite: "strict",
          expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        });
      }
      if (isProfileSwitchEnable) {
        if (profileArray?.length > 1) {
          setProfileList(profileArray);
          setShowSwitchProfLinkInHead(true);
          const visitedPages = JSON.parse(
            localStorage.getItem("visitedPages") ?? "{}"
          );
          if (!visitedPages[currentPageProduct]) {
            const selectedProfiles = JSON.parse(
              localStorage.getItem("selectedProfiles") ?? "{}"
            );
            visitedPages[currentPageProduct] =
              !!selectedProfiles[currentPageProduct];
            localStorage.setItem("visitedPages", JSON.stringify(visitedPages));
            if (!selectedProfiles[currentPageProduct]) {
              setShowSwitchProfilePopup(true);
            }
          }
        }
      } else {
        setShowSwitchProfilePopup(false);
        setShowSwitchProfLinkInHead(false);
      }
    };
    getProfile();
  }, [
    currentPageProduct,
    router.asPath,
    isProfileSwitchEnable,
    setShowSwitchProfLinkInHead,
    setShowSwitchProfilePopup,
    userProductProfilesList,
  ]);

  const handleProfileSelect = useCallback((profile: Profile) => {
    setTemporarySelectedProfile(profile);
  }, []);

  const handleSaveProfile = useCallback(async () => {
    if (temporarySelectedProfile) {
      setIsLoading(true);
      setSavedProfile(temporarySelectedProfile);

      const selectedProfiles = JSON.parse(
        localStorage.getItem("selectedProfiles") ?? "{}"
      );
      selectedProfiles[currentPageProduct] = {
        ...temporarySelectedProfile,
      };

      localStorage.setItem(
        "selectedProfiles",
        JSON.stringify(selectedProfiles)
      );
      Cookies.set("selectedProfiles", JSON.stringify(selectedProfiles), {
        path: "/",
        sameSite: "strict",
        expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      });

      const visitedPages = JSON.parse(
        localStorage.getItem("visitedPages") ?? "{}"
      );

      if (!visitedPages[currentPageProduct]) {
        visitedPages[currentPageProduct] = true;
        localStorage.setItem("visitedPages", JSON.stringify(visitedPages));
      }

      if (typeof window !== "undefined") window.location.reload();
    }
  }, [temporarySelectedProfile, currentPageProduct, setShowSwitchProfilePopup]);

  if (!showSwitchProfilePopup) return null;

  return (
    <>
      {isLoading && (
        <div className="modal-container-loader">
          <Loader />
        </div>
      )}
      {!isLoading && (
        <div className="modal-container-switch-popup">
          <div className="profile-modal">
            <div className="profile-content">
              <ul className="link-list">
                <li>
                  <Text
                    className="heading"
                    field={fields?.data?.popupMessage?.SelectProfile}
                    tag="h2"
                    data-testid="select-profile-head"
                  />
                </li>
                <li>
                  <RichText
                    className="description"
                    field={fields?.data?.popupMessage?.Message}
                    tag="p"
                  />
                </li>
              </ul>
              <ul className="link-list scrollable-list">
                {profileList.map(
                  ({ customerName, customerNumber, ...rest }) => (
                    <li
                      key={customerNumber}
                      className={
                        temporarySelectedProfile?.customerNumber ===
                        customerNumber
                          ? "choosen-profile"
                          : "profile"
                      }
                    >
                      <button
                        onClick={() =>
                          handleProfileSelect({
                            customerName,
                            customerNumber,
                            ...rest,
                          })
                        }
                        data-testid="profile-anchor"
                        className="profile-link"
                      >
                        {customerName}
                        {savedProfile?.customerNumber === customerNumber &&
                          " *Current Profile"}
                      </button>
                    </li>
                  )
                )}
              </ul>
            </div>
            <div className="call-to-action">
              <button
                className="primary"
                type="button"
                data-testid="select-profile-btn"
                onClick={handleSaveProfile}
              >
                Select Profile
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SwitchProfilePopup;
