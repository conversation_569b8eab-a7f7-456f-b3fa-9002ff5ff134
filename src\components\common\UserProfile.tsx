import { useContext, useEffect } from "react";
import { AuthContext } from "src/context/authContext";
import { useRouter } from "next/router";
import {
  checkIsServiceLine,
  checkProductAvailability,
} from "components/Platform/utils/profileUtils";
// Function to check if the default profile's dft_cst_id is empty
const isDefaultProfileEmpty = (profile: any) => {
  return !profile?.dft_cst_id; // Check if dft_cst_id is falsy
};

//For analytics, we need to track the user's profile information.
export const defaultProfileAnalyticsData: any = (profile: any) => {
  if (typeof window !== "undefined" && window?.digitalData !== undefined) {
    const { dft_cst_id = "", dft_cst_ird = "", highestParent = "" } = profile;
    window.digitalData.user.info.customerid = dft_cst_id;
    window.digitalData.user.info.highestparentcompanyid = highestParent;
    window.digitalData.user.info.ird = dft_cst_ird;
  }
};

const UserProfile = ({ product }: any): JSX.Element | null => {
  const router = useRouter();
  const { defaultUserProfile = {}, requiresLogin = true as boolean } =
    useContext(AuthContext) || {};

  useEffect(() => {
    const loadDefaultProfile = () => {
      const isErrorPage =
        router?.asPath?.toLocaleLowerCase()?.includes("404") ||
        router?.asPath?.toLocaleLowerCase()?.includes("access-denied");
      // Only proceed if login is not required and it's not an error page
      if (
        requiresLogin ||
        isErrorPage ||
        checkIsServiceLine(checkProductAvailability(product))
      ) {
        return;
      } else if (isDefaultProfileEmpty(defaultUserProfile)) {
        defaultProfileAnalyticsData({});
      } else {
        defaultProfileAnalyticsData(defaultUserProfile);
      }
    };
    loadDefaultProfile();
  });

  return null; // No model to render
};

export default UserProfile;
