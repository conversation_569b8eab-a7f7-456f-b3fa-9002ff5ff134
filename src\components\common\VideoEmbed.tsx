import React, { useEffect, useRef } from "react";
import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";

declare global {
  interface Window {
    KalturaPlayer: any;
  }
}

export default function VideoEmbed(props: any) {
  const kalturaEmbedCode =
    props?.rendering?.fields?.["Embed code"]?.value ?? "";

  console.log("Kaltura Embed Code:", kalturaEmbedCode); // Debugging log

  const DescriptionData: any = props?.fields?.Description;
  const TitleData: any = props?.fields?.Title;
  const kalturaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (kalturaRef.current && kalturaEmbedCode) {
      kalturaRef.current.innerHTML = kalturaEmbedCode;

      const scripts = Array.from(
        kalturaRef.current.getElementsByTagName("script")
      );

      scripts.forEach((script) => {
        const newScript = document.createElement("script");
        newScript.type = "text/javascript";

        if (script.src) {
          newScript.src = script.src;
          newScript.async = true;
          newScript.onload = () => {
            console.log("Script loaded successfully");
            initializeKalturaPlayer();
          };
          document.body.appendChild(newScript);
        } else {
          newScript.textContent = script.textContent;
          document.body.appendChild(newScript);
        }

        script.parentNode?.removeChild(script);
      });

      const initializeKalturaPlayer = () => {
        const kalturaDiv = kalturaRef.current?.querySelector(
          "div[id^='kaltura_player_']"
        );

        if (!kalturaDiv) {
          console.error("Kaltura target div not found");
          return;
        }

        // 🧽 Remove inline styles
        kalturaDiv.removeAttribute("style");

        const targetId = kalturaDiv.id;

        const partnerIdMatch = kalturaEmbedCode.match(/partnerId:\s*(\d+)/);
        const uiConfIdMatch = kalturaEmbedCode.match(/uiConfId:\s*(\d+)/);
        const entryIdMatch = kalturaEmbedCode.match(
          /entryId:\s*['"]([^'"]+)['"]/
        );

        const partnerId = partnerIdMatch
          ? parseInt(partnerIdMatch[1], 10)
          : undefined;
        const uiConfId = uiConfIdMatch
          ? parseInt(uiConfIdMatch[1], 10)
          : undefined;
        const entryId = entryIdMatch ? entryIdMatch[1] : undefined;

        if (!window.KalturaPlayer || !partnerId || !uiConfId || !entryId) {
          console.error("Missing Kaltura player config", {
            partnerId,
            uiConfId,
            entryId,
          });
          return;
        }

        try {
          const kalturaPlayer = window.KalturaPlayer.setup({
            targetId,
            provider: {
              partnerId,
              uiConfId,
            },
          });
          kalturaPlayer.loadMedia({ entryId });
        } catch (e) {
          console.error("Kaltura Initialization Error:", e);
        }
      };
    }
  }, [kalturaEmbedCode]);

  return (
    <div className="video-container" data-testid="video-container">
      <h3>
        <Text field={TitleData} />
      </h3>
      <RichText field={DescriptionData} />
      <div ref={kalturaRef}></div>
    </div>
  );
}
