import { useState, useEffect } from "react";
import DetailSectionTabs from "./common/DetailSectionTabs";
import { AnimateChangeInHeight } from "./common/AnimateChangeInHeight";
import NotSubscribedMessage from "./common/NotSubscribedMessage";
import ReadFullContent from "./common/ReadFullContent";
import PreviewDocuments from "./common/PreviewDocuments";

const ClassPlanTab = (props: {
  staticData: any;
  data: any;
  selectedState: string;
  entitlement: any;
}) => {
  const { data, selectedState, entitlement } = props;
  const [selectedItem, setSelectedItem] = useState<any>("");
  const [selectedDetail, setSelectedDetail] = useState("");

  useEffect(() => {
    if (selectedItem !== "") {
      setSelectedDetail(selectedItem?.third_level[0].id);
    }
  }, [selectedItem]);

  const listData = data.class_plan_relativities?.map((item: any) => [
    item.id,
    item.second_level,
  ]);

  const staticData = props.staticData.ClassPlanTabData;
  const summaryStaticData = props.staticData.Summary;

  const clasPlanTab =
    selectedItem !== "" &&
    selectedItem?.third_level?.length > 0 &&
    selectedItem?.third_level.map((item: any) => [item.id, null]);

  const filterList =
    selectedItem !== "" &&
    selectedItem?.third_level?.length > 0 &&
    selectedItem?.third_level.filter((item: any) => selectedDetail === item.id);

  const tabInfo = () => {
    const tabListInfo = filterList[0]?.html_file_name
      ? [
          {
            html_file_name: filterList[0]?.html_file_name,
            id: filterList[0]?.id,
          },
        ]
      : filterList[0]?.fourth_level;

    return (
      <div className="classplan-detail-content">
        {tabListInfo?.length > 0 &&
          tabListInfo?.map((item: any, index: any) => {
            return (
              <div key={index}>
                <span className="classplan-detail-header">{item.id}</span>
                {item.html_file_name !== null && (
                  <ReadFullContent
                    key={selectedDetail}
                    orderIndex={index}
                    contentClassName={"summary"}
                    label={""}
                    topicName={item.id}
                    content={item.html_file_name}
                    lineClamp={tabListInfo?.length < 2 ? false : 3}
                    expandLabel={
                      summaryStaticData.ReadFullExplanationOfChanges.value
                    }
                    collapseLabel={
                      summaryStaticData.CollapseExplanationOfChanges.value
                    }
                  />
                )}
              </div>
            );
          })}
      </div>
    );
  };

  const ListItem = ({ item, itemIndex }: { item: any; itemIndex: number }) => {
    return (
      <>
        <li
          key={data.filing_set.lob + itemIndex}
          tabIndex={0}
          onKeyUp={(ev) => {
            if (ev.key === "Enter" || ev.key === " ") {
              setSelectedItem(selectedItem === item ? "" : item);
            }
          }}
          className={`${"list-section-info"} flex-wrapper ${
            selectedItem.id === item.id ? "selected-item" : ""
          }`}
          onClick={() => {
            setSelectedItem(selectedItem === item ? "" : item);
            let evt = new CustomEvent("event-view-end");
            document.body.dispatchEvent(evt);

            let evt_details = new CustomEvent("event-detailBox-view");
            document.body.dispatchEvent(evt_details);
          }}
          data-testid={`list-section-info-${item.id}`}
        >
          <div
            data-testid="list-section-number-title"
            className="list-section-number-title"
          >
            <span className="list-section-number">{item.id}</span>
          </div>
        </li>
        {selectedItem !== "" && selectedItem?.id === item.id && (
          <div className="selected-item-content">
            <DetailSectionTabs
              tabNames={clasPlanTab}
              tabsInfo={tabInfo()}
              selectedDetail={selectedDetail}
              setSelectedDetail={setSelectedDetail}
              selectedState={selectedState}
              entitlement={entitlement}
            />
          </div>
        )}
      </>
    );
  };

  const FilingIdText = props.staticData.ListSectionData.FilingIdText.value;
  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type === "CNTSRV_LSC"
  )[0].filing_id;

  if (typeof window !== "undefined" && window.digitalData?.product?.FI) {
    window.digitalData.product.FI.filing_ID = filingId;
  }

  return (
    <div className="classplan-tab-content" data-testid="classplan-tab-content">
      <div className="classplan-wrapper">
        <div className="classplan-list-pane" data-testid="classplan-list-pane">
          {entitlement?.[selectedState]?.["CNTSRV_LSC"] === 1 ? (
            <>
              <div
                className="list-section-list-wrapper"
                data-testid="list-section-list-wrapper"
              >
                <div
                  className="list-section-filing-id"
                  data-testid="list-section-filing-id"
                >
                  <div>
                    Loss Costs {FilingIdText} {filingId}
                    <span className="about-filing">
                      {" "}
                      <PreviewDocuments
                        key={1}
                        data={data}
                        staticData={props.staticData}
                        tab={"loss_costs"}
                        filingId={filingId}
                        buttonValue="About this Filing"
                        selectedState={selectedState}
                      />
                    </span>
                  </div>
                </div>
                <ul
                  className="list-section-list"
                  data-testid="list-section-list"
                >
                  <>
                    {listData?.map(
                      ([group, groupItems]: any, index: number) => (
                        <AnimateChangeInHeight key={index}>
                          <details
                            className="list-group"
                            key={index}
                            onToggle={() => {
                              if (
                                groupItems
                                  .map(({ id }: { id: string }) => id)
                                  .includes(selectedItem)
                              )
                                setSelectedItem("");
                            }}
                          >
                            <summary className="list-grouping-header">
                              {group}
                            </summary>
                            {groupItems.map((item: any) => (
                              <ListItem
                                key={index}
                                item={item}
                                itemIndex={index}
                              />
                            ))}
                          </details>
                        </AnimateChangeInHeight>
                      )
                    )}
                  </>
                </ul>
              </div>
            </>
          ) : (
            <NotSubscribedMessage splitColumn={false} />
          )}
        </div>
        <div
          className={`classplan-detail ${
            selectedItem === "" ? "fi-accordion-data" : ""
          }`}
          data-testid="classplan-detail"
        >
          {entitlement?.[selectedState]?.["CNTSRV_LSC"] === 1 ? (
            selectedItem === "" ? (
              <>
                <div className="classplan-default-text">
                  <div className="classplan-description">
                    <span data-testid="classplan-default-text">
                      {staticData?.ClassPlanTabHighlightedText.value}
                    </span>{" "}
                    {staticData?.ClassPlanTabText.value}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="selected-classplan-item flex-wrapper">
                  <span className="selected-classplan-item-text">
                    <b>{selectedItem !== "" && selectedItem.id}</b>
                  </span>
                </div>
                <DetailSectionTabs
                  tabNames={clasPlanTab}
                  tabsInfo={tabInfo()}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              </>
            )
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default ClassPlanTab;
