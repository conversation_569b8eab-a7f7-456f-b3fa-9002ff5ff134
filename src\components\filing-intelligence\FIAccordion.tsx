import { useContext, useMemo } from "react";
import { AuthContext } from "../../context/authContext";
import Button from "@mui/material/Button";
import DownloadIcon from "@mui/icons-material/Download";
import { download } from "src/helpers/fi/documentDownload";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";
import PreviewDocuments from "./common/PreviewDocuments";

interface FIAccordion {
  staticData: any;
  filingData: any;
  content: any;
  data: any;
  tab?: any;
  selectedState?: string;
}

interface FilingData {
  event_id: string;
  filing_status:
    | "STATUS_PENDING"
    | "STATUS_CORRECTED"
    | "STATUS_APPROVED"
    | string;
  eff_date?: string;
}

const FIAccordion = ({
  staticData,
  data,
  filingData,
  content,
  tab,
  selectedState,
}: FIAccordion) => {
  const { accessToken } = useContext(AuthContext);

  const isCriticalUpdate = content?.value === "critical-update";
  const isHistoryUpdate = content?.value === "history-data";

  const btnCheck = (circularNumber: string, docType: string) => {
    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};

    return docType === "pdf" ? file_path : file_zip_path;
  };

  const handleDownloadCircular = async (
    docType: string,
    circular_number: string
  ) => {
    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circular_number
      )[0] || {};

    const circularFilePath = docType === "pdf" ? file_path : file_zip_path;

    await download({
      api_url:
        docType === "pdf"
          ? process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL ?? ""
          : process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_GATEWAY_URL ?? "",
      urlToPreSign: {
        url: circularFilePath,
        type: "CRC",
      },
      headers: { Authorization: accessToken },
      fileName: circularFilePath.substring(
        circularFilePath.lastIndexOf("/") + 1
      ),
      successMessage: (
        <ToastMessage
          type="success"
          title={staticData.RevisionData.SuccessTitle.value}
          description={staticData.RevisionData.SuccessMessage.value}
        />
      ),
      errorMessage: (
        <ToastMessage
          type="error"
          title={staticData.RevisionData.ErrorTitle.value}
          description={staticData.RevisionData.ErrorMessage.value}
        />
      ),
    });
  };

  const priority = {
    STATUS_PENDING: 1,
    STATUS_CORRECTED: 2,
    STATUS_APPROVED: 3,
  };

  filingData?.sort((a: FilingData, b: FilingData) => {
    const getPriority = (status: string): number => {
      return priority[status as keyof typeof priority] ?? 4;
    };

    return getPriority(a.filing_status) - getPriority(b.filing_status);
  });

  const getCircularNumber = (item: any) => {
    if (isHistoryUpdate) {
      return item.event_id;
    } else if (isCriticalUpdate) {
      return item.id;
    } else {
      return item.circular_number;
    }
  };

  const getCircularTitle = (item: any) => {
    if (isCriticalUpdate) {
      return "Circular";
    }

    if (isHistoryUpdate) {
      switch (item.filing_status) {
        case "STATUS_PENDING":
          return "Filed Circular";
        case "STATUS_APPROVED":
          return "Implementation Circular";
        case "STATUS_CORRECTED":
          return "Amendment Circular";
        default:
          return "Circular";
      }
    }

    return "Circular Number";
  };

  const getParentFiling = (criticalCircularId: string) => {
    const parentFiling = data.filing_document_list.find(
      (item: { critical_updates: any }) =>
        item?.critical_updates?.some(
          (item: { critical_update_id: string }) =>
            item?.critical_update_id === criticalCircularId
        )
    );
    return parentFiling;
  };

  const fiAccordianFilingClass = useMemo(() => {
    if (isCriticalUpdate) {
      return "fi-accordion-filing-text";
    } else if (isHistoryUpdate) {
      return "fi-accordion-filing-history";
    } else {
      return "fi-accordion-filing-circular";
    }
  }, [isCriticalUpdate, isHistoryUpdate]);

  return (
    <div className="accordion-content active" data-testid="accordion-content">
      {/* content */}
      {filingData?.map((item: any) => {
        let circNumber = getCircularNumber(item);
        let circTitle = getCircularTitle(item);
        return (
          <>
            {!isCriticalUpdate && !isHistoryUpdate && (
              <div className="rules-filing-id">
                {"Filing Id"}:{" "}
                <span className="rules-detail-value">
                  {item.parent_filing_id}
                </span>
              </div>
            )}
            {isCriticalUpdate && (
              <>
                <span className="fi-accordion-filing-key-message rules-detail-value">
                  Update to Original Circular:{" "}
                  {getParentFiling(circNumber).circular_number}{" "}
                </span>
                <Button
                  aria-label="Download Word Doc"
                  className="circular-download-btn"
                  data-testid="parent-circular-download-btn-doc"
                  data-action="download"
                  data-interaction="fi_download3"
                  data-circNumber={getParentFiling(circNumber).circular_number}
                  data-circStatus={circTitle}
                  data-format="word"
                  variant="text"
                  endIcon={<DownloadIcon />}
                  onClick={() =>
                    handleDownloadCircular(
                      "doc",
                      getParentFiling(circNumber).circular_number
                    )
                  }
                >
                  Word Doc
                </Button>
                <Button
                  aria-label="Download PDF"
                  className="circular-download-btn"
                  data-testid="parent-circular-download-btn-pdf"
                  data-action="download"
                  data-interaction="fi_download3"
                  data-circNumber={getParentFiling(circNumber).circular_number}
                  data-circStatus={circTitle}
                  data-format="pdf"
                  variant="text"
                  endIcon={<DownloadIcon />}
                  onClick={() =>
                    handleDownloadCircular(
                      "pdf",
                      getParentFiling(circNumber).circular_number
                    )
                  }
                >
                  PDF
                </Button>
                <div className="fi-accordion-filing-key-message">
                  {"Key Message"}:<br />
                  <span className="rules-detail-value">{item.key_message}</span>
                </div>
              </>
            )}
            <div className={fiAccordianFilingClass}>
              {circTitle}:{" "}
              <span className="rules-detail-value">{circNumber} </span>
              {isCriticalUpdate && btnCheck(circNumber, "pdf") && (
                <>
                  <Button
                    aria-label="Preview PDF"
                    className="circular-preview-btn"
                    data-testid="circular-preview-btn-pdf"
                    data-action="preview"
                    data-interaction="fi_download3"
                    data-circNumber={circNumber}
                    data-circStatus={circTitle}
                    data-format="pdf"
                    variant="text"
                  >
                    <span
                      className="critical-pdf-preview-icon"
                      data-testid="critical-pdf-preview-icon"
                    >
                      <PreviewDocuments
                        key={1}
                        data={data}
                        staticData={staticData}
                        tab={tab}
                        selectedState={selectedState ?? ""}
                        buttonValue="Preview"
                        document_list={[
                          {
                            file_path: btnCheck(circNumber, "pdf"),
                            doc_status: "filed",
                          },
                        ]}
                      />
                    </span>
                  </Button>
                </>
              )}
              {btnCheck(circNumber, "doc") && (
                <Button
                  aria-label="Download Word Doc"
                  className="circular-download-btn"
                  data-testid="circular-download-btn-doc"
                  data-action="download"
                  data-interaction="fi_download3"
                  data-circNumber={circNumber}
                  data-circStatus={circTitle}
                  data-format="word"
                  variant="text"
                  endIcon={<DownloadIcon />}
                  onClick={() => handleDownloadCircular("doc", circNumber)}
                >
                  Word Doc
                </Button>
              )}
              {btnCheck(circNumber, "pdf") && (
                <Button
                  aria-label="Download PDF"
                  className="circular-download-btn"
                  data-testid="circular-download-btn-pdf"
                  data-action="download"
                  data-interaction="fi_download3"
                  data-circNumber={circNumber}
                  data-circStatus={circTitle}
                  data-format="pdf"
                  variant="text"
                  endIcon={<DownloadIcon />}
                  onClick={() => handleDownloadCircular("pdf", circNumber)}
                >
                  PDF
                </Button>
              )}
            </div>
          </>
        );
      })}
    </div>
  );
};

export default FIAccordion;
