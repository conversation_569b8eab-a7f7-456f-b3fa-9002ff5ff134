import { useEffect, useContext, useState } from "react";
import { useRouter } from "next/router";
// import { pdfjs } from "react-pdf";
import Revision from "./Revision";
import { AuthContext } from "../../context/authContext";
// import CAData from "../../data/fi/CA.json";
// import HOData from "../../data/fi/HO.json";
// import BPData from "../../data/fi/BP.json";
// import GLData from "../../data/fi/GL.json";
// import FRData from "../../data/fi/FR.json";
// import jurisdictionData from "../../data/fi/jurisdiction.json";
// import sitecoreData from "../../data/fi/sitecoreData.json";
// import entitlement from "../../data/fi/entitlementData.json";
import { getPreSignedUrls } from "src/helpers/fi/documentDownload";
// import Loader from "components/common/Loader";
import { initDB, addStoreData, getStoreData } from "src/helpers/fi/fiCache";
import axios from "axios";
import axiosRetry from "axios-retry";
import RevisionSkeletonLoader from "./common/RevisionSkeletonLoader";

axiosRetry(axios, {
  retries: 3,
  // retryDelay: () => 1 * 1000,
  retryCondition: (error) => {
    return error?.response?.status !== 200;
  },
});

interface FilingIntelligence {
  fields: {
    data: {
      CADynamicData: any;
      HODynamicData: any;
      states: any;
    };
  };
  selectedLOB: string;
}

const FilingIntelligence = (props: FilingIntelligence) => {
  const router = useRouter();
  const route = router.asPath.replace(/\//g, "");

  const indexedDBStoreList =
    {
      filingintelligence: "FI",
      "filingintelligence-stg": "FI-STG",
    }[route.toLowerCase().replace(/#.*/, "")] || "";

  const staticData = JSON.parse(JSON.stringify(props.fields.data));
  delete staticData.CADynamicData;
  delete staticData.HODynamicData;

  const { entitlementByCustomer, accessToken } = useContext(AuthContext);

  const selectedProfiles = JSON.parse(
    localStorage.getItem("selectedProfiles") || "{}"
  );

  const { customerNumber } = selectedProfiles["Filing Intelligence"] || {};

  const entitlement = entitlementByCustomer?.find(
    (item: { customerNumber: number }) => item.customerNumber === customerNumber
  )?.customerLIParticipation;

  const CAEntitlement = entitlement?.LOB_CA ?? false;
  const HOEntitlement = entitlement?.LOB_HO ?? false;
  const BPEntitlement = entitlement?.LOB_BP ?? false;
  const FREntitlement = entitlement?.LOB_FR ?? false;

  // const [CAData, setCAData] = useState<any>(null);
  // const [HOData, setHOData] = useState<any>(null);
  // const [BPData, setBPData] = useState<any>(null);
  const [data, setData] = useState<any>({ jsonData: [] });
  const dataRefreshInterval = 1000 * 60 * 30;

  useEffect(() => {
    const loadPdfJs = async () => {
      const { pdfjs } = await import("react-pdf");
      pdfjs.GlobalWorkerOptions.workerSrc = new URL(
        "pdfjs-dist/build/pdf.worker.min.js",
        import.meta.url
        // `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`
      ).toString();
    };
    loadPdfJs();
  }, []);

  const revisionSetData =
    staticData.RevisionDate?.LOB_DATA?.targetItems?.flatMap((item: any) =>
      item.url.value.split(",").map((url: string) => ({
        revision: item.revison.value,
        url: url.trim(),
        lob: item.lob.value,
      }))
    );

  // const setDataByRevision = (revision: string, data: any) => {
  //   if (revision === "CA") setCAData(data);
  //   else if (revision === "HO") setHOData(data);
  //   else if (revision === "BP") setBPData(data);
  // };

  useEffect(() => {
    setData({ jsonData: [] });

    const jsonResp = async () => {
      if (!indexedDBStoreList) return;
      await initDB();
      // const { revision, url }: any = revisionSetData.find(
      //   (item: any) => item.lob === props.selectedLOB
      // ) || { revision: "", url: "" };
      const filteredRevisionSetData =
        revisionSetData.filter((item: any) => item.lob === props.selectedLOB) ||
        [];
      // revisionSetData.forEach(async ({ revision, url }) => {
      filteredRevisionSetData.forEach(async ({ revision, url }: any) => {
        // let storeData = await getStoreData("FI", revision);
        let storeData = await getStoreData(indexedDBStoreList, revision);

        if (
          (!storeData ||
            Date.now() > storeData.updatedDateTime + dataRefreshInterval) &&
          revision !== ""
        ) {
          getPreSignedUrls({
            api_url: process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_GATEWAY_URL || "",
            urlsToPreSign: [{ url: url, type: "JSON" }],
            headers: {
              Authorization: accessToken,
              "Cache-Control": "force-cache, max-age=43200",
            },
          })
            .then((pre) => {
              if (pre[0] === "Error") throw new Error();

              axios
                .get(pre[0].preSignedUrl, {
                  headers: { "Cache-Control": "force-cache, max-age=43200" },
                })
                .then(async ({ data: jsonData }: any) => {
                  setData((prev: any) => {
                    const updatedData = {
                      ...prev,
                      jsonData: [...prev.jsonData, jsonData],
                    };
                    addStoreData(indexedDBStoreList, {
                      id: revision,
                      updatedDateTime: Date.now(),
                      jsonData: [...prev.jsonData, jsonData],
                    });
                    return updatedData;
                  });
                })
                .catch(() => {
                  setData("error");
                });
            })
            .catch(() => {
              setData("error");
            });
        } else {
          setData(storeData);
        }
      });
    };
    jsonResp();
  }, [props.selectedLOB]);

  const filteredLOBDetails = props.selectedLOB !== "" && props.selectedLOB;

  function renderRevision(data: any, entitlement: any, lobType: string) {
    if (data?.jsonData?.length === 0) {
      return <RevisionSkeletonLoader />;
    } else if (data === "error") {
      return <p>{`${lobType} Revision Data Loading failed`}</p>;
    } else if (filteredLOBDetails === lobType) {
      return (
        <>
          {data?.jsonData?.map((item: any) => (
            <Revision
              key={item.filing_set.id}
              data={item}
              staticData={staticData}
              entitlement={entitlement}
              filteredLOBDetails={filteredLOBDetails}
            />
          ))}
        </>
      );
    }
    return null;
  }

  return (
    <>
      <hr className="revision-seperator" data-testid="revision-seperator" />
      {
        {
          LOB_BP: renderRevision(data, BPEntitlement, "LOB_BP"),
          LOB_CA: renderRevision(data, CAEntitlement, "LOB_CA"),
          LOB_HO: renderRevision(data, HOEntitlement, "LOB_HO"),
          LOB_FR: renderRevision(data, FREntitlement, "LOB_FR"),
        }[filteredLOBDetails as string]
      }
      <hr className="revision-seperator" />
    </>
  );
};

export default FilingIntelligence;
