import { useState, useEffect, useContext, useMemo } from "react";
import DownloadCircularButton from "./common/DownloadCircularButton";
import SubDetails from "./common/SubDetails";
import ListSection from "./common/ListSection";
import DetailSectionTabs from "./common/DetailSectionTabs";
import CorrespondingRules from "./common/CorrespondingRules";
import CorrespondingLossCosts from "./common/CorrespondingLossCosts";
import PreviewDocuments from "./common/PreviewDocuments";
import {
  createPropsForPreview,
  getActionStatus,
  getCircularTitle,
} from "src/helpers/fi/utils";
import { getFilingDetails } from "../../helpers/fi/filingDetails";
import ReadFullContent from "./common/ReadFullContent";
import FITimeline from "./common/FITimeline";
import { AuthContext } from "../../context/authContext";
import NotSubscribedMessage from "./common/NotSubscribedMessage";
import Accordion from "components/Platform/article/Accordion";
import { getCriticalUpdates } from "src/helpers/fi/fiAccordion";

const FormsTab = (props: {
  staticData: any;
  data: any;
  selectedState: string;
  entitlement: any;
}) => {
  const { accessToken } = useContext(AuthContext);

  const { data, selectedState, entitlement } = props;
  const staticData = props.staticData.FormsTabData;
  const summaryStaticData = props.staticData.Summary;

  const [listCircularNumbers, setListCircularNumbers] = useState("");
  const [circularFilePath, setCircularFilePath] = useState("");
  const [circularFileZipPath, setCircularFileZipPath] = useState("");
  const [selectedItem, setSelectedItem] = useState("");
  const [refinedDataLength, setRefinedDataLength] = useState(0);
  const [selectedDetail, setSelectedDetail] = useState("Details");
  const result = data.forms.filter(
    ({ id }: { id: string }) => id === selectedItem
  );
  const documentTitle = result[0]?.document_title;
  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)

  const rfcData = data.filings
    .filter(
      ({ service_type }: { service_type: string }) =>
        service_type === "CNTSRV_FRM"
    )[0]
    .edge.filter(
      (item: { rfc_state_list: string[]; dest_content_key: string }) =>
        (selectedState === "MU"
          ? false
          : item.rfc_state_list?.includes(selectedState)) &&
        item.dest_content_key === result[0]?.id
    );

  const actionType: string =
    rfcData.length > 0
      ? "rfc"
      : getActionStatus(props.staticData, result[0]?.action);
  const formType = result[0]?.form_type;
  const mandatoryInd = result[0]?.mandatory_indc === "Y" ? "Yes" : "No";
  const isMultistate = result[0]?.state_type === "MU" ? "Yes" : "No";
  let nonApplicableStates = result[0]?.non_applicable_states;
  nonApplicableStates = nonApplicableStates?.filter(
    (item: string) => item !== "MU"
  );
  const pendingAdoptionStates = result[0]?.pending_adoption_states;
  const documentList = result[0]?.document_list;
  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type === "CNTSRV_FRM"
  )[0].filing_id;
  const summaryList = result[0]?.summary_list;
  let amendmentList = result[0]?.amendment_summary_list;

  const criticalUpdates = getCriticalUpdates(data, selectedState, "CNTSRV_FRM");

  const topicName = (topicId: string) => {
    return (
      data.topics?.find((item: any) => topicId === item.filing_topic_id)
        ?.topic_name || "Miscellaneous"
    );
  };

  let allSummaryList =
    summaryList?.map((item: any) => {
      item.type = "summary";
      return item;
    }) || [];

  let allAmendmentList =
    amendmentList?.map((item: any) => {
      item.label = "amendment";
      return item;
    }) || [];

  let filteredAmendmentJursidiction = allAmendmentList.filter((item: any) =>
    selectedState === "MU" ? item.jurisdiction === selectedState : true
  );

  let allSummaryAndAmendmentList = [
    ...allSummaryList,
    ...filteredAmendmentJursidiction,
  ];

  const filteredSummaryAmendment = allSummaryAndAmendmentList.filter(
    (item: any) =>
      item.jurisdiction === "MU" || item.jurisdiction === selectedState
        ? true
        : false
  );

  const filteredSummaryList =
    filteredSummaryAmendment?.length > 0
      ? summaryList?.filter((item1: any) => {
          return filteredSummaryAmendment?.some(
            (item2: any) =>
              item2?.type === "summary" &&
              item1?.filing_topic_id !== item2?.filing_topic_id
          );
        })
      : summaryList;

  const filteredAmendmentList =
    filteredSummaryAmendment?.length > 0
      ? filteredAmendmentJursidiction?.filter((item1: any) => {
          return filteredSummaryAmendment?.some(
            (item2: any) =>
              item2?.label === "amendment" &&
              item1?.filing_topic_id !== item2?.filing_topic_id
          );
        })
      : filteredAmendmentJursidiction;

  const [filingStatus, setfilingStatus] = useState<string>("");

  if (typeof window !== "undefined") {
    window.digitalData = window.digitalData || {};
    window.digitalData.product = window.digitalData.product || {};
    window.digitalData.product.FI = window.digitalData.product.FI || {};
    window.digitalData.product.FI.form_number = selectedItem ?? "";
    window.digitalData.product.FI.rule_number = "";
  }

  useEffect(() => {
    const filingDetails = getFilingDetails({
      data: data,
      serviceType: "CNTSRV_FRM",
      selectedState: selectedState,
      isMUFiledCircular: true,
    });

    setListCircularNumbers(filingDetails.event_id);

    const circularNumber = filingDetails.event_id;

    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};

    setCircularFilePath(file_path);
    setCircularFileZipPath(file_zip_path);
    setfilingStatus(filingDetails.filing_status);
  }, [selectedState]);

  const summaryAmendmentClass = () => {
    if (filteredSummaryAmendment?.length > 0) {
      if (
        filteredAmendmentList?.length > 0 &&
        filteredSummaryList?.length > 0
      ) {
        return "forms-summary-amendment forms-summary-hr";
      }
    }
    if (filteredSummaryList?.length > 0) {
      return "forms-summary-hr";
    } else if (filteredAmendmentList?.length > 0) {
      return "forms-summary-hr";
    } else {
      return;
    }
  };

  let allFilteredSummaryAmendment = {};

  if (filteredSummaryAmendment && filteredSummaryAmendment.length > 0) {
    allFilteredSummaryAmendment = filteredSummaryAmendment.reduce(
      (acc, item) => {
        if (!acc[item.filing_topic_id]) {
          acc[item.filing_topic_id] = [];
        }
        acc[item.filing_topic_id].push(item);
        return acc;
      },
      {}
    );
  }

  const subDetailsList = [
    {
      label: staticData.FormType.value,
      value: formType,
    },
    { label: staticData.MandatoryInd.value, value: mandatoryInd },
    { label: staticData.IsMultistate.value, value: isMultistate },
    {
      label: staticData.NonApplicableStates.value,
      value: nonApplicableStates?.join(", "),
    },
    {
      label: staticData.PendingAdoptionStates.value,
      value: pendingAdoptionStates?.join(", "),
    },
  ];

  const circularTitle = useMemo(
    () => getCircularTitle(filingStatus),
    [filingStatus]
  );

  const downloadCircularButtonObj = {
    circularFilePath,
    circularFileZipPath,
    accessToken,
    revisionData: props.staticData.RevisionData,
    circularNumber: listCircularNumbers,
    circularTitle,
  };

  const formsDetails = () => (
    <div
      className="forms-detail-content"
      data-testid="forms-detail-content"
      key={selectedItem}
    >
      <div>
        {staticData.ActionType.value}:{" "}
        <span className="forms-detail-value">
          {{
            withdrawn: <span className="forms-action">Withdrawn from use</span>,
            rfc: (
              <span className="forms-action">Removed From Consideration</span>
            ),
          }[actionType?.toLowerCase()] || actionType}
        </span>
        {selectedItem === "" || (
          <span className="forms-preview-document pointer-cursor">
            <PreviewDocuments
              key={selectedItem}
              data={data}
              staticData={props.staticData}
              tab="forms"
              selectedItem={selectedItem}
              selectedState={selectedState}
              document_list={createPropsForPreview({
                jsonData: data,
                section: "Forms",
                state: selectedState,
                key: selectedItem,
                itemDocumentList: documentList,
              })}
              filingStatus={filingStatus}
              filterAction={
                actionType !== undefined && actionType === "Withdrawn"
                  ? actionType
                  : ""
              }
            />
          </span>
        )}
      </div>
      {subDetailsList.map((item, index) => (
        <SubDetails name={"forms-detail-value"} {...item} key={index} />
      ))}

      <div className="forms-filing-id">
        {staticData.FilingId.value}:{" "}
        <span className="forms-detail-value">{filingId}</span>
      </div>
      <div>
        {staticData.ListCircularNumbers.value}:{" "}
        <span className="forms-detail-circular-list">
          {listCircularNumbers}{" "}
        </span>
        <DownloadCircularButton {...downloadCircularButtonObj} />
      </div>
      {allSummaryAndAmendmentList?.length !== 0 ? (
        <>
          {filteredSummaryAmendment.length === 0 && filteredSummaryList && (
            <div className={summaryAmendmentClass()}>
              {filteredSummaryList.map((item: any, index: number) => (
                <ReadFullContent
                  key={selectedItem + selectedState}
                  orderIndex={index}
                  contentClassName="summary"
                  label="Explanation of Changes"
                  topicName={`Summary of ${topicName(item.filing_topic_id)}`}
                  content={item.summary}
                  lineClamp={
                    (filteredSummaryList !== undefined &&
                      filteredSummaryList?.length) +
                      (filteredAmendmentList !== undefined &&
                        filteredAmendmentList?.length) <
                    2
                      ? false
                      : 4
                  }
                  expandLabel={
                    summaryStaticData.ReadFullExplanationOfChanges.value
                  }
                  collapseLabel={
                    summaryStaticData.CollapseExplanationOfChanges.value
                  }
                />
              ))}
            </div>
          )}
          {filteredSummaryAmendment && (
            <div className={summaryAmendmentClass()}>
              {Object.values(allFilteredSummaryAmendment).map(
                (group: any, index: number) => (
                  <div key={group[0].filing_topic_id}>
                    {group.map((item: any) => (
                      <ReadFullContent
                        orderIndex={index}
                        key={
                          item.filing_topic_id +
                          (item.type === "summary"
                            ? "summary"
                            : item.filing_version)
                        }
                        contentClassName={`summary ${
                          item.type === "summary" ? "summary-amendment" : ""
                        }`}
                        label={
                          item.type === "summary"
                            ? `Explanation of Changes`
                            : `Amendment V${
                                item?.filing_version
                              } for ${topicName(item?.filing_topic_id)}`
                        }
                        topicName={`Summary of ${topicName(
                          item.filing_topic_id
                        )}`}
                        content={item.summary}
                        lineClamp={
                          filteredSummaryAmendment?.length < 2 ? false : 4
                        }
                        expandLabel={
                          summaryStaticData.ReadFullExplanationOfChanges.value
                        }
                        collapseLabel={
                          summaryStaticData.CollapseExplanationOfChanges.value
                        }
                      />
                    ))}
                  </div>
                )
              )}
            </div>
          )}
          {filteredSummaryAmendment.length === 0 && filteredAmendmentList && (
            <div className={summaryAmendmentClass()}>
              {filteredAmendmentList.map((summary: any, index: number) => (
                <ReadFullContent
                  key={selectedItem + summary.filing_version}
                  orderIndex={index}
                  contentClassName="summary"
                  label={`Amendment V${summary.filing_version} for ${topicName(
                    summary.filing_topic_id
                  )}`}
                  topicName={`Summary of ${topicName(summary.filing_topic_id)}`}
                  content={summary.summary}
                  lineClamp={
                    (filteredSummaryList !== undefined &&
                      filteredSummaryList?.length) +
                      (filteredAmendmentList !== undefined &&
                        filteredAmendmentList?.length) <
                    2
                      ? false
                      : 4
                  }
                  expandLabel={
                    summaryStaticData.ReadFullExplanationOfChanges.value
                  }
                  collapseLabel={
                    summaryStaticData.CollapseExplanationOfChanges.value
                  }
                />
              ))}
            </div>
          )}
        </>
      ) : (
        <>
          {/* {consolidatedList?.length !== 0 && <Loader />} */}
          <div className="summary-text">Explanation of Changes: </div>
          <div className="no-summary-text">{staticData.EocNoInfo?.value}</div>
        </>
      )}
    </div>
  );

  const relatedRuleIds = data.forms
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_RUL" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  let relatedRules = data.rules.filter((item: { id: string }) =>
    relatedRuleIds?.includes(item.id)
  );

  const relatedRulesMU = relatedRules?.filter(
    (item: { state_type: string }) => item.state_type === "MU"
  );

  const relatedRulesState = Object.values(
    relatedRules?.reduce((acc: any, obj: any) => {
      acc[obj.rule_s] = obj;
      return acc;
    }, {})
  );

  const rulesCount =
    selectedState === "MU" ? relatedRulesMU : relatedRulesState;

  const relatedLossCostIds = data.forms
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_LSC" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  let relatedLossCosts = data.loss_costs.filter((item: { id: string }) =>
    relatedLossCostIds?.includes(item.id)
  );

  const relatedLossCostsMU = relatedLossCosts.filter(
    (item: { state_type: string }) => item.state_type === "MU"
  );

  const relatedLossCostsState = Object.values(
    relatedLossCosts.reduce((acc: any, obj: any) => {
      acc[obj.rule_s] = obj;
      return acc;
    }, {})
  );

  const lossCostsCount =
    selectedState === "MU" ? relatedLossCostsMU : relatedLossCostsState;

  const formsTabs = [
    ["Details", null],
    ["History", null],
    ["Corresponding Rules", rulesCount.length],
    ["Corresponding Loss Costs", lossCostsCount.length],
  ];

  const tabsInfo = {
    Details: formsDetails(),
    History: (
      <FITimeline
        data={data}
        staticData={props.staticData}
        selectedItem={selectedItem}
        selectedState={selectedState}
        serviceType="CNTSRV_FRM"
      />
    ),
    "Corresponding Rules": (
      <CorrespondingRules
        key={selectedItem + selectedState}
        data={data}
        staticData={props.staticData}
        selectedState={selectedState}
        selectedItem={selectedItem}
        documentTitle={documentTitle}
        tabAttributes={["forms", "Form"]}
        entitlement={entitlement}
      />
    ),
    "Corresponding Loss Costs": (
      <CorrespondingLossCosts
        key={selectedItem + selectedState}
        data={data}
        staticData={props.staticData}
        selectedState={selectedState}
        selectedItem={selectedItem}
        documentTitle={documentTitle}
        tabAttributes={["forms", "Form"]}
        entitlement={entitlement}
      />
    ),
  }[selectedDetail];

  return (
    <div className="forms-tab-content" data-testid="forms-tab-content">
      <div className="forms-wrapper">
        <div className="forms-list-pane" data-testid="forms-list-pane">
          {entitlement?.[selectedState]?.["CNTSRV_FRM"] === 1 ? (
            <ListSection
              data={data}
              staticData={props.staticData}
              selectedState={selectedState}
              tab="forms"
              serviceType={["Forms", "CNTSRV_FRM"]}
              listAttributes="display_form_number"
              selectedItem={selectedItem}
              setSelectedItem={setSelectedItem}
              setRefinedDataLength={setRefinedDataLength}
              tabDetails={
                <DetailSectionTabs
                  tabNames={formsTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              }
            />
          ) : (
            <NotSubscribedMessage splitColumn={false} />
          )}
        </div>
        <div
          className={`forms-detail ${
            criticalUpdates.length > 0 &&
            selectedItem === "" &&
            criticalUpdates[0]?.id !== "" &&
            criticalUpdates[0]?.service_type?.includes("CNTSRV_FRM")
              ? "fi-accordion-data"
              : ""
          }`}
          data-testid="forms-detail"
        >
          {entitlement?.[selectedState]?.["CNTSRV_FRM"] === 1 &&
            (refinedDataLength === 0 ? null : selectedItem === "" ? (
              <>
                {criticalUpdates.length > 0 && (
                  <div className="critical-update fi-accordion-item">
                    {criticalUpdates.map((item, index) => (
                      <>
                        {item?.id !== "" &&
                          item?.service_type?.includes("CNTSRV_FRM") && (
                            <Accordion
                              key={index}
                              params={props}
                              staticData={props.staticData}
                              data={data}
                              fields={{
                                heading: { value: "" },
                                Title: {
                                  value: `Critical Update : Circular ${item.id}`,
                                },
                                Content: { value: "critical-update" },
                                product: { value: "FI" },
                                filingData: [item],
                              }}
                            />
                          )}
                      </>
                    ))}
                  </div>
                )}

                <div className="forms-default-text">
                  <div className="forms-description">
                    <span data-testid="forms-default-text">
                      {staticData.FormsTabHighlightedText.value}{" "}
                    </span>
                    {staticData.FormsTabText.value}
                  </div>
                </div>
              </>
            ) : (
              <div>
                <div className="selected-forms-item flex-wrapper">
                  <span className="selected-forms-item-text">
                    <b>{selectedItem}</b> - {documentTitle}
                  </span>
                </div>
                <DetailSectionTabs
                  tabNames={formsTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default FormsTab;
