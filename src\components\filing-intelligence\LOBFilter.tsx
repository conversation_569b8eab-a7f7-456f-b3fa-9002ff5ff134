import React, { useEffect, useState } from "react";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Placeholder,
  withSitecoreContext,
} from "@sitecore-jss/sitecore-jss-nextjs";
import RevisionDropdown from "./common/LOBDropdown";

const LOBFilter = (props: any) => {
  const [selectedLOB, setSelectedLOB] = useState<string>("");
  const [selectedLOBValue, setSelectedLOBValue] = useState<string>("");
  const [activeTab, setActiveTab] = useState(true);
  const { route } = props?.sitecoreContext || {};

  const expandedComponent = () => {
    if (activeTab) {
      return <ExpandLessIcon className="expand-less-icon" />;
    } else {
      return <ExpandMoreIcon className="expand-more-icon" />;
    }
  };

  const selectedLOBName = props?.fields?.LOB.find(
    (item: any) => item?.fields?.Code?.value === selectedLOB
  );

  const handleSubmit = () => {
    if (selectedLOB !== "") setSelectedLOBValue(selectedLOB);
    if (typeof window !== "undefined") {
      window.digitalData.page.pageInfo.page_LOB = selectedLOBName?.displayName;
      window.digitalData.product.FI.service_type = "";
      window.digitalData.product.FI.filing_ID = "";
      window.digitalData.product.FI.form_number = "";
      window.digitalData.product.FI.rule_number = "";
      window.digitalData.product.FI.filing_topic = "";
      window.digitalData.product.FI.LOB = selectedLOBName?.displayName;
    }

    var evt = new CustomEvent("event-view-end");
    const elements = document.getElementsByClassName(
      "revision-filter-dropdownsubmit"
    );
    if (elements.length > 0) {
      elements[0].dispatchEvent(evt);
    }
  };

  useEffect(() => {
    const handleStorageChange = (event: any) => {
      if (event?.detail?.key === "selectedProfiles") {
        setSelectedLOB("");
        setSelectedLOBValue("");
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  return (
    <div className="lob-filter">
      <div className="revision-filter-wrapper" data-testid="lob-filter">
        <div
          className="revision-filter-header flex-wrapper pointer-cursor"
          onClick={() => setActiveTab(!activeTab)}
          data-testid="content-arrow"
          tabIndex={0}
        >
          <div>
            <span>{props?.fields?.FilterText?.value}</span>
          </div>
          <div>
            <div className="content-tab-arrow-wrapper">
              <div
                className="content-tab-arrow pointer-cursor"
                // data-testid="content-arrow"
              >
                {expandedComponent()}
              </div>
            </div>
          </div>
        </div>
        {activeTab && (
          <div className="revision-filter-section flex-wrapper">
            <div className="revision-filter-dropdown flex-wrapper">
              <div className="revision-filter-selectdropdown">
                <div className="revision-filter-labelsection">
                  <div className="revision-filter-label">
                    <label htmlFor="lobfilter" className="choose-state">
                      {props?.fields?.LOBText?.value}
                    </label>
                  </div>
                  <div className="revision-filter-subLabel">
                    <span>{props?.fields?.LOBShortDescription?.value}</span>
                  </div>
                  <RevisionDropdown
                    lobOptions={props?.fields?.LOB}
                    selectedLOB={selectedLOB}
                    setSelectedLOB={setSelectedLOB}
                  />
                </div>
                <div className="revision-filter-dropdownsubmit">
                  <button
                    className={
                      selectedLOB !== ""
                        ? "revision-filter-show-result"
                        : "revision-filter-reult-disabled not-allowed-cursor"
                    }
                    data-testid="show-result"
                    onClick={handleSubmit}
                  >
                    {props?.fields?.ShowResultText?.value}
                  </button>
                </div>
              </div>
            </div>
            {route && <Placeholder name="jss-lob-info" rendering={route} />}
          </div>
        )}
      </div>
      <div>
        {route && (
          <Placeholder
            name="jss-filing-set"
            rendering={route}
            selectedLOB={selectedLOBValue}
          />
        )}
      </div>
    </div>
  );
};

export default withSitecoreContext()(LOBFilter);
