import React from "react";
import { RichText, Text } from "@sitecore-jss/sitecore-jss-nextjs";

interface LOBInfo {
  fields: any;
}

const LOBInfo = (props: LOBInfo) => {
  return (
    <section
      className="revision-filter-description"
      data-testid="filing-set-info"
    >
      <Text
        field={props.fields.Title}
        tag="span"
        className="filing-set-info-header"
      />
      <RichText field={props?.fields?.Description} tag="span" />
    </section>
  );
};

export default LOBInfo;
