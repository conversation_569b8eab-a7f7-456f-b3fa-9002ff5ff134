import { useState, useEffect, useContext, useMemo } from "react";
import DownloadCircularButton from "./common/DownloadCircularButton";
import ListSection from "./common/ListSection";
import DetailSectionTabs from "./common/DetailSectionTabs";
import CorrespondingForms from "./common/CorrespondingForms";
import CorrespondingRules from "./common/CorrespondingRules";
import { getFilingDetails } from "../../helpers/fi/filingDetails";
import ReadFullContent from "./common/ReadFullContent";
import { AuthContext } from "../../context/authContext";
import NotSubscribedMessage from "./common/NotSubscribedMessage";
// import Loader from "components/common/Loader";
// import { getSummaryHtml } from "src/helpers/fi/getSummaryHtml";
import Accordion from "components/Platform/article/Accordion";
import {
  getCriticalUpdates,
  getParentFiling,
} from "src/helpers/fi/fiAccordion";
import FITimeline from "./common/FITimeline";
import {
  getActionStatus,
  generateStateDropdownOptions,
  getCircularTitle,
} from "src/helpers/fi/utils";

const LossCostsTab = (props: {
  staticData: any;
  data: any;
  selectedState: string;
  entitlement: any;
}) => {
  const { accessToken } = useContext(AuthContext);

  const { data, selectedState, entitlement } = props;
  const staticData = props.staticData.LossCostsTabData;
  const summaryStaticData = props.staticData.Summary;
  const [listCircularNumbers, setListCircularNumbers] = useState("");
  const [circularFilePath, setCircularFilePath] = useState("");
  const [circularFileZipPath, setCircularFileZipPath] = useState("");
  const [selectedItem, setSelectedItem] = useState("");
  const [refinedDataLength, setRefinedDataLength] = useState(0);
  const [selectedDetail, setSelectedDetail] = useState("Details");
  const result = data.loss_costs.filter(
    (item: { id: string }) => item.id === selectedItem
  );
  const ruleNumber = result[0]?.rule_s;
  const documentTitle = result[0]?.document_title;
  const [filingStatus, setFilingStatus] = useState<string>("");
  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)

  let stateOptions = generateStateDropdownOptions(
    props?.staticData?.RevisionData?.SelectedStates?.targetItems || []
  );

  const selectedStateDetails = stateOptions?.filter(
    (item: any) => item.value === selectedState
  );

  const parentFiling = getParentFiling(data, selectedState, "CNTSRV_LSC");

  const criticalUpdates = getCriticalUpdates(data, selectedState, "CNTSRV_LSC");

  type summaryAndAmendmentItem = {
    label: string;
    summary: string;
    filing_topic_id: string;
    jurisdiction: string;
  };

  const rfcData = data.filings
    .filter(
      (item: { service_type: string }) => item.service_type === "CNTSRV_LSC"
    )[0]
    .edge.filter(
      (item: { rfc_state_list: string[]; dest_content_key: string }) =>
        (selectedState === "MU"
          ? false
          : item.rfc_state_list?.includes(selectedState)) &&
        item.dest_content_key === result[0]?.id
    );

  const actionType: string =
    rfcData.length > 0
      ? "rfc"
      : getActionStatus(props.staticData, result[0]?.action);
  const isMultistate = result[0]?.state_type === "MU" ? "Yes" : "No";
  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type == "CNTSRV_LSC"
  )[0].filing_id;

  let summaryList = result[0]?.summary_list;
  let amendmentList = result[0]?.amendment_summary_list;
  // amendmentList = amendmentList?.filter(
  //   (item: any) => item.jurisdiction === selectedState
  // );
  let allSummaryList: any = [];
  let allAmendmentList: any = [];
  // let allSummaryAndAmendmentList: any = [];

  const topicName = (topicId: string) => {
    return (
      data.topics?.find((item: any) => topicId === item.filing_topic_id)
        ?.topic_name || "Miscellaneous"
    );
  };

  if (selectedState === "MU" || isMultistate === "Yes") {
    allSummaryList =
      summaryList?.map((item: any) => {
        item.label = "Explanation of Changes";
        return item;
      }) || [];
    allAmendmentList =
      amendmentList?.map((item: any) => {
        item.label = `Amended V${item.filing_version} Multistate Explanation of Changes`;
        return item;
      }) || [];
  } else {
    let stateSummaryList =
      summaryList?.map((item: any) => ({
        ...item,
        label: "Explanation of Changes",
      })) || [];

    let multiStateResult =
      data.loss_costs.filter(
        (item: { rule_s: number; state_type: string }) =>
          item.rule_s === ruleNumber && item.state_type === "MU"
      )[0] || {};

    let multistateSummaryList = multiStateResult?.summary_list || [];
    if (multistateSummaryList?.length > 0) {
      multistateSummaryList = multistateSummaryList?.map((item: any) => {
        item.label = "Explanation of Changes";
        return item;
      });
    }
    allSummaryList = multistateSummaryList?.concat(stateSummaryList);

    let stateAmendmentList =
      amendmentList?.map((item: any) => ({
        ...item,
        label: `Amended V${item.filing_version} State Exception Explanation of Changes`,
      })) || [];

    let multistateAmendmentList =
      multiStateResult?.amendment_summary_list || [];
    if (multistateAmendmentList?.length > 0) {
      multistateAmendmentList = multistateAmendmentList?.map((item: any) => {
        item.label = `Amended V${item.filing_version} Multistate Explanation of Changes`;
        return item;
      });
    }
    allAmendmentList = [...multistateAmendmentList, ...stateAmendmentList];
    allAmendmentList.sort((a: any, b: any) =>
      Number(a["filing_version"]) > Number(b["filing_version"]) ? 1 : -1
    );
  }
  let allSummaryAndAmendmentList = [...allSummaryList, ...allAmendmentList];
  // let consolidatedList = [...allSummaryList, ...allAmendmentList];
  // const [allSummaryAndAmendmentList, setAllSummaryAndAmendmentList] = useState([
  //   ...allSummaryList,
  //   ...allAmendmentList,
  // ]);
  // const [isSummaryLoading, setIsSummaryLoading] = useState(false);

  // useEffect(() => {
  //   const loadSummaryHtml = async () => {
  //     setIsSummaryLoading(true);
  //     setAllSummaryAndAmendmentList(
  //       await getSummaryHtml(consolidatedList, accessToken)
  //     );
  //     setIsSummaryLoading(false);
  //   };
  //   loadSummaryHtml();
  // }, [selectedItem]);

  const exceptionsMultistate = data.loss_costs
    .filter(
      (item: any) => item.rule_s === ruleNumber && item.state_type !== "MU"
    )
    .map((item: any) => item.state_type)
    .sort()
    .join(", ");

  useEffect(() => {
    const filingDetails = getFilingDetails({
      data: data,
      serviceType: "CNTSRV_LSC",
      selectedState: selectedState,
      isMUFiledCircular: true,
    });

    const circularNumber = filingDetails.event_id;

    setListCircularNumbers(circularNumber);

    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};

    setCircularFilePath(file_path);
    setCircularFileZipPath(file_zip_path);
    setFilingStatus(filingDetails.filing_status);
  }, [selectedState]);

  const circularTitle = useMemo(
    () => getCircularTitle(filingStatus),
    [filingStatus]
  );

  const downloadCircularButtonObj = {
    circularFilePath,
    circularFileZipPath,
    accessToken,
    revisionData: props.staticData.RevisionData,
    circularNumber: listCircularNumbers,
    circularTitle,
  };

  const lossCostsDetails = () => (
    <div
      className="loss-costs-detail-content"
      data-testid="loss-costs-detail-content"
      key={selectedItem + selectedState}
    >
      <div>
        {staticData.ActionType.value}:{" "}
        <span className="loss-costs-detail-value">
          {{
            withdrawn: (
              <span className="loss-costs-action">Withdrawn from use</span>
            ),
            rfc: (
              <span className="loss-costs-action">
                Removed From Consideration
              </span>
            ),
          }[actionType?.toLowerCase()] || actionType}
        </span>
      </div>
      <div>
        {staticData.IsMultistate.value}:{" "}
        <span className="loss-costs-detail-value">{isMultistate}</span>
      </div>
      {selectedState === "MU" && exceptionsMultistate && (
        <div>
          {staticData.ExceptionsMultistateText.value}:{" "}
          <span className="loss-costs-detail-value">
            {exceptionsMultistate}
          </span>
        </div>
      )}
      <div className="loss-costs-filing-id">
        {staticData.FilingId.value}:{" "}
        <span className="loss-costs-detail-value">{filingId}</span>
      </div>
      <div>
        {staticData.ListCircularNumbers.value}:{" "}
        <span className="loss-costs-detail-circular-list">
          {listCircularNumbers}{" "}
        </span>
        <DownloadCircularButton {...downloadCircularButtonObj} />
      </div>
      {allSummaryAndAmendmentList?.length !== 0 ? (
        allSummaryAndAmendmentList
          .sort((a: summaryAndAmendmentItem, b: summaryAndAmendmentItem) => {
            return (
              (a.jurisdiction === "MU" ? -1 : 1) -
              (b.jurisdiction === "MU" ? -1 : 1)
            );
          })
          .map((item: summaryAndAmendmentItem, index: number) => (
            <ReadFullContent
              key={selectedItem + selectedState}
              orderIndex={index}
              contentClassName="summary"
              label={item?.label}
              topicName={
                (item.jurisdiction === "MU"
                  ? "Multistate "
                  : "State Exception ") +
                `Explanation of ${topicName(item?.filing_topic_id)}`
              }
              lineClamp={allSummaryAndAmendmentList?.length < 2 ? false : 4}
              content={item?.summary}
              expandLabel={summaryStaticData.ReadFullExplanationOfChanges.value}
              collapseLabel={
                summaryStaticData.CollapseExplanationOfChanges.value
              }
            />
          ))
      ) : (
        <>
          {/* {consolidatedList?.length !== 0 && <Loader />} */}
          <div className="summary-text">Explanation of Changes: </div>
          <div className="no-summary-text">{staticData.EocNoInfo?.value}</div>
        </>
      )}
    </div>
  );

  const formsCount = data.loss_costs
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_FRM" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : item.adopt_state_list?.length > 1)
    );

  const relatedRuleIds = data.loss_costs
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_RUL" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  const relatedRules = data.rules.filter((item: { id: string }) =>
    relatedRuleIds?.includes(item.id)
  );

  const relatedRulesMU = relatedRules?.filter(
    (item: { state_type: string }) => item.state_type === "MU"
  );

  const relatedRulesState = Object.values(
    relatedRules?.reduce((acc: any, obj: any) => {
      acc[obj.rule_s] = obj;
      return acc;
    }, {})
  );

  const rulesCount =
    selectedState === "MU" ? relatedRulesMU : relatedRulesState;

  const lossCostsTabs = [
    ["Details", null],
    ["History", null],
    ["Corresponding Forms", formsCount?.length],
    ["Corresponding Rules", rulesCount?.length],
  ];

  const tabsInfo = {
    Details: lossCostsDetails(),
    History: (
      <FITimeline
        data={data}
        staticData={props.staticData}
        selectedItem={selectedItem}
        selectedState={selectedState}
        serviceType="CNTSRV_LSC"
      />
    ),
    "Corresponding Forms": (
      <CorrespondingForms
        key={selectedItem + selectedState}
        data={data}
        staticData={props.staticData}
        selectedState={selectedState}
        selectedItem={selectedItem}
        selectedRuleNumber={ruleNumber}
        documentTitle={documentTitle}
        tabAttributes={["loss_costs", "Loss Cost"]}
        entitlement={entitlement}
        filingStatus={filingStatus}
      />
    ),
    "Corresponding Rules": (
      <CorrespondingRules
        key={selectedItem + selectedState}
        data={data}
        staticData={props.staticData}
        selectedState={selectedState}
        selectedItem={selectedItem}
        selectedRuleNumber={ruleNumber}
        documentTitle={documentTitle}
        tabAttributes={["loss_costs", "Loss Cost"]}
        entitlement={entitlement}
      />
    ),
  }[selectedDetail];

  return (
    <div
      className="loss-costs-tab-content"
      data-testid="loss-costs-tab-content"
    >
      <div className="loss-costs-wrapper">
        <div
          className="loss-costs-list-pane"
          data-testid="loss-costs-list-pane"
        >
          {entitlement?.[selectedState]?.["CNTSRV_LSC"] === 1 ? (
            <ListSection
              data={data}
              staticData={props.staticData}
              selectedState={selectedState}
              tab="loss_costs"
              serviceType={["Loss Costs", "CNTSRV_LSC"]}
              listAttributes="rule_s"
              titlePrefix="Rule"
              selectedItem={selectedItem}
              setSelectedItem={setSelectedItem}
              setRefinedDataLength={setRefinedDataLength}
              tabDetails={
                <DetailSectionTabs
                  tabNames={lossCostsTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              }
            />
          ) : (
            <NotSubscribedMessage splitColumn={false} />
          )}
        </div>
        <div
          className={`loss-costs-detail ${
            (parentFiling.length || criticalUpdates.length) > 0 &&
            selectedItem === "" &&
            criticalUpdates[0]?.id !== "" &&
            criticalUpdates[0]?.service_type?.includes("CNTSRV_LSC")
              ? "fi-accordion-data"
              : ""
          }`}
          data-testid={`loss-costs-detail ${selectedState}`}
        >
          {entitlement?.[selectedState]?.["CNTSRV_LSC"] === 1 &&
            (refinedDataLength === 0 ||
            filingStatus === undefined ||
            filingStatus === "" ? null : selectedItem === "" ? (
              <>
                {parentFiling.length > 0 && (
                  <div className="parent-filing fi-accordion-item">
                    {parentFiling.map((item, index) => (
                      <Accordion
                        key={index}
                        params={props}
                        staticData={props.staticData}
                        data={data}
                        fields={{
                          heading: ruleNumber,
                          Title: {
                            value: `This ${selectedStateDetails[0]?.label} revision also introduces the following prior revision(s):`,
                          },
                          Content: { value: "parent-filing" },
                          product: { value: "FI" },
                          filingData: item,
                        }}
                      />
                    ))}
                  </div>
                )}
                {criticalUpdates.length > 0 && (
                  <div
                    className={`critical-update ${
                      parentFiling.length > 0
                        ? "critical-update-hr"
                        : "fi-accordion-item"
                    }`}
                  >
                    {criticalUpdates.map((item, index) => (
                      <>
                        {item?.id !== "" &&
                          item?.service_type?.includes("CNTSRV_LSC") && (
                            <Accordion
                              key={index}
                              params={props}
                              staticData={props.staticData}
                              data={data}
                              fields={{
                                heading: ruleNumber,
                                Title: {
                                  value: `Critical Update : Circular ${item.id}`,
                                },
                                Content: { value: "critical-update" },
                                product: { value: "FI" },
                                filingData: [item],
                              }}
                            />
                          )}
                      </>
                    ))}
                  </div>
                )}
                <div className="loss-costs-default-text">
                  <div className="loss-costs-description">
                    <span data-testid="loss-costs-default-text">
                      {staticData.LossCostsTabHighlightedText.value}{" "}
                    </span>
                    {staticData.LossCostsTabText.value}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div
                  className="selected-loss-costs-item flex-wrapper"
                  data-testid="selected-loss-costs-item"
                >
                  <span className="selected-loss-costs-item-text">
                    <b>Loss Cost Rule {ruleNumber}</b> - {documentTitle}
                  </span>
                </div>
                <DetailSectionTabs
                  tabNames={lossCostsTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              </>
            ))}
        </div>
      </div>
    </div>
  );
};

export default LossCostsTab;
