import { useState, useEffect, useRef } from "react";
import dayjs from "dayjs";
import CheckIcon from "@mui/icons-material/Check";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import BlockIcon from "@mui/icons-material/Block";
import CloseIcon from "@mui/icons-material/Close";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import LockIcon from "@mui/icons-material/Lock";
// import { useAtom } from "jotai";
import FormsTab from "./FormsTab";
import RulesTab from "./RulesTab";
import LossCostsTab from "./LossCostsTab";
import SubjectTab from "./SubjectTab";
// import { selectedStateAtom } from "../../store/fi/atoms";
import { getFilingDetails } from "../../helpers/fi/filingDetails";
import NotSubscribedMessage from "./common/NotSubscribedMessage";
import { parseISO, format, addMinutes } from "date-fns";
import StateDropdown from "components/common/StateDropdown";
import ClassPlanTab from "./ClassPlanTab";
import { RichText } from "@sitecore-jss/sitecore-jss-nextjs";
import { Tooltip } from "@mui/material";
import { generateStateDropdownOptions } from "src/helpers/fi/utils";

interface Revision {
  data: any;
  staticData: any;
  entitlement: boolean | any;
  filteredLOBDetails?: string;
  showFootNotes?: boolean | any;
}
const FootNote = ({
  isRevisionEntitled,
  date,
  revisionUpdateDescription,
}: {
  isRevisionEntitled: boolean;
  date: string;
  revisionUpdateDescription: string;
}) => {
  revisionUpdateDescription = revisionUpdateDescription.replace(
    "XX/XX/XX",
    format(
      addMinutes(parseISO(date), parseISO(date).getTimezoneOffset()),
      "MM/dd/yy"
    )
  );
  return (
    <div className="revision-footnote" data-testid="revision-footnote">
      {isRevisionEntitled && (
        <RichText
          className="revision-description"
          field={{ value: revisionUpdateDescription }}
        />
      )}
    </div>
  );
};

const Revision = ({
  data,
  staticData,
  entitlement,
  filteredLOBDetails,
  showFootNotes = true,
}: Revision) => {
  const revisionStaticData = staticData.RevisionData;
  const bureauStaticData = staticData.BureauState;
  const revisionUpdateStaticData = staticData.RevisionDate;
  const isRevisionEntitled = Object.values(entitlement).some(
    (item: { FO: number; RU: number; LC: number }) =>
      Object.values(item).includes(1)
  );

  const [multistateTextInd, setMultistateTextInd] = useState(true);
  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)
  const [selectedState, setSelectedState] = useState<string>("MU");

  const navRef = useRef<HTMLDivElement>(null);
  const [showTabScrollArrows, setshowTabScrollArrows] = useState(false);
  const [leftArrowDisable, setLeftArrowDisable] = useState(true);
  const [rightArrowDisable, setRightArrowDisable] = useState(false);
  const [tempTab, setTempTab] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      const navContainerScrollWidth = navRef.current?.scrollWidth ?? 0;
      const navContainerClientWidth = navRef.current?.clientWidth ?? 0;
      const deviceWidth = window.innerWidth;

      filteredLOBDetails === "LOB_BP" &&
        setshowTabScrollArrows(
          navContainerScrollWidth > navContainerClientWidth &&
            deviceWidth <= 37.5 * 16
        );
      if (
        navContainerScrollWidth <= navContainerClientWidth ||
        deviceWidth >= 37.5 * 16
      ) {
        setRightArrowDisable(true);
      } else {
        setRightArrowDisable(false);
      }
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleNavigation = (direction: string) => {
    const navContainer = navRef.current;
    if (navContainer) {
      const scrollAmount = direction === "left" ? -100 : 200;
      navContainer.scrollLeft += scrollAmount;
      setLeftArrowDisable(navContainer.scrollLeft === 0);
      setRightArrowDisable(
        Math.ceil(navContainer.scrollLeft) >=
          navContainer.scrollWidth - navContainer.clientWidth - 1
      );
    }
  };

  let tabList = revisionStaticData.TabNames.targetItems.map(
    (item: { name: string }) => item.name
  );
  tabList = tabList.filter((item: string) => item !== "Loss Cost");
  tabList?.push("Loss Costs");
  filteredLOBDetails === "LOB_BP" && tabList?.push("Class Plan");
  tabList?.push("Filing Topic(s)");

  const [activeTab, setActiveTab] = useState("");
  const toggleActiveTab = (currentTab: string) => {
    setActiveTab(currentTab === activeTab ? "" : currentTab);
    if (typeof window !== "undefined" && window.digitalData?.product?.FI) {
      window.digitalData.product.FI.service_type = currentTab ?? "";
    }
    let evt = new CustomEvent("event-view-end");
    document.body.dispatchEvent(evt);
  };

  const tabProps = {
    data: data,
    staticData: staticData,
    selectedState: selectedState,
    entitlement: entitlement,
  };

  const [filingDetails, setFilingDetails] = useState<any>();
  const filingStatus: string = filingDetails?.filing_status;

  const [selectedTab, serviceType] = {
    Forms: [<FormsTab key={activeTab} {...tabProps} />, "CNTSRV_FRM"],
    Rules: [<RulesTab key={activeTab} {...tabProps} />, "CNTSRV_RUL"],
    "Loss Costs": [
      <LossCostsTab key={activeTab} {...tabProps} />,
      "CNTSRV_LSC",
    ],
    "Class Plan": [
      <ClassPlanTab key={activeTab} {...tabProps} />,
      "CNTSRV_FRM",
    ],
    "Filing Topic(s)": [
      <SubjectTab key={activeTab} {...tabProps} />,
      "CNTSRV_FRM",
    ],
  }[activeTab] || [null, "CNTSRV_FRM"];

  useEffect(() => {
    if (filteredLOBDetails !== "") {
      setSelectedState("MU");
      setActiveTab("");
      window.digitalData = window.digitalData || {};
      window.digitalData.page = window.digitalData.page || {};
      window.digitalData.page.pageInfo = window.digitalData.page.pageInfo || {};
      window.digitalData.product = window.digitalData.product || {};
      window.digitalData.product.FI = window.digitalData.product.FI || {};
      window.digitalData.page.pageInfo.page_jurisdiction = "MU";
      window.digitalData.product.FI.jurisdiction = "MU";
    }
  }, [filteredLOBDetails]);

  useEffect(() => {
    setFilingDetails(
      getFilingDetails({
        data: data,
        serviceType: serviceType,
        selectedState: selectedState,
        isMUFiledCircular: false,
      })
    );
  }, [selectedState, serviceType]);

  const [statusIcon, statusText] = {
    STATUS_APPROVED: [
      <CheckIcon key={filingStatus} className="status-approved-icon" />,
      staticData?.RevisionDate?.Implemented?.value,
    ],
    STATUS_PENDING: [
      <HourglassEmptyIcon key={filingStatus} className="status-pending-icon" />,
      staticData?.RevisionDate?.Not_Implemented?.value,
    ],
    STATUS_DISAPPROVED: [
      <CloseIcon key={filingStatus} className="status-disapproved-icon" />,
      "",
    ],
    STATUS_WITHDRAWN: [
      <BlockIcon key={filingStatus} className="status-withdrawn-icon" />,
      staticData?.RevisionDate?.STATUS_WITHDRAWN?.value,
    ],
  }[filingStatus] || [null, null];

  let filteredFilings: Array<any> = data.filings.map((item: any) => {
    return item.filing_status_applicability.filter(
      (item: { jurisdiction: string; filing_status: string }) =>
        item.jurisdiction === selectedState &&
        (item.filing_status === "STATUS_NOFILINGIMPACT" ||
          item.filing_status === "STATUS_NODATA")
    );
  });

  const bureauStateDetails =
    data?.bureau_state?.filter(
      (item: any) => item.jurisdiction === selectedState
    ) || [];

  let stateOptions = generateStateDropdownOptions(
    staticData?.RevisionData?.SelectedStates?.targetItems || []
  );

  const selectedStateDetails = stateOptions?.filter(
    (item: any) => item.value === selectedState
  );

  useEffect(() => {
    if (filteredFilings.flat().length < 3) {
      if (filteredLOBDetails === "LOB_BP2") {
        setTempTab(1);
      } else {
        setTempTab(0);
      }
    } else {
      setTempTab(0);
    }
  }, [filteredFilings, filteredLOBDetails]);

  const tabClasses = (tabName: string) => {
    if (filteredFilings.flat().length < 3) {
      if (filteredLOBDetails === "LOB_BP2" && tabName === "Filing Topic(s)") {
        return "tab temp-tab default-cursor";
      } else {
        return `tab pointer-cursor ${activeTab === tabName && "selected-tab"} ${
          filteredLOBDetails === "LOB_BP" && "business-owner"
        }`;
      }
    } else {
      return "disabled-tab default-cursor pointer-events-none";
    }
  };

  const noDataFilings =
    data.filings
      .filter(
        (item: { service_type: string }) => item?.service_type === serviceType
      )[0]
      ?.filing_status_applicability.filter(
        (item: { jurisdiction: string }) => item?.jurisdiction === selectedState
      )[0] ?? {};

  const noDataStatusIcon = (Object.keys(noDataFilings).length === 0 ||
    noDataFilings.filing_status === "STATUS_NODATA") && (
    <BlockIcon key={filingStatus} className="status-withdrawn-icon" />
  );

  const allServiceFilingDetails = [
    "CNTSRV_FRM",
    "CNTSRV_RUL",
    "CNTSRV_LSC",
  ].map((item) =>
    getFilingDetails({
      data: data,
      serviceType: item,
      selectedState: selectedState,
      isMUFiledCircular: false,
    })
  );

  const isAllFilingDetailsSame = allServiceFilingDetails.every(
    (item, _, arr) =>
      item.filing_status === arr[0].filing_status &&
      item.eff_date === arr[0].eff_date
  );

  return (
    <div className="revision-wrapper">
      <div className="revision-header flex-wrapper">
        <span className="revision-title" data-testid="revision-title">
          <span>
            {data.filing_set.lob === "LOB_CA"
              ? "Commercial Auto"
              : data.filing_set.lob === "LOB_HO"
              ? "Homeowners"
              : data.filing_set.lob === "LOB_BP"
              ? "Businessowners"
              : "Commercial Farm"}
          </span>{" "}
          {revisionStaticData.RevisionText.value}
        </span>
        <div className="revision-title-vertical-line" />
        {isRevisionEntitled ? (
          <form className="state-dropdown-section">
            {data.filing_set.lob !== "LOB_BP" &&
              data.filing_set.lob !== "LOB_FR" && (
                <label htmlFor="state" className="choose-state">
                  {revisionStaticData.ChooseState.value}
                </label>
              )}
            <div
              className="state-multistate-wrapper flex-wrapper"
              data-testid="select-state"
            >
              {data.filing_set.lob !== "LOB_BP" &&
              data.filing_set.lob !== "LOB_FR" ? (
                <StateDropdown
                  stateOptions={stateOptions}
                  selectedState={selectedState}
                  setSelectedState={setSelectedState}
                  setMultistateTextInd={setMultistateTextInd}
                  entitlement={entitlement}
                />
              ) : (
                <div className="flex-wrapper">
                  <span>Multistate information only</span>
                </div>
              )}
              {multistateTextInd ? (
                <div className="multistate-text" data-testid="multistate-text">
                  {revisionUpdateStaticData.UpdateDate.value.replace(
                    "{0}",
                    format(
                      addMinutes(
                        parseISO(
                          revisionUpdateStaticData[data.filing_set.lob].value
                        ),
                        parseISO(
                          revisionUpdateStaticData[data.filing_set.lob].value
                        ).getTimezoneOffset()
                      ),
                      "MM/dd/yy"
                    )
                  )}
                </div>
              ) : (
                <div className="multistate-text">
                  {revisionUpdateStaticData.UpdateDate.value.replace(
                    "{0}",
                    format(
                      addMinutes(
                        parseISO(
                          revisionUpdateStaticData[data.filing_set.lob].value
                        ),
                        parseISO(
                          revisionUpdateStaticData[data.filing_set.lob].value
                        ).getTimezoneOffset()
                      ),
                      "MM/dd/yy"
                    )
                  )}
                </div>
              )}
            </div>
          </form>
        ) : (
          <span className="lob-subscription" data-testid="lob-subscription">
            <LockIcon className="lob-subscription-icon" />
            Your subscription does not include this line of business.
          </span>
        )}
      </div>
      <div className="content-wrapper">
        {isRevisionEntitled &&
          (activeTab !== "Filing Topic(s)" &&
          (isAllFilingDetailsSame || activeTab) ? (
            <div
              className={`content-date-effective ${
                filingStatus || noDataStatusIcon ? "" : "content-filed"
              } ${bureauStateDetails.length > 0 ? "bureau-state-status" : ""}`}
            >
              {filingStatus && statusIcon ? (
                <div className="ellipse-wrapper flex-wrapper">
                  <div className="content-ellipse">{statusIcon}</div>
                  <hr />
                </div>
              ) : bureauStateDetails.length > 0 ? (
                <div className="not-filed-state">Bureau State</div>
              ) : noDataFilings.filing_status === "STATUS_NOFILINGIMPACT" ? (
                <div className="not-filed-state">
                  {staticData.RevisionDate?.Not_Filed_Selected_State?.value}
                </div>
              ) : (
                noDataFilings.filing_status === "STATUS_NODATA" && (
                  <div className="not-filed-state">
                    {
                      staticData.RevisionDate?.Not_Available_Filing_Intelligence
                        ?.value
                    }
                  </div>
                )
              )}
              {selectedState === "MU" ? (
                <div className="content-date">
                  {(filingStatus === "STATUS_APPROVED" ||
                    filingStatus === "STATUS_PENDING") && (
                    <div
                      className={
                        filingStatus === "STATUS_PENDING"
                          ? "status-tooltip status-pending default-cursor"
                          : "status-tooltip"
                      }
                    >
                      {statusText}
                      {filingStatus === "STATUS_APPROVED" && (
                        <span className="tooltip-text">
                          Check your specific state for implementation details
                        </span>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="content-date">
                  <div className="content-effective">{statusText}</div>
                  {filingDetails?.eff_date !== undefined &&
                    filingStatus === "STATUS_APPROVED" &&
                    dayjs(filingDetails?.eff_date).format("MM/DD/YY")}
                </div>
              )}
            </div>
          ) : bureauStateDetails.length > 0 ? (
            <div className="content-filing-topics">Bureau State</div>
          ) : noDataFilings.filing_status === "STATUS_NODATA" ? (
            <div className="content-filing-topics">
              {staticData.RevisionData.NoDataStatusText.value}
            </div>
          ) : noDataFilings.filing_status === "STATUS_NOFILINGIMPACT" ? (
            <div className="content-filing-topics">
              {staticData.RevisionDate?.Not_Filed_Selected_State?.value}
            </div>
          ) : (
            <div className="content-filing-topics">
              {staticData.RevisionDate?.Select_Content_Status?.value}
            </div>
          ))}
        <div className="outer-content">
          {isRevisionEntitled ? (
            <div className="content">
              <div className="content-type-tabs">
                <div className="content-type-text">
                  {filingStatus
                    ? revisionStaticData.ContentTypeText?.value
                    : noDataFilings.filing_status === "STATUS_NODATA"
                    ? staticData.RevisionData.NoDataContentTypeText.value
                    : revisionStaticData.NoContentText?.value}
                </div>

                <nav className="content-tabs" ref={navRef}>
                  {showTabScrollArrows && !leftArrowDisable && (
                    <button
                      className="arrowNav prev"
                      onClick={() => handleNavigation("left")}
                      disabled={leftArrowDisable}
                      data-testid={`left-nav`}
                    >
                      <span className="material-symbols-outlined">
                        chevron_left
                      </span>
                    </button>
                  )}
                  {tabList.map((tabName: string) => (
                    <Tooltip
                      key={tabName}
                      title={
                        tempTab && tabName === "Filing Topic(s)"
                          ? "Coming Soon"
                          : ""
                      }
                      arrow
                    >
                      <button
                        key={tabName}
                        className={tabClasses(tabName)}
                        onClick={() => toggleActiveTab(tabName)}
                        data-testid={`${tabName}-button`}
                        tabIndex={0}
                        data-interaction="fi_content"
                        data-content={tabName}
                        disabled={
                          tempTab && tabName === "Filing Topic(s)"
                            ? true
                            : false
                        }
                      >
                        {(entitlement[selectedState]["CNTSRV_FRM"] === 0 &&
                          tabName === "Forms") ||
                        (entitlement[selectedState]["CNTSRV_RUL"] === 0 &&
                          tabName === "Rules") ||
                        (entitlement[selectedState]["CNTSRV_LSC"] === 0 &&
                          (tabName === "Loss Costs" ||
                            tabName === "Class Plan")) ? (
                          <span className="tab-lock-icon">
                            <LockIcon className="lob-subscription-icon" />
                            {tabName}
                          </span>
                        ) : (
                          <span className="tab-no-lock-icon">{tabName}</span>
                        )}
                      </button>
                    </Tooltip>
                  ))}
                  {showTabScrollArrows && !rightArrowDisable && (
                    <button
                      className="arrowNav next"
                      onClick={() => handleNavigation("right")}
                      disabled={rightArrowDisable}
                      data-testid={`right-nav`}
                    >
                      <span className="material-symbols-outlined">
                        chevron_right
                      </span>
                    </button>
                  )}
                </nav>
              </div>
              <div className="content-description">
                <div
                  className="content-description-item"
                  data-testid="content-description-item"
                >
                  <span
                    className="content-description-item-text"
                    data-testid="content-description-text-id"
                  >
                    {data.filing_set.lob === "LOB_CA"
                      ? "Commercial Auto"
                      : data.filing_set.lob === "LOB_HO"
                      ? "Homeowners"
                      : data.filing_set.lob === "LOB_BP"
                      ? "Businessowners"
                      : "Commercial Farm"}{" "}
                    {revisionStaticData.RevisionText.value}:{" "}
                    <span>{data.filing_set.filing_set_title}</span>
                  </span>
                </div>
                <div
                  className="content-tab-arrow-wrapper"
                  data-testid="content-arrow-wrapper"
                >
                  {activeTab && (
                    <button
                      className="content-tab-arrow pointer-cursor"
                      data-testid="content-arrow"
                      onClick={() => toggleActiveTab("")}
                      tabIndex={0}
                    >
                      <ExpandLessIcon className="expand-less-icon" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <NotSubscribedMessage splitColumn={true} />
          )}
          {filteredFilings.flat().length < 3 && selectedTab}
          <div
            className="bureau-content-wrapper"
            data-testid="bureau-content-wrapper"
          >
            {bureauStateDetails.length > 0 && (
              <>
                <hr
                  className="bureau-content-hr"
                  data-testid="bureau-content-hr"
                />
                <div className="bureau-content" data-testid="bureau-content">
                  <span
                    className="bureau-content-description"
                    data-testid="bureau-content-description"
                    dangerouslySetInnerHTML={{
                      __html: bureauStaticData?.Message.value
                        .replace("{0}", selectedStateDetails[0]?.label)
                        .replace(
                          "{1}",
                          bureauStateDetails[0]?.bureau_state_name
                        ),
                    }}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {showFootNotes && (
        <FootNote
          isRevisionEntitled={isRevisionEntitled}
          date={revisionUpdateStaticData[data.filing_set.lob]?.value}
          revisionUpdateDescription={
            revisionUpdateStaticData.UpdateDescription.value
          }
        />
      )}
    </div>
  );
};

export default Revision;
