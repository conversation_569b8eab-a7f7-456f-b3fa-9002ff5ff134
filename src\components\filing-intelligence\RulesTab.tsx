import { useState, useEffect, useContext, useMemo } from "react";
import DownloadCircularButton from "./common/DownloadCircularButton";
import ListSection from "./common/ListSection";
import DetailSectionTabs from "./common/DetailSectionTabs";
import CorrespondingForms from "./common/CorrespondingForms";
import CorrespondingLossCosts from "./common/CorrespondingLossCosts";
import PreviewDocuments from "./common/PreviewDocuments";
import {
  createPropsForPreview,
  getActionStatus,
  generateStateDropdownOptions,
  getCircularTitle,
} from "src/helpers/fi/utils";
import { getFilingDetails } from "../../helpers/fi/filingDetails";
import ReadFullContent from "./common/ReadFullContent";
import { AuthContext } from "../../context/authContext";
import NotSubscribedMessage from "./common/NotSubscribedMessage";
// import Loader from "components/common/Loader";
// import { getSummaryHtml } from "src/helpers/fi/getSummaryHtml";
import { getRuleDisplayName } from "src/helpers/fi/getRuleDisplayName";
import Accordion from "components/Platform/article/Accordion";
import {
  getCriticalUpdates,
  getParentFiling,
} from "src/helpers/fi/fiAccordion";
import FITimeline from "./common/FITimeline";

const RulesTab = (props: {
  staticData: any;
  data: any;
  selectedState: string;
  entitlement: any;
}) => {
  const { accessToken } = useContext(AuthContext);

  const { data, selectedState, entitlement } = props;
  const staticData = props.staticData.RulesTabData;
  const summaryStaticData = props.staticData.Summary;

  const [filingStatus, setfilingStatus] = useState<string>("");
  const [listCircularNumbers, setListCircularNumbers] = useState("");
  const [circularFilePath, setCircularFilePath] = useState("");
  const [circularFileZipPath, setCircularFileZipPath] = useState("");
  const [selectedItem, setSelectedItem] = useState("");
  const [refinedDataLength, setRefinedDataLength] = useState(0);
  const [selectedDetail, setSelectedDetail] = useState("Details");
  const result = data.rules.filter(
    (item: { id: string }) => item.id === selectedItem
  );
  const ruleNumber = result[0]?.rule_s;
  const documentTitle = result[0]?.document_title;
  const documentList = result[0]?.document_list;
  const documentType = result[0]?.document_type;
  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)

  let stateOptions = generateStateDropdownOptions(
    props?.staticData?.RevisionData?.SelectedStates?.targetItems || []
  );

  const selectedStateDetails = stateOptions?.filter(
    (item: any) => item.value === selectedState
  );

  const parentFiling = getParentFiling(data, selectedState, "CNTSRV_RUL");

  const criticalUpdates = getCriticalUpdates(data, selectedState, "CNTSRV_RUL");

  type summaryAndAmendmentItem = {
    type: string;
    filing_version: string;
    label: string;
    summary: string;
    filing_topic_id: string;
    jurisdiction: string;
  };

  const rfcData = data.filings
    .filter(
      (item: { service_type: string }) => item.service_type === "CNTSRV_RUL"
    )[0]
    .edge.filter(
      (item: { rfc_state_list: string[]; dest_content_key: string }) =>
        (selectedState === "MU"
          ? false
          : item.rfc_state_list?.includes(selectedState)) &&
        item.dest_content_key === result[0]?.id
    );

  const actionType: string =
    rfcData.length > 0
      ? "rfc"
      : getActionStatus(props.staticData, result[0]?.action);
  const isMultistate = result[0]?.state_type === "MU" ? "Yes" : "No";
  const filingId =
    result[0]?.filing_id ||
    data.filings.filter(
      (filing: { service_type: string }) => filing.service_type == "CNTSRV_RUL"
    )[0]?.filing_id;

  let summaryList = result[0]?.summary_list;
  let amendmentList = result[0]?.amendment_summary_list;
  // amendmentList = amendmentList?.filter(
  //   (item: any) => item.jurisdiction === selectedState
  // );
  let allSummaryList: any = [];
  let allAmendmentList: any = [];
  // let allSummaryAndAmendmentList: any = [];

  const topicName = (topicId: string) => {
    return (
      data?.topics?.find((item: any) => topicId === item.filing_topic_id)
        ?.topic_name || "Miscellaneous"
    );
  };

  if (selectedState === "MU" || isMultistate === "Yes") {
    allSummaryList =
      summaryList?.map((item: any) => {
        item.label = "Explanation of Changes";
        return item;
      }) || [];
    allAmendmentList =
      amendmentList?.map((item: any) => {
        item.label =
          (item?.jurisdiction === "MU" ? `Multistate ` : `State Exception `) +
          `Amendment V${item.filing_version} of ${topicName(
            item.filing_topic_id
          )}`;
        return item;
      }) || [];
  } else {
    let stateSummaryList =
      summaryList?.map((item: any) => ({
        ...item,
        label: "Exception Explanation of Changes",
      })) || [];

    let multiStateResult =
      data.rules.filter(
        (item: { rule_s: number; state_type: string }) =>
          item.rule_s === ruleNumber && item.state_type === "MU"
      )[0] || {};

    let multistateSummaryList = multiStateResult?.summary_list || [];
    if (multistateSummaryList?.length > 0) {
      multistateSummaryList = multistateSummaryList?.map((item: any) => {
        item.label = "Explanation of Changes";
        return item;
      });
    }
    allSummaryList = multistateSummaryList?.concat(stateSummaryList);

    let stateAmendmentList =
      amendmentList?.map((item: any) => ({
        ...item,
        label: `Amendment V${item.filing_version} of ${topicName(
          item.filing_topic_id
        )}`,
      })) || [];

    let multistateAmendmentList =
      multiStateResult?.amendment_summary_list || [];
    if (multistateAmendmentList?.length > 0) {
      multistateAmendmentList = multistateAmendmentList?.map((item: any) => {
        item.label = `Amendment V${item.filing_version} of ${topicName(
          item.filing_topic_id
        )}`;
        return item;
      });
    }
    allAmendmentList = [...multistateAmendmentList, ...stateAmendmentList];
    allAmendmentList.sort((a: any, b: any) =>
      Number(a["filing_version"]) > Number(b["filing_version"]) ? 1 : -1
    );
  }
  let allSummaryAndAmendmentList = [...allSummaryList, ...allAmendmentList];
  // let consolidatedList = [...allSummaryList, ...allAmendmentList];
  // const [allSummaryAndAmendmentList, setAllSummaryAndAmendmentList] = useState([
  //   ...allSummaryList,
  //   ...allAmendmentList,
  // ]);
  // const [isSummaryLoading, setIsSummaryLoading] = useState(false);

  // useEffect(() => {
  //   const loadSummaryHtml = async () => {
  //     setIsSummaryLoading(true);
  //     setAllSummaryAndAmendmentList(
  //       await getSummaryHtml(consolidatedList, accessToken)
  //     );
  //     setIsSummaryLoading(false);
  //   };
  //   loadSummaryHtml();
  // }, [selectedItem]);

  const exceptionsMultistate = data.rules
    .filter(
      (item: any) => item.rule_s === ruleNumber && item.state_type !== "MU"
    )
    .map((item: any) => item.state_type)
    .sort()
    .join(", ");

  if (typeof window !== "undefined" && window.digitalData?.product?.FI) {
    window.digitalData.product.FI.rule_number = selectedItem ?? "";
    window.digitalData.product.FI.form_number = "";
  }

  useEffect(() => {
    const filingDetails = getFilingDetails({
      data: data,
      serviceType: "CNTSRV_RUL",
      selectedState: selectedState,
      isMUFiledCircular: true,
    });

    const circularNumber = filingDetails.event_id;

    setListCircularNumbers(filingDetails.event_id);

    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};

    setCircularFilePath(file_path);
    setCircularFileZipPath(file_zip_path);
    setfilingStatus(filingDetails.filing_status);
  }, [selectedState]);

  let allFilteredSummaryAmendment = {};
  if (allSummaryAndAmendmentList && allSummaryAndAmendmentList.length > 0) {
    allFilteredSummaryAmendment = allSummaryAndAmendmentList.reduce(
      (acc: any, item: any) => {
        if (!acc[item.filing_topic_id]) {
          acc[item.filing_topic_id] = [];
        }
        acc[item.filing_topic_id].push(item);
        return acc;
      },
      {}
    );
  }
  let itemIndex = 0;

  const circularTitle = useMemo(
    () => getCircularTitle(filingStatus),
    [filingStatus]
  );

  const downloadCircularButtonObj = {
    circularFilePath,
    circularFileZipPath,
    accessToken,
    revisionData: props.staticData.RevisionData,
    circularNumber: listCircularNumbers,
    circularTitle,
  };

  const rulesDetails = () => (
    <div className="rules-detail-content" key={selectedItem + selectedState}>
      <div>
        {staticData.ActionType.value}:{" "}
        <span className="rules-detail-value">
          {{
            withdrawn: <span className="rules-action">Withdrawn from use</span>,
            rfc: (
              <span className="rules-action">Removed From Consideration</span>
            ),
          }[actionType?.toLowerCase()] || actionType}
        </span>
        {selectedItem === "" || (
          <span className="rules-preview-document pointer-cursor">
            <PreviewDocuments
              key={selectedItem}
              data={data}
              staticData={props.staticData}
              tab="rules"
              selectedItem={selectedItem}
              selectedState={selectedState}
              document_list={createPropsForPreview({
                jsonData: data,
                section: "Rules",
                state: selectedState,
                key: ruleNumber,
                itemDocumentList: documentList,
              })}
              filingStatus={filingStatus}
              filterAction={
                actionType !== undefined && actionType === "Withdrawn"
                  ? actionType
                  : ""
              }
            />
          </span>
        )}
      </div>
      <div>
        {staticData.IsMultistate.value}:{" "}
        <span className="rules-detail-value">{isMultistate}</span>
      </div>
      {selectedState === "MU" && exceptionsMultistate && (
        <div>
          {staticData.ExceptionsMultistateText.value}:{" "}
          <span className="rules-detail-value">{exceptionsMultistate}</span>
        </div>
      )}
      <div className="rules-filing-id">
        {staticData.FilingId.value}:{" "}
        <span className="rules-detail-value">{filingId}</span>
      </div>
      <div>
        {staticData.ListCircularNumbers.value}:{" "}
        <span className="rules-detail-circular-list">
          {listCircularNumbers}{" "}
        </span>
        <DownloadCircularButton {...downloadCircularButtonObj} />
      </div>
      {allSummaryAndAmendmentList?.length !== 0 ? (
        <div
          className={
            Object.keys(allFilteredSummaryAmendment).length > 1
              ? "rules-summary-hr"
              : ""
          }
        >
          {Object.values(allFilteredSummaryAmendment).map(
            (group: summaryAndAmendmentItem[]) => (
              <div key={group[0].filing_topic_id}>
                {group
                  .sort(
                    (
                      a: summaryAndAmendmentItem,
                      b: summaryAndAmendmentItem
                    ) => {
                      return (
                        (a.jurisdiction === "MU" ? -1 : 1) -
                        (b.jurisdiction === "MU" ? -1 : 1)
                      );
                    }
                  )
                  .map((item: summaryAndAmendmentItem) => {
                    const index = itemIndex++;
                    return (
                      <ReadFullContent
                        key={
                          item.filing_topic_id +
                          (item.type === "summary"
                            ? "summary"
                            : item.filing_version)
                        }
                        orderIndex={index}
                        contentClassName={`summary ${
                          item.type === "summary" ? "summary-amendment" : ""
                        }`}
                        label={item?.label}
                        topicName={
                          (item?.jurisdiction === "MU"
                            ? `Multistate `
                            : `State Exception `) +
                          `Explanation of ${topicName(item.filing_topic_id)}`
                        }
                        content={item.summary}
                        lineClamp={
                          allSummaryAndAmendmentList?.length < 2 ? false : 4
                        }
                        expandLabel={
                          summaryStaticData.ReadFullExplanationOfChanges.value
                        }
                        collapseLabel={
                          summaryStaticData.CollapseExplanationOfChanges.value
                        }
                      />
                    );
                  })}
              </div>
            )
          )}
        </div>
      ) : (
        <>
          {/* {consolidatedList?.length !== 0 && <Loader />} */}
          <div className="summary-text">Explanation of Changes: </div>
          <div className="no-summary-text">{staticData.EocNoInfo?.value}</div>
        </>
      )}
    </div>
  );

  let formsCount = data.forms
    .filter((item: { edge: any[] }) =>
      item.edge.some(
        (item: {
          dest_content_type: string;
          dest_content_key: string;
          adopt_state_list: Array<string>;
        }) =>
          item.dest_content_type === "CNTSRV_RUL" &&
          item.dest_content_key === selectedItem &&
          (selectedState !== "MU"
            ? item.adopt_state_list?.includes(selectedState)
            : true)
      )
    )
    .map((item: { id: string }) => item.id);

  let totalFormsCount =
    formsCount !== undefined && formsCount ? formsCount.length : 0;

  if (
    selectedItem !== undefined &&
    selectedItem !== "" &&
    selectedState !== "MU"
  ) {
    let tempId = selectedItem.replace(selectedState, "MU");

    let muForms = data.rules.filter(
      (item: { id: string; state_type: string; edge: any[] }) =>
        item.id === tempId && item.state_type === "MU"
    );

    if (muForms !== undefined && muForms.length > 0) {
      let muFormCount = data.rules
        .filter(
          (item: { id: string; state_type: string; edge: any[] }) =>
            item.id === tempId && item.state_type === "MU"
        )[0]
        .edge.filter(
          (item: {
            dest_content_type: string;
            edge_type: string;
            adopt_state_list: string;
          }) =>
            item.dest_content_type === "CNTSRV_FRM" &&
            item.edge_type == "corresponding_content" &&
            item.adopt_state_list?.includes(selectedState)
        )
        .map((item: { dest_content_key: string }) => item.dest_content_key);

      totalFormsCount =
        formsCount !== undefined &&
        formsCount.length > 0 &&
        muFormCount !== undefined &&
        muFormCount.length > 0
          ? [...new Set([...formsCount, ...muFormCount])].length
          : formsCount !== undefined && formsCount.length > 0
          ? formsCount.length
          : muFormCount !== undefined && muFormCount.length > 0
          ? muFormCount.length
          : 0;
    }
  }

  const relatedLossCostIds = data.forms
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_LSC" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  let relatedLossCosts = data.loss_costs.filter((item: { id: string }) =>
    relatedLossCostIds?.includes(item.id)
  );

  const relatedLossCostsMU = relatedLossCosts.filter(
    (item: { state_type: string }) => item.state_type === "MU"
  );

  const relatedLossCostsState = Object.values(
    relatedLossCosts.reduce((acc: any, obj: any) => {
      acc[obj.rule_s] = obj;
      return acc;
    }, {})
  );

  const lossCostsCount =
    selectedState === "MU" ? relatedLossCostsMU : relatedLossCostsState;

  const rulesTabs = [
    ["Details", null],
    ["History", null],
    ["Corresponding Forms", totalFormsCount],
    ["Corresponding Loss Costs", lossCostsCount.length],
  ];

  const tabsInfo = {
    Details: rulesDetails(),
    History: (
      <FITimeline
        data={data}
        staticData={props.staticData}
        selectedItem={selectedItem}
        selectedState={selectedState}
        serviceType="CNTSRV_RUL"
      />
    ),
    "Corresponding Forms": (
      <CorrespondingForms
        key={selectedItem + selectedState}
        data={data}
        staticData={props.staticData}
        selectedState={selectedState}
        selectedItem={selectedItem}
        selectedRuleNumber={ruleNumber}
        documentType={documentType}
        documentTitle={documentTitle}
        filingStatus={filingStatus}
        tabAttributes={["rules", "Rule"]}
        entitlement={entitlement}
      />
    ),
    "Corresponding Loss Costs": (
      <CorrespondingLossCosts
        key={selectedItem + selectedState}
        data={data}
        staticData={props.staticData}
        selectedState={selectedState}
        selectedItem={selectedItem}
        selectedRuleNumber={ruleNumber}
        documentType={documentType}
        documentTitle={documentTitle}
        tabAttributes={["rules", "Rule"]}
        entitlement={entitlement}
      />
    ),
  }[selectedDetail];

  return (
    <div className="rules-tab-content" data-testid="rules-tab-content">
      <div className="rules-wrapper">
        <div className="rules-list-pane" data-testid="rules-list-pane">
          {entitlement?.[selectedState]?.["CNTSRV_RUL"] === 1 ? (
            <ListSection
              data={data}
              staticData={props.staticData}
              selectedState={selectedState}
              tab="rules"
              serviceType={["Rules", "CNTSRV_RUL"]}
              listAttributes="rule_s"
              titlePrefix="Rule"
              selectedItem={selectedItem}
              setSelectedItem={setSelectedItem}
              setRefinedDataLength={setRefinedDataLength}
              tabDetails={
                <DetailSectionTabs
                  tabNames={rulesTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              }
            />
          ) : (
            <NotSubscribedMessage splitColumn={false} />
          )}
        </div>
        <div
          className={`rules-detail ${
            (parentFiling.length || criticalUpdates.length) > 0 &&
            selectedItem === "" &&
            criticalUpdates[0]?.id !== "" &&
            criticalUpdates[0]?.service_type?.includes("CNTSRV_RUL")
              ? "fi-accordion-data"
              : ""
          }`}
          data-testid="rules-detail"
        >
          {entitlement?.[selectedState]?.["CNTSRV_RUL"] === 1 &&
            (refinedDataLength === 0 ||
            filingStatus === undefined ||
            filingStatus === "" ? null : selectedItem === "" ? (
              <>
                {parentFiling.length > 0 && (
                  <div className="parent-filing fi-accordion-item">
                    {parentFiling.map((item, index) => (
                      <Accordion
                        key={index}
                        params={props}
                        staticData={props.staticData}
                        data={data}
                        fields={{
                          heading: ruleNumber,
                          Title: {
                            value: `This ${selectedStateDetails[0]?.label} revision also introduces the following prior revision(s):`,
                          },
                          Content: { value: "parent-filing" },
                          product: { value: "FI" },
                          filingData: item,
                        }}
                      />
                    ))}
                  </div>
                )}
                {criticalUpdates.length > 0 && (
                  <div
                    className={`critical-update ${
                      parentFiling.length > 0
                        ? "critical-update-hr"
                        : "fi-accordion-item"
                    }`}
                  >
                    {criticalUpdates.map((item, index) => (
                      <>
                        {item?.id !== "" &&
                          item?.service_type?.includes("CNTSRV_RUL") && (
                            <Accordion
                              key={index}
                              params={props}
                              staticData={props.staticData}
                              data={data}
                              fields={{
                                heading: ruleNumber,
                                Title: {
                                  value: `Critical Update : Circular ${item.id}`,
                                },
                                Content: { value: "critical-update" },
                                product: { value: "FI" },
                                filingData: [item],
                              }}
                            />
                          )}
                      </>
                    ))}
                  </div>
                )}
                {filingStatus !== "STATUS_NOFILINGIMPACT" && (
                  <div className="rules-default-text">
                    <div className="rules-description">
                      <span data-testid="rules-default-text">
                        {staticData.RulesTabHighlightedText.value}{" "}
                      </span>
                      {staticData.RulesTabText.value}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <>
                <div className="selected-rules-item flex-wrapper">
                  <span className="selected-rules-item-text">
                    <b>
                      {getRuleDisplayName(ruleNumber, documentType, "rules")}
                    </b>
                    {getRuleDisplayName(ruleNumber, documentType, "rules") &&
                      " - "}
                    {`${documentType}` === "CT - Class Table" ? (
                      <b>{`${documentTitle}`}</b>
                    ) : (
                      `${documentTitle}`
                    )}
                  </span>
                </div>
                <DetailSectionTabs
                  tabNames={rulesTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              </>
            ))}
        </div>
      </div>
    </div>
  );
};

export default RulesTab;
