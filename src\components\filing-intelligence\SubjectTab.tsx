import React, { Fragment, useState, useMemo, useEffect } from "react";
import { groupBy } from "lodash";
// import DOMPurify from 'dompurify'
import Select from "react-select";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import LockIcon from "@mui/icons-material/Lock";
import ListSection from "./common/ListSection";
import DetailSectionTabs from "./common/DetailSectionTabs";
import ReadFullContent from "./common/ReadFullContent";
import {
  generateActionCountDropdownOptions,
  sortByAlphabeticalOrder,
} from "src/helpers/fi/utils";

interface ActionTypeOptions {
  label: string;
  value: string;
}

const SubjectTab = (props: {
  staticData: any;
  data: any;
  selectedState: string;
  entitlement: any;
}) => {
  const { data, selectedState, entitlement } = props || {};
  const staticData = props.staticData.SubjectTabData;
  const staticDataSummary = props.staticData.Summary;

  const allServicesText = "All Services";

  const getFilterDropdownOptions = (topics: any[]) => {
    const services = ["Forms", "Rules", "Loss Costs"];
    let topicsLength = topics?.length || 0;

    const actionCount: Record<string, number> = {
      [allServicesText]: topicsLength,
      Forms: 0,
      Rules: 0,
      "Loss Costs": 0,
    };
    const servicesList: Record<string, any[]> = {
      Forms: [],
      Rules: [],
      "Loss Costs": [],
    };

    if (topicsLength) {
      topics.forEach((item: { id: string }) => {
        const id = item?.id;
        if (!id) return;

        const list: string[] | any = subjectType(id, "COUNT") || [];
        if (list?.length) {
          list.forEach((item1: string) => {
            if (services.includes(item1)) {
              actionCount[item1] += 1;
              servicesList[item1].push(item);
            }
          });
        }
      });
    }

    const options: ActionTypeOptions[] =
      generateActionCountDropdownOptions(actionCount);

    return { options, servicesList };
  };

  const { options: filterOptions, servicesList } = useMemo(() => {
    if (!data?.topics) {
      return { options: [], servicesList: {} };
    }
    return getFilterDropdownOptions(data.topics);
  }, [data?.topics, selectedState]);

  const [selectedTopic, setSelectedTopic] = useState("");
  const [selectedDetail, setSelectedDetail] = useState("Background");
  const [selectedTopicGroup, setSelectedTopicGroup] = useState("");
  const [selectedFilter, setSelectedFilter] = useState<string>(
    filterOptions?.[0]?.value || allServicesText
  );

  const subjectDetailTabs = [
    ["Background", null],
    ["Topical Forms", null],
    ["Topical Rules", null],
    ["Topical Loss Costs", null],
  ].filter(
    (item) =>
      ["LOB_CA", "LOB_HO"].includes(data.filing_set.lob) ||
      item[0]?.includes(selectedTopicGroup) ||
      item[0] === "Background"
  );

  const serviceLine = {
    Forms: "CNTSRV_FRM",
    Rules: "CNTSRV_RUL",
    "Loss Costs": "CNTSRV_LSC",
  };

  const selectedTopicName = data.topics.filter(
    (item: { id: string }) => item.id === selectedTopic
  )[0]?.topic_name;

  const listSectionProps = {
    key: selectedTopic + selectedDetail,
    data: data,
    staticData: props.staticData,
    selectedState: selectedState,
    selectedTopic: selectedTopic,
    selectedTopicName: selectedTopicName,
  };

  function checkTopicFiled(serviceType: string) {
    return (
      data.filings
        .filter(
          (item: { service_type: string }) => item.service_type === serviceType
        )[0]
        .filing_status_applicability.filter(
          (item: { jurisdiction: string }) =>
            item.jurisdiction === selectedState
        )[0]?.filing_status !== "STATUS_NOFILINGIMPACT"
    );
  }

  function getTopicResults(topicId: string) {
    let topicResult = [];
    data.forms.some((form: { topic_id: string[] }) =>
      form.topic_id?.includes(topicId)
    ) &&
      checkTopicFiled("CNTSRV_FRM") &&
      topicResult.push("Forms");
    data.rules.some(
      (rule: { topic_id: string[]; state_type: string }) =>
        rule.topic_id?.includes(topicId) &&
        (rule.state_type === selectedState || rule.state_type === "MU")
    ) &&
      checkTopicFiled("CNTSRV_RUL") &&
      topicResult.push("Rules");
    data.loss_costs.some(
      (lossCost: { topic_id: string[]; state_type: string }) =>
        lossCost.topic_id?.includes(topicId) &&
        (lossCost.state_type === selectedState || lossCost.state_type === "MU")
    ) &&
      checkTopicFiled("CNTSRV_LSC") &&
      topicResult.push("Loss Costs");

    return topicResult;
  }

  function subjectType(topicId: string, type?: string) {
    if (!topicId) return;

    const topicResponse = getTopicResults(topicId);

    if (!topicResponse?.length) return;

    return type?.toLowerCase() === "count" ? (
      topicResponse
    ) : (
      <span className="subject-types">({topicResponse.join(", ")})</span>
    );
  }

  const Background = (tab: string) => {
    const summary = data[tab]
      .filter(
        (item: any) =>
          item.topic_name === selectedTopicName && item.id === selectedTopic
      )
      .map((item: any) => item.topic_background)[0];

    if (typeof window !== "undefined" && window.digitalData?.product?.FI) {
      window.digitalData.product.FI.filing_topic = selectedTopicName ?? "";
    }

    return (
      <div className="subject-background-container">
        {summary ? (
          <ReadFullContent
            orderIndex={0}
            topicName={""}
            contentClassName="summary"
            label="Summary"
            content={summary}
            lineClamp={[summary].length < 2 ? false : 3}
            expandLabel="Read Full Explanation of Changes"
            collapseLabel="Collapse Explanation of Changes"
          />
        ) : (
          <span className="no-summary-available">
            {staticDataSummary?.NoSummaryText?.value}
          </span>
        )}
      </div>
    );
  };

  const tabsInfo = {
    Background: Background("topics"),
    "Topical Forms": (
      <ListSection
        {...listSectionProps}
        tab="forms"
        serviceType={["Forms", "CNTSRV_FRM"]}
        listAttributes="display_form_number"
      />
    ),
    "Topical Rules": (
      <ListSection
        {...listSectionProps}
        tab="rules"
        serviceType={["Rules", "CNTSRV_RUL"]}
        listAttributes="rule_s"
        titlePrefix="Rule"
      />
    ),
    "Topical Loss Costs": (
      <ListSection
        {...listSectionProps}
        tab="loss_costs"
        serviceType={["Loss Costs", "CNTSRV_LSC"]}
        listAttributes="rule_s"
        titlePrefix="Rule"
      />
    ),
  }[selectedDetail];

  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
      border: 0,
      boxShadow: "none",
    }),
  };

  const topicData = useMemo(() => {
    const topicsToSort =
      !selectedFilter || selectedFilter?.toLowerCase() === "all services"
        ? data?.topics
        : servicesList[selectedFilter] || [];
    setSelectedTopic("");
    return sortByAlphabeticalOrder(topicsToSort, "topic_name");
  }, [data?.topics, servicesList, selectedFilter]);

  const groupedTopicData = topicData.flatMap((item: any) => {
    return getTopicResults(item.id)?.map((item1: any) => ({
      ...item,
      group: item1,
    }));
  });

  useEffect(() => {
    filterOptions.length > 0 &&
      setSelectedFilter(filterOptions?.[0]?.value || allServicesText);
  }, [selectedState]);

  useEffect(() => {
    setSelectedDetail("Background");
  }, [selectedTopicGroup]);

  const TopicTypeFilter = () => (
    <div
      className="all-list-section"
      data-testid="all-list-section"
      data-interaction="fi_sort_topic_dropdown"
      data-filter-selected={selectedFilter}
    >
      <span
        className="list-section-type-text"
        data-testid="list-section-type-text"
      >
        Topic Type:
      </span>
      <Select
        key={selectedState}
        classNamePrefix="react-select"
        className="select-filter-type pointer-cursor"
        instanceId="select-filter-type pointer-cursor"
        data-testid="select-filter-type"
        options={filterOptions}
        components={{
          IndicatorSeparator: () => null,
          DropdownIndicator: () => <ExpandMoreIcon />,
        }}
        isSearchable={false}
        styles={reactSelectStyle}
        onChange={(e: any) => {
          setSelectedFilter(e?.value || allServicesText);
        }}
        defaultValue={
          filterOptions?.[0] || {
            label: allServicesText,
            value: allServicesText,
          }
        }
      />
    </div>
  );

  const TopicsListItem = (subject: {
    id: string;
    topicName: string;
    topicGroup: string;
  }) => {
    return (
      <Fragment key={subject.id}>
        {subjectType(subject.id) && (
          <>
            <div
              className={`subject-info pointer-cursor ${
                (selectedTopic === subject.id &&
                  selectedTopicGroup === subject.topicGroup &&
                  "subject-info-selected") ||
                ""
              }`}
              data-testid={subject.id}
              onClick={() => {
                setSelectedTopic(
                  selectedTopic === subject.id &&
                    selectedTopicGroup === subject.topicGroup
                    ? ""
                    : subject.id
                );
                setSelectedTopicGroup(subject.topicGroup);
                let evt = new CustomEvent("event-view-end");
                document.body.dispatchEvent(evt);

                let evt_details = new CustomEvent("event-detailBox-view");
                document.body.dispatchEvent(evt_details);
              }}
            >
              <span className="subject-name">{subject.topicName}</span>{" "}
              {["LOB_CA", "LOB_HO"].includes(data.filing_set.lob) &&
                subjectType(subject.id)}
            </div>
            {selectedTopic === subject.id && (
              <div className="detail-pane">
                <DetailSectionTabs
                  tabNames={subjectDetailTabs}
                  tabsInfo={tabsInfo}
                  selectedDetail={selectedDetail}
                  setSelectedDetail={setSelectedDetail}
                  selectedState={selectedState}
                  entitlement={entitlement}
                />
              </div>
            )}
          </>
        )}
      </Fragment>
    );
  };

  return (
    <div className="subject-tab-content">
      <div className="subject-wrapper">
        <div className="subject-list">
          <div className="subject-tab-text" data-testid="subject-tab-text">
            {staticData.SubjectText.value}{" "}
            {data.filing_set.lob === "LOB_CA"
              ? "Commercial Auto"
              : data.filing_set.lob === "LOB_HO"
              ? "Homeowners"
              : data.filing_set.lob === "LOB_BP"
              ? "Businessowners"
              : "Commercial Farm"}{" "}
            {staticData.RevisionText.value}
          </div>
          <div
            className="subject-all-list-section"
            data-testid="subject-all-list-section"
          >
            {["LOB_CA", "LOB_HO"].includes(data.filing_set.lob) && (
              <TopicTypeFilter />
            )}
          </div>
          <div className="subject-section-list">
            {["LOB_CA", "LOB_HO"].includes(data.filing_set.lob)
              ? topicData.map((subject: { id: string; topic_name: string }) => (
                  <TopicsListItem
                    key={subject.id}
                    id={subject.id}
                    topicName={subject.topic_name}
                    topicGroup={""}
                  />
                ))
              : Object.entries(
                  groupBy(groupedTopicData, ({ group }) => group)
                ).map(([groupName, groupItems]) => (
                  <details
                    key={groupName}
                    className={`topics-list-group ${
                      entitlement[selectedState][serviceLine[groupName]] === 0
                        ? "topics-list-group-disabled"
                        : ""
                    }`}
                    open={false}
                  >
                    <summary className="topics-list-grouping-header">
                      {groupName} Filing Topics
                      {entitlement[selectedState][serviceLine[groupName]] ===
                      0 ? (
                        <LockIcon style={{ fontSize: "1rem" }} />
                      ) : (
                        <> ({groupItems.length})</>
                      )}
                    </summary>
                    {groupItems
                      .filter(
                        () =>
                          entitlement[selectedState][serviceLine[groupName]] !==
                          0
                      )
                      .map((subject: any) => (
                        <TopicsListItem
                          key={subject.id}
                          id={subject.id}
                          topicName={subject.topic_name}
                          topicGroup={groupName}
                        />
                      ))}
                  </details>
                ))}
          </div>
        </div>
        <div className="subject-detail">
          {selectedTopic === "" ? (
            <div className="subject-default-text">
              <b data-testid="subject-default-text">
                {staticData.SubjectTabHighlightedText.value}
              </b>{" "}
              {staticData.SubjectTabText.value}
            </div>
          ) : (
            <>
              <div className="selected-topic-item flex-wrapper">
                <span className="selected-topic-item-text">
                  {selectedTopicName}
                </span>
              </div>
              <DetailSectionTabs
                tabNames={subjectDetailTabs}
                tabsInfo={tabsInfo}
                selectedDetail={selectedDetail}
                setSelectedDetail={setSelectedDetail}
                selectedState={selectedState}
                entitlement={entitlement}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubjectTab;
