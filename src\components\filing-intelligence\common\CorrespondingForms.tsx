import { useState, useEffect, useContext, useMemo } from "react";
import Select from "react-select";
import PreviewDocuments from "./PreviewDocuments";
import DownloadCircularButton from "./DownloadCircularButton";
import SubDetails from "./SubDetails";
import {
  createPropsForPreview,
  getActionStatus,
  getCircularTitle,
} from "src/helpers/fi/utils";
import { AuthContext } from "../../../context/authContext";
import { getFilingDetails } from "../../../helpers/fi/filingDetails";
import NotSubscribedMessage from "./NotSubscribedMessage";
import { getRuleDisplayName } from "src/helpers/fi/getRuleDisplayName";

interface CorrespondingForms {
  data: any;
  staticData: any;
  selectedItem: string;
  selectedState: string;
  selectedRuleNumber: number | string;
  documentType: string;
  documentTitle: string;
  tabAttributes: string[];
  entitlement: any;
  filingStatus?: string;
}

const CorrespondingForms = (props: CorrespondingForms) => {
  const {
    data,
    selectedItem,
    selectedState,
    selectedRuleNumber,
    documentType,
    documentTitle,
    tabAttributes: [tab, tabName],
    entitlement,
    filingStatus,
  } = props;
  const staticData = props.staticData.CorrespondingForms;
  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
      border: 0,
      boxShadow: "none",
    }),
  };

  //const [selectedState, setSelectedState] = useAtom(selectedStateAtom)
  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type === "CNTSRV_FRM"
  )[0].filing_id;

  const { accessToken } = useContext(AuthContext);

  const relatedFormIds = data[tab]
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_FRM" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : tab === "loss_costs"
          ? item.adopt_state_list?.length > 1
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  let relatedForms = data.forms.filter((item: { id: string }) =>
    relatedFormIds.includes(item.id)
  );

  if (tab === "rules" && selectedState !== "MU") {
    const ruleNumber = data[tab].filter(
      (item: { id: string }) => item.id === selectedItem
    )[0].rule_s;

    const muFormIds = data[tab]
      .filter(
        (item: { id: string; rule_s: string; state_type: string }) =>
          item.rule_s == ruleNumber && item.state_type === "MU"
      )[0]
      ?.edge?.filter(
        (item: {
          dest_content_type: string;
          edge_type: string;
          adopt_state_list: string;
        }) =>
          item.dest_content_type === "CNTSRV_FRM" &&
          item.edge_type == "corresponding_content" &&
          item.adopt_state_list?.includes(selectedState)
      )
      .map((item: { dest_content_key: string }) => item.dest_content_key);

    if (muFormIds !== undefined && muFormIds.length > 0) {
      let muData = data.forms.filter((item: { id: string }) =>
        muFormIds.includes(item.id)
      );
      relatedForms = [...new Set([...relatedForms, ...muData])];
      relatedForms = [...new Set([...relatedForms])];
    }

    relatedForms.sort((formId: { id: string }, keyId: { id: string }) =>
      formId.id.localeCompare(keyId.id)
    );
  }

  const formOptions = relatedForms.map(
    (form: { id: string; document_title: string }) => ({
      label: `Form ${form.id} - ${form.document_title}`,
      value: form.id,
    })
  );
  const defaultFormId = relatedForms[0] ? relatedForms[0].id : null;
  const [selectedForm, setSelectedForm] = useState(defaultFormId);
  const [circularFilePath, setCircularFilePath] = useState("");
  const [circularFileZipPath, setCircularFileZipPath] = useState("");
  const selectedFormDetails =
    relatedForms.filter(
      (item: { id: string }) => item.id === selectedForm
    )[0] || {};
  const [listCircularNumbers, setListCircularNumbers] = useState("");

  const filteredStateFilings: Array<any> = data.filings
    .filter(
      (item: { service_type: string }) => item.service_type === "CNTSRV_FRM"
    )[0]
    .filing_status_applicability.filter(
      (item: { jurisdiction: string }) => item.jurisdiction === selectedState
    );

  useEffect(() => {
    const filingDetails = getFilingDetails({
      data,
      serviceType: "CNTSRV_FRM",
      selectedState,
      isMUFiledCircular: true,
    });
    const circularNumber = filingDetails.event_id;
    setListCircularNumbers(circularNumber);
    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};
    setCircularFilePath(file_path);
    setCircularFileZipPath(file_zip_path);
  }, [selectedState]);

  const correspondingText =
    staticData.UseText.value +
    " " +
    (selectedRuleNumber
      ? !documentTitle?.toLowerCase().includes("classifications")
        ? getRuleDisplayName(selectedRuleNumber, documentType, tab)
        : documentTitle?.toLowerCase().includes("classifications") &&
          documentType === "CT - Class Table"
        ? `${selectedRuleNumber}`
        : ""
      : documentTitle);

  const getFormRuleDisplayName = () => {
    if (tabName === "Rule") {
      return getRuleDisplayName(selectedRuleNumber, documentType, tab);
    }
    return `${
      tabName === "Loss Cost" ? `${tabName} Rule` : tabName
    } ${selectedRuleNumber}`;
  };

  const getCorrespondingDisplayedText = (
    correspondingText: string,
    selectedRuleNumber: string | number
  ) => {
    const isRuleMH = correspondingText.includes("Rule MH");
    const isSelectedRuleNumberAlpha = /[A-Za-z]/.test(
      selectedRuleNumber?.toString() || ""
    );

    let displayedText = correspondingText;

    if (isRuleMH) {
      displayedText = correspondingText.replace("Rule MH", "Mobilehome Rule");
    }

    const classificationText = isSelectedRuleNumberAlpha
      ? " Classifications"
      : "";

    return `${displayedText}  ${classificationText}`;
  };

  const subDetailsList = [
    {
      label: staticData.ActionText.value,
      value: getActionStatus(props.staticData, selectedFormDetails.action),
    },
    {
      label: staticData.FormType.value,
      value: selectedFormDetails.form_type,
    },
    {
      label: staticData.MandatoryIndc.value,
      value: selectedFormDetails.mandatory_indc === "Y" ? "Yes" : "No",
    },
    {
      label: staticData.StateType.value,
      value: selectedFormDetails.state_type === "MU" ? "Yes" : "No",
    },
    {
      label: staticData.NonApplicableStates.value,
      value: selectedFormDetails.non_applicable_states?.join(", "),
    },
    {
      label: staticData.PendingAdoptionStates.value,
      value: selectedFormDetails.pending_adoption_states?.join(", "),
    },
  ];

  const correspondingDisplayedText = useMemo(
    () => getCorrespondingDisplayedText(correspondingText, selectedRuleNumber),
    [correspondingText, selectedRuleNumber]
  );

  const circularTitle = useMemo(
    () => getCircularTitle(filingStatus || ""),
    [filingStatus]
  );

  const downloadCircularButtonObj = {
    circularFilePath,
    circularFileZipPath,
    accessToken,
    revisionData: props.staticData.RevisionData,
    circularNumber: listCircularNumbers,
    circularTitle,
  };

  return (
    <div className="corresponding-forms" data-testid="corresponding-forms">
      {entitlement[selectedState]["CNTSRV_FRM"] === 1 ? (
        relatedForms.length > 0 ? (
          <>
            {relatedForms.length !== 1 && (
              <>
                <div className="hint-text">
                  {`${staticData.HintText.value} ${getFormRuleDisplayName()}`}
                </div>
                <Select
                  className="select-corresponding-forms"
                  instanceId="select-corresponding-forms"
                  options={formOptions}
                  components={{ IndicatorSeparator: () => null }}
                  maxMenuHeight={176}
                  isSearchable={false}
                  styles={reactSelectStyle}
                  onChange={(e: any) => setSelectedForm(e.value)}
                  defaultValue={formOptions[0]}
                />
              </>
            )}
            <div>
              <span
                className="forms-preview-document pointer-cursor"
                data-testid="forms-preview-document"
              >
                <PreviewDocuments
                  key={selectedItem + selectedForm}
                  data={data}
                  staticData={props.staticData}
                  tab="forms"
                  selectedItem={selectedForm}
                  selectedState={selectedState}
                  document_list={createPropsForPreview({
                    jsonData: data,
                    section: "Forms",
                    state: selectedState,
                    key: selectedFormDetails.display_form_number,
                    itemDocumentList: selectedFormDetails.document_list,
                  })}
                  filingStatus={filingStatus}
                />
              </span>
              <span className="forms-selected-value">
                {staticData.FormText.value} {selectedForm}{" "}
              </span>{" "}
              {staticData.UseText.value}{" "}
              <span className="forms-selected-value">
                {getFormRuleDisplayName()}
                {/* // : {tabName === "Rule" &&
                  // selectedRuleNumber.toString().includes("MH")
                  //   ? " Mobilehome "
                  //   : documentType?.includes("RUA - State Additional Rules")
                  //   ? "Additional Rule "
                  //   : documentType?.includes("CLA - Classification Pages")
                  //   ? "Classification Pages Rule "
                  //   : tabName + " "}
                  // <span className="forms-selected-value">
                  //   {tabName === "Rule" &&
                  //   selectedRuleNumber.toString().includes("MH")
                  //     ? selectedRuleNumber.toString().replace("MH", "Rule")*/}
              </span>{" "}
              - {documentTitle}
            </div>
            <div className="forms-detail" data-testid="forms-detail">
              {subDetailsList.map((item, index) => (
                <SubDetails name={"forms-value"} {...item} key={index} />
              ))}
              <div className="forms-filing-id">
                {staticData.FilingId.value}:{" "}
                <span className="forms-value">{filingId}</span>
              </div>
              <div>
                {staticData.CircularNumber.value}:{" "}
                <span className="forms-circular-list">
                  {listCircularNumbers}
                </span>
                <DownloadCircularButton {...downloadCircularButtonObj} />
              </div>
            </div>
          </>
        ) : (
          <>
            {filteredStateFilings[0].filing_status !==
            "STATUS_NOFILINGIMPACT" ? (
              <>
                <span className="forms-text-value">
                  {staticData.NoCorrespondingText.value}
                </span>{" "}
                {correspondingDisplayedText}
              </>
            ) : (
              <span className="forms-text-value">
                Forms have not been filed for this state
              </span>
            )}
          </>
        )
      ) : (
        <NotSubscribedMessage splitColumn={false} />
      )}
    </div>
  );
};

export default CorrespondingForms;
