import { useState, useEffect, useContext, useMemo } from "react";
import Select from "react-select";
import DownloadCircularButton from "./DownloadCircularButton";
import SubDetails from "./SubDetails";
import { getFilingDetails } from "../../../helpers/fi/filingDetails";
import { AuthContext } from "../../../context/authContext";
import NotSubscribedMessage from "./NotSubscribedMessage";
import { getActionStatus, getCircularTitle } from "src/helpers/fi/utils";
import { getRuleDisplayName } from "src/helpers/fi/getRuleDisplayName";

interface CorrespondingLossCosts {
  data: any;
  staticData: any;
  selectedState: string;
  selectedItem: string;
  selectedRuleNumber?: number | string;
  documentType: string;
  documentTitle: string;
  tabAttributes: string[];
  entitlement: any;
}

const CorrespondingLossCosts = (props: CorrespondingLossCosts) => {
  const {
    data,
    selectedState,
    selectedItem,
    selectedRuleNumber,
    documentType,
    documentTitle,
    tabAttributes: [tab, tabName],
    entitlement,
  } = props;
  const staticData = props.staticData.CorrespondingLossCosts;

  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
      border: 0,
      boxShadow: "none",
    }),
  };

  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)
  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type === "CNTSRV_LSC"
  )[0].filing_id;

  const { accessToken } = useContext(AuthContext);

  const partOfInd = (lossCostId: string) =>
    data.filings.some(
      (item: { service_type: string; edge: any[] }) =>
        item.service_type === "CNTSRV_LSC" &&
        item.edge.some(
          (item: { dest_content_type: string; dest_content_key: string }) =>
            item.dest_content_type === "CNTSRV_LSC" &&
            item.dest_content_key === lossCostId
        )
    );

  const relatedLossCostIds = data[tab]
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    .edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_LSC" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : tab === "rules"
          ? item.adopt_state_list?.length > 0
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  let relatedLossCosts = data.loss_costs.filter((item: { id: string }) =>
    relatedLossCostIds.includes(item.id)
  );

  if ((tab === "rules" || tab === "loss_costs") && selectedState !== "MU") {
    const id = data[tab].filter(
      (item: { id: string }) => item.id === selectedItem
    )[0].rule_s;

    let muIds = data[tab]
      .filter(
        (item: { id: string; rule_s: string; state_type: string }) =>
          item.state_type === "MU" && item.rule_s === id
      )[0]
      ?.edge?.filter(
        (item: any) =>
          item.dest_content_type === "CNTSRV_LSC" &&
          item.edge_type === "corresponding_content" &&
          item.adopt_state_list?.includes(selectedState)
      )
      .map((item: { dest_content_key: string[] }) => item.dest_content_key);

    if (muIds !== undefined && muIds.length > 0) {
      let muData = data.loss_costs.filter((item: { id: string }) =>
        muIds.includes(item.id)
      );
      relatedLossCosts = [...new Set([...relatedLossCosts, ...muData])];
    }

    relatedLossCosts.sort((formId: { id: string }, keyId: { id: string }) =>
      formId.id.localeCompare(keyId.id)
    );
  }

  const relatedLossCostsMU = relatedLossCosts.filter(
    (item: { state_type: string }) => item.state_type === "MU"
  );

  const relatedLossCostsState = Object.values(
    relatedLossCosts.reduce((acc: any, obj: any) => {
      acc[obj.rule_s] = obj;
      return acc;
    }, {})
  );

  relatedLossCosts =
    selectedState === "MU" ? relatedLossCostsMU : relatedLossCostsState;

  const lossCostOptions = relatedLossCosts.map(
    (lossCost: { rule_s: number; document_title: string; id: string }) => ({
      label: `Loss Costs Rule ${lossCost.rule_s} - ${lossCost.document_title}`,
      value: lossCost.id,
    })
  );

  const filteredStateFilings: Array<any> =
    data.filings
      .filter(
        (item: { service_type: string }) => item.service_type === "CNTSRV_LSC"
      )[0]
      ?.filing_status_applicability.filter(
        (item: { jurisdiction: string }) => item.jurisdiction === selectedState
      ) || [];

  const defaultLossCostId = relatedLossCosts[0] ? relatedLossCosts[0].id : null;
  const [selectedLossCost, setSelectedLossCost] = useState(defaultLossCostId);

  const selectedLossCostDetails =
    relatedLossCosts.filter(
      (item: { id: string }) => item.id === selectedLossCost
    )[0] || {};

  const stateExceptionForms =
    selectedLossCostDetails.rule_s !== undefined && selectedState !== "MU"
      ? data.loss_costs.filter(
          (item: { rule_s: string; state_type: string }) =>
            item.rule_s === selectedLossCostDetails.rule_s &&
            item.state_type === selectedState
        )
      : undefined;

  const [listCircularNumbers, setListCircularNumbers] = useState("");
  const [circularFilePath, setCircularFilePath] = useState("");
  const [circularFileZipPath, setCircularFileZipPath] = useState("");
  const [filingStatus, setfilingStatus] = useState<string>("");

  useEffect(() => {
    const filingDetails = getFilingDetails({
      data,
      serviceType: "CNTSRV_LSC",
      selectedState,
      isMUFiledCircular: true,
    });
    const circularNumber = filingDetails.event_id;
    setListCircularNumbers(circularNumber);
    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};

    setCircularFilePath(file_path);
    setCircularFileZipPath(file_zip_path);
    setfilingStatus(filingDetails.filing_status);
  }, [selectedState]);

  const subDetailsList = [
    {
      label: staticData.ActionText.value,
      value: getActionStatus(props.staticData, selectedLossCostDetails.action),
    },
    {
      label: staticData.StateType.value,
      value:
        stateExceptionForms !== undefined && stateExceptionForms.length > 0
          ? "No"
          : "Yes",
    },
    {
      label: staticData.PartOfInd.value,
      value: partOfInd(selectedLossCostDetails.id) === true ? "Yes" : "No",
    },
  ];

  const circularTitle = useMemo(
    () => getCircularTitle(filingStatus),
    [filingStatus]
  );

  const downloadCircularButtonObj = {
    circularFilePath,
    circularFileZipPath,
    accessToken,
    revisionData: props.staticData.RevisionData,
    circularNumber: listCircularNumbers,
    circularTitle,
  };

  return (
    <div
      className="corresponding-loss-costs"
      data-testid="corresponding-loss-costs"
    >
      {entitlement[selectedState]["CNTSRV_LSC"] === 1 ? (
        relatedLossCosts.length > 0 ? (
          <>
            {relatedLossCosts.length != 1 && (
              <>
                <div className="hint-text">
                  {staticData.HintText.value} {tabName}{" "}
                  {tab === "forms" ? selectedItem : selectedRuleNumber}
                </div>
                <Select
                  className="select-corresponding-loss-costs"
                  instanceId="select-corresponding-loss-costs"
                  options={lossCostOptions}
                  components={{ IndicatorSeparator: () => null }}
                  maxMenuHeight={176}
                  isSearchable={false}
                  styles={reactSelectStyle}
                  onChange={(e: any) => setSelectedLossCost(e.value)}
                  defaultValue={lossCostOptions[0]}
                />
              </>
            )}
            <div>
              <span className="loss-costs-selected-value">
                {staticData.LossCostText.value} {selectedLossCostDetails.rule_s}{" "}
              </span>{" "}
              {staticData.UseText.value}{" "}
              <span className="loss-costs-selected-value">
                {tab === "forms"
                  ? selectedItem
                  : getRuleDisplayName(selectedRuleNumber, documentType, tab)}
              </span>{" "}
              - {documentTitle}
            </div>
            <div className="loss-costs-detail" data-testid="loss-costs-detail">
              {subDetailsList.map((item, index) => (
                <SubDetails name={"loss-costs-value"} {...item} key={index} />
              ))}
              <div
                className="loss-costs-download"
                data-testid="loss-costs-download"
              >
                {/* {staticData.LossCostsDownload.value}: */}
              </div>
              <div
                className="loss-costs-filing-id"
                data-testid="loss-costs-filing-id"
              >
                {staticData.FilingIDText.value}:{" "}
                <span className="loss-costs-value">{filingId}</span>
              </div>
              <div>
                {staticData.CircularNumber.value}:{" "}
                <span className="loss-costs-circular-list">
                  {listCircularNumbers}
                </span>
                <DownloadCircularButton {...downloadCircularButtonObj} />
              </div>
            </div>
          </>
        ) : (
          <>
            {filteredStateFilings[0].filing_status !==
            "STATUS_NOFILINGIMPACT" ? (
              <>
                <span
                  className="loss-costs-text-value"
                  data-testid="loss-costs-text-value"
                >
                  {data.filing_set.lob === "LOB_HO"
                    ? staticData.StateSpecificReviewMsg.value
                    : staticData.NoCorrespondingText.value}
                </span>
                {data.filing_set.lob === "LOB_HO"
                  ? ""
                  : tab === "forms"
                  ? " " +
                    staticData.UseText.value +
                    " " +
                    tabName +
                    "  " +
                    selectedItem
                  : tab === "rules"
                  ? " " +
                    staticData.UseText.value +
                    " " +
                    (selectedRuleNumber
                      ? !documentTitle
                          ?.toLowerCase()
                          .includes("classifications")
                        ? getRuleDisplayName(
                            selectedRuleNumber,
                            documentType,
                            tab
                          )
                        : documentTitle
                            ?.toLowerCase()
                            .includes("classifications") &&
                          documentType === "CT - Class Table"
                        ? `${selectedRuleNumber}`
                        : ""
                      : documentTitle) +
                    "  " +
                    (/[A-Za-z]/.test(selectedRuleNumber)
                      ? "Classifications"
                      : "")
                  : selectedRuleNumber}
              </>
            ) : (
              <span className="loss-costs-text-value">
                Loss Costs have not been filed for this state
              </span>
            )}
          </>
        )
      ) : (
        <NotSubscribedMessage splitColumn={false} />
      )}
    </div>
  );
};

export default CorrespondingLossCosts;
