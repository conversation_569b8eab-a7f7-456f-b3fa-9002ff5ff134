import { useState, useEffect, useContext, useMemo } from "react";
import Select from "react-select";
import DownloadCircularButton from "./DownloadCircularButton";
import SubDetails from "./SubDetails";
import PreviewDocuments from "./PreviewDocuments";
import {
  createPropsForPreview,
  getActionStatus,
  getCircularTitle,
} from "src/helpers/fi/utils";
import { AuthContext } from "../../../context/authContext";
import { getFilingDetails } from "../../../helpers/fi/filingDetails";
import NotSubscribedMessage from "./NotSubscribedMessage";
import { getRuleDisplayName } from "src/helpers/fi/getRuleDisplayName";

interface CorrespondingRules {
  data: any;
  staticData: any;
  selectedState: string;
  selectedItem: string;
  selectedRuleNumber?: number | string;
  documentTitle: string;
  tabAttributes: string[];
  entitlement: any;
}

const CorrespondingRules = (props: CorrespondingRules) => {
  const {
    data,
    selectedState,
    selectedItem,
    selectedRuleNumber,
    documentTitle,
    tabAttributes: [tab, tabName],
    entitlement,
  } = props;
  const staticData = props.staticData.CorrespondingRules;
  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
      border: 0,
      boxShadow: "none",
    }),
  };

  const { accessToken } = useContext(AuthContext);

  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type === "CNTSRV_RUL"
  )[0].filing_id;

  //const [selectedState, setSelectedState] = useAtom(selectedStateAtom)
  const partOfInd = (ruleId: string) =>
    data.filings.some(
      (item: { service_type: string; edge: any[] }) =>
        item.service_type === "CNTSRV_RUL" &&
        item.edge.some(
          (item: { dest_content_type: string; dest_content_key: string }) =>
            item.dest_content_type === "CNTSRV_RUL" &&
            item.dest_content_key === ruleId
        )
    );

  const relatedRuleIds = data[tab]
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    .edge.filter(
      (item: { dest_content_type: string; adopt_state_list: Array<string> }) =>
        item.dest_content_type === "CNTSRV_RUL" &&
        (selectedState !== "MU"
          ? item.adopt_state_list?.includes(selectedState)
          : tab === "loss_costs"
          ? item.adopt_state_list?.length > 0
          : true)
    )
    .map((item: { dest_content_key: string }) => item.dest_content_key);

  let relatedRules = data.rules.filter((item: { id: string }) =>
    relatedRuleIds.includes(item.id)
  );

  const relatedRulesMU = relatedRules.filter(
    (item: { state_type: string }) => item.state_type === "MU"
  );

  const relatedRulesState = Object.values(
    relatedRules.reduce((acc: any, obj: any) => {
      acc[obj.rule_s] = obj;
      return acc;
    }, {})
  );

  relatedRules = selectedState === "MU" ? relatedRulesMU : relatedRulesState;

  const HORulesType = [
    "RUA - State Additional Rules",
    "RU - Rules - State Exception And Multistate",
    "CLA - Classification Pages",
  ];

  relatedRules =
    data.filing_set.lob === "LOB_HO"
      ? HORulesType.map((documentType: string) =>
          relatedRules.filter((item: any) =>
            item.document_type.includes(documentType)
          )
        ).flat()
      : relatedRules;

  const ruleOptions = relatedRules.map(
    (rule: {
      rule_s: number;
      document_title: string;
      id: string;
      document_type: string;
    }) => ({
      label:
        // getRuleDisplayName(rule.rule_s, rule.document_type) +
        // // rule.rule_s.toString().includes("MH")
        // //   ? "Mobilehome Rule" +
        // //     rule.rule_s.toString().replace("MH", "") +
        // //     " - " +
        // //     rule.document_title
        // //   : rule.document_type?.includes("RUA - State Additional Rules")
        // //   ? "Additional Rule " + rule.rule_s + " - " + rule.document_title
        // //   : rule.document_type?.includes("CLA - Classification Pages")
        // //   ? "Classification Pages Rule " +
        // //     rule.rule_s +
        // //     " - " +
        // //     rule.document_title
        // //   : "Rule " + rule.rule_s

        // " - " +
        // rule.document_title.toString(),

        ["LOB_HO", "LOB_BP"].includes(data.filing_set.lob) &&
        rule.document_type === "CT - Class Table"
          ? rule.document_title
          : getRuleDisplayName(rule.rule_s, rule.document_type, "rules") +
            " - " +
            rule.document_title.toString(),
      value: rule.id,
    })
  );

  const defaultRuleId = relatedRules[0] ? relatedRules[0].id : null;
  const [selectedRule, setSelectedRule] = useState(defaultRuleId);
  const [filingStatus, setfilingStatus] = useState<string>("");
  const [listCircularNumbers, setListCircularNumbers] = useState("");
  const [circularFilePath, setCircularFilePath] = useState("");
  const [circularFileZipPath, setCircularFileZipPath] = useState("");

  useEffect(() => {
    const filingDetails = getFilingDetails({
      data,
      serviceType: tab === "forms" ? "CNTSRV_FRM" : "CNTSRV_LSC",
      selectedState,
      isMUFiledCircular: true,
    });

    const filingDetail = getFilingDetails({
      data,
      serviceType: "CNTSRV_RUL",
      selectedState,
      isMUFiledCircular: true,
    });
    const circularNumber = filingDetail.event_id;

    setListCircularNumbers(circularNumber);

    const { file_path, file_zip_path } =
      data.filing_document_list.filter(
        (document: { circular_number: string }) =>
          document.circular_number === circularNumber
      )[0] || {};

    setCircularFilePath(file_path);
    setCircularFileZipPath(file_zip_path);
    setfilingStatus(filingDetails.filing_status);
  }, [selectedState]);

  const selectedRuleDetails =
    relatedRules.filter(
      (item: { id: string }) => item.id === selectedRule
    )[0] || {};

  const stateExceptionForms =
    selectedRuleDetails.rule_s !== undefined && selectedState !== "MU"
      ? data.rules.filter(
          (item: { rule_s: string; state_type: string }) =>
            item.rule_s === selectedRuleDetails.rule_s &&
            item.state_type === selectedState
        )
      : undefined;

  const filteredStateFilings: Array<any> = data.filings
    .filter(
      (item: { service_type: string }) => item.service_type === "CNTSRV_RUL"
    )
    .map((item: any) =>
      item.filing_status_applicability.filter(
        (status: { jurisdiction: string }) =>
          status.jurisdiction === selectedState
      )
    )
    .flat();

  const subDetailsList = [
    {
      label: staticData.ActionText.value,
      value: getActionStatus(props.staticData, selectedRuleDetails.action),
    },
    {
      label: staticData.StateType.value,
      value:
        stateExceptionForms !== undefined && stateExceptionForms.length > 0
          ? "No"
          : "Yes",
    },
    {
      label: staticData.PartOfInd.value,
      value: partOfInd(selectedRuleDetails.id) === true ? "Yes" : "No",
    },
  ];

  const circularTitle = useMemo(
    () => getCircularTitle(filingStatus),
    [filingStatus]
  );

  const downloadCircularButtonObj = {
    circularFilePath,
    circularFileZipPath,
    accessToken,
    revisionData: props.staticData.RevisionData,
    circularNumber: listCircularNumbers,
    circularTitle,
  };

  return (
    <div className="corresponding-rules" data-testid="corresponding-rules">
      {entitlement[selectedState]["CNTSRV_RUL"] === 1 ? (
        relatedRules.length > 0 ? (
          <>
            {relatedRules.length != 1 && (
              <>
                <div className="hint-text">
                  {staticData.HintText.value}{" "}
                  {tabName === "Loss Cost" ? tabName + " Rule " : tabName}{" "}
                  {tab === "forms" ? selectedItem : selectedRuleNumber}
                </div>
                <Select
                  className="select-corresponding-rules"
                  instanceId="select-corresponding-rules"
                  options={ruleOptions}
                  components={{ IndicatorSeparator: () => null }}
                  maxMenuHeight={176}
                  isSearchable={false}
                  styles={reactSelectStyle}
                  onChange={(e: any) => setSelectedRule(e.value)}
                  defaultValue={ruleOptions[0]}
                />
              </>
            )}
            <div>
              <span
                className="rules-preview-document pointer-cursor"
                data-testid="rules-preview-document"
              >
                <PreviewDocuments
                  selectedItem={selectedRule}
                  selectedState={selectedState}
                  data={data}
                  staticData={props.staticData}
                  tab="rules"
                  key={selectedItem + selectedRule}
                  filterAction={""}
                  filingStatus={filingStatus}
                  document_list={createPropsForPreview({
                    jsonData: data,
                    section: "Rules",
                    state: selectedRuleDetails.state_type,
                    key: selectedRuleDetails.rule_s,
                    itemDocumentList: selectedRuleDetails.document_list,
                  })}
                />
              </span>
              <span className="rules-selected-value">
                {staticData.RuleText.value.includes("Rule") &&
                  getRuleDisplayName(
                    selectedRuleDetails.rule_s,
                    selectedRuleDetails.document_type,
                    "rules",
                    selectedRuleDetails.document_title
                  )}
              </span>{" "}
              {staticData.UseText.value}{" "}
              {tabName === "Loss Cost" ? tabName + " Rule " : tabName}{" "}
              <span className="rules-selected-value">
                {tab === "forms" ? selectedItem : selectedRuleNumber}
              </span>{" "}
              - {documentTitle}
            </div>
            <div className="rules-detail" data-testid="rules-detail">
              {subDetailsList.map((item, index) => (
                <SubDetails name={"rules-value"} {...item} key={index} />
              ))}

              <div className="rules-filing-id">
                {staticData.FilingIDText.value}:{" "}
                <span className="rules-value">{filingId}</span>
              </div>
              <div>
                {staticData.CircularNumberText.value}:{" "}
                <span className="rules-circular-list">
                  {listCircularNumbers}
                </span>
                <DownloadCircularButton {...downloadCircularButtonObj} />
              </div>
            </div>
          </>
        ) : (
          <>
            {filteredStateFilings[0].filing_status !==
            "STATUS_NOFILINGIMPACT" ? (
              <>
                <span className="rules-text-value">
                  {staticData.NoCorrespondingText.value}
                </span>{" "}
                {staticData.UseText.value} {tabName}{" "}
                {tab === "forms" ? selectedItem : selectedRuleNumber}
              </>
            ) : (
              <span className="rules-text-value">
                Rules have not been filed for this state
              </span>
            )}
          </>
        )
      ) : (
        <NotSubscribedMessage splitColumn={false} />
      )}
    </div>
  );
};

export default CorrespondingRules;
