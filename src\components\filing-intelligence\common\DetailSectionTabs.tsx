import LockIcon from "@mui/icons-material/Lock";
import NotSubscribedMessage from "./NotSubscribedMessage";

interface DetailSectionTabsTypes {
  tabNames: Array<Array<any>>;
  tabsInfo: string | JSX.Element | undefined;
  selectedDetail: string;
  setSelectedDetail: any;
  selectedState: string;
  entitlement: any;
}

const DetailSectionTabs = ({
  tabNames,
  tabsInfo,
  selectedDetail,
  setSelectedDetail,
  selectedState,
  entitlement,
}: DetailSectionTabsTypes) => {
  const checkEntitlement = (tab: string) => {
    if (["Details", "History", "Background"].includes(tab)) return false;
    else
      return (
        (entitlement[selectedState]["CNTSRV_FRM"] === 0 &&
          (tab === "Corresponding Forms" || tab === "Topical Forms")) ||
        (entitlement[selectedState]["CNTSRV_RUL"] === 0 &&
          (tab === "Corresponding Rules" || tab === "Topical Rules")) ||
        (entitlement[selectedState]["CNTSRV_LSC"] === 0 &&
          (tab === "Corresponding Loss Costs" || tab === "Topical Loss Costs"))
      );
  };

  const handleClick = (tab: string) => {
    setSelectedDetail(tab);

    const evt = new CustomEvent("event-detailBox-show");
    document.body.dispatchEvent(evt);
  };

  return (
    <>
      <nav
        data-testid="topical-sections"
        className={`topical-sections ${
          tabNames.length <= 2 ? "section-one" : ""
        }`}
      >
        {tabNames.map(([tab, correspondingCount]: [string, number]) => (
          <div key={tab} className="topical-nav-tab">
            <button
              id={`fi_details_download${
                tab ? `_${tab?.toLowerCase()?.split(" ")?.join("_")}` : ""
              }`}
              tabIndex={0}
              className={`topical pointer-cursor ${
                selectedDetail === tab ? "selected-detail auto-cursor" : ""
              }`}
              onClick={() => handleClick(tab)}
              data-testid={`topical ${tab}`}
              data-interaction={`fi_details`}
              data-detail-selected={tab}
            >
              <span className="tab-name">
                <LockIcon
                  className={`lob-subscription-icon ${
                    !checkEntitlement(tab) ? "lob-icon-hidden" : ""
                  }`}
                />
                {tab}
                {!checkEntitlement(tab) &&
                  correspondingCount > 1 &&
                  ` (${correspondingCount})`}
              </span>
            </button>
            <div className={`${selectedDetail === tab ? "overview-hr" : ""}`} />
          </div>
        ))}
      </nav>
      {(entitlement[selectedState]["CNTSRV_FRM"] === 0 &&
        selectedDetail === "Topical Forms") ||
      (entitlement[selectedState]["CNTSRV_RUL"] === 0 &&
        selectedDetail === "Topical Rules") ||
      (entitlement[selectedState]["CNTSRV_LSC"] === 0 &&
        selectedDetail === "Topical Loss Costs") ? (
        <NotSubscribedMessage splitColumn={false} />
      ) : (
        tabsInfo
      )}
    </>
  );
};

export default DetailSectionTabs;
