import React from "react";
import Button from "@mui/material/Button";
import DownloadIcon from "@mui/icons-material/Download";
import { download } from "src/helpers/fi/documentDownload";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";

const DownloadCircularButton = ({
  circularFilePath,
  circularFileZipPath,
  accessToken,
  revisionData,
  circularNumber,
  circularTitle,
}: any) => {
  const downloadButtonList = [
    {
      id: "doc",
      name: "Word Doc",
    },
    {
      id: "pdf",
      name: "PDF",
    },
  ];

  const handleDownloadCircular = (docType: string) => {
    const isPdf = docType === "pdf" || false;
    download({
      api_url: isPdf
        ? process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL ?? ""
        : process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_GATEWAY_URL ?? "",
      urlToPreSign: {
        url: isPdf ? circularFilePath : circularFileZipPath,
        type: "CRC",
      },
      headers: { Authorization: accessToken },
      fileName: isPdf
        ? circularFilePath.substring(circularFilePath.lastIndexOf("/") + 1)
        : circularFileZipPath.substring(
            circularFileZipPath.lastIndexOf("/") + 1
          ),
      successMessage: (
        <ToastMessage
          type="success"
          title={revisionData.SuccessTitle.value}
          description={revisionData.SuccessMessage.value}
        />
      ),
      errorMessage: (
        <ToastMessage
          type="error"
          title={revisionData.ErrorTitle.value}
          description={revisionData.ErrorMessage.value}
        />
      ),
    });
  };

  return (
    <>
      {downloadButtonList.map(({ id = "", name = "" }) => {
        return (
          <Button
            Key={id}
            aria-label={`Download ${name}`}
            data-testid={`circular-download-btn-${id}`}
            className="circular-download-btn"
            data-action="download"
            data-interaction="fi_download3"
            data-format={id?.toLowerCase() === "pdf" ? "pdf" : "word"}
            data-circNumber={circularNumber}
            data-circStatus={circularTitle}
            variant="text"
            endIcon={<DownloadIcon />}
            onClick={() => handleDownloadCircular(id)}
          >
            {name}
          </Button>
        );
      })}
    </>
  );
};

export default DownloadCircularButton;
