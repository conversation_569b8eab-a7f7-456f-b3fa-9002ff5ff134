import { useState, useRef, useEffect, useContext } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import DownloadIcon from "@mui/icons-material/Download";
import { downloadAsZip } from "src/helpers/fi/documentDownload";
import useOnClickOutside from "src/hooks/fi/useOnClickOutside";
import * as CONST from "../../../helpers/fi/Constants";
import {
  getActionStatus,
  mergeArraysByReplacingId,
  getStoiData,
} from "src/helpers/fi/utils";
import { AuthContext } from "../../../context/authContext";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";

interface DownloadDropdown {
  listSectionData: any;
  data: any;
  staticData: any;
  selectedState: string;
  tab?: "forms" | "rules" | "loss_costs";
  mode: "forms" | "rules" | "loss_costs" | "preview";
  selectAllOptions: boolean;
  selectedItem: string;
  filingId: string;
  filterAction: string;
  filingStatus: string;
}

const DownloadDropdown = ({
  listSectionData,
  data,
  staticData,
  selectedState,
  tab,
  mode,
  selectAllOptions,
  selectedItem,
  filingId,
  filterAction,
  filingStatus,
}: DownloadDropdown) => {
  const { accessToken } = useContext(AuthContext);
  const dropdownRef = useRef(null);
  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)
  const [checkedItems, setCheckedItems] = useState<boolean>(false);
  let tempData: Array<any> = [];

  useOnClickOutside(dropdownRef, () => setDropdownVisibility(false));

  const getPreviewModeLabel = (docStatus: string) => {
    return {
      approved: "Approved",
      filed: "Filed",
      amended_v1: "Amended (v1)",
      amended_v2: "Amended (v2)",
    }[docStatus];
  };

  const getTabModesLabel = (itemAction: string, docStatus: string) => {
    let label: string;

    let aliasDocStatus = {
      approved: "clean",
      filed: "markup",
      amended_v1: "markup",
      amended_v2: "markup",
    }[docStatus];

    let aliasAction = { New: "New", Revised: "Revised" }[itemAction];

    if (
      mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS ||
      mode === CONST.FIDownloadCons.ACTIVE_TAB_RULES ||
      mode === CONST.FIDownloadCons.ACTIVE_TAB_LOSS_COSTS
    ) {
      label = `${aliasAction} (${aliasDocStatus})`;
    } else {
      label = itemAction;
    }

    return label;
  };

  if (
    mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS ||
    mode === CONST.FIDownloadCons.ACTIVE_TAB_RULES ||
    mode === CONST.FIDownloadCons.ACTIVE_TAB_LOSS_COSTS
  ) {
    listSectionData = listSectionData.filter(
      (item: any) =>
        getActionStatus(staticData, item.action).toLowerCase() !== "withdrawn"
    );

    tempData = listSectionData
      .filter(
        (doc: any) =>
          filingStatus?.toLowerCase() ===
          CONST.FIDownloadCons.FILING_STATUS_APPROVED // when filing status is approved
            ? true
            : doc.doc_status !== CONST.FIDownloadCons.STATUS_APPROVED // when filing status is other than approved
      )
      .map((item: any) =>
        item.document_list.map((list: any) =>
          JSON.stringify({
            id: `${getActionStatus(staticData, item.action)} ${
              list.doc_status
            }`,
            action: getActionStatus(staticData, item.action),
            doc_status: list.doc_status,
            filing_version: +list.filing_version, //prefix with + sign to convert string to number
            mandatory:
              mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS
                ? item.mandatory_indc
                : "N", ////mandatory_indc doesn't apply for rules and loss_costs so defaulat set to 'N'
            checked: false,
            label: getTabModesLabel(
              getActionStatus(staticData, item.action),
              list.doc_status
            ),
          })
        )
      );

    tempData = [...new Set(tempData.flat())].map((item: string) =>
      JSON.parse(item)
    );
  } else if (mode === CONST.FIDownloadCons.PREVIEW) {
    tempData = listSectionData.map((item: any) => ({
      id: item.doc_status,
      label: getPreviewModeLabel(item.doc_status),
      doc_status: item.doc_status,
      checked: false,
    }));
  }

  if (mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS) {
    tempData = tempData.filter(
      (item: any) =>
        (getActionStatus(staticData, item.action) === "New" ||
          getActionStatus(staticData, item.action) === "Revised") &&
        !item.doc_status.includes("amended")
    );
  } else if (
    mode === CONST.FIDownloadCons.ACTIVE_TAB_RULES ||
    mode === CONST.FIDownloadCons.ACTIVE_TAB_LOSS_COSTS
  ) {
    const versions = tempData.map(
      (item: { filing_version: number }) => item.filing_version
    );
    tempData = tempData.filter(
      (item: any) =>
        (getActionStatus(staticData, item.action) === "New" ||
          getActionStatus(staticData, item.action) === "Revised") &&
        (item.filing_version === Math.min(...versions) ||
          item.filing_version === Math.max(...versions))
    );

    ////remove duplicate rows from the object
    tempData = tempData.reduce(
      (
        prev,
        { id, action, label, doc_status, filing_version, checked, mandatory }
      ) =>
        prev.some(
          (item: any) =>
            item.id === id &&
            item.label === label &&
            getActionStatus(staticData, item.action) === action &&
            item.doc_status === doc_status &&
            item.filing_version === filing_version &&
            item.checked === checked &&
            item.mandatory === mandatory
        )
          ? prev
          : [
              ...prev,
              {
                id,
                action,
                label,
                doc_status,
                filing_version,
                checked,
                mandatory,
              },
            ],
      []
    );
  }

  let newTempData: any = [];
  let allNewItems = tempData.filter(
    (item: any) => getActionStatus(staticData, item.action) === "New"
  );
  let allRevised = tempData.filter(
    (item: any) => getActionStatus(staticData, item.action) === "Revised"
  );

  if (allNewItems.length > 0) {
    allNewItems = refreshTempData(allNewItems);
  }

  if (allRevised.length > 0) {
    allRevised = refreshTempData(allRevised);
  }

  if (allNewItems.length > 0) newTempData.push([...allNewItems.flat()]);
  if (allRevised.length > 0) newTempData.push([...allRevised.flat()]);
  if (newTempData.length > 0) tempData = [...newTempData.flat()];

  const [dropdownList, setDropdownList] = useState<Array<any>>(tempData);
  const [localCopyDropdownList, setLocalCopyDropdownList] =
    useState<Array<any>>(tempData);

  const [dropdownVisibility, setDropdownVisibility] = useState(false);

  useEffect(() => {
    if (filterAction === "New" || filterAction === "Revised") {
      setDropdownList(
        localCopyDropdownList.filter(
          (item: any) =>
            getActionStatus(staticData, item.action) === filterAction
        )
      );
    }
  }, [filterAction]);

  useEffect(() => {
    setLocalCopyDropdownList(tempData);
  }, [selectedState]);

  useEffect(() => {
    if (filingStatus === "STATUS_PENDING") {
      const dropdownLabelList = new Set<string>();
      setDropdownList(
        dropdownList.filter((item: any) => {
          if (
            item?.doc_status === "approved" ||
            dropdownLabelList.has(item.label)
          ) {
            return false;
          }
          dropdownLabelList.add(item.label);
          return true;
        })
      );
    }
  }, [filingStatus]);

  const handleOptionClick = (
    allChecked: boolean,
    type: string,
    indexClicked: number,
    isChecked: boolean
  ) => {
    let arr1 = [...dropdownList];

    const hasMandatory = arr1.some((item: any) => item?.mandatory);
    if (hasMandatory) {
      arr1 = arr1.filter(
        (item: any) =>
          item?.mandatory ===
          (filterAction?.toLowerCase() === "mandatory" ? "Y" : "N")
      );
    }

    if (
      type === CONST.FIDownloadCons.TYPE_ALL ||
      type === CONST.FIDownloadCons.TYPE_APPROVED ||
      type.split("/")[0] === CONST.FIDownloadCons.TYPE_FILED ||
      type.split("/")[1] === CONST.FIDownloadCons.TYPE_AMENDED_V1 ||
      type.split("/")[2] === CONST.FIDownloadCons.TYPE_AMENDED_V2
    ) {
      arr1
        .filter((item) =>
          type === CONST.FIDownloadCons.TYPE_ALL
            ? true
            : item.doc_status === type.split("/")[0] ||
              item.doc_status === type.split("/")[1] ||
              item.doc_status === type.split("/")[2]
        )
        .forEach((item: any) => (item.checked = !allChecked));
    } else {
      arr1[indexClicked].checked = !isChecked;
    }

    setDropdownList(arr1);

    let checkedItems = dropdownList.filter(
      (item: any) => item.checked === true
    ).length;

    if (checkedItems > 0) setCheckedItems(true);
    else setCheckedItems(false);
  };

  function refreshTempData(items: any) {
    let newTempData: any = [];
    let amended = items.filter((item: any) =>
      item.doc_status.includes("amended")
    ).length;
    let filed = items.filter((item: any) =>
      item.doc_status.includes("filed")
    ).length;
    let approved = items.filter((item: any) =>
      item.doc_status.includes("approved")
    ).length;

    if (amended > 0 && filed > 0)
      //remove filed, keep amended
      newTempData.push(
        items.filter(
          (item: any) =>
            item.doc_status !== CONST.FIDownloadCons.STATUS_FILED &&
            item.doc_status !== CONST.FIDownloadCons.STATUS_APPROVED
        )
      );
    else if (amended > 0 && filed === 0)
      //when no filed, keep amended
      newTempData.push(
        items.filter((item: any) => item.doc_status.includes("amended"))
      );
    else if (amended === 0 && filed > 0)
      //when no amended, keep filed
      newTempData.push(
        items.filter((item: any) => item.doc_status.includes("filed"))
      );

    if (approved > 0)
      newTempData.push(
        items.filter((item: any) => item.doc_status.includes("approved"))
      );

    return newTempData;
  }

  function flatDocuments(formData: any, status: string, folderName: string) {
    let documents: any;
    let filedDocuments: any;

    let docData = (docstatus: string) => {
      let doc = formData.map((item: any) => ({
        id: item.id,
        action: getActionStatus(staticData, item.action),
        document_title: item.document_title,
        state_type: item.state_type,
        file_path: item.document_list
          .filter((element: any) => element.doc_status === docstatus)
          .map((list: any) =>
            list.file_path.replace("[", "").replace("]", "").replace(/'/g, "")
          )
          .flat()[0],
        doc_status: item.document_list
          .filter((element: any) => element.doc_status === docstatus)
          .map((list: any) => list.doc_status)
          .flat()[0],
        folderName:
          item.mandatory_indc === "Y"
            ? `${"Mandatory"}${" "}${folderName}`
            : `${folderName}`,
      }));

      return doc;
    };

    if (status === CONST.FIDownloadCons.STATUS_APPROVED)
      documents = docData(status);

    if (
      status === CONST.FIDownloadCons.STATUS_FILED ||
      status === CONST.FIDownloadCons.STATUS_REVISED ||
      status.includes("amended")
    ) {
      const statuses = ["amended_v2", "amended_v1", "filed", "revised"];

      for (let _status of statuses) {
        documents = docData(_status);
        documents = documents.filter(
          (item: any) => item.file_path !== undefined
        );

        if (documents.length !== 0) {
          if (_status.includes("amended")) {
            filedDocuments = docData(status);

            if (filedDocuments.length > 0)
              documents = mergeArraysByReplacingId(
                "downloadDropdown",
                filedDocuments,
                documents
              );
          }
          break;
        }
      }
    }

    return documents;
  }

  function RulesDocuments(status: string, RuleData: any, folderName: string) {
    let documents: any;
    let docData = (docstatus: string) => {
      let doc = RuleData.map((d: any) => ({
        id: mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS ? d.id : d.rule_s,
        folderName: folderName,
        state_type: d.state_type,
        file_path: d.document_list
          .filter((el: any) => el.doc_status === docstatus)
          .map((list: any) =>
            list.file_path.replace("[", "").replace("]", "").replace(/'/g, "")
          )
          .flat()[0],
        doc_status: d.document_list
          .filter((el: any) => el.doc_status === docstatus)
          .map((list: any) => list.doc_status)
          .flat()[0],
      }));

      return doc;
    };

    if (status === CONST.FIDownloadCons.STATUS_APPROVED) {
      documents = docData(status);

      if (selectedState !== "MU") {
        let ruleId = documents.map((item: any) => item.id);
        const muData = data[mode].filter(
          (item: any) =>
            ruleId.includes(item.rule_s) && item.state_type === "MU"
        );

        let combineUrl = muData.map((item: any) => ({
          rule_s: item.rule_s,
          mergeUrl: item.document_list
            .filter((element: any) => element.doc_status === status)
            .map((list: any) =>
              list.file_path.replace("[", "").replace("]", "").replace(/'/g, "")
            )
            .flat()[0],
        }));

        const mergedData = combineUrl.map((item: any) => ({
          ...documents.find((doc: any) => doc.id === item.rule_s),
          ...item,
        }));

        documents = mergedData;
      }
    }

    if (
      status === CONST.FIDownloadCons.STATUS_FILED ||
      status === CONST.FIDownloadCons.STATUS_REVISED ||
      status.includes("amended")
    ) {
      const documentStatus = ["amended_v2", "amended_v1", "filed", "revised"];
      for (let docStatus of documentStatus) {
        documents = docData(docStatus);
        documents = documents.filter((doc: any) => doc.file_path !== undefined);
        if (documents.length !== 0) break;
      }
    }

    return documents;
  }

  const handleDownload = async () => {
    if (!dropdownList.some((item: { checked: boolean }) => item.checked))
      return;

    let urlsToPreSignAndDownload: any[] = [];
    let downloadData;
    let zipName;

    let filingVersion = String(dropdownList[0]?.filing_version) || "";

    let stoi = ["rules", "loss_costs"].includes(mode?.toLowerCase())
      ? getStoiData(data, mode, selectedState, filingVersion)
      : "";

    if (mode === CONST.FIDownloadCons.PREVIEW) {
      let status = dropdownList
        .filter((item: { checked: boolean }) => item.checked)
        .map((item: { doc_status: string }) => item.doc_status);

      listSectionData.forEach((item: any) => {
        item.folder = "";
        item.fileName = `${selectedItem}_${getPreviewModeLabel(
          item.doc_status
        )}.pdf`;
      });

      downloadData = listSectionData.filter((item: { doc_status: string }) =>
        status.includes(item.doc_status)
      );
      zipName = `${selectedState}-${selectedItem}.zip`;
    } else if (mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS) {
      let documents: any = [];
      const allNewItems = listSectionData.filter(
        (item: any) =>
          getActionStatus(staticData, item.action).toLowerCase() ===
          CONST.FIDownloadCons.ACTION_TYPE_NEW
      );
      const allRevisedItems = listSectionData.filter(
        (item: any) =>
          getActionStatus(staticData, item.action).toLowerCase() ===
          CONST.FIDownloadCons.ACTION_TYPE_REVISED
      );
      const selectedOption = dropdownList
        .filter((item: any) => item.checked === true)
        .map((item: any) => item.label);

      if (
        dropdownList.filter(
          (item) =>
            item.checked &&
            item.doc_status === CONST.FIDownloadCons.STATUS_APPROVED
        ).length > 0
      ) {
        if (selectedOption.includes("New (clean)")) {
          documents.push(
            flatDocuments(
              allNewItems,
              CONST.FIDownloadCons.STATUS_APPROVED,
              "New (clean)"
            )
          );
        }
        if (selectedOption.includes("Revised (clean)")) {
          documents.push(
            flatDocuments(
              allRevisedItems,
              CONST.FIDownloadCons.STATUS_APPROVED,
              "Revised (clean)"
            )
          );
        }
      }

      if (
        dropdownList.filter(
          (item) =>
            item.checked &&
            item.doc_status === CONST.FIDownloadCons.STATUS_FILED
        ).length > 0
      ) {
        if (selectedOption.includes("New (markup)")) {
          documents.push(
            flatDocuments(
              allNewItems,
              CONST.FIDownloadCons.STATUS_FILED,
              "New (markup)"
            )
          );
        }
        if (selectedOption.includes("Revised (markup)")) {
          documents.push(
            flatDocuments(
              allRevisedItems,
              CONST.FIDownloadCons.STATUS_FILED,
              "Revised (markup)"
            )
          );
        }
      }

      if (
        dropdownList.filter(
          (item) =>
            item.checked &&
            item.doc_status === CONST.FIDownloadCons.STATUS_AMENDED_V1
        ).length > 0
      ) {
        if (selectedOption.includes("New (markup)")) {
          documents.push(
            flatDocuments(
              allNewItems,
              CONST.FIDownloadCons.STATUS_AMENDED_V1,
              "New (markup)"
            )
          );
        }
        if (selectedOption.includes("Revised (markup)")) {
          documents.push(
            flatDocuments(
              allRevisedItems,
              CONST.FIDownloadCons.STATUS_AMENDED_V1,
              "Revised (markup)"
            )
          );
        }
      }

      for (let doc of documents) {
        doc.forEach((element: any) => {
          urlsToPreSignAndDownload.push({
            url: element.file_path,
            fileName: createFileName(element.id, element.doc_status), //createFileName(element.file_path, element.doc_status),
            type:
              element.file_path === "" || undefined
                ? CONST.FIDownloadCons.FORM_FILE_TYPE
                : element.file_path?.split("/")[0] !== ""
                ? element.file_path?.split("/")[0]
                : element.file_path?.split("/")[1],
            folder: `${mode.toUpperCase()}\\${element.folderName}`,
            mergeUrl: "",
            mergeUrlType: "",
            stoi,
          });
        });
      }

      zipName = `${selectedState}-${filingId}.zip`;
      downloadData = urlsToPreSignAndDownload;
    } else if (
      mode === CONST.FIDownloadCons.ACTIVE_TAB_RULES ||
      mode === CONST.FIDownloadCons.ACTIVE_TAB_LOSS_COSTS
    ) {
      let documents: any = [];
      const allNewItems = listSectionData.filter(
        (item: any) =>
          getActionStatus(staticData, item.action).toLowerCase() === "new"
      );
      const allRevisedItems = listSectionData.filter(
        (item: any) =>
          getActionStatus(staticData, item.action).toLowerCase() === "revised"
      );
      const selectedOption = dropdownList
        .filter((item: any) => item.checked === true)
        .map((item: any) => item.label);

      if (
        dropdownList.filter(
          (item) =>
            item.checked &&
            item.doc_status === CONST.FIDownloadCons.STATUS_APPROVED
        ).length > 0
      ) {
        if (selectedOption.includes("New (clean)")) {
          documents.push(
            RulesDocuments(
              CONST.FIDownloadCons.STATUS_APPROVED,
              allNewItems,
              "New (clean)"
            )
          );
        }
        if (selectedOption.includes("Revised (clean)")) {
          documents.push(
            RulesDocuments(
              CONST.FIDownloadCons.STATUS_APPROVED,
              allRevisedItems,
              "Revised (clean)"
            )
          );
        }
      }

      if (
        dropdownList.filter(
          (item) =>
            item.checked &&
            item.doc_status === CONST.FIDownloadCons.STATUS_FILED
        ).length > 0
      ) {
        if (selectedOption.includes("New (markup)")) {
          documents.push(
            RulesDocuments(
              CONST.FIDownloadCons.STATUS_FILED,
              allNewItems,
              "New (markup)"
            )
          );
        }
        if (selectedOption.includes("Revised (markup)")) {
          documents.push(
            RulesDocuments(
              CONST.FIDownloadCons.STATUS_FILED,
              allRevisedItems,
              "Revised (markup)"
            )
          );
        }
      }

      if (
        dropdownList.filter(
          (item) =>
            item.checked &&
            item.doc_status === CONST.FIDownloadCons.STATUS_AMENDED_V1
        ).length > 0
      ) {
        if (selectedOption.includes("New (markup)")) {
          documents.push(
            RulesDocuments(
              CONST.FIDownloadCons.STATUS_AMENDED_V1,
              allNewItems,
              "New (markup)"
            )
          );
        }
        if (selectedOption.includes("Revised (markup)")) {
          documents.push(
            RulesDocuments(
              CONST.FIDownloadCons.STATUS_AMENDED_V1,
              allRevisedItems,
              "Revised (markup)"
            )
          );
        }
      }

      documents.forEach((item: any) => {
        item.folder = `${mode.toUpperCase()}\\${item[0].folderName}`;
        item.type = CONST.FIDownloadCons.RULE_FILE_TYPE;
      });

      for (let doc of documents) {
        doc.forEach((element: any) => {
          urlsToPreSignAndDownload.push({
            url: element.file_path,
            fileName: createFileName(element.id, element.doc_status),
            type:
              element.file_path === "" || undefined
                ? CONST.FIDownloadCons.RULE_FILE_TYPE
                : element.file_path?.split("/")[0] !== ""
                ? element.file_path?.split("/")[0]
                : element.file_path?.split("/")[1],
            folder: doc.folder,
            mergeUrl: element.mergeUrl,
            mergeUrlType: "",
            stoi,
          });
        });
      }

      zipName = `${selectedState}-${filingId}.zip`;
      downloadData = urlsToPreSignAndDownload;
    }

    downloadData = downloadData.filter(
      (item: { url: string }) =>
        !item.url?.includes("N/A") && item.url !== "" && item.url !== undefined
    );

    await downloadAsZip({
      api_url: process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL ?? "",
      urlsToPreSignAndDownload: downloadData,
      headers: { Authorization: accessToken },
      zipName: zipName,
      successMessage: (
        <ToastMessage
          type="success"
          title={staticData.RevisionData.SuccessTitle.value}
          description={staticData.RevisionData.SuccessMessage.value}
        />
      ),
      errorMessage: (
        <ToastMessage
          type="error"
          title={staticData.RevisionData.ErrorTitle.value}
          description={staticData.RevisionData.ErrorMessage.value}
        />
      ),
    });

    setDropdownVisibility(false);
  };

  function createFileName(filename: string, status: string) {
    filename =
      mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS
        ? `${filename}_${status}.${"pdf"}`
        : `${mode} ${filename} - ${selectedState}_${status}.pdf`;
    return filename;
  }

  const allItemCheck = (selectionType: string) =>
    dropdownList
      .filter((item: any) =>
        selectionType === CONST.FIDownloadCons.TYPE_ALL
          ? true
          : item.doc_status === selectionType.split("/")[0] ||
            item.doc_status === selectionType.split("/")[1] ||
            item.doc_status === selectionType.split("/")[2]
      )
      .every((item: { checked: boolean }) => item.checked === true);

  const dropdownItem = (
    label: string,
    isChecked: boolean,
    onClick: () => void
  ) => (
    <span
      className={`dropdown-item pointer-cursor flex-wrapper ${
        isChecked ? "dropdown-item-selected" : ""
      }`}
      onClick={onClick}
      data-testid={`dropdown-item ${label}`}
    >
      <input
        type="checkbox"
        readOnly={true}
        checked={isChecked}
        tabIndex={-1}
        className="dropdown-item-checkbox"
        data-testid={`dropdown-item-checkbox ${label}`}
      />
      <span
        className="dropdown-item-checkmark"
        data-testid={`dropdown-item-checkmark ${label}`}
      />
      <span
        className="dropdown-item-text"
        data-testid={`dropdown-item-text ${label}`}
      >
        {label}
      </span>
    </span>
  );

  // const hasMandatoryProperty = dropdownList.some((item: any) =>
  //   item.hasOwnProperty("mandatory")
  // );

  const checkedList = dropdownList
    .filter((item) => item.checked)
    .map((item) => item.label);

  const analyticsList: string[] = [];

  const individualList: { [key: string]: string } = {
    "New (clean)": "New (clean)",
    "Revised (clean)": "Revised (clean)",
    "New (markup)": "New (markup)",
    "Revised (markup)": "Revised (markup)",
  };

  const addToDownloadList = (labels: string[], allDownload: string) => {
    if (labels.every((label) => checkedList.includes(label))) {
      analyticsList.push(allDownload);
    } else {
      labels.forEach((label) => {
        if (
          checkedList.includes(label) &&
          !analyticsList.includes(individualList[label])
        ) {
          analyticsList.push(individualList[label]);
        }
      });
    }
  };

  if (data?.filing_set.lob === "LOB_BP") {
    if (
      checkedList.includes("New (markup)") &&
      checkedList.includes("Revised (markup)")
    ) {
      analyticsList.push("All Filed (markup)");
    } else {
      const markup = checkedList.find(
        (item) => item === "New (markup)" || item === "Revised (markup)"
      );
      if (markup) {
        analyticsList.push(markup);
      }
    }
  } else {
    if (dropdownList.length === 2) {
      if (checkedList.includes("New (clean)"))
        analyticsList.push("All Approved (clean)");
      if (checkedList.includes("New (markup)"))
        analyticsList.push("All Filed (markup)");
    } else if (dropdownList.length === 4) {
      addToDownloadList(
        ["New (clean)", "Revised (clean)"],
        "All Approved (clean)"
      );
      addToDownloadList(
        ["New (markup)", "Revised (markup)"],
        "All Filed (markup)"
      );
    }
  }

  const isWithdrawnAction =
    data[tab]?.filter(
      (item: any) =>
        item.id === selectedItem &&
        getActionStatus(staticData, item.action)
          .toLowerCase()
          .includes("withdrawn")
    ).length > 0
      ? true
      : false;

  return (
    <div
      data-testid="download-dropdown"
      ref={dropdownRef}
      className="download-dropdown"
    >
      <span
        className={
          filterAction.toLowerCase() === "new" ||
          filterAction.toLowerCase() === "revised" ||
          filterAction.toLowerCase().includes("all") ||
          filterAction.toLowerCase().includes("state") ||
          filterAction.toLowerCase() === "mandatory" ||
          (filterAction === "" && !isWithdrawnAction) //empty check is from PreviewDocuments.tsx as it is supplied as empty
            ? "dropdown-btn flex-wrapper pointer-cursor"
            : "dropdown-btn-disabled flex-wrapper pointer-cursor"
        }
        data-testid="dropdown-btn"
        onClick={() => setDropdownVisibility(!dropdownVisibility)}
      >
        Download Options <ExpandMoreIcon />
      </span>
      {dropdownVisibility && (
        <div className="dropdown-menu" data-testid="dropdown-menu">
          {selectAllOptions && mode === CONST.FIDownloadCons.PREVIEW && (
            <>
              {dropdownItem("All", allItemCheck("all"), () =>
                handleOptionClick(allItemCheck("all"), "all", -1, false)
              )}
              <hr
                className="dropdown-options-seperator"
                data-testid="dropdown-options-seperator"
              />
            </>
          )}
          {(mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS ||
            mode === CONST.FIDownloadCons.ACTIVE_TAB_RULES ||
            mode === CONST.FIDownloadCons.ACTIVE_TAB_LOSS_COSTS) &&
            filingStatus === "STATUS_PENDING" && (
              <>
                {dropdownItem(
                  "All Filed (markup)",
                  allItemCheck("filed/amended_v1/amended_v2"),
                  () =>
                    handleOptionClick(
                      allItemCheck("filed/amended_v1/amended_v2"),
                      "filed/amended_v1/amended_v2",
                      -1,
                      false
                    )
                )}
                <hr className="dropdown-options-seperator" />
              </>
            )}
          {(mode === CONST.FIDownloadCons.ACTIVE_TAB_FORMS ||
            mode === CONST.FIDownloadCons.ACTIVE_TAB_RULES ||
            mode === CONST.FIDownloadCons.ACTIVE_TAB_LOSS_COSTS) &&
            filingStatus === "STATUS_APPROVED" && (
              <>
                {dropdownItem(
                  "All Approved (clean)",
                  allItemCheck(CONST.FIDownloadCons.STATUS_APPROVED),
                  () =>
                    handleOptionClick(
                      allItemCheck(CONST.FIDownloadCons.STATUS_APPROVED),
                      CONST.FIDownloadCons.STATUS_APPROVED,
                      -1,
                      false
                    )
                )}
                {dropdownItem(
                  "All Filed (markup)",
                  allItemCheck("filed/amended_v1/amended_v2"),
                  () =>
                    handleOptionClick(
                      allItemCheck("filed/amended_v1/amended_v2"),
                      "filed/amended_v1/amended_v2",
                      -1,
                      false
                    )
                )}

                <hr className="dropdown-options-seperator" />
              </>
            )}

          {dropdownList
            .sort((a: any, b: any) => a?.label?.localeCompare(b.label))
            .filter(
              (v, i, a) => a.findIndex((v2) => v2.label === v.label) === i
            )
            .map((item: any, index: number) =>
              dropdownItem(item.label, item.checked, () =>
                handleOptionClick(false, "", index, item.checked)
              )
            )}
          <hr className="dropdown-options-seperator" />
          <div
            data-testid="pointer-events"
            className={
              checkedItems ? "pointer-events-visible" : "pointer-events-none"
            }
            data-interaction="fi_download"
            data-filter-selected={filterAction}
            data-dl-option={analyticsList.map((item: any) => item)}
          >
            <button
              data-testid="download-now-btn pointer-cursor"
              className={
                checkedItems
                  ? "download-now-btn download-dropdown-visible"
                  : "download-now-btn download-dropdown-disabled"
              }
              onClick={() => handleDownload()}
              aria-disabled={true}
            >
              Download Now <DownloadIcon style={{ fontSize: "1rem" }} />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DownloadDropdown;
