import { useContext } from "react";
import dayjs from "dayjs";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import ReadFullContent from "./ReadFullContent";
import { download } from "src/helpers/fi/documentDownload";
import { AuthContext } from "../../../context/authContext";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";
import Accordion from "components/Platform/article/Accordion";
import { getHistoryDetails } from "src/helpers/fi/fiAccordion";

interface FITimeline {
  data: any;
  staticData: any;
  selectedItem: string;
  selectedState: string;
  serviceType: string;
}

const FITimeline = ({
  data,
  staticData,
  selectedItem,
  selectedState,
  serviceType,
}: FITimeline) => {
  const formsTabStaticData = staticData.FormsTabData;
  const { accessToken } = useContext(AuthContext);
  const relatedAgendaMinutes = data.forms
    .filter((item: { id: string }) => item.id === selectedItem)[0]
    ?.edge.filter(
      (item: { edge_type: string }) =>
        item.edge_type.toLowerCase() === "agenda_minutes"
    )
    .map((item: any) => ({
      dest_content_key: item.dest_content_key,
      history: item.history,
    }));

  const agendaMinutesList = data.agenda_minute_list
    ?.filter(
      (item: { id: string; action_type: string }) =>
        relatedAgendaMinutes?.some(
          (related: any) => related.dest_content_key === item.id
        ) && item.action_type?.toLowerCase() === "agenda"
    )
    .map((item: any) => {
      const related = relatedAgendaMinutes?.find(
        (related: any) => related.dest_content_key === item.id
      );
      return {
        ...item,
        history: related ? related?.history : null,
      };
    })
    .sort(
      (a: any, b: any) => Date.parse(b.panel_date) - Date.parse(a.panel_date)
    );

  const agendaMinutesListMinutes = data.agenda_minute_list
    ?.filter(
      (item: { id: string; action_type: string }) =>
        relatedAgendaMinutes?.some(
          (related: any) => related.dest_content_key === item.id
        ) && item.action_type?.toLowerCase() === "minutes"
    )
    .map((item: any) => {
      const related = relatedAgendaMinutes?.find(
        (related: any) => related.dest_content_key === item.id
      );
      return {
        ...item,
        history: related ? related?.history : null,
      };
    })
    .sort(
      (a: any, b: any) => Date.parse(b.panel_date) - Date.parse(a.panel_date)
    );

  const handleDownload = async (fileName: string, filePath: string) => {
    await download({
      api_url: process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL || "",
      urlToPreSign: {
        url: filePath,
        type: filePath.substring(1, 4),
      },
      headers: { Authorization: accessToken },
      fileName: fileName + ".pdf",
      successMessage: (
        <ToastMessage
          type="success"
          title={staticData.RevisionData.SuccessTitle.value}
          description={staticData.RevisionData.SuccessMessage.value}
        />
      ),
      errorMessage: (
        <ToastMessage
          type="error"
          title={staticData.RevisionData.ErrorTitle.value}
          description={staticData.RevisionData.ErrorMessage.value}
        />
      ),
    });
  };

  function getMinutesDetails(panelDate: string) {
    return data.agenda_minute_list.filter(
      (item: any) =>
        item.panel_date === panelDate &&
        item.action_type.toLowerCase() === "minutes"
    )[0];
  }

  const historyData = getHistoryDetails(data, selectedState, serviceType);

  const tabName: string | null =
    {
      CNTSRV_FRM: "form",
      CNTSRV_RUL: "rule",
      CNTSRV_LSC: "loss cost",
    }[serviceType] || null;

  const agendaMinutesListLength = agendaMinutesList?.length || 0;

  return (
    <>
      <div className="timeline-section" data-testid="timeline-section">
        {getHistoryDetails.length > 0 && (
          <div
            className="fi-accordion-item fi-accordion-basic"
            data-testid="fi-accordion-basic"
          >
            <Accordion
              staticData={staticData}
              data={data}
              fields={{
                Title: {
                  value: `Circulars associated with this ${tabName}`,
                },
                Content: {
                  value: "history-data",
                },
                product: { value: "FI" },
                filingData: historyData,
              }}
            />
          </div>
        )}
        {serviceType === "CNTSRV_FRM" &&
          (!agendaMinutesListLength ? (
            <span className="timeline-no-data">
              <span className="timeline-no-data-highlighted">
                {formsTabStaticData.NoHistoryHighlightedText.value}
              </span>{" "}
              {formsTabStaticData.NoHistoryText.value + " " + selectedItem}
            </span>
          ) : (
            agendaMinutesList.map((item: any, index: number) => (
              <div className="timeline-item" key={index}>
                <div
                  className="timeline-date"
                  data-testid={`timeline-date ${item?.panel_date}`}
                >
                  {dayjs(item.panel_date).format("MM/DD/YY")}
                </div>
                <div className="timeline-line-wrapper">
                  <div className="timeline-pointer" />
                  {agendaMinutesListLength > 1 && (
                    <>
                      <div className="timeline-line" />
                      <div className="timeline-arrow">
                        <ExpandMoreIcon className="timeline-arrow-icon" />
                      </div>
                    </>
                  )}
                </div>
                <div
                  className="timeline-content"
                  data-testid={`timeline-content ${item?.history}`}
                >
                  {item.history && (
                    <ReadFullContent
                      orderIndex={0}
                      topicName=""
                      key={index + selectedItem}
                      contentClassName="fi-timeline"
                      content={item.history}
                      lineClamp={agendaMinutesListLength < 2 ? false : 3}
                      expandLabel="Read Full Summary"
                      collapseLabel="Collapse Summary"
                    />
                  )}
                  <div
                    className="timeline-download-section"
                    data-testid={`timeline-download-section ${item?.id}`}
                  >
                    <div
                      className="timeline-download pointer-cursor"
                      data-testid={`timeline-pdf-download ${item?.id}`}
                      onClick={() => handleDownload(item.id, item.file_path)}
                    >
                      <span>Download Agenda Circular:</span>
                      <PictureAsPdfIcon style={{ fontSize: "1.25rem" }} />
                    </div>
                    {data.filing_set.lob === "LOB_FR" && (
                      <ul>
                        <li>
                          <span className="timeline-panel-text">
                            Panel Item ID:
                          </span>{" "}
                          {item.action_type.toLowerCase() === "agenda" &&
                            item.panel_number}
                        </li>
                      </ul>
                    )}
                    <div
                      className="timeline-download pointer-cursor"
                      onClick={() =>
                        handleDownload(
                          getMinutesDetails(item.panel_date).id,
                          getMinutesDetails(item.panel_date).file_path
                        )
                      }
                    >
                      <span>Download Minutes Circular:</span>
                      <PictureAsPdfIcon style={{ fontSize: "1.25rem" }} />
                    </div>
                    {data.filing_set.lob === "LOB_FR" && (
                      <ul>
                        <li>
                          <span className="timeline-panel-text">
                            Panel Item ID:
                          </span>{" "}
                          {agendaMinutesListMinutes.map(
                            (item: any) => item.panel_number
                          )}
                        </li>
                      </ul>
                    )}
                  </div>
                </div>
              </div>
            ))
          ))}
      </div>
    </>
  );
};

export default FITimeline;
