import { useState } from "react";
import Select, { components } from "react-select";
import LockIcon from "@mui/icons-material/Lock";

interface StateDropdown {
  lobOptions: any;
  selectedLOB: string;
  setSelectedLOB: any;
}

const LOBDropdown = ({
  lobOptions,
  selectedLOB,
  setSelectedLOB,
}: StateDropdown) => {
  const [stateInput, setStateInput] = useState("");
  console.log(stateInput, "stateInput");

  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
    }),
  };

  const lobOptionsList = lobOptions
    ?.map((item: any) => ({
      label: item.displayName,
      value: item.fields.Code.value,
      icon: item.fields["Active Flag"].value === "Y" ? false : <LockIcon />,
      isDisabled: item.fields["Active Flag"].value === "Y" ? false : true,
    }))
    .sort((a: any, b: any) => a.label.localeCompare(b.label));

  const handleInput = (item: string) => {
    setStateInput(item);
  };

  const { Option } = components;
  const IconOption = (props: any) => {
    return (
      <Option {...props}>
        {props.data.icon && <span>{props.data.icon}</span>}
        {props.data.label}
      </Option>
    );
  };

  return (
    <Select
      key={selectedLOB}
      aria-labelledby="Lob Filter dropdown"
      name="lobfilter"
      className="lob-filter"
      instanceId={`lob-filter ${selectedLOB}`}
      placeholder={
        <div className="select-lob-placeholder">Line of Business</div>
      }
      options={lobOptionsList}
      components={{ IndicatorSeparator: () => null, Option: IconOption }}
      maxMenuHeight={140}
      styles={reactSelectStyle}
      onChange={(e: any) => {
        setSelectedLOB(e.value);
      }}
      onInputChange={handleInput}
      value={lobOptionsList?.find((item: any) => item.value === selectedLOB)}
    />
  );
};

export default LOBDropdown;
