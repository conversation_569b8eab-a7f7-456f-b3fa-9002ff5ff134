/* eslint-disable @typescript-eslint/no-empty-function */
import React, { useState, useEffect, useContext } from "react";
import { AnimateChangeInHeight } from "./AnimateChangeInHeight";
import Select from "react-select";
import { groupBy } from "lodash";
import DownloadIcon from "@mui/icons-material/Download";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import PreviewDocuments from "./PreviewDocuments";
import {
  createPropsForPreview,
  getActionStatus,
  mergeArraysByReplacingId,
  generateStateDropdownOptions,
  getStoiData,
} from "src/helpers/fi/utils";
import { download } from "src/helpers/fi/documentDownload";
import { getFilingDetails } from "src/helpers/fi/filingDetails";
import DownloadDropdown from "./DownloadDropdown";
import { AuthContext } from "../../../context/authContext";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";
import Accordion from "components/Platform/article/Accordion";
import { getRuleDisplayName } from "src/helpers/fi/getRuleDisplayName";
import { getGroupingName } from "src/helpers/fi/getGroupingName";
import {
  getCriticalUpdates,
  getParentFiling,
} from "src/helpers/fi/fiAccordion";

interface ListSectionTypes {
  data: any;
  staticData: any;
  selectedState: string;
  tab: "forms" | "rules" | "loss_costs";
  serviceType: Array<string>;
  listAttributes: string;
  titlePrefix?: string;
  selectedTopic?: string;
  selectedTopicName?: string;
  selectedItem?: string;
  setSelectedItem?: any;
  tabDetails?: JSX.Element;
  setRefinedDataLength?: any;
}

const ListSection = (props: ListSectionTypes) => {
  const {
    data,
    selectedState,
    tab,
    serviceType,
    listAttributes: title,
    titlePrefix = "",
    selectedTopic = "",
    selectedTopicName,
    selectedItem,
    setSelectedItem = () => {},
    tabDetails,
    setRefinedDataLength = () => {},
  } = props;

  const serviceTypeMap = {
    forms: "CNTSRV_FRM",
    rules: "CNTSRV_RUL",
    loss_costs: "CNTSRV_LSC",
  };

  if (typeof window !== "undefined" && window.digitalData?.product?.FI) {
    window.digitalData.product.FI.service_type = tab ?? "";
  }

  const { accessToken } = useContext(AuthContext);

  const staticData = props.staticData.ListSectionData;
  // const [accordionList, setAccordionList] = useState(false);

  // const [selectedState, setSelectedState] = useAtom(selectedStateAtom)
  const stateSpecificLabelText =
    tab === "forms" ? `State Specific ${serviceType[0]}` : "State Exceptions";

  let refinedData = props.hasOwnProperty("selectedTopic")
    ? data[tab].filter((item: { topic_id: string[] }) =>
        item.topic_id?.includes(selectedTopic)
      )
    : data[tab];

  const filingsEdgeData = data.filings.filter(
    (item: { service_type: string }) => item.service_type === serviceType[1]
  )[0].edge;

  const isLossCostAdoptState =
    data.filings[2]?.edge[0]?.adopt_state_list?.includes(selectedState);

  const mergeById = (
    array1: Array<any>,
    array2: Array<any>,
    attribute1: string,
    attribute2: string
  ) =>
    array1.map((itm) => ({
      ...array2.find((item) => item[attribute1] === itm[attribute2] && item),
      ...itm,
    }));

  refinedData = mergeById(
    refinedData,
    filingsEdgeData,
    "dest_content_key",
    "id"
  );
  const multiStateData = refinedData.filter(
    (item: any) => item.state_type === "MU"
  );

  if (tab === "forms") {
    refinedData = refinedData.filter(
      (item: {
        adopt_state_list: string[];
        withdraw_state_list: string[];
        rfc_state_list: string[];
        non_applicable_states: string[];
      }) =>
        selectedState === "MU"
          ? (item.adopt_state_list?.length > 1 ||
              item.withdraw_state_list?.length > 1 ||
              item.rfc_state_list?.length > 1) &&
            !item.non_applicable_states?.includes(selectedState)
          : item.adopt_state_list?.includes(selectedState) ||
            item.withdraw_state_list?.includes(selectedState) ||
            item.rfc_state_list?.includes(selectedState)
    );
  } else if (tab === "rules" || "loss_costs") {
    refinedData = refinedData.filter(
      (item: { state_type: string }) => item.state_type === selectedState
    );

    if (tab === "loss_costs") {
      if (multiStateData.length > 0 && isLossCostAdoptState) {
        let adoptedStateData = mergeArraysByReplacingId(
          "loss_costs",
          multiStateData,
          refinedData
        );
        refinedData = [...adoptedStateData];
        refinedData =
          selectedState === "MU"
            ? refinedData
            : refinedData.filter(
                (item: any) =>
                  item.adopt_state_list?.includes(selectedState) ||
                  item.withdraw_state_list?.includes(selectedState)
              );
      } else {
        refinedData = [...refinedData];
        refinedData =
          selectedState === "MU"
            ? refinedData
            : refinedData.filter(
                (item: any) =>
                  item.adopt_state_list?.includes(selectedState) ||
                  item.withdraw_state_list?.includes(selectedState)
              );
      }
    } else if (tab === "rules") {
      const refinedMU = multiStateData
        .map((data: any) => data.rule_s)
        .filter(
          (item: any) =>
            !refinedData.map((data: any) => data.rule_s).includes(item)
        );

      const refinedMUData = multiStateData.filter((item: any) =>
        refinedMU.includes(item.rule_s)
      );
      refinedData = [...refinedData, ...refinedMUData];
      refinedData =
        selectedState === "MU"
          ? refinedData
          : refinedData.filter(
              (item: any) =>
                item.state_type?.includes(selectedState) ||
                item.state_type?.includes("MU")
            );
    }
  }

  let stateOptions = generateStateDropdownOptions(
    props?.staticData?.RevisionData?.SelectedStates?.targetItems || []
  );

  const selectedStateDetails = stateOptions?.filter(
    (item: any) => item.value === selectedState
  );

  const parentFiling = getParentFiling(
    data,
    selectedState,
    serviceTypeMap[tab]
  );

  const criticalUpdates = getCriticalUpdates(
    data,
    selectedState,
    serviceTypeMap[tab]
  );

  const actionCount = refinedData
    ?.filter((item: { rfc_state_list: string[]; mandatory_indc: string }) =>
      selectedState === "MU"
        ? true
        : !item.rfc_state_list?.includes(selectedState)
    )
    .map((item: { action: string }) => item.action)
    .reduce((obj: { [item: string]: any }, item: string) => {
      obj[item] = (obj[item] || 0) + 1;
      return obj;
    }, {});

  const filterOptions = Object.entries(actionCount).map(([key, value]) => ({
    label: `${getActionStatus(props.staticData, key)} (${value})`,
    value: getActionStatus(props.staticData, key),
  }));

  const mandatoryCount = refinedData.filter(
    (item: { mandatory_indc: string }) => item.mandatory_indc === "Y"
  ).length;
  mandatoryCount > 0 &&
    filterOptions.push({
      label: `Mandatory (${mandatoryCount})`,
      value: "Mandatory",
    });
  filterOptions.sort((a, b) => a.value.localeCompare(b.value));

  let listCount = refinedData.filter(
    (item: { rfc_state_list: string[] }) =>
      !item.rfc_state_list || !item.rfc_state_list?.includes(selectedState)
  ).length;

  listCount > 0 &&
  getFilingDetails({
    data: data,
    serviceType: serviceType[1],
    selectedState: selectedState,
    isMUFiledCircular: false,
  }).filing_status !== "STATUS_PENDING"
    ? filterOptions.unshift({
        label: `All Approved ${serviceType[0]} (${listCount})`,
        value: `All Approved ${serviceType[0]}`,
      })
    : filterOptions.unshift({
        label: `All ${serviceType[0]} (${listCount})`,
        value: `All ${serviceType[0]}`,
      });

  const rfcCount = refinedData.filter((item: { rfc_state_list: string[] }) =>
    selectedState === "MU"
      ? false
      : item.rfc_state_list?.includes(selectedState)
  ).length;
  rfcCount > 0 &&
    filterOptions.push({
      label: `Removed from Consideration (${rfcCount})`,
      value: "Removed from Consideration",
    });

  const stateSpecificCount = refinedData.filter(
    (item: { state_type: string }) =>
      selectedState === "MU" ? false : item.state_type === selectedState
  ).length;
  stateSpecificCount > 0 &&
    filterOptions.push({
      label: `${stateSpecificLabelText} (${stateSpecificCount})`,
      value: stateSpecificLabelText,
    });

  const reactSelectStyle = {
    control: (base: any) => ({
      ...base,
      border: 0,
      boxShadow: "none",
    }),
  };

  const [selectedFilter, setSelectedFilter] = useState(filterOptions[0]?.value);
  const filingId = data.filings.filter(
    (filing: { service_type: string }) => filing.service_type === serviceType[1]
  )[0].filing_id;

  const allFilingIds = data.filings
    .filter(
      (filing: { service_type: string }) =>
        filing.service_type === serviceType[1]
    )
    .map((filing: { filing_id: string }) => filing.filing_id);

  const filingStatus = getFilingDetails({
    data: data,
    serviceType: serviceType[1],
    selectedState: selectedState,
    isMUFiledCircular: false,
  }).filing_status;

  const tabName = {
    forms: "Form",
    rules: "Rule",
    loss_costs: "Loss Cost",
  }[tab];

  /*RG demo code , should be refactored or done better*/
  const downloadDocumentAction = async (
    item: {
      document_list: Array<{
        file_path: string;
        doc_status: string;
        filing_version: string;
      }>;
      display_form_number: string;
      rule_s: string;
      id: string;
    },
    event: React.MouseEvent | React.KeyboardEvent
  ) => {
    event.preventDefault();
    event.stopPropagation();

    if (item && item.document_list && item.document_list.length > 0) {
      const documentStatus = ["approved", "amended_v2", "amended_v1", "filed"];
      let downloadDoc:
        | { file_path: string; doc_status: string; filing_version: string }
        | undefined;
      let mergeUrl;

      for (let docStatus of documentStatus) {
        if (selectedState === "MU" || filingStatus !== "STATUS_APPROVED") {
          downloadDoc =
            docStatus !== "approved"
              ? item.document_list.filter(
                  (el) => el.doc_status === docStatus
                )[0]
              : undefined;
        } else {
          downloadDoc = item.document_list.filter(
            (el) => el.doc_status === docStatus
          )[0];

          if (
            data[tab !== "forms" ? tab : ""]?.filter(
              (item1: { id: string }) => item1.id === item.id
            )[0]?.state_type !== "MU"
          ) {
            mergeUrl =
              data[tab !== "forms" ? tab : ""]
                ?.filter(
                  (data: any) =>
                    data.rule_s === item.rule_s && data.state_type === "MU"
                )[0]
                ?.document_list.filter(
                  (item: any) => item?.doc_status === "approved"
                )[0]
                ?.file_path.replace("[", "")
                .replace("]", "")
                .replace(/'/g, "") || "";
          }
        }
        if (downloadDoc !== undefined) break;
      }

      let file_path =
        downloadDoc !== undefined
          ? downloadDoc.file_path
              .replace("[", "")
              .replace("]", "")
              .replace(/'/g, "")
          : "";
      let type = file_path.substring(0, 3);

      // Gowtham's code for type
      // if (file_path.startsWith("/")) type = file_path.substring(1, 4);

      // For datahub-flv override with FLV type
      if (file_path.startsWith("/")) {
        type =
          item.display_form_number !== undefined
            ? file_path.substring(1, 4)
            : "FLV";
      }

      let filingVersion = downloadDoc?.filing_version;

      await download({
        api_url: process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL || "",
        urlToPreSign: {
          url: file_path,
          type,
          stoi: ["Rule", "Loss Cost"].includes(tabName)
            ? getStoiData(data, tab, selectedState, filingVersion ?? "")
            : "",
          mergeUrl,
        },
        headers: { Authorization: accessToken },
        fileName:
          tabName +
          " " +
          (item.display_form_number ?? item.rule_s) +
          " - " +
          selectedState +
          ".pdf",
        successMessage: (
          <ToastMessage
            type="success"
            title={props.staticData.RevisionData.SuccessTitle.value}
            description={props.staticData.RevisionData.SuccessMessage.value}
          />
        ),
        errorMessage: (
          <ToastMessage
            type="error"
            title={props.staticData.RevisionData.ErrorTitle.value}
            description={props.staticData.RevisionData.ErrorMessage.value}
          />
        ),
      });
    }
  };
  /*RG end demo code */

  let listSectionData = refinedData
    .filter(
      (item: {
        rfc_state_list: string[];
        mandatory_indc: string;
        action: string;
        state_type: string;
      }) =>
        selectedFilter === `All Approved ${serviceType[0]}` ||
        selectedFilter === `All ${serviceType[0]}`
          ? selectedState === "MU"
            ? true
            : !item.rfc_state_list ||
              !item.rfc_state_list?.includes(selectedState)
          : selectedFilter === stateSpecificLabelText
          ? item.state_type === selectedState
          : selectedFilter === "Removed from Consideration"
          ? selectedState === "MU"
            ? item.rfc_state_list?.length > 0
            : item.rfc_state_list?.includes(selectedState)
          : selectedFilter === "Mandatory"
          ? item.mandatory_indc === "Y"
          : selectedFilter === getActionStatus(props.staticData, item.action) &&
            (selectedState === "MU"
              ? true
              : !item.rfc_state_list?.includes(selectedState))
    )
    .sort((a: any, b: any) =>
      typeof a[title] === "string"
        ? a[title].localeCompare(b[title])
        : a[title] > b[title]
        ? 1
        : -1
    );
  const HORulesType = [
    "RUA - State Additional Rules",
    "RU - Rules - State Exception And Multistate",
    "CLA - Classification Pages",
  ];

  listSectionData =
    data.filing_set.lob === "LOB_HO" && tabName === "Rule"
      ? HORulesType.map((documentType: string) =>
          listSectionData.filter((item: any) =>
            item.document_type.includes(documentType)
          )
        ).flat()
      : listSectionData;

  const withdrawAction = listSectionData
    .filter((item: any) =>
      getActionStatus(props.staticData, item.action)
        .toLowerCase()
        .includes("withdrawn")
    )
    .map((item: any) => item.id);

  const filteredStateFilings: Array<any> = data.filings
    .filter(
      (item: { service_type: string }) => item.service_type === serviceType[1]
    )[0]
    .filing_status_applicability.filter(
      (item: { jurisdiction: string }) => item.jurisdiction === selectedState
    );

  useEffect(() => {
    props.hasOwnProperty("selectedTopic") ||
      setRefinedDataLength(refinedData.length);
  }, [refinedData.length]);

  useEffect(() => {
    filterOptions.length > 0 && setSelectedFilter(filterOptions[0].value);
  }, [selectedState]);

  useEffect(() => {
    props.hasOwnProperty("selectedTopic") || setSelectedItem("");
  }, [selectedFilter, selectedState]);

  const ListItem = ({ item, itemIndex }: { item: any; itemIndex: number }) => (
    <React.Fragment>
      <li
        key={data.filing_set.lob + itemIndex}
        tabIndex={0}
        onKeyUp={(ev) => {
          if (ev.key === "Enter" || ev.key === " ") {
            !selectedTopic &&
              setSelectedItem(selectedItem === item.id ? "" : item.id);
          }
        }}
        className={`${
          selectedTopicName ? "list-section-topical" : "list-section-info"
        } flex-wrapper ${selectedItem === item.id ? "selected-item" : ""}`}
        onClick={() => {
          !selectedTopic &&
            setSelectedItem(selectedItem === item.id ? "" : item.id);
          let evt = new CustomEvent("event-view-end");
          document.body.dispatchEvent(evt);

          let evt_details = new CustomEvent("event-detailBox-view");
          document.body.dispatchEvent(evt_details);
        }}
        data-testid={`list-section-info-${item[title]}`}
      >
        <div
          data-testid="list-section-number-title"
          className="list-section-number-title"
        >
          <span className="list-section-number">
            {["LOB_HO", "LOB_BP"].includes(data.filing_set.lob) &&
            (tab === "rules" || tab === "loss_costs")
              ? getRuleDisplayName(item[title], item["document_type"], tab)
              : (tab === "loss_costs" ? "Loss Cost " : "") +
                titlePrefix +
                " " +
                item[title]}
          </span>
          {getRuleDisplayName(item[title], item["document_type"], tab) !==
            item.document_title && (
            <>
              {(["LOB_HO", "LOB_BP"].includes(data.filing_set.lob) &&
              (tab === "rules" || tab === "loss_costs")
                ? getRuleDisplayName(item[title], item["document_type"], tab)
                : titlePrefix + " " + item[title]) && " - "}
              <span className="list-section-title">
                {item.document_type === "CT - Class Table" ? (
                  <b>{item.document_title}</b>
                ) : (
                  item.document_title
                )}
              </span>
            </>
          )}
        </div>
        {serviceType[0] === "Loss Costs" ? null : (
          <PreviewDocuments
            key={itemIndex}
            data={data}
            staticData={props.staticData}
            tab={tab}
            selectedItem={item.id}
            selectedState={selectedState}
            document_list={createPropsForPreview({
              jsonData: data,
              section: serviceType[0],
              state: selectedState,
              key:
                serviceType[0] === "Forms"
                  ? item.display_form_number
                  : item.rule_s,
              itemDocumentList: item.document_list,
            })}
            filingStatus={filingStatus}
            filterAction={selectedFilter}
          />
        )}
        {/* <DownloadIcon className='list-section-download' /> */}
        {/* RG, should be a button, replace with button, check css, its breaking */}
        {item.document_list.length >= 0 && (
          <DownloadIcon
            className={
              !withdrawAction.includes(item.id)
                ? "list-section-download pointer-cursor"
                : "list-section-hide-download"
            }
            data-testid={`list-section-download-${item[title]}`}
            onClick={(ev: any) => downloadDocumentAction(item, ev)}
            tabIndex={0}
            role="button"
            aria-label="Download"
            data-interaction="fi_download2"
            data-action="download"
            data-content-number={item.id}
            style={{ fontSize: "1.95rem" }}
            onKeyUp={(ev: any) => {
              if (ev.key === "Enter" || ev.key === " ")
                downloadDocumentAction(item, ev);
            }}
          />
        )}
      </li>
      {selectedItem === item.id && (
        <div className="selected-item-content">{tabDetails}</div>
      )}
    </React.Fragment>
  );

  if (filteredStateFilings[0]?.filing_status === "STATUS_NOFILINGIMPACT") {
    return (
      <div className="list-section-list-wrapper list-section-not-filed">
        <div className="list-section-notfiled-text">
          <span>{serviceType[0]} have not been filed for this state</span>
        </div>
      </div>
    );
  }

  const toggleAccordion = (index: number) => {
    const allDetails = document.querySelectorAll("details");

    allDetails.forEach((detail, idx) => {
      if (idx !== index && detail.hasAttribute("open")) {
        detail.removeAttribute("open");
      }
    });
  };

  const sortDataByRules = (item: any) => {
    const parseRule = (item: any) => {
      if (typeof item === "string" && item.includes("-")) {
        return parseInt(item.split("-")[0], 10);
      }
      return parseInt(item, 10);
    };

    return item.sort(
      (a: any, b: any) => parseRule(a.rule_s) - parseRule(b.rule_s)
    );
  };

  listSectionData = sortDataByRules(listSectionData);

  return (
    <>
      {listSectionData.length > 0 &&
      filingStatus !== undefined &&
      filingStatus !== "" ? (
        <div
          className="list-section-list-wrapper"
          data-testid="list-section-list-wrapper"
        >
          <div
            className="list-section-filing-id"
            data-testid="list-section-filing-id"
          >
            {allFilingIds.map((filingId: string, index: number) => {
              if (window?.digitalData) {
                window.digitalData.product.FI.filing_ID = filingId;
              }
              return (
                <div key={index}>
                  {props.hasOwnProperty("selectedTopic") ? (
                    <>
                      <b>
                        {/* {(window.digitalData.product.FI.filing_ID = filingId)} */}
                        {`${serviceType[0]} related to ${selectedTopicName} ${staticData.FromFilingText.value} ${filingId}`}
                      </b>
                    </>
                  ) : (
                    `${serviceType[0]} ${staticData.FilingIdText.value} ${filingId}`
                  )}
                  {!props.hasOwnProperty("selectedTopic") &&
                    ["Forms", "Rules", "Loss Costs", "Class Plan"].includes(
                      serviceType[0]
                    ) && (
                      <span className="about-filing">
                        {" "}
                        <PreviewDocuments
                          key={1}
                          data={data}
                          staticData={props.staticData}
                          tab={tab}
                          filingId={filingId}
                          buttonValue="About this Filing"
                          selectedState={selectedState}
                          filingStatus={filingStatus}
                          filterAction={selectedFilter}
                        />
                      </span>
                    )}
                </div>
              );
            })}
          </div>
          {(tab === "rules" || tab === "loss_costs" || tab === "forms") &&
            selectedItem === "" &&
            criticalUpdates[0]?.id !== "" &&
            (parentFiling.length > 0 || criticalUpdates.length > 0) && (
              <div className="fi-accordion-list">
                {(tab === "rules" || tab === "loss_costs") &&
                  parentFiling.length > 0 && (
                    <div
                      className={` ${
                        criticalUpdates.length > 0 ? "parent-filing" : ""
                      }`}
                    >
                      {parentFiling.map((item, index) => (
                        <div
                          key={index}
                          className="fi-accordion-wrapper"
                          onClick={() => toggleAccordion(index)}
                        >
                          <Accordion
                            params={props}
                            staticData={props.staticData}
                            selectedState={selectedState}
                            data={data}
                            fields={{
                              heading: { value: "" },
                              Title: {
                                value: `This ${selectedStateDetails[0]?.label} revision also introduces the following prior revision(s):`,
                              },
                              Content: { value: "parent-filing" },
                              product: { value: "FI" },
                              filingData: item,
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                {criticalUpdates.length > 0 && (
                  <div
                    className={`critical-update ${
                      (tab === "rules" || tab === "loss_costs") &&
                      parentFiling.length > 0
                        ? "critical-update-hr"
                        : ""
                    }`}
                  >
                    {criticalUpdates.map((item, index) => (
                      <div
                        key={`${index + parentFiling.length}`}
                        className={"fi-accordion-wrapper"}
                        onClick={() =>
                          toggleAccordion(index + parentFiling.length)
                        }
                      >
                        <Accordion
                          key={index}
                          params={props}
                          staticData={props.staticData}
                          data={data}
                          fields={{
                            heading: { value: "" },
                            Title: {
                              value: `Critical Update : Circular ${item.id}`,
                            },
                            Content: { value: "critical-update" },
                            product: { value: "FI" },
                            filingData: [item],
                          }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

          <div
            className="wrapper-all-list-section"
            data-testid="wrapper-all-list-section"
          >
            <div
              className="all-list-section"
              data-testid="all-list-section"
              data-interaction="fi_download"
              data-filter-selected={selectedFilter}
            >
              <span
                className="list-section-type-text"
                data-testid="list-section-type-text"
              >
                {serviceType[0]} {staticData.TypeText.value}:
              </span>
              <Select
                key={selectedState}
                classNamePrefix="react-select"
                className="select-filter-type pointer-cursor"
                instanceId="select-filter-type pointer-cursor"
                data-testid="select-filter-type"
                options={filterOptions}
                components={{
                  IndicatorSeparator: () => null,
                  DropdownIndicator: () => <ExpandMoreIcon />,
                }}
                isSearchable={false}
                styles={reactSelectStyle}
                onChange={(e: any) => {
                  setSelectedFilter(e.value);
                }}
                defaultValue={filterOptions[0]}
              />
            </div>
            <div
              className="list-section-download-options"
              data-testid="pointer-event-enabled-disabled"
              style={
                selectedFilter !== "New" &&
                selectedFilter !== "Revised" &&
                selectedFilter !== "Mandatory" &&
                !selectedFilter.includes("All") &&
                selectedFilter !== stateSpecificLabelText
                  ? { pointerEvents: "none" }
                  : { pointerEvents: "visible" }
              }
            >
              <DownloadDropdown
                key={selectedState + selectedFilter}
                listSectionData={listSectionData}
                data={data}
                staticData={props.staticData}
                selectedState={selectedState}
                mode={tab}
                selectAllOptions={true}
                selectedItem={filingId}
                filingId={filingId}
                filterAction={selectedFilter}
                filingStatus={filingStatus === undefined ? "" : filingStatus}
              />
            </div>
          </div>
          <ul className="list-section-list" data-testid="list-section-list">
            {tab === "forms" || props.hasOwnProperty("selectedTopic") ? (
              listSectionData.map((item: any, index: number) => (
                <ListItem key={index} item={item} itemIndex={index} />
              ))
            ) : (
              <>
                {Object.entries(
                  groupBy(
                    listSectionData.filter((item: any) =>
                      item.hasOwnProperty("document_type")
                    ),
                    ({ document_type }: { document_type: string }) =>
                      (getGroupingName()[document_type] ?? false) ||
                      document_type
                  )
                )
                  .sort((a, b) => {
                    const groupingOrder = Object.values(getGroupingName());
                    const aIndex = groupingOrder.indexOf(a[0]);
                    const bIndex = groupingOrder.indexOf(b[0]);
                    return aIndex - bIndex;
                  })
                  .map(([group, groupItems]: any, index: number) => (
                    <AnimateChangeInHeight key={index}>
                      <details
                        className="list-group"
                        key={index}
                        onToggle={() => {
                          if (
                            groupItems
                              .map(({ id }: { id: string }) => id)
                              .includes(selectedItem)
                          )
                            setSelectedItem("");
                        }}
                      >
                        <summary className="list-grouping-header">
                          {group} ({groupItems.length})
                        </summary>
                        {groupItems.map((item: any) => (
                          <ListItem key={index} item={item} itemIndex={index} />
                        ))}
                      </details>
                    </AnimateChangeInHeight>
                  ))}
                {listSectionData
                  .filter(
                    (item: { document_type: string }) =>
                      !item.hasOwnProperty("document_type")
                  )
                  .map((item: any, index: number) => (
                    <ListItem key={index} item={item} itemIndex={index} />
                  ))}
              </>
            )}
          </ul>
        </div>
      ) : (
        <div className="empty-subject-section">
          There are no {serviceType[0]} associated with {selectedTopicName} in
          this revision set
        </div>
      )}
    </>
  );
};

export default ListSection;
