import { type ReactNode, useEffect } from "react";
import { createPortal } from "react-dom";
// import FocusTrap from "focus-trap-react";

export default function Modal({
  show,
  onModalClick,
  children,
}: {
  show: boolean;
  onModalClick: any;
  children: ReactNode | ReactNode[];
}) {
  useEffect(() => {
    let timeout: number;
    if (typeof window !== "undefined") {
      document.documentElement.style.scrollbarGutter = "stable";
      timeout = window.setTimeout(() => {
        if (show) document.body.style.overflowY = "hidden";
        else document.body.style.overflowY = "auto";
      }, 400);
    }

    let modalclass = document.querySelector(".modal");
    modalclass?.addEventListener("click", (e: any) => {
      const res = e.target.className;
      if (res === "filing-intelligence modal") {
        onModalClick();
      }
    });

    return () => {
      if (typeof window !== "undefined" && timeout)
        window.clearTimeout(timeout);
    };
  }, [show]);

  if (!show) return null;

  return createPortal(
    // <FocusTrap>
    <main
      className="filing-intelligence modal"
      data-testid="filing-intelligence modal"
      onClick={(ev) => {
        ev.preventDefault();
        ev.stopPropagation();
      }}
    >
      <div className="modal-content" data-testid="modal-content">
        {children}
      </div>
    </main>,
    // </FocusTrap>
    document.body
  );
}
