interface NotSubscribedMessage {
  splitColumn: boolean;
}

const NotSubscribedSupportMsg = () => (
  <>
    <p>
      For details on how to gain access, please talk to your company
      {"'"}s ISO Participation Official or designee.
    </p>
    <p>
      Not sure who that is? Verisk Customer Support can help:
      <br />
      <a href="mailto:<EMAIL>">
        <u><EMAIL></u>
      </a>
      <br />
      1-800-888-4476
    </p>
  </>
);

const NotSubscribedMessage = (props: NotSubscribedMessage) => {
  const { splitColumn } = props;

  return (
    <div
      className="entitlement-subscription-container"
      data-testid="entitlement-subscription-container"
    >
      <div className="not-subscribed">
        <div
          className={splitColumn ? "not-subscribed-first" : "no-split"}
          data-testid="not-subscribed"
        >
          <h5
            className="not-subscribed-title"
            data-testid="not-subscribed-title"
          >
            This content is locked
          </h5>
          <p data-testid="not-subscribed-info">
            It looks like you don{"'"}t have permission to access this
            information. Most information is governed by your company{"'"}s
            subscription and your level of access to this subscription, which
            can include a combination of line of business, state, and content
            type.
          </p>
          {!splitColumn && (
            <span data-testid="not-subscribed-last">
              <NotSubscribedSupportMsg />
            </span>
          )}
        </div>
        {splitColumn && (
          <div
            className="not-subscribed-last"
            data-testid="not-subscribed-last"
          >
            <NotSubscribedSupportMsg />
          </div>
        )}
      </div>
    </div>
  );
};

export default NotSubscribedMessage;
