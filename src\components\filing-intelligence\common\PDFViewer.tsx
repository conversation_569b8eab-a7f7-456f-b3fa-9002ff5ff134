import { useState, useEffect, Fragment } from "react";
import { Document, Page } from "react-pdf";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";

export default function PDFViewer({ documentUrl }: { documentUrl: string }) {
  const [numPages, setNumPages] = useState(0);
  const [scale, setScale] = useState(0.7);
  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
  }
  useEffect(() => {
    function setScaleFromWindowSize() {
      // TO DO set scale based on window size
      if (typeof window !== "undefined") {
        setScale(0.8);
        if (window.innerWidth >= 320) setScale(0.45);
        if (window.innerWidth >= 420) setScale(0.55);
        if (window.innerWidth >= 520) setScale(0.7);
        if (window.innerWidth >= 640) setScale(0.9);
        if (window.innerWidth >= 768) setScale(1.1);
        if (window.innerWidth >= 1024) setScale(1.2);
      }
    }
    setScaleFromWindowSize();
    window.addEventListener("resize", setScaleFromWindowSize);

    return () => {
      window.removeEventListener("resize", setScaleFromWindowSize);
    };
  }, []);
  return (
    <Document
      file={documentUrl}
      className="document"
      data-testid="document"
      onLoadSuccess={onDocumentLoadSuccess}
      loading={
        <div className="loader">
          <span></span>
        </div>
      }
      error={
        <div className="error">
          <p className="message">
            Error fetching document to preview, please try again later...
          </p>
        </div>
      }
    >
      <div className="page-container" data-testid="page-container">
        {Array.from({ length: numPages }, (_, idx) => (
          <Fragment key={`${documentUrl}_${idx}`}>
            <Page
              pageNumber={idx + 1}
              className="page"
              key={idx}
              scale={scale}
              loading={
                <div className="loader" data-testid="loader">
                  <span></span>
                </div>
              }
              error={
                <div className="error">
                  <p className="message">
                    Error fetching page to preview, please try again later...
                  </p>
                </div>
              }
            />
            {/* add a separator here if needed */}
          </Fragment>
        ))}
      </div>
    </Document>
  );
}
