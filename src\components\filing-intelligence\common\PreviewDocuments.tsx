import {
  useState,
  type MouseEvent,
  type KeyboardEvent,
  useEffect,
  useRef,
  memo,
  useContext,
} from "react";
import Button from "@mui/material/Button";
import DownloadIcon from "@mui/icons-material/Download";
import PreviewIcon from "@mui/icons-material/Preview";
import CloseIcon from "@mui/icons-material/Close";
import Modal from "./Modal";
import PDFViewer from "./PDFViewer";
import PreviewDocumentsTabs from "./PreviewDocumentsTabs";
import { getPreSignedUrls, download } from "src/helpers/fi/documentDownload";
import {
  getDocumentUrlForPreview,
  generateTabsForPreview,
  generateStateDropdownOptions,
} from "src/helpers/fi/utils";
import DownloadDropdown from "./DownloadDropdown";
import { AuthContext } from "../../../context/authContext";
import ReadFullContent from "./ReadFullContent";
import ToastMessage from "components/Platform/common/AlertToastNotification/ToastMessage";
// import Loader from "components/common/Loader";

interface PreviewDocuments {
  data?: any;
  staticData: any;
  tab: "forms" | "rules" | "loss_costs";
  selectedItem?: string;
  selectedState: string;
  document_list?: Array<{
    file_path: string;
    doc_status?: string;
    merge_file_path?: string;
    filing_version?: string;
  }>;
  filingStatus?: string;
  filterAction?: string;
  buttonValue?: string;
  filingId?: string;
}

/*RG assumption, document_list never changes*/
const PreviewDocuments = ({
  data,
  staticData,
  tab,
  selectedItem,
  selectedState,
  document_list,
  filingStatus,
  filterAction,
  buttonValue,
  filingId,
}: PreviewDocuments) => {
  const approvedDocumentList = data[tab]
    ?.filter(
      (item: { state_type: string; id: string }) =>
        item.state_type === selectedState && item.id === selectedItem
    )[0]
    ?.document_list.filter(
      (item: {
        file_path: string;
        doc_status?: string;
        merge_file_path?: string;
      }) => item.doc_status === "approved"
    );

  const summaryStaticData = staticData.Summary;

  const serviceTypeMap = {
    forms: "CNTSRV_FRM",
    rules: "CNTSRV_RUL",
    loss_costs: "CNTSRV_LSC",
  };

  const popupFiling = (popupTab: string) =>
    data.filings
      .filter(
        (item: any) =>
          item.service_type === serviceTypeMap[tab] && item.id === filingId
      )
      .map((item: any) => {
        const muApplicability = item.filing_status_applicability.find(
          (applicability: any) => applicability.jurisdiction === popupTab
        );

        const stdoiDates = Array.isArray(muApplicability?.stdoi_dates)
          ? muApplicability.stdoi_dates
          : [];

        const urls = stdoiDates
          .map((date: any) => date.atf_url)
          .filter((url: any) => url !== undefined);

        return {
          id: item.id,
          filed_em_title: muApplicability?.filed_em_title ?? "",
          atf_url: urls ?? "",
        };
      });

  const getStoiData = (filingVersion: any) =>
    data?.filings
      ?.filter(
        (item: { jurisdiction: string; service_type: string }) =>
          item.service_type === (tab === "rules" ? "CNTSRV_RUL" : "CNTSRV_LSC")
      )[0]
      ?.filing_status_applicability?.filter(
        (item: { jurisdiction: string; filing_status: string }) =>
          item.jurisdiction === selectedState &&
          item.filing_status === "STATUS_APPROVED"
      )[0]
      ?.stdoi_dates?.filter(
        (item: { stoi: string; filing_version: string }) =>
          item.filing_version === filingVersion
      )
      .reduce((acc: any, item: any) => {
        const date = new Date(item.stoi);
        return !isNaN(date.getTime())
          ? acc + date.getFullYear().toString()
          : acc;
      }, "");

  const urlsToPreSign = document_list?.map((el) => ({
    url: el?.file_path.replace("[", "").replace("]", "").replace(/'/g, ""),
    type: "",
    doc_status: el?.doc_status || "", //RG revisit, RU brings docStatus & FO bring doc_status
    mergeUrl:
      (approvedDocumentList &&
        el?.merge_file_path
          ?.replace("[", "")
          .replace("]", "")
          .replace(/'/g, "")) ||
      "",
    mergeUrlType: "",
    stoi:
      tab === "rules" || tab === "loss_cost"
        ? getStoiData(el?.filing_version)
        : "",
  }));

  urlsToPreSign?.forEach((el) => {
    let type = el?.url?.substring(0, 3);
    if (el?.url?.startsWith("/")) type = el?.url?.substring(1, 4);
    el.type = type;
  });

  //RG test code for mergeurl , mergeUrl doc will be appended in the end
  // if (urlsToPreSign[0]?.type.toUpperCase() === 'FRM') {
  //   let draft = urlsToPreSign.filter((el) => el.doc_status?.toLowerCase() === 'draft')[0];
  //   let clean = urlsToPreSign.filter((el) => el.doc_status?.toLowerCase() === 'clean')[0];
  //   draft.mergeUrl = clean.url;
  //   draft.mergeUrlType = clean.type;
  // }

  const { accessToken } = useContext(AuthContext);

  // const selectedAction = data[tab].filter(
  //   (item: any) => item.id === selectedItem
  // )[0].action;

  const [showModal, setShowModal] = useState(false);
  const [documentUrl, setDocumentUrl] = useState("");

  const [showLoader, setShowLoader] = useState(false);
  const [showError, setShowError] = useState(false);
  const [listOfStates, setListOfStates] = useState([""]);
  const [switchTab, setSwitchTab] = useState("MU");

  let stateOptions = generateStateDropdownOptions(
    staticData?.RevisionData?.SelectedStates?.targetItems || []
  );

  const selectedStateDetails = stateOptions?.filter(
    (item: any) => item.value === selectedState
  );

  var aboutFiling = popupFiling(switchTab);

  useEffect(() => {
    selectedState !== "MU"
      ? setListOfStates(["Multistate", selectedStateDetails[0]?.label])
      : setListOfStates(["Multistate"]);
  }, [selectedState]);

  const iconEl = useRef<HTMLElement>(null);
  const abortController = useRef<AbortController>(new AbortController());
  let tabs: Array<string> =
    buttonValue === "About this Filing"
      ? listOfStates
      : generateTabsForPreview(urlsToPreSign, filingStatus);
  if (
    data.filing_set.lob === "LOB_BP" &&
    !tabs.includes("Classification Mapping Guide") &&
    buttonValue === "About this Filing"
  )
    tabs.push("Classification Mapping Guide");
  const [selectedTab, setSelectedTab] = useState("");
  const [apiResponse, setApiResponse] = useState<
    Array<{
      type: string;
      url: string;
      preSignedUrl: string;
    }>
  >([]);

  useEffect(() => {
    return () => {
      abortController.current.abort("Component removed from UI"); //RG if any pending api call abort
    };
  }, []);

  async function callApiAndSetResponse() {
    try {
      abortController.current = new AbortController();

      let resp: any = await getPreSignedUrls({
        api_url: process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL ?? "",
        urlsToPreSign,
        signal: abortController.current.signal,
        headers: { Authorization: accessToken },
      });
      setApiResponse(resp);
      let goToTab = "";
      if (tabs.length > 0)
        //Revisit the logic
        goToTab = tabs[0];
      setSelectedTab(goToTab);
      setDocumentUrl(getDocumentUrlForPreview(urlsToPreSign, resp, goToTab));
    } catch (err) {
      console.log(err);
      if ((err as { name: string }).name === "AbortError") return;
      setShowError(true);
    }
    setShowLoader(false);
  }

  function selectTab(goToTab: string) {
    if (buttonValue === "About this Filing") setSelectedTab(goToTab);
    goToTab === "Multistate" ? setSwitchTab("MU") : setSwitchTab(selectedState);
    if (apiResponse.length > 0) {
      setSelectedTab(goToTab);
      setDocumentUrl(
        getDocumentUrlForPreview(urlsToPreSign, apiResponse, goToTab)
      );
    }
  }

  async function openModal(ev: MouseEvent | KeyboardEvent) {
    ev.preventDefault();
    ev.stopPropagation();
    if (iconEl.current) iconEl.current.blur(); //RG, hack to remove focus from icon when modal is closed
    if (tabs.length > 0) setSelectedTab(tabs[0]);
    if (buttonValue === "About this Filing") {
      setShowModal(true);
      return;
    }
    if ((document_list?.length ?? 0) > 0) {
      setShowModal(true);
      if (apiResponse?.length === 0) {
        setShowLoader(true);
        await callApiAndSetResponse();
      }
    }
  }

  const handleDownloadCircular = (docType: string, filePath: string) => {
    download({
      api_url:
        docType === "pdf"
          ? process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_PDF_GATEWAY_URL ?? ""
          : process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_GATEWAY_URL ?? "",
      urlToPreSign: {
        url: filePath,
        type: "CRC",
      },
      headers: { Authorization: accessToken },
      fileName: filePath.substring(filePath.lastIndexOf("/") + 1),
      successMessage: (
        <ToastMessage
          type="success"
          title={staticData.RevisionData.SuccessTitle.value}
          description={staticData.RevisionData.SuccessMessage.value}
        />
      ),
      errorMessage: (
        <ToastMessage
          type="error"
          title={staticData.RevisionData.ErrorTitle.value}
          description={staticData.RevisionData.ErrorMessage.value}
        />
      ),
    });
  };
  const Filing = () => (
    <>
      <div className="filing-id">FILING {aboutFiling[0]?.id}</div>
      {aboutFiling[0]?.atf_url.length > 0 ? (
        <>
          <div className="filing-title">{aboutFiling[0]?.filed_em_title}</div>
          {aboutFiling[0]?.atf_url.map((item: any, index: any) => (
            <ReadFullContent
              key={index}
              orderIndex={index + 1}
              contentClassName="about"
              label={"About this Filing"}
              content={item}
              lineClamp={
                index > 0 ? 3 : aboutFiling[0].atf_url.length > 1 ? 3 : false
              }
              expandLabel={summaryStaticData.ReadFullSummary.value}
              collapseLabel={summaryStaticData.CollapseReadFullSummary.value}
            />
          ))}
        </>
      ) : (
        <p
          className=""
          dangerouslySetInnerHTML={{
            __html: summaryStaticData.AboutThisFiling.value,
          }}
        />
      )}
    </>
  );

  const ClassificationMappingGuide = () => {
    const relatedId = data.filings
      .filter((item: any) => item.id === filingId)[0]
      .edge.filter(
        (edge: any) =>
          edge.edge_type === "classification_plan" &&
          !edge.dest_content_key.endsWith("excel")
      )[0]?.dest_content_key;

    const classPlanData = data.filing_document_list.filter(
      (document: any) => document.id === relatedId
    )[0];
    const classPlanExcelData = data.filing_document_list.filter(
      (document: any) => document?.document_type === "excel"
    )[0];

    return (
      <>
        {tab === "rules" && (
          <>
            {classPlanData?.classmapping_html && (
              <>
                <b>{classPlanExcelData?.classmapping_title}</b>
                <ReadFullContent
                  key={1}
                  orderIndex={1 + 1}
                  contentClassName="classification-mapping-guide"
                  label={"About"}
                  content={classPlanData?.classmapping_html}
                  lob={data.filing_set.lob}
                  lineClamp={false}
                  expandLabel={summaryStaticData.ReadFullSummary.value}
                  collapseLabel={
                    summaryStaticData.CollapseReadFullSummary.value
                  }
                />
              </>
            )}
            <Button
              aria-label="Download Word Doc"
              className="circular-download-btn"
              data-testid="circular-download-btn-doc"
              variant="text"
              endIcon={<DownloadIcon />}
              onClick={() => {
                handleDownloadCircular("pdf", classPlanData?.file_path);
              }}
            >
              Circular {classPlanData?.circular_number}
            </Button>
          </>
        )}
        {classPlanExcelData?.classmapping_title && (
          <Button
            aria-label="Download Word Doc"
            className="circular-download-btn"
            data-testid="circular-download-btn-doc"
            variant="text"
            endIcon={<DownloadIcon />}
            onClick={() => {
              handleDownloadCircular("excel", classPlanExcelData?.file_path);
            }}
          >
            {classPlanExcelData?.classmapping_title}
          </Button>
        )}
      </>
    );
  };

  return (
    <>
      {/* RG, should be a button, replace with button, check css, its breaking */}
      <a
        className="list-section-preview"
        data-testid="list-section-preview"
        onClick={openModal}
        tabIndex={0}
        role="button"
        aria-label="Preview"
        data-interaction="fi-download2"
        data-action="preview"
        data-content-number={selectedItem}
        onKeyUp={(ev) => {
          if (ev.key === "Enter" || ev.key === " ") openModal(ev);
        }}
        ref={iconEl}
      >
        {buttonValue === "About this Filing" ? (
          <>
            {buttonValue}{" "}
            {/* <div className="eye-icons">
              <ErrorIcon fontSize="0.8125rem" transform="rotate(180deg)" />
            </div> */}
          </>
        ) : buttonValue === "Preview" ? (
          <>
            {buttonValue} <PreviewIcon />
          </>
        ) : (
          <PreviewIcon />
        )}
      </a>
      <Modal show={showModal} onModalClick={() => setShowModal(false)}>
        <div className="header-close" data-testid="header-close">
          <button
            aria-label="close"
            className="close pointer-cursor"
            data-testid="close"
            onClick={(ev) => {
              ev.preventDefault();
              ev.stopPropagation();
              setShowModal(false);
              setApiResponse([]);
            }}
          >
            <CloseIcon />
          </button>
        </div>
        <div className="preview-tab-wrapper" data-testid="preview-tab-wrapper">
          {buttonValue !== "About this Filing" && (
            <div className="download-wrapper" data-testid="download-wrapper">
              <DownloadDropdown
                listSectionData={urlsToPreSign}
                data={data}
                staticData={staticData}
                tab={tab}
                mode="preview"
                selectAllOptions={true}
                selectedItem={selectedItem ?? ""}
                selectedState={selectedState}
                filingId={""}
                filterAction={filterAction === "Withdrawn" ? filterAction : ""}
                filingStatus={filingStatus === undefined ? "" : filingStatus}
              />
            </div>
          )}
          <PreviewDocumentsTabs
            tabs={tabs}
            onChange={selectTab}
            selected={selectedTab}
          />
        </div>
        <div className="document-container" data-testid="document-container">
          {buttonValue === "About this Filing" ? (
            {
              "Classification Mapping Guide": <ClassificationMappingGuide />,
            }[selectedTab] || <Filing />
          ) : showLoader ? (
            <>
              <div className="fi-loader" data-testid="loader">
                <span></span>
              </div>
              {/* <Loader /> */}
            </>
          ) : showError ? (
            <div className="error" data-testid="error">
              <p className="message">
                Error fetching document to preview, please try again later...
              </p>
            </div>
          ) : (
            <PDFViewer documentUrl={documentUrl} />
          )}
        </div>
      </Modal>
    </>
  );
};

export default memo(PreviewDocuments);
