import { useState, useEffect } from "react";

interface PreviewDocumentsTabs {
  tabs: Array<string>;
  onChange: (tab: string) => void;
  selected: string;
}

export default function PreviewDocumentsTabs({
  tabs,
  onChange,
  selected,
}: PreviewDocumentsTabs) {
  const [selectedTab, setSelectedTab] = useState(selected);

  useEffect(() => {
    setSelectedTab(selected);
    onChange(selected);
  }, []);

  if (!Array.isArray(tabs) || tabs.length === 0) return <></>;

  return (
    <div className="tabs" data-testid="tabs">
      <div
        className="topical-sections preview-document-sections"
        data-testid="topical-sections preview-document-sections"
      >
        {tabs.map((tab: string, index: number) => (
          <div key={tab} className="topical-nav-tab">
            <button
              tabIndex={index}
              className={`topical ${
                selectedTab === tab ? "selected-detail" : ""
              }`}
              data-testid={`topical-${tab}`}
              onClick={() => {
                setSelectedTab(tab);
                onChange(tab);
              }}
              onKeyUp={(ev) => {
                if (ev.key === "Enter") {
                  ev.preventDefault();
                  ev.stopPropagation();
                  setSelectedTab(tab);
                  onChange(tab);
                }
              }}
            >
              {tab}
            </button>
            <div
              className={`${selectedTab === tab ? "overview-hr" : ""}`}
              data-testid="overview-hr"
            />
          </div>
        ))}
      </div>
    </div>
  );
}
