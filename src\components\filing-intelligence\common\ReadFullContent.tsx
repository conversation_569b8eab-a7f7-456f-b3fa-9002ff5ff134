import { useState, useRef, useEffect, useContext } from "react";
import DOMPurify from "dompurify";
import Button from "@mui/material/Button";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { useQuery } from "@tanstack/react-query";
import { getPreSignedUrls } from "src/helpers/fi/documentDownload";
import { AuthContext } from "src/context/authContext";
import Loader from "components/common/Loader";

interface ReadFullContent {
  contentClassName: string;
  orderIndex: number;
  label?: string;
  topicName?: string;
  content: string;
  lob?: string;
  lineClamp?: number | false;
  expandLabel?: string;
  collapseLabel?: string;
}

const ReadFullContent = ({
  contentClassName,
  orderIndex,
  label = "",
  topicName,
  content,
  lob,
  lineClamp,
  expandLabel,
  collapseLabel,
}: ReadFullContent) => {
  const [isReadFull, setIsReadFull] = useState(true);
  const [overflowInd, setOverflowInd] = useState(false);
  const readFullContentRef = useRef<any>(null);

  const { accessToken } = useContext(AuthContext);
  const {
    isPending,
    error,
    data: parsedContent = "",
  } = useQuery({
    queryKey: ["summary", content],
    staleTime: 24 * 60 * 60 * 1000,
    queryFn: async () => {
      if (!content.startsWith("FI") || !content.endsWith(".html"))
        return content;

      let preSignedDetails = await getPreSignedUrls({
        api_url: process.env.NEXT_PUBLIC_FI_DOWNLOADAPI_GATEWAY_URL ?? "",
        urlsToPreSign: [{ url: content, type: "HTML" }],
        headers: { Authorization: accessToken },
      });

      let htmlResponse = await fetch(preSignedDetails[0].preSignedUrl);

      return htmlResponse.text();
    },
  });

  const readFullContent = label
    ? parsedContent.replace(
        // /<p([^>]*)>/,
        /<([a-zA-Z]+)([^>]*)>/, // NOSONAR
        label.includes("About")
          ? `<$1$2><span class="read-full-label">${
              orderIndex > 1
                ? lob === "LOB_BP"
                  ? ""
                  : `Amendment ${orderIndex - 1}`
                : "About This Filing"
            }</span><br/>`
          : `<$1$2><span class="read-full-eoc-label read-full-label">${
              orderIndex !== 0 ? "" : "Explanation of Changes:"
            }</span>` +
              `<$1$2><span class="read-full-label">${
                label.includes("Amendment")
                  ? label
                  : label.includes("Summary")
                  ? `<$1$2><span class="read-full-label">${label}</span>`
                  : topicName
              }:</span>`
      )
    : parsedContent;

  const toggleReadFull = () => {
    setIsReadFull(!isReadFull);
  };

  useEffect(() => {
    setOverflowInd(
      readFullContentRef?.current?.clientHeight <
        readFullContentRef?.current?.scrollHeight
    );

    const overflowFunc = () => {
      setOverflowInd(
        readFullContentRef?.current?.clientHeight <
          readFullContentRef?.current?.scrollHeight
      );
    };

    window.addEventListener("resize", overflowFunc);

    return () => {
      window.removeEventListener("resize", overflowFunc);
    };
  }, [parsedContent, isReadFull]);

  if (isPending) return <Loader />;
  if (error) return <p>Error occurred while loading {label}</p>;

  return (
    <div
      className={`read-full-wrapper ${contentClassName}`}
      data-testid={`read-full-wrapper ${contentClassName}`}
    >
      {label.includes("About") ? (
        <>
          <span
            ref={readFullContentRef}
            style={{ WebkitLineClamp: Number(lineClamp) }}
            className={`read-full-content ${lineClamp} ${
              isReadFull && lineClamp ? "read-full-clamp" : ""
            }`}
            data-testid={"read-full-content read-full-clamp"}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(readFullContent),
            }}
          />
          {lineClamp && (overflowInd || !isReadFull) && (
            <Button
              disableRipple
              aria-label="Read Full Content"
              className="read-full-btn"
              variant="text"
              endIcon={
                isReadFull ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />
              }
              onClick={toggleReadFull}
            >
              {isReadFull ? expandLabel : collapseLabel}
            </Button>
          )}
        </>
      ) : (
        <>
          <span
            ref={readFullContentRef}
            style={{ WebkitLineClamp: Number(lineClamp) }}
            className={`read-full-content ${
              isReadFull && lineClamp ? "read-full-clamp" : ""
            }`}
            data-testid={"read-full-content read-full-clamp"}
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(readFullContent),
            }}
          />
          {lineClamp && (overflowInd || !isReadFull) && (
            <Button
              disableRipple
              aria-label="Read Full Content"
              className="read-full-btn"
              variant="text"
              endIcon={
                isReadFull ? <KeyboardArrowDownIcon /> : <KeyboardArrowUpIcon />
              }
              onClick={toggleReadFull}
            >
              {isReadFull ? expandLabel : collapseLabel}
            </Button>
          )}
        </>
      )}
    </div>
  );
};

export default ReadFullContent;
