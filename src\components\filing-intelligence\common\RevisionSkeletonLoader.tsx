import React from "react";
// import Skeleton from "react-loading-skeleton";
import Skeleton from "@mui/material/Skeleton";
// import "react-loading-skeleton/dist/skeleton.css";

const RevisionSkeletonLoader = () => {
  return (
    <div className="revision-wrapper">
      <div className="revision-header flex-wrapper">
        <div className="revision-title" data-testid="revision-title">
          <Skeleton width={317.63} height={40.5} />
        </div>
        <div className="revision-title-vertical-line" />
        <div>
          <Skeleton width={336.65} height={67.83} />
        </div>
      </div>
      <div className="content-wrapper">
        <div className="content-date-effective">
          <div className="flex-wrapper">
            <div className="content-ellipse">
              <Skeleton
                variant="circular"
                width={40}
                height={35}
                style={{ marginRight: `10px` }}
              />
            </div>
            {/* <hr /> */}
            {/* <div className="content-filing-topics">
              <Skeleton width={40} height={60} />
            </div> */}
          </div>
          <Skeleton
            variant="text"
            width={120}
            style={{ marginRight: `10px` }}
          />
        </div>

        <div className="outer-content">
          <div className="content">
            <div className="content-type-tabs">
              <div className="content-type-text"></div>
              <Skeleton width={300} height={20} />
              <nav className="content-tabs">
                <Skeleton width={150} height={50.63} />
                <Skeleton width={150} height={50.63} />
                <Skeleton width={150} height={50.63} />
                <Skeleton width={150} height={50.63} />
              </nav>
            </div>
            <div className="content-description">
              <div
                className="content-description-item"
                data-testid="content-description-item"
              >
                <span
                  className="content-description-item-text"
                  data-testid="content-description-text-id"
                >
                  <span>
                    <Skeleton variant="text" width={800} />
                  </span>
                  <Skeleton variant="text" width={600} />
                </span>
              </div>
              {/* <div
                className="content-tab-arrow-wrapper"
                data-testid="content-arrow-wrapper"
              >
                <Skeleton width={"auto"} height={30} />
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RevisionSkeletonLoader;
